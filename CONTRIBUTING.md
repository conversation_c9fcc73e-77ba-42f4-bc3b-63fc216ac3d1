# 🤝 Guide de Contribution - Sensei AI Suite

Merci de votre intérêt pour contribuer à Sensei AI Suite ! Ce guide vous explique comment participer au développement du projet.

## 🎯 **Types de Contributions**

Nous accueillons tous types de contributions :

- 🐛 **Corrections de bugs**
- ✨ **Nouvelles fonctionnalités**
- 📚 **Amélioration de la documentation**
- 🧪 **Tests supplémentaires**
- 🔧 **Optimisations de performance**
- 🔒 **Améliorations de sécurité**

## 🚀 **Démarrage Rapide**

### **1. Fork et Clone**
```bash
# Fork le projet sur GitHub
# Puis cloner votre fork
git clone https://github.com/VOTRE-USERNAME/sensei-ai-suite.git
cd sensei-ai-suite

# Ajouter le remote upstream
git remote add upstream https://github.com/sensei-ai/sensei-ai-suite.git
```

### **2. Configuration de l'Environnement**
```bash
# Installation des dépendances de développement
poetry install --with dev

# Activation de l'environnement
poetry shell

# Installation des hooks pre-commit
pre-commit install
```

### **3. Vérification de l'Installation**
```bash
# Tests rapides
python scripts/testing/quick_test.py

# Tests complets
poetry run pytest

# Vérification du code
poetry run black --check src/ tests/
poetry run isort --check-only src/ tests/
poetry run mypy src/
```

## 🔄 **Workflow de Développement**

### **1. Créer une Branche**
```bash
# Synchroniser avec upstream
git fetch upstream
git checkout main
git merge upstream/main

# Créer une branche feature
git checkout -b feature/nom-de-votre-feature

# Ou pour un bugfix
git checkout -b fix/description-du-bug
```

### **2. Développement**
```bash
# Faire vos modifications
# Ajouter des tests
# Mettre à jour la documentation si nécessaire

# Vérifier régulièrement
poetry run pytest tests/
python scripts/testing/quick_test.py
```

### **3. Validation Avant Commit**
```bash
# Formatage automatique
poetry run black src/ tests/
poetry run isort src/ tests/

# Vérifications
poetry run mypy src/
poetry run flake8 src/

# Tests complets
poetry run pytest --cov=src/sensei --cov-report=term-missing
```

### **4. Commit et Push**
```bash
# Commit avec message descriptif
git add .
git commit -m "feat: ajouter nouvelle fonctionnalité X"

# Push vers votre fork
git push origin feature/nom-de-votre-feature
```

### **5. Pull Request**
1. Aller sur GitHub
2. Créer une Pull Request depuis votre branche
3. Remplir le template de PR
4. Attendre la review

## 📝 **Standards de Code**

### **Style de Code**
- **Python** : PEP 8 avec Black (line length: 88)
- **Imports** : isort avec profil Black
- **Type hints** : Obligatoires pour toutes les fonctions publiques
- **Docstrings** : Format Google style

### **Exemple de Code Bien Formaté**
```python
from typing import Dict, List, Optional

import pandas as pd
from pydantic import BaseModel, Field


class ProspectData(BaseModel):
    """Données d'un prospect pour prédiction.
    
    Args:
        id_prospect: Identifiant unique du prospect
        nb_interactions: Nombre d'interactions historiques
        vitesse_reponse: Vitesse de réponse (rapide/moyen/lent)
    """
    
    id_prospect: str = Field(..., description="Identifiant unique")
    nb_interactions: int = Field(default=0, ge=0, description="Nombre d'interactions")
    vitesse_reponse: str = Field(
        default="moyen", 
        pattern="^(rapide|moyen|lent)$",
        description="Vitesse de réponse"
    )


def predict_conversion(data: ProspectData) -> Dict[str, float]:
    """Prédit la probabilité de conversion d'un prospect.
    
    Args:
        data: Données du prospect
        
    Returns:
        Dictionnaire avec la probabilité et la confiance
        
    Raises:
        ValueError: Si les données sont invalides
    """
    if data.nb_interactions < 0:
        raise ValueError("Le nombre d'interactions doit être positif")
    
    # Logique de prédiction...
    return {"probability": 0.85, "confidence": 0.92}
```

### **Conventions de Nommage**
- **Variables/fonctions** : `snake_case`
- **Classes** : `PascalCase`
- **Constantes** : `UPPER_SNAKE_CASE`
- **Fichiers** : `snake_case.py`
- **Modules** : `snake_case`

## 🧪 **Tests**

### **Structure des Tests**
```
tests/
├── unit/                   # Tests unitaires
│   ├── test_models.py
│   ├── test_api.py
│   └── test_utils.py
├── integration/            # Tests d'intégration
│   ├── test_bigquery.py
│   └── test_pipeline.py
└── fixtures/               # Données de test
    ├── sample_data.json
    └── mock_responses.py
```

### **Écriture de Tests**
```python
import pytest
from unittest.mock import Mock, patch

from sensei.models.conversion import ConversionModel


class TestConversionModel:
    """Tests pour le modèle de conversion."""
    
    def setup_method(self):
        """Setup avant chaque test."""
        self.model = ConversionModel()
    
    def test_model_initialization(self):
        """Test l'initialisation du modèle."""
        assert self.model.model_name == "conversion"
        assert not self.model.is_trained
    
    @patch('sensei.models.conversion.joblib.load')
    def test_model_loading(self, mock_load):
        """Test le chargement d'un modèle."""
        mock_load.return_value = Mock()
        
        self.model.load("fake/path")
        
        assert self.model.is_trained
        mock_load.assert_called_once()
    
    def test_prediction_without_training(self):
        """Test qu'une prédiction sans entraînement lève une erreur."""
        with pytest.raises(ValueError, match="doit être entraîné"):
            self.model.predict(pd.DataFrame())
```

### **Exécution des Tests**
```bash
# Tests unitaires seulement
poetry run pytest tests/unit/ -v

# Tests d'intégration (nécessite BigQuery)
poetry run pytest tests/integration/ -v

# Tests avec couverture
poetry run pytest --cov=src/sensei --cov-report=html

# Tests spécifiques
poetry run pytest tests/unit/test_models.py::TestConversionModel::test_model_initialization
```

## 📚 **Documentation**

### **Docstrings**
Utilisez le format Google style :

```python
def train_model(data: pd.DataFrame, target: str, params: Optional[Dict] = None) -> Dict:
    """Entraîne un modèle de machine learning.
    
    Args:
        data: DataFrame avec les données d'entraînement
        target: Nom de la colonne target
        params: Paramètres optionnels du modèle
        
    Returns:
        Dictionnaire avec les métriques d'entraînement
        
    Raises:
        ValueError: Si les données sont vides ou invalides
        KeyError: Si la colonne target n'existe pas
        
    Example:
        >>> data = pd.DataFrame({'feature': [1, 2], 'target': [0, 1]})
        >>> metrics = train_model(data, 'target')
        >>> print(metrics['accuracy'])
        0.85
    """
```

### **README et Guides**
- Utilisez des emojis pour la lisibilité
- Incluez des exemples de code
- Ajoutez des captures d'écran si pertinent
- Maintenez la cohérence avec le style existant

## 🔒 **Sécurité**

### **Bonnes Pratiques**
- ❌ **Jamais** de credentials en dur dans le code
- ✅ Utiliser les variables d'environnement
- ✅ Valider tous les inputs utilisateur
- ✅ Utiliser le mode read-only par défaut
- ✅ Logger les accès sensibles

### **Exemple Sécurisé**
```python
from config.settings import settings
from sensei.utils.logging import get_logger

logger = get_logger(__name__)

def secure_query(query: str) -> pd.DataFrame:
    """Exécute une requête sécurisée."""
    # Validation
    if "DELETE" in query.upper() or "UPDATE" in query.upper():
        raise ValueError("Seules les requêtes SELECT sont autorisées")
    
    # Audit
    logger.info("Exécution requête", query_preview=query[:100])
    
    # Exécution avec limites
    return client.query_df(query, max_bytes=settings.bigquery_max_bytes_billed)
```

## 📋 **Checklist Pull Request**

Avant de soumettre votre PR, vérifiez :

### **Code**
- [ ] Le code suit les standards de style (Black, isort)
- [ ] Tous les tests passent
- [ ] La couverture de code est maintenue (>80%)
- [ ] Les type hints sont présents
- [ ] Les docstrings sont complètes

### **Tests**
- [ ] Tests unitaires pour les nouvelles fonctionnalités
- [ ] Tests d'intégration si applicable
- [ ] Tests de régression pour les corrections de bugs

### **Documentation**
- [ ] README mis à jour si nécessaire
- [ ] CHANGELOG.md mis à jour
- [ ] Docstrings ajoutées/mises à jour
- [ ] Exemples d'utilisation fournis

### **Sécurité**
- [ ] Pas de credentials en dur
- [ ] Validation des inputs
- [ ] Logging approprié
- [ ] Respect du mode read-only

## 🏷️ **Conventions de Commit**

Utilisez le format [Conventional Commits](https://www.conventionalcommits.org/) :

```
type(scope): description

[body optionnel]

[footer optionnel]
```

### **Types**
- `feat`: Nouvelle fonctionnalité
- `fix`: Correction de bug
- `docs`: Documentation seulement
- `style`: Formatage, pas de changement de code
- `refactor`: Refactoring sans changement de fonctionnalité
- `test`: Ajout ou modification de tests
- `chore`: Maintenance, dépendances

### **Exemples**
```bash
feat(api): ajouter endpoint de prédiction batch
fix(models): corriger erreur de chargement des modèles NLP
docs(readme): mettre à jour guide d'installation
test(integration): ajouter tests BigQuery
```

## 🎯 **Roadmap et Priorités**

### **v1.1 - Prochaine Version**
- [ ] Modèles avec vraies données de production
- [ ] Pipeline d'entraînement automatisé
- [ ] Métriques de drift des modèles
- [ ] API GraphQL

### **v1.2 - Fonctionnalités Avancées**
- [ ] A/B testing des modèles
- [ ] Support multi-tenant
- [ ] Cache Redis pour les prédictions
- [ ] Streaming des prédictions

### **Comment Contribuer**
1. Choisissez une tâche dans les issues GitHub
2. Commentez pour indiquer que vous travaillez dessus
3. Suivez le workflow de développement
4. Soumettez votre PR

## 🆘 **Aide et Support**

### **Ressources**
- **Documentation** : [README.md](README.md)
- **Issues** : [GitHub Issues](https://github.com/sensei-ai/sensei-ai-suite/issues)
- **Discussions** : [GitHub Discussions](https://github.com/sensei-ai/sensei-ai-suite/discussions)

### **Contact**
- **Email** : <EMAIL>
- **Slack** : #sensei-ai-dev (pour les contributeurs réguliers)

### **Questions Fréquentes**

**Q: Comment tester avec de vraies données BigQuery ?**
R: Configurez vos credentials et utilisez `pytest tests/integration/ -v`

**Q: Comment ajouter un nouveau modèle ?**
R: Héritez de `BaseModel`, implémentez les méthodes abstraites, et ajoutez le décorateur `@register_model`

**Q: Comment déboguer l'API ?**
R: Utilisez `--log-level debug` et consultez `logs/sensei-ai.log`

---

**🙏 Merci de contribuer à Sensei AI Suite !**

Votre contribution aide à améliorer l'IA pour la prospection commerciale. Chaque ligne de code, chaque test, chaque amélioration compte !
