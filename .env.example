# Fichier d'exemple pour les variables d'environnement
# Copiez ce fichier vers .env et remplissez les valeurs

# =============================================================================
# Configuration générale
# =============================================================================

# Environnement (development, staging, production)
ENVIRONMENT=development

# Niveau de log (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# Configuration Google Cloud Platform
# =============================================================================

# ID du projet GCP
GOOGLE_CLOUD_PROJECT=your-gcp-project-id

# Chemin vers le fichier de credentials de service account
# (optionnel si vous utilisez Secret Manager)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Région GCP par défaut
GOOGLE_CLOUD_REGION=europe-west1

# =============================================================================
# Configuration BigQuery
# =============================================================================

# Dataset source (lecture seule)
BQ_SOURCE_DATASET=serving_layer

# Dataset ML (lecture/écriture)
BQ_ML_DATASET=serving_layer_ml

# Limite de coût par requête en bytes (20GB par défaut)
BQ_MAX_BYTES_BILLED=***********

# =============================================================================
# Configuration Secret Manager
# =============================================================================

# Noms des secrets dans Google Secret Manager
SECRET_BIGQUERY_CREDENTIALS=bigquery-service-account-key
SECRET_DATABASE_URL=database-url

# =============================================================================
# Configuration de sécurité
# =============================================================================

# Salt pour le hashage des PII (CHANGEZ EN PRODUCTION!)
PII_HASH_SALT=your-secure-salt-here

# Activation de l'audit des prédictions
ENABLE_AUDIT=true

# Activation de la vérification du consentement RGPD
ENABLE_CONSENT_CHECK=true

# Rôle utilisateur par défaut (admin, data_scientist, business_user, viewer)
DEFAULT_USER_ROLE=data_scientist

# =============================================================================
# Configuration des modèles ML
# =============================================================================

# Répertoire de sauvegarde des modèles
MODELS_PATH=./models

# Modèle Sentence-BERT pour NLP
SENTENCE_TRANSFORMER_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# Taille de batch par défaut pour les prédictions
DEFAULT_BATCH_SIZE=1000

# =============================================================================
# Configuration MLflow (optionnel)
# =============================================================================

# URI du serveur MLflow
MLFLOW_TRACKING_URI=http://localhost:5000

# Nom de l'expérience MLflow
MLFLOW_EXPERIMENT_NAME=sensei-ai

# =============================================================================
# Configuration Optuna (optimisation hyperparamètres)
# =============================================================================

# URL de la base de données Optuna
OPTUNA_DB_URL=sqlite:///optuna.db

# Nombre d'essais par défaut pour l'optimisation
OPTUNA_N_TRIALS=100

# =============================================================================
# Configuration Docker
# =============================================================================

# Port pour l'API (si applicable)
API_PORT=8080

# Nombre de workers pour l'API
API_WORKERS=4

# =============================================================================
# Configuration de développement
# =============================================================================

# Activation du mode debug
DEBUG=false

# Activation du hot reload
HOT_RELOAD=false

# Répertoire des logs
LOGS_PATH=./logs

# Répertoire des données temporaires
DATA_PATH=./data

# =============================================================================
# Configuration CI/CD
# =============================================================================

# Registry Docker
DOCKER_REGISTRY=ghcr.io

# Nom de l'image Docker
DOCKER_IMAGE_NAME=sensei-ai

# Tag Docker
DOCKER_TAG=latest

# =============================================================================
# Configuration de monitoring
# =============================================================================

# Activation du monitoring
ENABLE_MONITORING=false

# URL du service de monitoring
MONITORING_URL=

# Clé API pour le monitoring
MONITORING_API_KEY=

# =============================================================================
# Configuration de notification
# =============================================================================

# Webhook Slack pour les notifications
SLACK_WEBHOOK_URL=

# Email pour les notifications d'erreur
NOTIFICATION_EMAIL=

# =============================================================================
# Configuration de rétention des données
# =============================================================================

# Durée de rétention des prédictions (en jours)
RETENTION_PREDICTIONS_DAYS=365

# Durée de rétention des features (en jours)
RETENTION_FEATURES_DAYS=90

# Durée de rétention des logs d'audit (en jours)
RETENTION_AUDIT_DAYS=2555

# Durée de rétention des artefacts de modèles (en jours)
RETENTION_MODELS_DAYS=1095

# =============================================================================
# Configuration de performance
# =============================================================================

# Nombre de threads pour le traitement parallèle
N_THREADS=4

# Taille mémoire maximale pour les DataFrames (en MB)
MAX_MEMORY_MB=8192

# Activation du cache
ENABLE_CACHE=true

# =============================================================================
# Configuration de test
# =============================================================================

# Base de données de test
TEST_DATABASE_URL=sqlite:///test.db

# Répertoire des fixtures de test
TEST_FIXTURES_PATH=./tests/fixtures

# Activation des tests lents
RUN_SLOW_TESTS=false
