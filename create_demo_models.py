#!/usr/bin/env python3
"""
Création de modèles de démonstration pour tester l'API complète.
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def update_registry(model_name, model_path, score, samples):
    """Met à jour le registre."""
    registry_path = Path("models/registry.json")
    
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
    else:
        registry = {"models": {}, "storage": {"local_path": "./models/"}}
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    registry["models"][model_name] = {
        "version": timestamp,
        "path": str(model_path),
        "score": score,
        "samples": samples,
        "created_at": datetime.now().isoformat(),
        "status": "active"
    }
    
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)

def create_demo_conversion_model():
    """Crée un modèle de conversion de démonstration."""
    print("🎯 Création du modèle de conversion de démonstration...")
    
    # Modèle simple avec sklearn
    model = RandomForestClassifier(n_estimators=10, random_state=42)
    
    # Données d'entraînement simulées
    np.random.seed(42)
    n_samples = 1000
    
    # Features simulées
    X = np.random.randn(n_samples, 10)
    y = (X[:, 0] + X[:, 1] > 0).astype(int)  # Target simple
    
    # Entraînement
    model.fit(X, y)
    
    # Ajout de méthodes personnalisées
    def predict_proba_custom(self, X):
        if hasattr(X, 'values'):
            X = X.values
        return super(RandomForestClassifier, self).predict_proba(X)
    
    def _prepare_features(self, df):
        # Simulation de préparation de features
        features = ['nb_interactions', 'duree_reponses_minutes', 'score_decouverte_moy_30j',
                   'nb_interactions_30j', 'nb_emails_30j', 'nb_appels_30j', 'heure_soumission',
                   'jour_semaine_soumission']
        
        # Créer des features numériques
        result = pd.DataFrame()
        for feature in features:
            if feature in df.columns:
                result[feature] = pd.to_numeric(df[feature], errors='coerce').fillna(0)
            else:
                result[feature] = np.random.randn(len(df))
        
        # Ajouter des features catégorielles encodées
        cat_features = ['vitesse_reponse', 'budget_declare', 'secteur_activite']
        for feature in cat_features:
            if feature in df.columns:
                le = LabelEncoder()
                result[f'{feature}_encoded'] = le.fit_transform(df[feature].fillna('unknown'))
            else:
                result[f'{feature}_encoded'] = np.random.randint(0, 3, len(df))
        
        return result.iloc[:, :10]  # Garder seulement 10 features
    
    # Ajout des méthodes personnalisées
    import types
    model.predict_proba = types.MethodType(predict_proba_custom, model)
    model._prepare_features = types.MethodType(_prepare_features, model)
    
    # Sauvegarde
    model_path = Path("models/conversion")
    model_path.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_file = model_path / f"conversion_model_{timestamp}.pkl"
    joblib.dump(model, model_file)
    
    update_registry("conversion", model_file, 0.82, 1000)
    
    print(f"✅ Modèle de conversion créé: {model_file}")
    return model

def create_demo_channel_model():
    """Crée un modèle de canal de démonstration."""
    print("📞 Création du modèle de canal de démonstration...")
    
    # Modèle simple avec sklearn
    model = RandomForestClassifier(n_estimators=10, random_state=42)
    
    # Données d'entraînement simulées
    np.random.seed(42)
    n_samples = 1000
    
    # Features simulées
    X = np.random.randn(n_samples, 8)
    
    # Target avec plusieurs classes
    canaux = ['email_matin', 'email_apres_midi', 'email_soir', 
              'appel_matin', 'appel_apres_midi', 'reunion_matin']
    y = np.random.choice(range(len(canaux)), n_samples)
    
    # Entraînement
    model.fit(X, y)
    
    # Ajout de méthodes personnalisées
    def _prepare_features(self, df):
        # Simulation de préparation de features
        features = ['nb_appels_historique', 'ratio_emails', 'ratio_appels', 'ratio_reunions']
        
        result = pd.DataFrame()
        for feature in features:
            if feature in df.columns:
                result[feature] = pd.to_numeric(df[feature], errors='coerce').fillna(0)
            else:
                result[feature] = np.random.randn(len(df))
        
        # Features catégorielles
        cat_features = ['vitesse_reponse', 'budget_declare', 'secteur_activite']
        for feature in cat_features:
            if feature in df.columns:
                le = LabelEncoder()
                result[f'{feature}_encoded'] = le.fit_transform(df[feature].fillna('unknown'))
            else:
                result[f'{feature}_encoded'] = np.random.randint(0, 3, len(df))
        
        # Ajouter une feature booléenne
        result['has_phone'] = (np.random.rand(len(df)) > 0.5).astype(int)
        
        return result.iloc[:, :8]  # Garder seulement 8 features
    
    def predict_custom(self, X):
        if hasattr(X, 'values'):
            X = X.values
        predictions = super(RandomForestClassifier, self).predict(X)
        # Convertir les indices en noms de canaux
        return [canaux[pred] for pred in predictions]
    
    # Ajout des méthodes personnalisées
    import types
    model._prepare_features = types.MethodType(_prepare_features, model)
    model.predict = types.MethodType(predict_custom, model)
    model._canaux = canaux
    
    # Sauvegarde
    model_path = Path("models/channel")
    model_path.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_file = model_path / f"channel_model_{timestamp}.pkl"
    joblib.dump(model, model_file)
    
    update_registry("channel", model_file, 0.75, 1000)
    
    print(f"✅ Modèle de canal créé: {model_file}")
    return model

def test_models():
    """Test des modèles créés."""
    print("\n🧪 Test des modèles de démonstration...")
    
    # Test données
    test_data = pd.DataFrame({
        'id_prospect': ['12345'],
        'nb_interactions': [3],
        'duree_reponses_minutes': [45.0],
        'vitesse_reponse': ['rapide'],
        'budget_declare': ['grand'],
        'secteur_activite': ['tech'],
        'score_decouverte_moy_30j': [0.8],
        'nb_interactions_30j': [3],
        'nb_emails_30j': [2],
        'nb_appels_30j': [1],
        'heure_soumission': [14],
        'jour_semaine_soumission': [3],
        'nb_appels_historique': [2],
        'ratio_emails': [0.6],
        'ratio_appels': [0.3],
        'ratio_reunions': [0.1]
    })
    
    try:
        # Test modèle de conversion
        conversion_model = joblib.load("models/conversion/conversion_model_20250718_204849.pkl")
        X_conv = conversion_model._prepare_features(test_data)
        pred_conv = conversion_model.predict(X_conv)
        proba_conv = conversion_model.predict_proba(X_conv)
        print(f"✅ Conversion: Prédiction={pred_conv[0]}, Proba={proba_conv[0][1]:.3f}")
        
    except Exception as e:
        print(f"❌ Erreur conversion: {e}")
    
    try:
        # Test modèle de canal
        channel_model = joblib.load("models/channel/channel_model_20250718_204849.pkl")
        X_chan = channel_model._prepare_features(test_data)
        pred_chan = channel_model.predict(X_chan)
        print(f"✅ Canal: Prédiction={pred_chan[0]}")
        
    except Exception as e:
        print(f"❌ Erreur canal: {e}")

def main():
    """Fonction principale."""
    print("🚀 Création des Modèles de Démonstration")
    print("=" * 50)
    
    # Création des modèles
    create_demo_conversion_model()
    create_demo_channel_model()
    
    # Test des modèles
    test_models()
    
    # Affichage du registre final
    print("\n📋 Registre final:")
    with open("models/registry.json", 'r') as f:
        registry = json.load(f)
    
    for model_name, model_info in registry["models"].items():
        print(f"  ✅ {model_name}: v{model_info['version']} (score: {model_info['score']})")
    
    print("\n🎉 Modèles de démonstration créés!")
    print("🚀 Prêt pour tester l'API: python api_model_serving.py")

if __name__ == "__main__":
    main()
