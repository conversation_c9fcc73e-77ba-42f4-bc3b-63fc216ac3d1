#!/usr/bin/env python3
"""
Test manuel des modèles ML Sensei AI Suite.
Teste les modèles sans dépendances externes.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_model_registry():
    """Test du registre des modèles."""
    print("🏭 Test du registre des modèles...")
    
    try:
        from sensei.models.base import MODEL_REGISTRY
        print(f"✅ Registre des modèles: {len(MODEL_REGISTRY)} modèles")
        
        for model_name, model_class in MODEL_REGISTRY.items():
            print(f"  📋 {model_name}: {model_class.__name__}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_conversion_model():
    """Test du modèle de conversion."""
    print("\n🎯 Test du modèle de conversion...")
    
    try:
        from sensei.models.conversion import ConversionModel
        
        # Initialisation
        model = ConversionModel()
        print(f"✅ Modèle initialisé: {model.model_name}")
        
        # Création de données de test
        np.random.seed(42)
        n_samples = 100
        
        # Features numériques
        numerical_features = {
            'heure_soumission': np.random.randint(0, 24, n_samples),
            'duree_reponses_minutes': np.random.exponential(60, n_samples),
            'nb_interactions': np.random.poisson(3, n_samples),
            'score_decouverte_moy_30j': np.random.uniform(0, 1, n_samples),
        }
        
        # Features catégorielles
        categorical_features = {
            'vitesse_reponse': np.random.choice(['rapide', 'moyen', 'lent'], n_samples),
            'budget_declare': np.random.choice(['petit', 'moyen', 'grand'], n_samples),
            'secteur_activite': np.random.choice(['tech', 'finance', 'retail'], n_samples),
        }
        
        # Target
        target = {
            'y_converted_90j': np.random.choice([0, 1], n_samples, p=[0.7, 0.3])
        }
        
        # Création du DataFrame
        data = {**numerical_features, **categorical_features, **target}
        df = pd.DataFrame(data)
        df['id_prospect'] = [f"prospect_{i}" for i in range(n_samples)]
        df['date_features'] = pd.date_range('2025-01-01', periods=n_samples, freq='D')
        
        print(f"✅ Données de test créées: {df.shape}")
        
        # Test d'identification des types de features
        cat_features, num_features = model._identify_feature_types(df)
        print(f"✅ Features identifiées: {len(cat_features)} catégorielles, {len(num_features)} numériques")
        
        # Test de préparation des features
        X_prepared = model._prepare_features(df)
        print(f"✅ Features préparées: {X_prepared.shape}")
        
        # Test de prédiction sans entraînement (doit lever une exception)
        try:
            model.predict(df)
            print("❌ La prédiction sans entraînement devrait échouer")
            return False
        except ValueError as e:
            print(f"✅ Exception attendue: {str(e)[:50]}...")
        
        print("✅ Modèle de conversion testé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_channel_model():
    """Test du modèle de canal."""
    print("\n📞 Test du modèle de canal...")
    
    try:
        from sensei.models.channel import ChannelModel
        
        # Initialisation
        model = ChannelModel()
        print(f"✅ Modèle initialisé: {model.model_name}")
        
        # Création de données de test
        np.random.seed(42)
        n_samples = 100
        
        # Features de base
        data = {
            'id_prospect': [f"prospect_{i}" for i in range(n_samples)],
            'date_features': pd.date_range('2025-01-01', periods=n_samples, freq='D'),
            'heure_soumission': np.random.randint(0, 24, n_samples),
            'duree_reponses_minutes': np.random.exponential(60, n_samples),
            'nb_interactions': np.random.poisson(3, n_samples),
            'vitesse_reponse': np.random.choice(['rapide', 'moyen', 'lent'], n_samples),
            'budget_declare': np.random.choice(['petit', 'moyen', 'grand'], n_samples),
            'secteur_activite': np.random.choice(['tech', 'finance', 'retail'], n_samples),
            
            # Features spécifiques au canal
            'canal_prefere': np.random.choice(['email', 'appel', 'reunion'], n_samples),
            'timing_prefere': np.random.choice(['matin', 'apres_midi', 'soir'], n_samples),
            'ratio_emails': np.random.uniform(0, 1, n_samples),
            'ratio_appels': np.random.uniform(0, 1, n_samples),
            'ratio_reunions': np.random.uniform(0, 1, n_samples),
        }
        
        df = pd.DataFrame(data)
        print(f"✅ Données de test créées: {df.shape}")
        
        # Test de création du target canal+timing
        target_series = model._create_channel_timing_target(df)
        print(f"✅ Target canal+timing créé: {len(target_series.unique())} combinaisons uniques")
        
        # Test d'identification des types de features
        cat_features, num_features = model._identify_feature_types_catboost(df)
        print(f"✅ Features CatBoost identifiées: {len(cat_features)} catégorielles, {len(num_features)} numériques")
        
        # Test de préparation des features
        X_prepared = model._prepare_features_catboost(df)
        print(f"✅ Features préparées: {X_prepared.shape}")
        
        print("✅ Modèle de canal testé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nlp_model():
    """Test du modèle NLP."""
    print("\n🗣️ Test du modèle NLP...")
    
    try:
        from sensei.models.nlp_signals import NlpSignalsModel
        
        # Initialisation
        model = NlpSignalsModel()
        print(f"✅ Modèle initialisé: {model.model_name}")
        
        # Test de préprocessing du texte
        test_texts = [
            "[00:12:34] Speaker 1: Bonjour!!! Comment allez-vous ???",
            "Speaker 2: Très bien merci, et vous?",
            "Nous aimerions discuter de votre solution.",
            "Le timing ne convient pas pour le moment.",
            "Pouvez-vous nous envoyer plus d'informations?"
        ]
        
        for text in test_texts:
            cleaned = model._preprocess_text(text)
            print(f"  Original: {text[:50]}...")
            print(f"  Nettoyé: {cleaned[:50]}...")
        
        # Création de données de test
        n_samples = 10
        data = {
            'callId': [f"call_{i}" for i in range(n_samples)],
            'contactCrmId': [f"contact_{i}" for i in range(n_samples)],
            'content': [
                "Bonjour, nous sommes intéressés par votre solution.",
                "Le prix semble un peu élevé pour notre budget.",
                "Pouvez-vous nous faire une démonstration?",
                "Nous avons besoin de plus de temps pour réfléchir.",
                "Votre solution correspond exactement à nos besoins.",
                "Il faudrait que nous en discutions en interne.",
                "Quand pouvons-nous commencer l'implémentation?",
                "Avez-vous des références dans notre secteur?",
                "Le timing ne convient pas, nous préférerions commencer plus tard.",
                "Nous sommes prêts à signer le contrat."
            ],
            'startDate': pd.date_range('2025-01-01', periods=n_samples, freq='H'),
            'speakerId': [f"speaker_{i%3}" for i in range(n_samples)]
        }
        
        df = pd.DataFrame(data)
        print(f"✅ Données de transcription créées: {df.shape}")
        
        # Test de préparation des features
        features_df = model._prepare_features(df)
        print(f"✅ Features NLP préparées: {features_df.shape}")
        
        print("✅ Modèle NLP testé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test manuel des modèles ML Sensei AI Suite")
    print("=" * 60)
    
    # Configuration de l'environnement
    os.environ['ENVIRONMENT'] = 'development'
    os.environ['LOG_LEVEL'] = 'INFO'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'sensei-ai-dev'
    
    tests = [
        ("Registre des modèles", test_model_registry),
        ("Modèle de conversion", test_conversion_model),
        ("Modèle de canal", test_channel_model),
        ("Modèle NLP", test_nlp_model),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 Résumé des tests:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Tous les modèles ML fonctionnent correctement!")
        return 0
    else:
        print("⚠️  Certains modèles nécessitent des corrections")
        return 1

if __name__ == "__main__":
    sys.exit(main())
