#!/usr/bin/env python3
"""
Main entry point for Sensei AI Suite.

This script provides a unified interface to all Sensei AI functionality.
"""

import argparse
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sensei.utils.logging import log_info, log_error


def run_training(args):
    """Run training pipeline."""
    log_info("Starting training pipeline", capacity=args.capacity, models=args.models)
    
    if args.simple:
        from scripts.train_simple import main as train_simple_main
        return train_simple_main()
    else:
        from scripts.train_production import main as train_production_main
        return train_production_main()


def run_api(args):
    """Run API server."""
    log_info("Starting API server", port=args.port, host=args.host)
    
    from src.sensei.api.api_simple import main as api_main
    return api_main()


def run_tests(args):
    """Run test suite."""
    log_info("Running test suite", test_type=args.test_type)
    
    if args.test_type == "validation":
        from tests.test_validation import main as test_validation_main
        return test_validation_main()
    elif args.test_type == "pipeline":
        from tests.test_complete_pipeline import main as test_pipeline_main
        return test_pipeline_main()
    else:
        log_error("Unknown test type", test_type=args.test_type)
        return 1


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Sensei AI Suite - Production ML Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Training
  python main.py train --simple --samples 1000
  python main.py train --capacity medium --max-samples 5000
  
  # API
  python main.py api --port 8000
  
  # Tests
  python main.py test --test-type validation
  python main.py test --test-type pipeline
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Training command
    train_parser = subparsers.add_parser("train", help="Train ML models")
    train_parser.add_argument("--simple", action="store_true", help="Use simple training")
    train_parser.add_argument("--capacity", choices=["small", "medium", "large", "xlarge"], 
                             default="medium", help="Server capacity")
    train_parser.add_argument("--models", nargs="+", choices=["conversion", "channel"], 
                             default=["conversion", "channel"], help="Models to train")
    train_parser.add_argument("--max-samples", type=int, help="Maximum samples")
    train_parser.add_argument("--samples", type=int, default=1000, help="Samples for simple training")
    
    # API command
    api_parser = subparsers.add_parser("api", help="Run API server")
    api_parser.add_argument("--host", default="0.0.0.0", help="Host to bind")
    api_parser.add_argument("--port", type=int, default=8000, help="Port to bind")
    api_parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--test-type", choices=["validation", "pipeline"], 
                            default="validation", help="Type of test to run")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == "train":
            return run_training(args)
        elif args.command == "api":
            return run_api(args)
        elif args.command == "test":
            return run_tests(args)
        else:
            log_error("Unknown command", command=args.command)
            return 1
            
    except KeyboardInterrupt:
        log_info("Operation cancelled by user")
        return 1
    except Exception as e:
        log_error(f"Operation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
