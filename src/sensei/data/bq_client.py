"""
Client BigQuery sécurisé avec limitation des coûts et accès en lecture seule.
"""

import os
from typing import Optional, Dict, Any
import json

import pandas as pd
from google.cloud import bigquery
from google.oauth2 import service_account
import structlog

from ..utils.secrets import get_secret_manager
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SecureBigQueryClient:
    """
    Client BigQuery sécurisé avec limitations de coûts et accès contrôlé.
    """
    
    # Limite de coût par requête : 20 GB
    MAX_BYTES_BILLED = 20 * 1024**3
    
    # Datasets autorisés en lecture
    ALLOWED_READ_DATASETS = ["serving_layer"]
    
    # Datasets autorisés en écriture
    ALLOWED_WRITE_DATASETS = ["serving_layer_ml"]
    
    def __init__(
        self,
        project_id: Optional[str] = None,
        credentials_path: Optional[str] = None,
        use_secret_manager: bool = True
    ):
        """
        Initialise le client BigQuery sécurisé.
        
        Args:
            project_id: ID du projet GCP
            credentials_path: Chemin vers le fichier de credentials
            use_secret_manager: Utiliser Secret Manager pour les credentials
        """
        self.project_id = project_id or os.getenv("GOOGLE_CLOUD_PROJECT")
        if not self.project_id:
            raise ValueError("project_id requis ou GOOGLE_CLOUD_PROJECT manquant")
        
        # Configuration des credentials
        credentials = None
        if use_secret_manager:
            try:
                secret_manager = get_secret_manager()
                credentials_json = secret_manager.get_bigquery_credentials()
                credentials_info = json.loads(credentials_json)
                credentials = service_account.Credentials.from_service_account_info(
                    credentials_info
                )
                logger.info("Credentials chargés depuis Secret Manager")
            except Exception as e:
                logger.warning(
                    "Impossible de charger les credentials depuis Secret Manager",
                    error=str(e)
                )
        
        elif credentials_path:
            credentials = service_account.Credentials.from_service_account_file(
                credentials_path
            )
            logger.info("Credentials chargés depuis fichier", path=credentials_path)
        
        # Initialisation du client BigQuery
        self.client = bigquery.Client(
            project=self.project_id,
            credentials=credentials
        )
        
        logger.info(
            "Client BigQuery initialisé",
            project_id=self.project_id,
            max_bytes_billed=self.MAX_BYTES_BILLED
        )
    
    def _validate_query_permissions(self, sql: str) -> None:
        """
        Valide que la requête respecte les permissions de sécurité.
        
        Args:
            sql: Requête SQL à valider
            
        Raises:
            PermissionError: Si la requête viole les règles de sécurité
        """
        sql_upper = sql.upper()
        
        # Vérification des opérations interdites
        forbidden_operations = ["DELETE", "DROP", "TRUNCATE", "ALTER"]
        for operation in forbidden_operations:
            if operation in sql_upper:
                raise PermissionError(f"Opération interdite détectée: {operation}")
        
        # Vérification des datasets en lecture
        for dataset in self.ALLOWED_READ_DATASETS:
            if f"{dataset}." in sql.lower():
                continue
        
        # Vérification des datasets en écriture (pour INSERT/UPDATE/CREATE)
        if any(op in sql_upper for op in ["INSERT", "UPDATE", "CREATE"]):
            allowed = False
            for dataset in self.ALLOWED_WRITE_DATASETS:
                if f"{dataset}." in sql.lower():
                    allowed = True
                    break
            
            if not allowed:
                raise PermissionError(
                    f"Écriture autorisée uniquement sur: {self.ALLOWED_WRITE_DATASETS}"
                )
    
    def query_df(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None,
        max_bytes_billed: Optional[int] = None
    ) -> pd.DataFrame:
        """
        Exécute une requête SQL et retourne un DataFrame pandas.
        
        Args:
            sql: Requête SQL
            params: Paramètres de la requête
            max_bytes_billed: Limite de coût personnalisée
            
        Returns:
            DataFrame avec les résultats
            
        Raises:
            PermissionError: Si la requête viole les règles de sécurité
            Exception: Si la requête échoue
        """
        # Validation de sécurité
        self._validate_query_permissions(sql)
        
        # Configuration de la requête
        job_config = bigquery.QueryJobConfig()
        job_config.maximum_bytes_billed = max_bytes_billed or self.MAX_BYTES_BILLED
        
        if params:
            job_config.query_parameters = [
                bigquery.ScalarQueryParameter(key, "STRING", value)
                for key, value in params.items()
            ]
        
        try:
            logger.info(
                "Exécution de la requête BigQuery",
                sql_preview=sql[:200] + "..." if len(sql) > 200 else sql,
                max_bytes_billed=job_config.maximum_bytes_billed
            )
            
            # Exécution de la requête
            query_job = self.client.query(sql, job_config=job_config)
            df = query_job.to_dataframe()
            
            logger.info(
                "Requête exécutée avec succès",
                rows_returned=len(df),
                bytes_processed=query_job.total_bytes_processed,
                bytes_billed=query_job.total_bytes_billed,
                job_id=query_job.job_id
            )
            
            return df
            
        except Exception as e:
            logger.error(
                "Erreur lors de l'exécution de la requête",
                error=str(e),
                sql_preview=sql[:200] + "..." if len(sql) > 200 else sql
            )
            raise
    
    def execute_query(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None,
        max_bytes_billed: Optional[int] = None
    ) -> bigquery.QueryJob:
        """
        Exécute une requête SQL sans retourner de résultats (pour INSERT/CREATE).
        
        Args:
            sql: Requête SQL
            params: Paramètres de la requête
            max_bytes_billed: Limite de coût personnalisée
            
        Returns:
            Job BigQuery
        """
        # Validation de sécurité
        self._validate_query_permissions(sql)
        
        # Configuration de la requête
        job_config = bigquery.QueryJobConfig()
        job_config.maximum_bytes_billed = max_bytes_billed or self.MAX_BYTES_BILLED
        
        if params:
            job_config.query_parameters = [
                bigquery.ScalarQueryParameter(key, "STRING", value)
                for key, value in params.items()
            ]
        
        try:
            logger.info(
                "Exécution de la requête BigQuery (sans résultats)",
                sql_preview=sql[:200] + "..." if len(sql) > 200 else sql
            )
            
            query_job = self.client.query(sql, job_config=job_config)
            query_job.result()  # Attendre la fin de l'exécution
            
            logger.info(
                "Requête exécutée avec succès",
                bytes_processed=query_job.total_bytes_processed,
                bytes_billed=query_job.total_bytes_billed,
                job_id=query_job.job_id
            )
            
            return query_job
            
        except Exception as e:
            logger.error(
                "Erreur lors de l'exécution de la requête",
                error=str(e),
                sql_preview=sql[:200] + "..." if len(sql) > 200 else sql
            )
            raise


# Instance globale du client
_bq_client: Optional[SecureBigQueryClient] = None


def get_bq_client() -> SecureBigQueryClient:
    """
    Retourne l'instance globale du client BigQuery.
    
    Returns:
        Instance de SecureBigQueryClient
    """
    global _bq_client
    if _bq_client is None:
        _bq_client = SecureBigQueryClient()
    return _bq_client
