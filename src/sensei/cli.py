"""
Interface CLI pour Sensei AI Suite.
"""

import os
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Optional

import typer
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
import structlog

from .features.build_store import FeatureStoreBuilder
from .models.base import get_model_class, MODEL_REGISTRY
from .data.bq_client import get_bq_client
from .utils.logging import get_logger, configure_logging

# Configuration de l'application Typer
app = typer.Typer(
    name="sensei",
    help="Sensei AI Suite - Optimisation de la prospection marketing & commerciale",
    add_completion=False
)

# Console Rich pour l'affichage
console = Console()
logger = get_logger(__name__)


@app.callback()
def main(
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Mode verbeux"),
    log_level: str = typer.Option("INFO", "--log-level", help="Niveau de log"),
    project_id: Optional[str] = typer.Option(None, "--project-id", help="ID du projet GCP")
):
    """
    Sensei AI Suite - CLI principal.
    """
    # Configuration du logging
    if verbose:
        log_level = "DEBUG"
    
    configure_logging(level=log_level, json_logs=False)
    
    # Configuration du projet GCP
    if project_id:
        os.environ["GOOGLE_CLOUD_PROJECT"] = project_id
    
    logger.info(
        "Sensei AI Suite démarré",
        log_level=log_level,
        project_id=os.getenv("GOOGLE_CLOUD_PROJECT")
    )


@app.command()
def build_features(
    target_date: str = typer.Option(
        None,
        "--date",
        help="Date cible (YYYY-MM-DD). Par défaut: hier"
    ),
    setup_dataset: bool = typer.Option(
        True,
        "--setup-dataset/--no-setup-dataset",
        help="Configurer le dataset ML si nécessaire"
    )
):
    """
    Construit le Feature Store quotidien.
    
    Extrait et transforme les données de Typeform, HubSpot et Modjo
    pour créer la table serving_layer_ml.features_daily.
    """
    try:
        # Parsing de la date
        if target_date:
            parsed_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        else:
            parsed_date = date.today() - timedelta(days=1)
        
        console.print(f"🏗️  Construction des features pour le {parsed_date}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Initialisation du builder
            task = progress.add_task("Initialisation...", total=None)
            builder = FeatureStoreBuilder()
            
            # Configuration du dataset si nécessaire
            if setup_dataset:
                progress.update(task, description="Configuration du dataset ML...")
                builder.setup_ml_dataset()
            
            # Construction des features
            progress.update(task, description="Construction des features...")
            builder.build_features_daily(parsed_date)
            
            progress.update(task, description="✅ Terminé", completed=True)
        
        console.print(f"✅ Features construites avec succès pour le {parsed_date}")
        
    except Exception as e:
        console.print(f"❌ Erreur: {str(e)}", style="red")
        logger.error("Erreur lors de la construction des features", error=str(e))
        raise typer.Exit(1)


@app.command()
def train(
    model_name: str = typer.Argument(..., help="Nom du modèle à entraîner"),
    train_date_start: str = typer.Option(
        None,
        "--train-start",
        help="Date de début d'entraînement (YYYY-MM-DD)"
    ),
    train_date_end: str = typer.Option(
        None,
        "--train-end", 
        help="Date de fin d'entraînement (YYYY-MM-DD)"
    ),
    val_split: float = typer.Option(0.2, "--val-split", help="Proportion de validation"),
    optimize: bool = typer.Option(False, "--optimize", help="Optimiser les hyperparamètres"),
    save_path: str = typer.Option("./models", "--save-path", help="Chemin de sauvegarde")
):
    """
    Entraîne un modèle ML.
    
    Modèles disponibles: conversion, channel, nlp_signals
    """
    try:
        console.print(f"🤖 Entraînement du modèle '{model_name}'")
        
        # Vérification du modèle
        if model_name not in MODEL_REGISTRY:
            console.print(
                f"❌ Modèle '{model_name}' non trouvé. Disponibles: {list(MODEL_REGISTRY.keys())}",
                style="red"
            )
            raise typer.Exit(1)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Chargement des données
            task = progress.add_task("Chargement des données...", total=None)
            
            # Dates par défaut
            if not train_date_end:
                end_date = date.today() - timedelta(days=1)
            else:
                end_date = datetime.strptime(train_date_end, "%Y-%m-%d").date()
            
            if not train_date_start:
                start_date = end_date - timedelta(days=90)  # 3 mois par défaut
            else:
                start_date = datetime.strptime(train_date_start, "%Y-%m-%d").date()
            
            # Requête des données d'entraînement
            bq_client = get_bq_client()
            
            if model_name == "nlp_signals":
                # Données spéciales pour NLP (transcriptions Modjo)
                sql = f"""
                SELECT *
                FROM `{bq_client.project_id}.serving_layer.vw_dim_modjo_transcript`
                WHERE DATE(startDate) BETWEEN '{start_date}' AND '{end_date}'
                  AND content IS NOT NULL
                  AND LENGTH(content) > 50
                """
            else:
                # Données du feature store
                sql = f"""
                SELECT *
                FROM `{bq_client.project_id}.serving_layer_ml.features_daily`
                WHERE date_features BETWEEN '{start_date}' AND '{end_date}'
                  AND id_prospect IS NOT NULL
                """
            
            df = bq_client.query_df(sql)
            
            if df.empty:
                console.print("❌ Aucune donnée trouvée pour la période spécifiée", style="red")
                raise typer.Exit(1)
            
            progress.update(task, description=f"Données chargées: {len(df)} lignes")
            
            # Séparation train/validation
            if model_name != "nlp_signals":  # NLP est non-supervisé
                train_size = int(len(df) * (1 - val_split))
                train_df = df.iloc[:train_size]
                val_df = df.iloc[train_size:] if val_split > 0 else None
            else:
                train_df = df
                val_df = None
            
            # Initialisation du modèle
            progress.update(task, description="Initialisation du modèle...")
            model_class = get_model_class(model_name)
            model = model_class()
            
            # Optimisation des hyperparamètres si demandée
            if optimize and hasattr(model, 'optimize_hyperparameters'):
                progress.update(task, description="Optimisation des hyperparamètres...")
                optimization_results = model.optimize_hyperparameters(train_df)
                console.print(f"🎯 Meilleur score: {optimization_results['best_score']:.4f}")
            
            # Entraînement
            progress.update(task, description="Entraînement en cours...")
            training_results = model.train(train_df, val_df)
            
            # Sauvegarde
            progress.update(task, description="Sauvegarde du modèle...")
            save_dir = Path(save_path) / model_name / datetime.now().strftime("%Y%m%d_%H%M%S")
            model.save(save_dir)
            
            progress.update(task, description="✅ Terminé", completed=True)
        
        # Affichage des résultats
        console.print("✅ Entraînement terminé avec succès!")
        
        # Table des métriques
        table = Table(title="Métriques d'entraînement")
        table.add_column("Métrique", style="cyan")
        table.add_column("Valeur", style="green")
        
        for key, value in training_results.items():
            if isinstance(value, float):
                table.add_row(key, f"{value:.4f}")
            else:
                table.add_row(key, str(value))
        
        console.print(table)
        console.print(f"📁 Modèle sauvegardé dans: {save_dir}")
        
    except Exception as e:
        console.print(f"❌ Erreur: {str(e)}", style="red")
        logger.error("Erreur lors de l'entraînement", model_name=model_name, error=str(e))
        raise typer.Exit(1)


@app.command()
def score(
    model_name: str = typer.Argument(..., help="Nom du modèle à utiliser"),
    model_path: str = typer.Option(..., "--model-path", help="Chemin vers le modèle sauvegardé"),
    target_date: str = typer.Option(
        None,
        "--date",
        help="Date cible pour le scoring (YYYY-MM-DD). Par défaut: hier"
    ),
    output_table: str = typer.Option(
        None,
        "--output-table",
        help="Table BigQuery de sortie (ex: serving_layer_ml.predictions_conversion)"
    ),
    batch_size: int = typer.Option(1000, "--batch-size", help="Taille des batches")
):
    """
    Effectue le scoring avec un modèle entraîné.
    """
    try:
        # Parsing de la date
        if target_date:
            parsed_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        else:
            parsed_date = date.today() - timedelta(days=1)
        
        console.print(f"🎯 Scoring avec le modèle '{model_name}' pour le {parsed_date}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Chargement du modèle
            task = progress.add_task("Chargement du modèle...", total=None)
            
            model_class = get_model_class(model_name)
            model = model_class()
            model.load(model_path)
            
            # Chargement des données
            progress.update(task, description="Chargement des données...")
            
            bq_client = get_bq_client()
            
            if model_name == "nlp_signals":
                sql = f"""
                SELECT *
                FROM `{bq_client.project_id}.serving_layer.vw_dim_modjo_transcript`
                WHERE DATE(startDate) = '{parsed_date}'
                  AND content IS NOT NULL
                """
            else:
                sql = f"""
                SELECT *
                FROM `{bq_client.project_id}.serving_layer_ml.features_daily`
                WHERE date_features = '{parsed_date}'
                  AND id_prospect IS NOT NULL
                """
            
            df = bq_client.query_df(sql)
            
            if df.empty:
                console.print("❌ Aucune donnée trouvée pour le scoring", style="red")
                raise typer.Exit(1)
            
            progress.update(task, description=f"Données chargées: {len(df)} lignes")
            
            # Scoring par batches
            progress.update(task, description="Scoring en cours...")
            
            all_predictions = []
            for i in range(0, len(df), batch_size):
                batch_df = df.iloc[i:i+batch_size]
                
                if model_name == "channel":
                    # Prédictions spéciales pour le modèle canal/timing
                    predictions_df = model.predict_channel_timing(batch_df)
                    predictions_df['id_prospect'] = batch_df['id_prospect'].values
                else:
                    # Prédictions standard
                    predictions = model.predict(batch_df)
                    predictions_df = pd.DataFrame({
                        'id_prospect': batch_df['id_prospect'].values,
                        'prediction': predictions,
                        'model_name': model_name,
                        'model_version': model.version,
                        'prediction_date': parsed_date,
                        'created_at': datetime.now()
                    })
                
                all_predictions.append(predictions_df)
            
            # Consolidation des résultats
            final_predictions = pd.concat(all_predictions, ignore_index=True)
            
            # Sauvegarde en BigQuery si spécifiée
            if output_table:
                progress.update(task, description="Sauvegarde en BigQuery...")
                
                # Création de la table de sortie
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS `{bq_client.project_id}.{output_table}` (
                    id_prospect STRING,
                    prediction FLOAT64,
                    model_name STRING,
                    model_version STRING,
                    prediction_date DATE,
                    created_at TIMESTAMP
                )
                PARTITION BY prediction_date
                CLUSTER BY model_name, id_prospect
                """
                
                bq_client.execute_query(create_table_sql)
                
                # Insertion des prédictions (à implémenter selon le besoin)
                console.print(f"📊 {len(final_predictions)} prédictions générées")
            
            progress.update(task, description="✅ Terminé", completed=True)
        
        console.print("✅ Scoring terminé avec succès!")
        console.print(f"📊 {len(final_predictions)} prédictions générées")
        
        # Affichage d'un échantillon
        if len(final_predictions) > 0:
            console.print("\n📋 Échantillon des prédictions:")
            console.print(final_predictions.head().to_string())
        
    except Exception as e:
        console.print(f"❌ Erreur: {str(e)}", style="red")
        logger.error("Erreur lors du scoring", model_name=model_name, error=str(e))
        raise typer.Exit(1)


@app.command()
def list_models():
    """
    Liste les modèles disponibles.
    """
    console.print("🤖 Modèles disponibles:")
    
    table = Table()
    table.add_column("Nom", style="cyan")
    table.add_column("Description", style="green")
    table.add_column("Type", style="yellow")
    
    descriptions = {
        "conversion": ("Prédiction de conversion prospects", "Classification binaire"),
        "channel": ("Recommandation canal/timing optimal", "Classification multiclasse"),
        "nlp_signals": ("Analyse thématiques transcriptions", "Clustering non-supervisé")
    }
    
    for model_name in MODEL_REGISTRY.keys():
        desc, type_model = descriptions.get(model_name, ("", ""))
        table.add_row(model_name, desc, type_model)
    
    console.print(table)


@app.command()
def status():
    """
    Affiche le statut du système.
    """
    console.print("📊 Statut Sensei AI Suite")
    
    try:
        # Test de connexion BigQuery
        bq_client = get_bq_client()
        test_query = f"SELECT 1 as test"
        bq_client.query_df(test_query)
        bq_status = "✅ Connecté"
    except Exception as e:
        bq_status = f"❌ Erreur: {str(e)}"
    
    # Vérification du dataset ML
    try:
        dataset_query = f"""
        SELECT table_name 
        FROM `{bq_client.project_id}.serving_layer_ml.INFORMATION_SCHEMA.TABLES`
        """
        tables_df = bq_client.query_df(dataset_query)
        ml_dataset_status = f"✅ {len(tables_df)} tables"
    except Exception:
        ml_dataset_status = "❌ Dataset non configuré"
    
    table = Table(title="Statut des composants")
    table.add_column("Composant", style="cyan")
    table.add_column("Statut", style="green")
    
    table.add_row("BigQuery", bq_status)
    table.add_row("Dataset ML", ml_dataset_status)
    table.add_row("Modèles disponibles", str(len(MODEL_REGISTRY)))
    table.add_row("Projet GCP", os.getenv("GOOGLE_CLOUD_PROJECT", "Non configuré"))
    
    console.print(table)


if __name__ == "__main__":
    app()
