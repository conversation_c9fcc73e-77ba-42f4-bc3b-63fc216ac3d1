"""
Configuration du logging structuré avec structlog.
"""

import logging
import os
import sys
from typing import Any, Dict

import structlog
from structlog.types import Processor


def configure_logging(
    level: str = "INFO",
    json_logs: bool = True,
    service_name: str = "sensei-ai"
) -> None:
    """
    Configure le système de logging structuré.
    
    Args:
        level: Niveau de log (DEBUG, INFO, WARNING, ERROR)
        json_logs: Si True, utilise le format JSON pour les logs
        service_name: Nom du service pour les logs
    """
    # Configuration du niveau de log
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Processeurs communs
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_logger_name,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.TimeStamper(fmt="iso"),
    ]
    
    # Ajout des métadonnées de service
    def add_service_metadata(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        event_dict["service"] = service_name
        event_dict["version"] = "1.0.0"
        return event_dict
    
    processors.append(add_service_metadata)
    
    if json_logs:
        # Format JSON pour la production
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Format coloré pour le développement
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    
    # Configuration de structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(log_level),
        logger_factory=structlog.WriteLoggerFactory(file=sys.stdout),
        cache_logger_on_first_use=True,
    )
    
    # Configuration du logging standard Python
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )
    
    # Suppression des logs verbeux des librairies tierces
    logging.getLogger("google").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Retourne un logger structuré.
    
    Args:
        name: Nom du logger (généralement __name__)
        
    Returns:
        Logger structuré configuré
    """
    return structlog.get_logger(name)


# Configuration automatique au chargement du module
def _auto_configure() -> None:
    """Configure automatiquement le logging selon l'environnement."""
    # Détection de l'environnement
    is_production = os.getenv("ENVIRONMENT", "development") == "production"
    log_level = os.getenv("LOG_LEVEL", "INFO")
    
    configure_logging(
        level=log_level,
        json_logs=is_production,
        service_name="sensei-ai"
    )


# Auto-configuration
_auto_configure()
