"""
Gestion sécurisée des secrets via Google Secret Manager.
"""

import os
from typing import Optional

from google.cloud import secretmanager
import structlog

logger = structlog.get_logger(__name__)


class SecretManager:
    """Gestionnaire de secrets Google Cloud."""
    
    def __init__(self, project_id: Optional[str] = None):
        """
        Initialise le gestionnaire de secrets.
        
        Args:
            project_id: ID du projet GCP. Si None, utilise GOOGLE_CLOUD_PROJECT.
        """
        self.project_id = project_id or os.getenv("GOOGLE_CLOUD_PROJECT")
        if not self.project_id:
            raise ValueError(
                "project_id requis ou variable GOOGLE_CLOUD_PROJECT manquante"
            )
        
        self.client = secretmanager.SecretManagerServiceClient()
        logger.info("SecretManager initialisé", project_id=self.project_id)
    
    def get_secret(self, secret_name: str, version: str = "latest") -> str:
        """
        Récupère un secret depuis Google Secret Manager.
        
        Args:
            secret_name: Nom du secret
            version: Version du secret (défaut: "latest")
            
        Returns:
            Valeur du secret
            
        Raises:
            Exception: Si le secret n'existe pas ou n'est pas accessible
        """
        try:
            name = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version}"
            response = self.client.access_secret_version(request={"name": name})
            secret_value = response.payload.data.decode("UTF-8")
            
            logger.info(
                "Secret récupéré avec succès",
                secret_name=secret_name,
                version=version
            )
            return secret_value
            
        except Exception as e:
            logger.error(
                "Erreur lors de la récupération du secret",
                secret_name=secret_name,
                version=version,
                error=str(e)
            )
            raise
    
    def get_bigquery_credentials(self) -> str:
        """
        Récupère les credentials BigQuery depuis Secret Manager.
        
        Returns:
            JSON des credentials de service account
        """
        return self.get_secret("bigquery-service-account-key")
    
    def get_database_url(self) -> str:
        """
        Récupère l'URL de base de données depuis Secret Manager.
        
        Returns:
            URL de connexion à la base de données
        """
        return self.get_secret("database-url")


# Instance globale du gestionnaire de secrets
_secret_manager: Optional[SecretManager] = None


def get_secret_manager() -> SecretManager:
    """
    Retourne l'instance globale du gestionnaire de secrets.
    
    Returns:
        Instance de SecretManager
    """
    global _secret_manager
    if _secret_manager is None:
        _secret_manager = SecretManager()
    return _secret_manager


def get_secret(secret_name: str, version: str = "latest") -> str:
    """
    Fonction utilitaire pour récupérer un secret.
    
    Args:
        secret_name: Nom du secret
        version: Version du secret
        
    Returns:
        Valeur du secret
    """
    return get_secret_manager().get_secret(secret_name, version)
