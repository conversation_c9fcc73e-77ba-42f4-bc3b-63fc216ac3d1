"""
Utilitaires de sécurité et conformité RGPD.
"""

import hashlib
import hmac
import secrets
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import json
import uuid

import pandas as pd
import structlog

from .logging import get_logger

logger = get_logger(__name__)


class PIIHasher:
    """
    Gestionnaire de hashage sécurisé des données PII (Personally Identifiable Information).
    
    Utilise SHA256 avec salt pour garantir l'irréversibilité tout en permettant
    la cohérence des identifiants hashés.
    """
    
    def __init__(self, salt: Optional[str] = None):
        """
        Initialise le hasheur PII.
        
        Args:
            salt: Salt personnalisé. Si None, utilise un salt par défaut sécurisé.
        """
        # Salt par défaut sécurisé (à changer en production)
        self.salt = salt or "sensei-ai-pii-salt-2024"
        
        logger.info("PIIHasher initialisé avec salt personnalisé" if salt else "PIIHasher initialisé avec salt par défaut")
    
    def hash_identifier(self, identifier: str) -> str:
        """
        Hashe un identifiant PII de manière sécurisée.
        
        Args:
            identifier: Identifiant à hasher (email, téléphone, etc.)
            
        Returns:
            Hash SHA256 hexadécimal
        """
        if not identifier or not isinstance(identifier, str):
            return ""
        
        # Normalisation (minuscules, suppression espaces)
        normalized = identifier.strip().lower()
        
        # Hashage avec salt
        salted_data = f"{self.salt}:{normalized}".encode('utf-8')
        hash_hex = hashlib.sha256(salted_data).hexdigest()
        
        return hash_hex
    
    def hash_dataframe_columns(
        self, 
        df: pd.DataFrame, 
        pii_columns: List[str],
        suffix: str = "_hash"
    ) -> pd.DataFrame:
        """
        Hashe les colonnes PII d'un DataFrame.
        
        Args:
            df: DataFrame source
            pii_columns: Liste des colonnes contenant des PII
            suffix: Suffixe pour les nouvelles colonnes hashées
            
        Returns:
            DataFrame avec colonnes PII hashées
        """
        result_df = df.copy()
        
        for col in pii_columns:
            if col in df.columns:
                hash_col = f"{col}{suffix}"
                result_df[hash_col] = df[col].apply(self.hash_identifier)
                
                # Suppression de la colonne originale pour sécurité
                result_df = result_df.drop(columns=[col])
                
                logger.info(
                    "Colonne PII hashée",
                    original_column=col,
                    hash_column=hash_col,
                    rows_processed=len(df)
                )
        
        return result_df


class AuditLogger:
    """
    Système d'audit pour le suivi des prédictions ML et conformité RGPD.
    """
    
    def __init__(self, project_id: str):
        """
        Initialise le système d'audit.
        
        Args:
            project_id: ID du projet GCP
        """
        self.project_id = project_id
        self.pii_hasher = PIIHasher()
        
    def create_audit_record(
        self,
        model_name: str,
        model_version: str,
        id_prospect: str,
        prediction_value: float,
        feature_hash: str,
        prediction_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Crée un enregistrement d'audit pour une prédiction.
        
        Args:
            model_name: Nom du modèle
            model_version: Version du modèle
            id_prospect: Identifiant du prospect (sera hashé)
            prediction_value: Valeur de la prédiction
            feature_hash: Hash des features utilisées
            prediction_metadata: Métadonnées additionnelles
            
        Returns:
            Enregistrement d'audit
        """
        audit_record = {
            'audit_id': str(uuid.uuid4()),
            'model_name': model_name,
            'model_version': model_version,
            'id_prospect_hash': self.pii_hasher.hash_identifier(id_prospect),
            'prediction_timestamp': datetime.utcnow().isoformat(),
            'feature_hash': feature_hash,
            'prediction_value': prediction_value,
            'prediction_metadata': json.dumps(prediction_metadata or {}),
            'created_at': datetime.utcnow().isoformat()
        }
        
        logger.info(
            "Enregistrement d'audit créé",
            audit_id=audit_record['audit_id'],
            model_name=model_name,
            model_version=model_version
        )
        
        return audit_record
    
    def log_prediction_batch(
        self,
        model_name: str,
        model_version: str,
        predictions_df: pd.DataFrame,
        feature_hashes: List[str]
    ) -> List[Dict[str, Any]]:
        """
        Enregistre un batch de prédictions dans l'audit.
        
        Args:
            model_name: Nom du modèle
            model_version: Version du modèle
            predictions_df: DataFrame avec id_prospect et predictions
            feature_hashes: Liste des hashs de features
            
        Returns:
            Liste des enregistrements d'audit
        """
        audit_records = []
        
        for i, row in predictions_df.iterrows():
            feature_hash = feature_hashes[i] if i < len(feature_hashes) else ""
            
            record = self.create_audit_record(
                model_name=model_name,
                model_version=model_version,
                id_prospect=row['id_prospect'],
                prediction_value=row.get('prediction', 0.0),
                feature_hash=feature_hash,
                prediction_metadata={
                    'batch_id': str(uuid.uuid4()),
                    'batch_size': len(predictions_df),
                    'prediction_index': i
                }
            )
            
            audit_records.append(record)
        
        logger.info(
            "Batch d'audit enregistré",
            model_name=model_name,
            batch_size=len(audit_records)
        )
        
        return audit_records


class DataRetentionManager:
    """
    Gestionnaire de rétention des données conforme RGPD.
    """
    
    # Durées de rétention par type de données (en jours)
    RETENTION_POLICIES = {
        'predictions': 365,      # 1 an pour les prédictions
        'features': 90,          # 3 mois pour les features
        'audit_logs': 2555,      # 7 ans pour l'audit (obligation légale)
        'model_artifacts': 1095, # 3 ans pour les artefacts de modèles
        'training_data': 180     # 6 mois pour les données d'entraînement
    }
    
    def __init__(self, project_id: str):
        """
        Initialise le gestionnaire de rétention.
        
        Args:
            project_id: ID du projet GCP
        """
        self.project_id = project_id
    
    def get_retention_date(self, data_type: str) -> datetime:
        """
        Calcule la date limite de rétention pour un type de données.
        
        Args:
            data_type: Type de données
            
        Returns:
            Date limite de rétention
        """
        if data_type not in self.RETENTION_POLICIES:
            raise ValueError(f"Type de données non supporté: {data_type}")
        
        retention_days = self.RETENTION_POLICIES[data_type]
        retention_date = datetime.utcnow() - timedelta(days=retention_days)
        
        return retention_date
    
    def generate_cleanup_queries(self) -> Dict[str, str]:
        """
        Génère les requêtes SQL de nettoyage pour chaque type de données.
        
        Returns:
            Dictionnaire des requêtes de nettoyage
        """
        queries = {}
        
        # Nettoyage des prédictions anciennes
        predictions_date = self.get_retention_date('predictions')
        queries['predictions'] = f"""
        DELETE FROM `{self.project_id}.serving_layer_ml.predictions_*`
        WHERE prediction_date < '{predictions_date.date()}'
        """
        
        # Nettoyage des features anciennes
        features_date = self.get_retention_date('features')
        queries['features'] = f"""
        DELETE FROM `{self.project_id}.serving_layer_ml.features_daily`
        WHERE date_features < '{features_date.date()}'
        """
        
        # Nettoyage des logs d'audit (attention: durée légale)
        audit_date = self.get_retention_date('audit_logs')
        queries['audit'] = f"""
        DELETE FROM `{self.project_id}.serving_layer_ml.audit`
        WHERE DATE(prediction_timestamp) < '{audit_date.date()}'
        """
        
        logger.info(
            "Requêtes de nettoyage générées",
            retention_policies=self.RETENTION_POLICIES
        )
        
        return queries


class ConsentManager:
    """
    Gestionnaire de consentement RGPD pour le traitement des données.
    """
    
    def __init__(self, project_id: str):
        """
        Initialise le gestionnaire de consentement.
        
        Args:
            project_id: ID du projet GCP
        """
        self.project_id = project_id
    
    def check_processing_consent(self, id_prospect: str) -> bool:
        """
        Vérifie le consentement pour le traitement des données d'un prospect.
        
        Args:
            id_prospect: Identifiant du prospect
            
        Returns:
            True si le consentement est accordé
            
        Note:
            Dans une implémentation réelle, ceci interrogerait une base de données
            de consentements. Ici, on retourne True par défaut.
        """
        # TODO: Implémenter la vérification réelle du consentement
        # Exemple: requête vers une table de consentements
        
        logger.debug(
            "Vérification du consentement",
            id_prospect_hash=PIIHasher().hash_identifier(id_prospect)
        )
        
        return True  # Par défaut, on assume le consentement
    
    def filter_consented_prospects(self, prospects_df: pd.DataFrame) -> pd.DataFrame:
        """
        Filtre les prospects selon leur consentement.
        
        Args:
            prospects_df: DataFrame avec les prospects
            
        Returns:
            DataFrame filtré avec seulement les prospects consentants
        """
        if 'id_prospect' not in prospects_df.columns:
            logger.warning("Colonne id_prospect manquante pour la vérification du consentement")
            return prospects_df
        
        # Vérification du consentement pour chaque prospect
        consent_mask = prospects_df['id_prospect'].apply(self.check_processing_consent)
        filtered_df = prospects_df[consent_mask].copy()
        
        removed_count = len(prospects_df) - len(filtered_df)
        if removed_count > 0:
            logger.info(
                "Prospects filtrés par consentement",
                total_prospects=len(prospects_df),
                consented_prospects=len(filtered_df),
                removed_prospects=removed_count
            )
        
        return filtered_df
    
    def log_consent_check(self, id_prospect: str, consent_granted: bool) -> None:
        """
        Enregistre une vérification de consentement dans les logs d'audit.
        
        Args:
            id_prospect: Identifiant du prospect
            consent_granted: Résultat de la vérification
        """
        logger.info(
            "Vérification de consentement",
            id_prospect_hash=PIIHasher().hash_identifier(id_prospect),
            consent_granted=consent_granted,
            timestamp=datetime.utcnow().isoformat()
        )


class SecurityValidator:
    """
    Validateur de sécurité pour les opérations ML.
    """
    
    @staticmethod
    def validate_model_access(model_name: str, user_role: str) -> bool:
        """
        Valide l'accès à un modèle selon le rôle utilisateur.
        
        Args:
            model_name: Nom du modèle
            user_role: Rôle de l'utilisateur
            
        Returns:
            True si l'accès est autorisé
        """
        # Matrice d'autorisation simple
        access_matrix = {
            'admin': ['conversion', 'channel', 'nlp_signals'],
            'data_scientist': ['conversion', 'channel', 'nlp_signals'],
            'business_user': ['conversion', 'channel'],
            'viewer': []
        }
        
        allowed_models = access_matrix.get(user_role, [])
        is_authorized = model_name in allowed_models
        
        logger.info(
            "Validation d'accès modèle",
            model_name=model_name,
            user_role=user_role,
            authorized=is_authorized
        )
        
        return is_authorized
    
    @staticmethod
    def validate_data_export(data_size: int, user_role: str) -> bool:
        """
        Valide l'export de données selon la taille et le rôle.
        
        Args:
            data_size: Nombre de lignes à exporter
            user_role: Rôle de l'utilisateur
            
        Returns:
            True si l'export est autorisé
        """
        # Limites d'export par rôle
        export_limits = {
            'admin': 1000000,      # 1M lignes
            'data_scientist': 100000,  # 100k lignes
            'business_user': 10000,    # 10k lignes
            'viewer': 1000             # 1k lignes
        }
        
        limit = export_limits.get(user_role, 0)
        is_authorized = data_size <= limit
        
        logger.info(
            "Validation d'export de données",
            data_size=data_size,
            user_role=user_role,
            limit=limit,
            authorized=is_authorized
        )
        
        return is_authorized
