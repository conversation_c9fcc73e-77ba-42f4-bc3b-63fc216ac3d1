#!/usr/bin/env python3
"""
Détecteur automatique d'overfitting pour les modèles Sensei AI.
"""

from typing import Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum

class OverfittingLevel(Enum):
    """Niveaux de sévérité de l'overfitting."""
    NONE = "none"
    MILD = "mild"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"

@dataclass
class OverfittingAlert:
    """Alerte d'overfitting."""
    level: OverfittingLevel
    message: str
    metric_name: str
    metric_value: float
    threshold: float
    recommendation: str

class OverfittingDetector:
    """Détecteur automatique d'overfitting."""
    
    def __init__(self):
        # Seuils pour différents types de modèles
        self.thresholds = {
            'binary_classification': {
                'auc_max': 0.85,
                'auc_perfect': 0.95,
                'cv_variance_max': 0.05,
                'train_val_gap_max': 0.15,
                'precision_recall_perfect': 0.98
            },
            'multiclass_classification': {
                'accuracy_max': 0.75,
                'accuracy_perfect': 0.95,
                'cv_variance_max': 0.05,
                'train_val_gap_max': 0.10,
                'f1_perfect': 0.95
            }
        }
    
    def detect_binary_overfitting(
        self, 
        train_auc: float,
        val_auc: float,
        cv_scores: List[float],
        train_precision: float,
        train_recall: float,
        val_precision: float,
        val_recall: float,
        positive_rate: float
    ) -> Tuple[OverfittingLevel, List[OverfittingAlert]]:
        """Détecte l'overfitting pour classification binaire."""
        
        alerts = []
        max_level = OverfittingLevel.NONE
        
        thresholds = self.thresholds['binary_classification']
        
        # 1. AUC trop élevé
        if val_auc > thresholds['auc_perfect']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.CRITICAL,
                message=f"AUC validation parfait ({val_auc:.3f})",
                metric_name="val_auc",
                metric_value=val_auc,
                threshold=thresholds['auc_perfect'],
                recommendation="Ajouter plus de bruit dans les données ou réduire la complexité du modèle"
            ))
            max_level = OverfittingLevel.CRITICAL
            
        elif val_auc > thresholds['auc_max']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.MODERATE,
                message=f"AUC validation élevé ({val_auc:.3f})",
                metric_name="val_auc",
                metric_value=val_auc,
                threshold=thresholds['auc_max'],
                recommendation="Augmenter la régularisation ou réduire le nombre de features"
            ))
            max_level = max(max_level, OverfittingLevel.MODERATE)
        
        # 2. Variance en validation croisée
        if cv_scores:
            cv_std = np.std(cv_scores)
            if cv_std > thresholds['cv_variance_max']:
                alerts.append(OverfittingAlert(
                    level=OverfittingLevel.MILD,
                    message=f"Variance élevée en CV ({cv_std:.3f})",
                    metric_name="cv_std",
                    metric_value=cv_std,
                    threshold=thresholds['cv_variance_max'],
                    recommendation="Augmenter la taille des données ou stabiliser le modèle"
                ))
                max_level = max(max_level, OverfittingLevel.MILD)
        
        # 3. Écart train/validation
        train_val_gap = abs(train_auc - val_auc)
        if train_val_gap > thresholds['train_val_gap_max']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.MODERATE,
                message=f"Écart train/val important ({train_val_gap:.3f})",
                metric_name="train_val_gap",
                metric_value=train_val_gap,
                threshold=thresholds['train_val_gap_max'],
                recommendation="Augmenter la régularisation ou utiliser plus de données"
            ))
            max_level = max(max_level, OverfittingLevel.MODERATE)
        
        # 4. Performance parfaite
        if (val_precision > thresholds['precision_recall_perfect'] and 
            val_recall > thresholds['precision_recall_perfect']):
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.CRITICAL,
                message="Precision et Recall parfaits",
                metric_name="precision_recall",
                metric_value=min(val_precision, val_recall),
                threshold=thresholds['precision_recall_perfect'],
                recommendation="Vérifier la présence de data leakage ou ajouter du bruit"
            ))
            max_level = OverfittingLevel.CRITICAL
        
        # 5. Taux de conversion irréaliste
        if positive_rate < 0.005:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.MILD,
                message=f"Taux de conversion très faible ({positive_rate:.1%})",
                metric_name="positive_rate",
                metric_value=positive_rate,
                threshold=0.005,
                recommendation="Vérifier la logique de création du target"
            ))
            max_level = max(max_level, OverfittingLevel.MILD)
        elif positive_rate > 0.3:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.MILD,
                message=f"Taux de conversion très élevé ({positive_rate:.1%})",
                metric_name="positive_rate",
                metric_value=positive_rate,
                threshold=0.3,
                recommendation="Vérifier la logique de création du target"
            ))
            max_level = max(max_level, OverfittingLevel.MILD)
        
        return max_level, alerts
    
    def detect_multiclass_overfitting(
        self,
        train_accuracy: float,
        val_accuracy: float,
        cv_scores: List[float],
        val_f1_macro: float,
        num_classes: int
    ) -> Tuple[OverfittingLevel, List[OverfittingAlert]]:
        """Détecte l'overfitting pour classification multiclasse."""
        
        alerts = []
        max_level = OverfittingLevel.NONE
        
        thresholds = self.thresholds['multiclass_classification']
        
        # 1. Accuracy trop élevée
        if val_accuracy > thresholds['accuracy_perfect']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.CRITICAL,
                message=f"Accuracy validation parfaite ({val_accuracy:.3f})",
                metric_name="val_accuracy",
                metric_value=val_accuracy,
                threshold=thresholds['accuracy_perfect'],
                recommendation="Ajouter plus de bruit ou réduire drastiquement la complexité"
            ))
            max_level = OverfittingLevel.CRITICAL
            
        elif val_accuracy > thresholds['accuracy_max']:
            # Ajustement du seuil selon le nombre de classes
            adjusted_threshold = thresholds['accuracy_max'] - (num_classes - 2) * 0.05
            if val_accuracy > adjusted_threshold:
                alerts.append(OverfittingAlert(
                    level=OverfittingLevel.MODERATE,
                    message=f"Accuracy validation élevée ({val_accuracy:.3f})",
                    metric_name="val_accuracy",
                    metric_value=val_accuracy,
                    threshold=adjusted_threshold,
                    recommendation="Augmenter la régularisation ou ajouter du bruit"
                ))
                max_level = max(max_level, OverfittingLevel.MODERATE)
        
        # 2. Variance en validation croisée
        if cv_scores:
            cv_std = np.std(cv_scores)
            if cv_std > thresholds['cv_variance_max']:
                alerts.append(OverfittingAlert(
                    level=OverfittingLevel.MILD,
                    message=f"Variance élevée en CV ({cv_std:.3f})",
                    metric_name="cv_std",
                    metric_value=cv_std,
                    threshold=thresholds['cv_variance_max'],
                    recommendation="Stabiliser le modèle ou augmenter les données"
                ))
                max_level = max(max_level, OverfittingLevel.MILD)
        
        # 3. Écart train/validation
        train_val_gap = abs(train_accuracy - val_accuracy)
        if train_val_gap > thresholds['train_val_gap_max']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.MODERATE,
                message=f"Écart train/val important ({train_val_gap:.3f})",
                metric_name="train_val_gap",
                metric_value=train_val_gap,
                threshold=thresholds['train_val_gap_max'],
                recommendation="Augmenter la régularisation"
            ))
            max_level = max(max_level, OverfittingLevel.MODERATE)
        
        # 4. F1 score parfait
        if val_f1_macro > thresholds['f1_perfect']:
            alerts.append(OverfittingAlert(
                level=OverfittingLevel.CRITICAL,
                message=f"F1 macro parfait ({val_f1_macro:.3f})",
                metric_name="val_f1_macro",
                metric_value=val_f1_macro,
                threshold=thresholds['f1_perfect'],
                recommendation="Vérifier la présence de data leakage"
            ))
            max_level = OverfittingLevel.CRITICAL
        
        return max_level, alerts
    
    def generate_report(
        self, 
        level: OverfittingLevel, 
        alerts: List[OverfittingAlert],
        model_name: str
    ) -> str:
        """Génère un rapport d'overfitting."""
        
        if level == OverfittingLevel.NONE:
            return f"✅ {model_name}: Aucun overfitting détecté - Modèle sain"
        
        level_icons = {
            OverfittingLevel.MILD: "⚠️",
            OverfittingLevel.MODERATE: "🔶",
            OverfittingLevel.SEVERE: "🔴",
            OverfittingLevel.CRITICAL: "🚨"
        }
        
        report = f"{level_icons[level]} {model_name}: Overfitting {level.value.upper()} détecté\n"
        report += "="*60 + "\n"
        
        for i, alert in enumerate(alerts, 1):
            report += f"{i}. {alert.message}\n"
            report += f"   Métrique: {alert.metric_name} = {alert.metric_value:.3f} (seuil: {alert.threshold:.3f})\n"
            report += f"   Recommandation: {alert.recommendation}\n\n"
        
        # Actions recommandées selon le niveau
        if level == OverfittingLevel.CRITICAL:
            report += "🚨 ACTIONS URGENTES:\n"
            report += "- Arrêter l'utilisation du modèle en production\n"
            report += "- Vérifier la présence de data leakage\n"
            report += "- Revoir complètement la logique de création du target\n"
        elif level == OverfittingLevel.SEVERE:
            report += "🔴 ACTIONS REQUISES:\n"
            report += "- Augmenter drastiquement la régularisation\n"
            report += "- Ajouter plus de bruit dans les données\n"
            report += "- Réduire la complexité du modèle\n"
        elif level == OverfittingLevel.MODERATE:
            report += "🔶 ACTIONS RECOMMANDÉES:\n"
            report += "- Ajuster les hyperparamètres de régularisation\n"
            report += "- Valider avec plus de données\n"
            report += "- Surveiller les performances en production\n"
        
        return report
    
    def should_stop_training(self, level: OverfittingLevel) -> bool:
        """Détermine si l'entraînement doit être arrêté."""
        return level in [OverfittingLevel.SEVERE, OverfittingLevel.CRITICAL]
