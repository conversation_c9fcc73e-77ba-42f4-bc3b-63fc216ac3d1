-- Template SQL pour la création de la table features_daily
-- Variables Jinja: {{ target_date }}, {{ project_id }}

CREATE OR REPLACE TABLE `{{ project_id }}.serving_layer_ml.features_daily`
PARTITION BY DATE(date_features)
CLUSTER BY id_prospect AS

WITH 
-- ============================================================================
-- TYPEFORM: Agrégation des réponses et comportements
-- ============================================================================
typeform_features AS (
  SELECT 
    id_prospect,
    -- Métadonnées temporelles
    DATE(dt_soumission) as date_soumission,
    EXTRACT(HOUR FROM dt_soumission) as heure_soumission,
    EXTRACT(DAYOFWEEK FROM dt_soumission) as jour_semaine_soumission,
    
    -- Comportement de réponse
    duree_reponses_minutes,
    CASE 
      WHEN duree_reponses_minutes < 2 THEN 'tres_rapide'
      WHEN duree_reponses_minutes < 5 THEN 'rapide' 
      WHEN duree_reponses_minutes < 10 THEN 'normal'
      ELSE 'lent'
    END as vitesse_reponse,
    
    -- Comptage des réponses par formulaire
    COUNT(*) OVER (PARTITION BY id_prospect, nom_formulaire) as nb_reponses_formulaire,
    
    -- Analyse des abandons (formulaires non complétés)
    CASE WHEN COUNT(*) OVER (PARTITION BY id_soumission_formulaire) < 5 THEN 1 ELSE 0 END as abandon_probable,
    
    -- Variables catégorielles pivotées (exemples basés sur les questions communes)
    MAX(CASE WHEN texte_question LIKE '%budget%' THEN valeur_reponse END) as budget_declare,
    MAX(CASE WHEN texte_question LIKE '%secteur%' OR texte_question LIKE '%activité%' THEN valeur_reponse END) as secteur_activite,
    MAX(CASE WHEN texte_question LIKE '%taille%' OR texte_question LIKE '%effectif%' THEN valeur_reponse END) as taille_entreprise,
    MAX(CASE WHEN texte_question LIKE '%urgence%' OR texte_question LIKE '%délai%' THEN valeur_reponse END) as urgence_projet,
    MAX(CASE WHEN texte_question LIKE '%expérience%' THEN valeur_reponse END) as experience_anterieure,
    
    -- Score de complétude du profil
    COUNT(DISTINCT CASE WHEN valeur_reponse IS NOT NULL AND valeur_reponse != '' THEN texte_question END) as nb_questions_repondues,
    COUNT(DISTINCT texte_question) as nb_questions_total,
    SAFE_DIVIDE(
      COUNT(DISTINCT CASE WHEN valeur_reponse IS NOT NULL AND valeur_reponse != '' THEN texte_question END),
      COUNT(DISTINCT texte_question)
    ) as taux_completion_profil
    
  FROM `{{ project_id }}.serving_layer.vw_reponses_typeform_deno`
  WHERE DATE(dt_soumission) <= '{{ target_date }}'
  GROUP BY id_prospect, id_soumission_formulaire, dt_soumission, duree_reponses_minutes, nom_formulaire
),

-- ============================================================================
-- HUBSPOT: Activité CRM et scores
-- ============================================================================
hubspot_features AS (
  SELECT 
    c.id_contact as id_prospect,
    
    -- Scores et métriques HubSpot
    c.hubspotscore,
    c.num_unique_conversion_events,
    c.num_conversion_events, 
    c.num_associated_deals,
    c.hs_analytics_num_page_views,
    c.nombre_de_transactions_terminees,
    
    -- Statut et qualification
    c.statut_du_lead,
    c.relationship_status,
    CASE WHEN c.lead_traite = 'true' THEN 1 ELSE 0 END as lead_traite,
    
    -- Géolocalisation
    c.ip_country,
    
    -- Ancienneté et récence
    DATE_DIFF('{{ target_date }}', DATE(c.dt_creation_contact), DAY) as anciennete_contact_jours,
    DATE_DIFF('{{ target_date }}', DATE(c.dt_modification_contact), DAY) as jours_depuis_derniere_modif,
    
    -- Activité e-mails (derniers 30 jours)
    COUNTIF(DATE(e.hs_createdate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}') as nb_emails_30j,
    
    -- Activité appels (derniers 30 jours) 
    COUNTIF(DATE(a.dt_creation_appel) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}') as nb_appels_30j,
    AVG(CASE WHEN DATE(a.dt_creation_appel) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN a.hs_call_duration END) as duree_moyenne_appels_30j,
    
    -- Activité réunions (derniers 30 jours)
    COUNTIF(DATE(r.dt_creation_reunion) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}') as nb_reunions_30j,
    
    -- Tickets de support
    COUNTIF(DATE(t.dt_creation_ticket) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}') as nb_tickets_30j
    
  FROM `{{ project_id }}.serving_layer.vw_dim_contact` c
  LEFT JOIN `{{ project_id }}.serving_layer.vw_dim_email` e 
    ON c.id_contact = e.id_contact_destinataire
  LEFT JOIN `{{ project_id }}.serving_layer.vw_dim_appel` a 
    ON c.id_contact = a.id_contact  -- Assuming this join exists
  LEFT JOIN `{{ project_id }}.serving_layer.vw_dim_reunion` r 
    ON c.id_contact = r.id_contact  -- Assuming this join exists  
  LEFT JOIN `{{ project_id }}.serving_layer.vw_dim_ticket` t 
    ON c.id_contact = t.id_contact  -- Assuming this join exists
  WHERE DATE(c.dt_creation_contact) <= '{{ target_date }}'
  GROUP BY 
    c.id_contact, c.hubspotscore, c.num_unique_conversion_events, c.num_conversion_events,
    c.num_associated_deals, c.hs_analytics_num_page_views, c.nombre_de_transactions_terminees,
    c.statut_du_lead, c.relationship_status, c.lead_traite, c.ip_country,
    c.dt_creation_contact, c.dt_modification_contact
),

-- ============================================================================
-- MODJO: Agrégats des appels et scores IA
-- ============================================================================
modjo_features AS (
  SELECT 
    contactCrmId as id_prospect,
    
    -- Compteurs d'appels
    COUNT(*) as nb_appels_modjo_total,
    COUNT(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' THEN 1 END) as nb_appels_modjo_30j,
    COUNT(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 7 DAY) AND '{{ target_date }}' THEN 1 END) as nb_appels_modjo_7j,
    
    -- Durées d'appels
    AVG(duration) as duree_moyenne_appels_modjo,
    MAX(duration) as duree_max_appel_modjo,
    SUM(duration) as duree_totale_appels_modjo,
    
    -- Scores IA moyens (derniers appels)
    AVG(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN score_appel_prequai END) as score_prequai_moy_30j,
    AVG(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN score_ice_breaker END) as score_ice_breaker_moy_30j,
    AVG(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN score_phase_decouverte END) as score_decouverte_moy_30j,
    AVG(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN score_consolider_vente END) as score_consolidation_moy_30j,
    AVG(CASE WHEN DATE(startDate) BETWEEN DATE_SUB('{{ target_date }}', INTERVAL 30 DAY) AND '{{ target_date }}' 
             THEN score_visio_vente END) as score_visio_moy_30j,
    
    -- Derniers scores (appel le plus récent)
    LAST_VALUE(score_appel_prequai) OVER (
      PARTITION BY contactCrmId 
      ORDER BY startDate 
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as dernier_score_prequai,
    
    -- Récence du dernier appel
    DATE_DIFF('{{ target_date }}', MAX(DATE(startDate)), DAY) as jours_depuis_dernier_appel_modjo
    
  FROM `{{ project_id }}.serving_layer.vw_dim_modjo_call_summary` 
  WHERE DATE(startDate) <= '{{ target_date }}'
    AND contactCrmId IS NOT NULL
  GROUP BY contactCrmId
),

-- ============================================================================
-- LABEL: Conversion dans les 90 jours (deals "closedwon")
-- ============================================================================
conversion_label AS (
  SELECT 
    COALESCE(t.id_contact, t.email_client) as id_prospect,
    MAX(CASE 
      WHEN t.dealstage = 'closedwon' 
      AND DATE(t.closedate) BETWEEN '{{ target_date }}' AND DATE_ADD('{{ target_date }}', INTERVAL 90 DAY)
      THEN 1 
      ELSE 0 
    END) as y_converted_90j,
    
    -- Informations sur la conversion
    MIN(CASE WHEN t.dealstage = 'closedwon' THEN DATE(t.closedate) END) as date_premiere_conversion,
    MAX(CASE WHEN t.dealstage = 'closedwon' THEN t.montant END) as montant_max_conversion,
    SUM(CASE WHEN t.dealstage = 'closedwon' THEN t.montant END) as montant_total_conversions
    
  FROM `{{ project_id }}.serving_layer.vw_fact_transaction` t
  WHERE DATE(t.createdate) <= '{{ target_date }}'
  GROUP BY COALESCE(t.id_contact, t.email_client)
)

-- ============================================================================
-- ASSEMBLAGE FINAL
-- ============================================================================
SELECT 
  '{{ target_date }}' as date_features,
  COALESCE(tf.id_prospect, hf.id_prospect, mf.id_prospect, cl.id_prospect) as id_prospect,
  
  -- Features Typeform
  tf.date_soumission,
  tf.heure_soumission,
  tf.jour_semaine_soumission,
  tf.duree_reponses_minutes,
  tf.vitesse_reponse,
  tf.nb_reponses_formulaire,
  tf.abandon_probable,
  tf.budget_declare,
  tf.secteur_activite,
  tf.taille_entreprise,
  tf.urgence_projet,
  tf.experience_anterieure,
  tf.nb_questions_repondues,
  tf.nb_questions_total,
  tf.taux_completion_profil,
  
  -- Features HubSpot
  COALESCE(hf.hubspotscore, 0) as hubspotscore,
  COALESCE(hf.num_unique_conversion_events, 0) as num_unique_conversion_events,
  COALESCE(hf.num_conversion_events, 0) as num_conversion_events,
  COALESCE(hf.num_associated_deals, 0) as num_associated_deals,
  COALESCE(hf.hs_analytics_num_page_views, 0) as hs_analytics_num_page_views,
  COALESCE(hf.nombre_de_transactions_terminees, 0) as nombre_de_transactions_terminees,
  hf.statut_du_lead,
  hf.relationship_status,
  COALESCE(hf.lead_traite, 0) as lead_traite,
  hf.ip_country,
  hf.anciennete_contact_jours,
  hf.jours_depuis_derniere_modif,
  COALESCE(hf.nb_emails_30j, 0) as nb_emails_30j,
  COALESCE(hf.nb_appels_30j, 0) as nb_appels_30j,
  hf.duree_moyenne_appels_30j,
  COALESCE(hf.nb_reunions_30j, 0) as nb_reunions_30j,
  COALESCE(hf.nb_tickets_30j, 0) as nb_tickets_30j,
  
  -- Features Modjo
  COALESCE(mf.nb_appels_modjo_total, 0) as nb_appels_modjo_total,
  COALESCE(mf.nb_appels_modjo_30j, 0) as nb_appels_modjo_30j,
  COALESCE(mf.nb_appels_modjo_7j, 0) as nb_appels_modjo_7j,
  mf.duree_moyenne_appels_modjo,
  mf.duree_max_appel_modjo,
  mf.duree_totale_appels_modjo,
  mf.score_prequai_moy_30j,
  mf.score_ice_breaker_moy_30j,
  mf.score_decouverte_moy_30j,
  mf.score_consolidation_moy_30j,
  mf.score_visio_moy_30j,
  mf.dernier_score_prequai,
  mf.jours_depuis_dernier_appel_modjo,
  
  -- Label de conversion
  COALESCE(cl.y_converted_90j, 0) as y_converted_90j,
  cl.date_premiere_conversion,
  cl.montant_max_conversion,
  cl.montant_total_conversions,
  
  -- Métadonnées
  CURRENT_TIMESTAMP() as dt_creation,
  SHA256(CONCAT(COALESCE(tf.id_prospect, hf.id_prospect, mf.id_prospect, cl.id_prospect), '{{ target_date }}')) as feature_hash

FROM typeform_features tf
FULL OUTER JOIN hubspot_features hf ON tf.id_prospect = hf.id_prospect
FULL OUTER JOIN modjo_features mf ON COALESCE(tf.id_prospect, hf.id_prospect) = mf.id_prospect  
FULL OUTER JOIN conversion_label cl ON COALESCE(tf.id_prospect, hf.id_prospect, mf.id_prospect) = cl.id_prospect

WHERE COALESCE(tf.id_prospect, hf.id_prospect, mf.id_prospect, cl.id_prospect) IS NOT NULL
