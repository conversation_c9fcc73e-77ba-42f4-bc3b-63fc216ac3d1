"""
Script de construction du Feature Store quotidien.
"""

import os
from datetime import datetime, date
from typing import Optional
from pathlib import Path

from jinja2 import Environment, FileSystemLoader
import structlog

from ..data.bq_client import get_bq_client
from ..utils.logging import get_logger

logger = get_logger(__name__)


class FeatureStoreBuilder:
    """
    Constructeur du Feature Store quotidien pour serving_layer_ml.
    """
    
    def __init__(self, project_id: Optional[str] = None):
        """
        Initialise le constructeur de Feature Store.
        
        Args:
            project_id: ID du projet GCP
        """
        self.project_id = project_id or os.getenv("GOOGLE_CLOUD_PROJECT")
        if not self.project_id:
            raise ValueError("project_id requis ou GOOGLE_CLOUD_PROJECT manquant")
        
        self.bq_client = get_bq_client()
        
        # Configuration Jinja2 pour les templates SQL
        template_dir = Path(__file__).parent / "sql_templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        logger.info(
            "FeatureStoreBuilder initialisé",
            project_id=self.project_id,
            template_dir=str(template_dir)
        )
    
    def _render_sql_template(self, template_name: str, **kwargs) -> str:
        """
        Rend un template SQL avec les variables Jinja.
        
        Args:
            template_name: Nom du fichier template
            **kwargs: Variables à injecter dans le template
            
        Returns:
            SQL rendu
        """
        template = self.jinja_env.get_template(template_name)
        return template.render(project_id=self.project_id, **kwargs)
    
    def create_dataset_if_not_exists(self) -> None:
        """
        Crée le dataset serving_layer_ml s'il n'existe pas.
        """
        try:
            dataset_id = f"{self.project_id}.serving_layer_ml"
            
            # Vérification de l'existence du dataset
            try:
                self.bq_client.client.get_dataset(dataset_id)
                logger.info("Dataset existe déjà", dataset_id=dataset_id)
                return
            except Exception:
                pass
            
            # Création du dataset
            from google.cloud import bigquery
            
            dataset = bigquery.Dataset(dataset_id)
            dataset.location = "EU"  # Localisation européenne pour RGPD
            dataset.description = "Dataset ML pour les features et prédictions Sensei AI"
            
            # Configuration de la rétention (30 jours pour les logs)
            dataset.default_table_expiration_ms = 30 * 24 * 60 * 60 * 1000  # 30 jours
            
            dataset = self.bq_client.client.create_dataset(dataset, timeout=30)
            
            logger.info(
                "Dataset créé avec succès",
                dataset_id=dataset_id,
                location=dataset.location
            )
            
        except Exception as e:
            logger.error(
                "Erreur lors de la création du dataset",
                dataset_id=dataset_id,
                error=str(e)
            )
            raise
    
    def build_features_daily(self, target_date: date) -> None:
        """
        Construit la table features_daily pour une date donnée.
        
        Args:
            target_date: Date cible pour la construction des features
        """
        try:
            logger.info(
                "Début de la construction des features quotidiennes",
                target_date=target_date.isoformat()
            )
            
            # Rendu du template SQL
            sql = self._render_sql_template(
                "features_daily.sql",
                target_date=target_date.isoformat()
            )
            
            # Exécution de la requête
            job = self.bq_client.execute_query(sql)
            
            # Vérification du nombre de lignes créées
            result_table = f"{self.project_id}.serving_layer_ml.features_daily"
            count_sql = f"""
            SELECT COUNT(*) as nb_rows 
            FROM `{result_table}` 
            WHERE date_features = '{target_date.isoformat()}'
            """
            
            count_df = self.bq_client.query_df(count_sql)
            nb_rows = count_df.iloc[0]['nb_rows']
            
            logger.info(
                "Features quotidiennes construites avec succès",
                target_date=target_date.isoformat(),
                nb_rows=nb_rows,
                job_id=job.job_id,
                bytes_processed=job.total_bytes_processed
            )
            
        except Exception as e:
            logger.error(
                "Erreur lors de la construction des features",
                target_date=target_date.isoformat(),
                error=str(e)
            )
            raise
    
    def create_audit_table(self) -> None:
        """
        Crée la table d'audit pour le suivi RGPD.
        """
        try:
            sql = f"""
            CREATE TABLE IF NOT EXISTS `{self.project_id}.serving_layer_ml.audit` (
                audit_id STRING NOT NULL,
                model_name STRING NOT NULL,
                model_version STRING NOT NULL,
                id_prospect_hash STRING NOT NULL,
                prediction_timestamp TIMESTAMP NOT NULL,
                feature_hash STRING NOT NULL,
                prediction_value FLOAT64,
                prediction_metadata JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
            )
            PARTITION BY DATE(prediction_timestamp)
            CLUSTER BY model_name, id_prospect_hash
            """
            
            self.bq_client.execute_query(sql)
            
            logger.info("Table d'audit créée avec succès")
            
        except Exception as e:
            logger.error("Erreur lors de la création de la table d'audit", error=str(e))
            raise
    
    def setup_ml_dataset(self) -> None:
        """
        Configure complètement le dataset ML (dataset + tables).
        """
        logger.info("Configuration du dataset ML")
        
        # Création du dataset
        self.create_dataset_if_not_exists()
        
        # Création de la table d'audit
        self.create_audit_table()
        
        logger.info("Dataset ML configuré avec succès")


def build_features_for_date(target_date: date, project_id: Optional[str] = None) -> None:
    """
    Fonction utilitaire pour construire les features pour une date donnée.
    
    Args:
        target_date: Date cible
        project_id: ID du projet GCP
    """
    builder = FeatureStoreBuilder(project_id)
    builder.setup_ml_dataset()
    builder.build_features_daily(target_date)


if __name__ == "__main__":
    # Test avec la date d'hier
    from datetime import timedelta
    
    yesterday = date.today() - timedelta(days=1)
    build_features_for_date(yesterday)
