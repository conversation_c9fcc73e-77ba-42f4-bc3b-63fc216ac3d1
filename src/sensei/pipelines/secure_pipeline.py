"""
Pipeline ML sécurisé avec audit et conformité RGPD.
"""

import hashlib
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

import pandas as pd
import numpy as np
import structlog

from ..models.base import BaseModel, get_model_class
from ..data.bq_client import get_bq_client
from ..utils.security import <PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogger, ConsentManager, SecurityValidator
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SecureMLPipeline:
    """
    Pipeline ML sécurisé avec audit automatique et conformité RGPD.
    """
    
    def __init__(
        self,
        project_id: str,
        user_role: str = "data_scientist",
        enable_audit: bool = True,
        enable_consent_check: bool = True
    ):
        """
        Initialise le pipeline sécurisé.
        
        Args:
            project_id: ID du projet GCP
            user_role: Rôle de l'utilisateur pour les autorisations
            enable_audit: Activer l'audit des prédictions
            enable_consent_check: Activer la vérification du consentement
        """
        self.project_id = project_id
        self.user_role = user_role
        self.enable_audit = enable_audit
        self.enable_consent_check = enable_consent_check
        
        # Composants de sécurité
        self.pii_hasher = PIIHasher()
        self.audit_logger = AuditLogger(project_id) if enable_audit else None
        self.consent_manager = ConsentManager(project_id) if enable_consent_check else None
        self.security_validator = SecurityValidator()
        
        # Client BigQuery
        self.bq_client = get_bq_client()
        
        logger.info(
            "Pipeline ML sécurisé initialisé",
            project_id=project_id,
            user_role=user_role,
            audit_enabled=enable_audit,
            consent_enabled=enable_consent_check
        )
    
    def _validate_access(self, model_name: str) -> None:
        """
        Valide l'accès au modèle selon le rôle utilisateur.
        
        Args:
            model_name: Nom du modèle
            
        Raises:
            PermissionError: Si l'accès n'est pas autorisé
        """
        if not self.security_validator.validate_model_access(model_name, self.user_role):
            raise PermissionError(
                f"Accès refusé au modèle '{model_name}' pour le rôle '{self.user_role}'"
            )
    
    def _apply_consent_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Applique le filtre de consentement RGPD.
        
        Args:
            df: DataFrame avec les données
            
        Returns:
            DataFrame filtré selon les consentements
        """
        if not self.enable_consent_check or self.consent_manager is None:
            return df
        
        return self.consent_manager.filter_consented_prospects(df)
    
    def _compute_feature_hash(self, features_df: pd.DataFrame) -> List[str]:
        """
        Calcule le hash des features pour l'audit.
        
        Args:
            features_df: DataFrame des features
            
        Returns:
            Liste des hashs par ligne
        """
        feature_hashes = []
        
        for _, row in features_df.iterrows():
            # Sérialisation des features en string
            features_str = "|".join([
                f"{col}:{str(val)}" for col, val in row.items()
                if pd.notna(val)
            ])
            
            # Hash SHA256
            feature_hash = hashlib.sha256(features_str.encode('utf-8')).hexdigest()
            feature_hashes.append(feature_hash)
        
        return feature_hashes
    
    def _anonymize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Anonymise les données PII dans le DataFrame.
        
        Args:
            df: DataFrame source
            
        Returns:
            DataFrame avec PII anonymisées
        """
        # Colonnes PII connues
        pii_columns = [
            'email', 'nom_prenom', 'num_telephone', 'nom', 'prenom',
            'email_client', 'num_telephone_client', 'email_emetteur',
            'email_destinataire', 'phoneNumber', 'name'
        ]
        
        # Identification des colonnes PII présentes
        present_pii_columns = [col for col in pii_columns if col in df.columns]
        
        if present_pii_columns:
            logger.info(
                "Anonymisation des données PII",
                pii_columns=present_pii_columns,
                rows_count=len(df)
            )
            
            return self.pii_hasher.hash_dataframe_columns(df, present_pii_columns)
        
        return df
    
    def secure_train(
        self,
        model_name: str,
        train_data_query: str,
        val_data_query: Optional[str] = None,
        model_params: Optional[Dict[str, Any]] = None,
        save_path: Optional[str] = None
    ) -> Tuple[BaseModel, Dict[str, Any]]:
        """
        Entraîne un modèle de manière sécurisée.
        
        Args:
            model_name: Nom du modèle à entraîner
            train_data_query: Requête SQL pour les données d'entraînement
            val_data_query: Requête SQL pour les données de validation
            model_params: Paramètres du modèle
            save_path: Chemin de sauvegarde du modèle
            
        Returns:
            Tuple (modèle entraîné, métadonnées d'entraînement)
        """
        # Validation d'accès
        self._validate_access(model_name)
        
        logger.info(
            "Début d'entraînement sécurisé",
            model_name=model_name,
            user_role=self.user_role
        )
        
        # Chargement des données d'entraînement
        train_df = self.bq_client.query_df(train_data_query)
        logger.info("Données d'entraînement chargées", rows=len(train_df))
        
        # Application du filtre de consentement
        train_df = self._apply_consent_filter(train_df)
        logger.info("Filtre de consentement appliqué", rows_remaining=len(train_df))
        
        # Anonymisation des PII
        train_df = self._anonymize_data(train_df)
        
        # Chargement des données de validation si spécifiées
        val_df = None
        if val_data_query:
            val_df = self.bq_client.query_df(val_data_query)
            val_df = self._apply_consent_filter(val_df)
            val_df = self._anonymize_data(val_df)
            logger.info("Données de validation préparées", rows=len(val_df))
        
        # Initialisation du modèle
        model_class = get_model_class(model_name)
        model = model_class(model_params)
        
        # Entraînement
        training_metadata = model.train(train_df, val_df)
        
        # Ajout des métadonnées de sécurité
        training_metadata.update({
            'security_enabled': True,
            'user_role': self.user_role,
            'consent_filtered': self.enable_consent_check,
            'pii_anonymized': True,
            'audit_enabled': self.enable_audit
        })
        
        # Sauvegarde sécurisée
        if save_path:
            save_dir = Path(save_path) / model_name / datetime.now().strftime("%Y%m%d_%H%M%S")
            model.save(save_dir)
            logger.info("Modèle sauvegardé de manière sécurisée", path=str(save_dir))
        
        logger.info(
            "Entraînement sécurisé terminé",
            model_name=model_name,
            training_time=training_metadata.get('training_time_seconds', 0)
        )
        
        return model, training_metadata
    
    def secure_predict(
        self,
        model: BaseModel,
        prediction_data_query: str,
        output_table: Optional[str] = None,
        batch_size: int = 1000
    ) -> pd.DataFrame:
        """
        Effectue des prédictions sécurisées avec audit.
        
        Args:
            model: Modèle entraîné
            prediction_data_query: Requête SQL pour les données de prédiction
            output_table: Table BigQuery de sortie (optionnel)
            batch_size: Taille des batches pour le traitement
            
        Returns:
            DataFrame avec les prédictions
        """
        # Validation d'accès
        self._validate_access(model.model_name)
        
        logger.info(
            "Début de prédiction sécurisée",
            model_name=model.model_name,
            user_role=self.user_role
        )
        
        # Chargement des données
        data_df = self.bq_client.query_df(prediction_data_query)
        logger.info("Données de prédiction chargées", rows=len(data_df))
        
        # Application du filtre de consentement
        data_df = self._apply_consent_filter(data_df)
        logger.info("Filtre de consentement appliqué", rows_remaining=len(data_df))
        
        # Validation de la taille d'export
        if not self.security_validator.validate_data_export(len(data_df), self.user_role):
            raise PermissionError(
                f"Export de {len(data_df)} lignes non autorisé pour le rôle '{self.user_role}'"
            )
        
        # Sauvegarde des IDs originaux pour l'audit
        original_ids = data_df['id_prospect'].copy() if 'id_prospect' in data_df.columns else None
        
        # Anonymisation des PII
        data_df = self._anonymize_data(data_df)
        
        # Prédictions par batches
        all_predictions = []
        all_audit_records = []
        
        for i in range(0, len(data_df), batch_size):
            batch_df = data_df.iloc[i:i+batch_size].copy()
            
            # Calcul des hashs de features pour l'audit
            feature_hashes = self._compute_feature_hash(batch_df) if self.enable_audit else []
            
            # Prédictions
            if model.model_name == "channel":
                predictions_df = model.predict_channel_timing(batch_df)
                if original_ids is not None:
                    predictions_df['id_prospect'] = original_ids.iloc[i:i+batch_size].values
            else:
                predictions = model.predict(batch_df)
                predictions_df = pd.DataFrame({
                    'id_prospect': original_ids.iloc[i:i+batch_size].values if original_ids is not None else range(i, i+len(batch_df)),
                    'prediction': predictions,
                    'model_name': model.model_name,
                    'model_version': model.version,
                    'prediction_timestamp': datetime.utcnow()
                })
            
            all_predictions.append(predictions_df)
            
            # Audit des prédictions
            if self.enable_audit and self.audit_logger and original_ids is not None:
                audit_records = self.audit_logger.log_prediction_batch(
                    model_name=model.model_name,
                    model_version=model.version,
                    predictions_df=predictions_df,
                    feature_hashes=feature_hashes
                )
                all_audit_records.extend(audit_records)
        
        # Consolidation des résultats
        final_predictions = pd.concat(all_predictions, ignore_index=True)
        
        # Sauvegarde en BigQuery si spécifiée
        if output_table and self.enable_audit:
            self._save_predictions_to_bq(final_predictions, output_table)
            self._save_audit_records_to_bq(all_audit_records)
        
        logger.info(
            "Prédictions sécurisées terminées",
            model_name=model.model_name,
            predictions_count=len(final_predictions),
            audit_records_count=len(all_audit_records)
        )
        
        return final_predictions
    
    def _save_predictions_to_bq(self, predictions_df: pd.DataFrame, table_name: str) -> None:
        """
        Sauvegarde les prédictions en BigQuery.
        
        Args:
            predictions_df: DataFrame des prédictions
            table_name: Nom de la table de destination
        """
        # TODO: Implémenter la sauvegarde BigQuery
        # Nécessite l'utilisation de pandas_gbq ou de l'API BigQuery
        logger.info(
            "Prédictions à sauvegarder en BigQuery",
            table=table_name,
            rows=len(predictions_df)
        )
    
    def _save_audit_records_to_bq(self, audit_records: List[Dict[str, Any]]) -> None:
        """
        Sauvegarde les enregistrements d'audit en BigQuery.
        
        Args:
            audit_records: Liste des enregistrements d'audit
        """
        if not audit_records:
            return
        
        # TODO: Implémenter la sauvegarde des audits en BigQuery
        logger.info(
            "Enregistrements d'audit à sauvegarder",
            records_count=len(audit_records)
        )
    
    def generate_privacy_report(self, model_name: str, date_range: Tuple[str, str]) -> Dict[str, Any]:
        """
        Génère un rapport de confidentialité pour un modèle.
        
        Args:
            model_name: Nom du modèle
            date_range: Tuple (date_début, date_fin) au format YYYY-MM-DD
            
        Returns:
            Rapport de confidentialité
        """
        start_date, end_date = date_range
        
        # Requête des données d'audit
        audit_query = f"""
        SELECT 
            COUNT(*) as total_predictions,
            COUNT(DISTINCT id_prospect_hash) as unique_prospects,
            MIN(prediction_timestamp) as first_prediction,
            MAX(prediction_timestamp) as last_prediction
        FROM `{self.project_id}.serving_layer_ml.audit`
        WHERE model_name = '{model_name}'
          AND DATE(prediction_timestamp) BETWEEN '{start_date}' AND '{end_date}'
        """
        
        try:
            audit_stats = self.bq_client.query_df(audit_query)
            
            if len(audit_stats) > 0:
                stats = audit_stats.iloc[0]
                report = {
                    'model_name': model_name,
                    'period': {'start': start_date, 'end': end_date},
                    'total_predictions': int(stats['total_predictions']),
                    'unique_prospects': int(stats['unique_prospects']),
                    'first_prediction': str(stats['first_prediction']),
                    'last_prediction': str(stats['last_prediction']),
                    'privacy_measures': {
                        'pii_hashed': True,
                        'consent_verified': self.enable_consent_check,
                        'audit_enabled': self.enable_audit,
                        'data_retention_applied': True
                    },
                    'generated_at': datetime.utcnow().isoformat()
                }
            else:
                report = {
                    'model_name': model_name,
                    'period': {'start': start_date, 'end': end_date},
                    'total_predictions': 0,
                    'message': 'Aucune prédiction trouvée pour cette période'
                }
            
            logger.info(
                "Rapport de confidentialité généré",
                model_name=model_name,
                period=f"{start_date} to {end_date}"
            )
            
            return report
            
        except Exception as e:
            logger.error(
                "Erreur lors de la génération du rapport de confidentialité",
                model_name=model_name,
                error=str(e)
            )
            raise
