"""
Modèle d'analyse NLP des transcriptions Modjo avec <PERSON>ce-BERT + UMAP/HDBSCAN.
"""

import re
from typing import Any, Dict, List, Optional, Tuple
import warnings

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
import umap
import hdbscan
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import structlog

from .base import BaseModel, register_model
from ..utils.logging import get_logger

logger = get_logger(__name__)

# Suppression des warnings
warnings.filterwarnings('ignore', category=UserWarning)


@register_model("nlp_signals")
class NlpSignalsModel(BaseModel):
    """
    Modèle d'analyse NLP des transcriptions d'appels Modjo.
    
    Utilise Sentence-BERT pour créer des embeddings sémantiques,
    puis UMAP pour la réduction de dimensionnalité et HDBSCAN
    pour le clustering automatique des thématiques d'appels.
    """
    
    model_name = "nlp_signals"
    version = "1.0.0"
    
    def __init__(self, model_params: Optional[Dict[str, Any]] = None):
        """
        Initialise le modèle NLP.
        
        Args:
            model_params: Paramètres du modèle
        """
        # Paramètres par défaut
        default_params = {
            'sentence_transformer_model': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
            'umap_n_components': 50,
            'umap_n_neighbors': 15,
            'umap_min_dist': 0.1,
            'umap_metric': 'cosine',
            'hdbscan_min_cluster_size': 5,
            'hdbscan_min_samples': 3,
            'hdbscan_metric': 'euclidean',
            'max_sequence_length': 512,
            'min_text_length': 50
        }
        
        if model_params:
            default_params.update(model_params)
        
        super().__init__(default_params)
        
        # Modèles NLP
        self.sentence_transformer = None
        self.umap_model = None
        self.hdbscan_model = None
        self.tfidf_vectorizer = None
        
        # Données d'entraînement
        self.embeddings = None
        self.reduced_embeddings = None
        self.cluster_labels = None
        self.cluster_topics = {}
        self.processed_texts = []
    
    def _create_model(self) -> SentenceTransformer:
        """
        Crée l'instance du modèle Sentence-BERT.
        
        Returns:
            Instance de SentenceTransformer
        """
        model_name = self.model_params['sentence_transformer_model']
        return SentenceTransformer(model_name)
    
    def _preprocess_text(self, text: str) -> str:
        """
        Préprocesse le texte des transcriptions.
        
        Args:
            text: Texte brut de la transcription
            
        Returns:
            Texte nettoyé
        """
        if not isinstance(text, str) or len(text.strip()) == 0:
            return ""
        
        # Nettoyage de base
        text = text.strip()
        
        # Suppression des métadonnées temporelles (ex: [00:12:34])
        text = re.sub(r'\[\d{2}:\d{2}:\d{2}\]', '', text)
        
        # Suppression des marqueurs de speaker (ex: "Speaker 1:", "Client:")
        text = re.sub(r'^(Speaker \d+|Client|Consultant|Agent):\s*', '', text, flags=re.MULTILINE)
        
        # Suppression des répétitions excessives
        text = re.sub(r'(.)\1{3,}', r'\1\1', text)  # Max 2 répétitions
        
        # Suppression des caractères spéciaux excessifs
        text = re.sub(r'[^\w\s\.,!?;:\-\'\"]+', ' ', text)
        
        # Normalisation des espaces
        text = re.sub(r'\s+', ' ', text)
        
        # Suppression des phrases trop courtes (probablement du bruit)
        sentences = text.split('.')
        meaningful_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        text = '. '.join(meaningful_sentences)
        
        return text.strip()
    
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prépare les transcriptions pour l'analyse NLP.
        
        Args:
            df: DataFrame avec les transcriptions Modjo
            
        Returns:
            DataFrame avec les textes préprocessés
        """
        # Vérification de la présence de la colonne content
        if 'content' not in df.columns:
            raise ValueError("Colonne 'content' manquante dans les transcriptions")
        
        # Préprocessing des textes
        processed_texts = []
        valid_indices = []
        
        for idx, row in df.iterrows():
            text = self._preprocess_text(row['content'])
            
            # Filtrage des textes trop courts
            if len(text) >= self.model_params['min_text_length']:
                processed_texts.append(text)
                valid_indices.append(idx)
        
        logger.info(
            "Textes préprocessés",
            total_texts=len(df),
            valid_texts=len(processed_texts),
            avg_length=np.mean([len(t) for t in processed_texts]) if processed_texts else 0
        )
        
        # Création du DataFrame résultant
        result_df = df.loc[valid_indices].copy()
        result_df['processed_content'] = processed_texts
        
        return result_df
    
    def _create_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Crée les embeddings Sentence-BERT.
        
        Args:
            texts: Liste des textes à encoder
            
        Returns:
            Array des embeddings
        """
        logger.info("Création des embeddings Sentence-BERT", text_count=len(texts))
        
        # Troncature des textes si nécessaire
        max_length = self.model_params['max_sequence_length']
        truncated_texts = [
            text[:max_length] if len(text) > max_length else text
            for text in texts
        ]
        
        # Création des embeddings
        embeddings = self.sentence_transformer.encode(
            truncated_texts,
            show_progress_bar=True,
            batch_size=32
        )
        
        logger.info(
            "Embeddings créés",
            embedding_shape=embeddings.shape,
            embedding_dim=embeddings.shape[1]
        )
        
        return embeddings
    
    def _reduce_dimensions(self, embeddings: np.ndarray) -> np.ndarray:
        """
        Réduit la dimensionnalité avec UMAP.
        
        Args:
            embeddings: Embeddings haute dimension
            
        Returns:
            Embeddings réduits
        """
        logger.info("Réduction de dimensionnalité avec UMAP")
        
        self.umap_model = umap.UMAP(
            n_components=self.model_params['umap_n_components'],
            n_neighbors=self.model_params['umap_n_neighbors'],
            min_dist=self.model_params['umap_min_dist'],
            metric=self.model_params['umap_metric'],
            random_state=42
        )
        
        reduced_embeddings = self.umap_model.fit_transform(embeddings)
        
        logger.info(
            "Dimensionnalité réduite",
            original_dim=embeddings.shape[1],
            reduced_dim=reduced_embeddings.shape[1]
        )
        
        return reduced_embeddings
    
    def _cluster_embeddings(self, reduced_embeddings: np.ndarray) -> np.ndarray:
        """
        Effectue le clustering avec HDBSCAN.
        
        Args:
            reduced_embeddings: Embeddings réduits
            
        Returns:
            Labels des clusters
        """
        logger.info("Clustering avec HDBSCAN")
        
        self.hdbscan_model = hdbscan.HDBSCAN(
            min_cluster_size=self.model_params['hdbscan_min_cluster_size'],
            min_samples=self.model_params['hdbscan_min_samples'],
            metric=self.model_params['hdbscan_metric']
        )
        
        cluster_labels = self.hdbscan_model.fit_predict(reduced_embeddings)
        
        # Statistiques de clustering
        n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        n_noise = list(cluster_labels).count(-1)
        
        logger.info(
            "Clustering terminé",
            n_clusters=n_clusters,
            n_noise=n_noise,
            noise_ratio=n_noise / len(cluster_labels)
        )
        
        return cluster_labels
    
    def _extract_cluster_topics(self, texts: List[str], cluster_labels: np.ndarray) -> Dict[int, Dict[str, Any]]:
        """
        Extrait les thématiques de chaque cluster.
        
        Args:
            texts: Textes originaux
            cluster_labels: Labels des clusters
            
        Returns:
            Dictionnaire des thématiques par cluster
        """
        logger.info("Extraction des thématiques par cluster")
        
        # TF-IDF pour l'extraction de mots-clés
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,  # Pas de stop words pour le français
            ngram_range=(1, 2),
            min_df=2
        )
        
        cluster_topics = {}
        unique_clusters = set(cluster_labels)
        
        for cluster_id in unique_clusters:
            if cluster_id == -1:  # Bruit
                continue
            
            # Textes du cluster
            cluster_mask = cluster_labels == cluster_id
            cluster_texts = [texts[i] for i in range(len(texts)) if cluster_mask[i]]
            
            if len(cluster_texts) == 0:
                continue
            
            # Extraction des mots-clés avec TF-IDF
            try:
                tfidf_matrix = self.tfidf_vectorizer.fit_transform(cluster_texts)
                feature_names = self.tfidf_vectorizer.get_feature_names_out()
                
                # Moyennes TF-IDF par terme
                mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
                
                # Top mots-clés
                top_indices = np.argsort(mean_scores)[-10:][::-1]
                top_keywords = [feature_names[i] for i in top_indices]
                top_scores = [mean_scores[i] for i in top_indices]
                
                # Exemple de texte représentatif
                cluster_center_idx = np.argmax(np.sum(tfidf_matrix.toarray(), axis=1))
                representative_text = cluster_texts[cluster_center_idx][:200] + "..."
                
                cluster_topics[cluster_id] = {
                    'size': len(cluster_texts),
                    'keywords': list(zip(top_keywords, top_scores)),
                    'representative_text': representative_text,
                    'avg_length': np.mean([len(text) for text in cluster_texts])
                }
                
            except Exception as e:
                logger.warning(
                    "Erreur lors de l'extraction des mots-clés",
                    cluster_id=cluster_id,
                    error=str(e)
                )
                
                cluster_topics[cluster_id] = {
                    'size': len(cluster_texts),
                    'keywords': [],
                    'representative_text': cluster_texts[0][:200] + "..." if cluster_texts else "",
                    'avg_length': np.mean([len(text) for text in cluster_texts])
                }
        
        logger.info(
            "Thématiques extraites",
            n_clusters_with_topics=len(cluster_topics)
        )
        
        return cluster_topics
    
    def train(
        self,
        train_df: pd.DataFrame,
        val_df: Optional[pd.DataFrame] = None,
        target_column: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Entraîne le modèle NLP sur les transcriptions.
        
        Args:
            train_df: DataFrame avec les transcriptions
            val_df: Non utilisé pour ce modèle non-supervisé
            target_column: Non utilisé pour ce modèle non-supervisé
            
        Returns:
            Métriques d'entraînement
        """
        logger.info(
            "Début de l'entraînement NLP",
            transcription_count=len(train_df)
        )
        
        from datetime import datetime
        start_time = datetime.now()
        
        # Préparation des données
        processed_df = self._prepare_features(train_df)
        self.processed_texts = processed_df['processed_content'].tolist()
        
        if len(self.processed_texts) == 0:
            raise ValueError("Aucun texte valide après préprocessing")
        
        # Initialisation du modèle Sentence-BERT
        self.sentence_transformer = self._create_model()
        
        # Création des embeddings
        self.embeddings = self._create_embeddings(self.processed_texts)
        
        # Réduction de dimensionnalité
        self.reduced_embeddings = self._reduce_dimensions(self.embeddings)
        
        # Clustering
        self.cluster_labels = self._cluster_embeddings(self.reduced_embeddings)
        
        # Extraction des thématiques
        self.cluster_topics = self._extract_cluster_topics(self.processed_texts, self.cluster_labels)
        
        training_time = (datetime.now() - start_time).total_seconds()
        
        # Métriques
        n_clusters = len(set(self.cluster_labels)) - (1 if -1 in self.cluster_labels else 0)
        n_noise = list(self.cluster_labels).count(-1)
        
        self.training_metadata = {
            "training_time_seconds": training_time,
            "transcription_count": len(train_df),
            "valid_texts": len(self.processed_texts),
            "n_clusters": n_clusters,
            "noise_ratio": n_noise / len(self.cluster_labels),
            "embedding_dim": self.embeddings.shape[1],
            "reduced_dim": self.reduced_embeddings.shape[1],
            "model_params": self.model_params,
            "trained_at": datetime.now().isoformat()
        }
        
        self.is_trained = True
        
        logger.info(
            "Entraînement NLP terminé",
            training_time=training_time,
            n_clusters=n_clusters,
            noise_ratio=n_noise / len(self.cluster_labels)
        )
        
        return self.training_metadata
    
    def predict(self, df: pd.DataFrame) -> np.ndarray:
        """
        Prédit les clusters pour de nouvelles transcriptions.
        
        Args:
            df: DataFrame avec les nouvelles transcriptions
            
        Returns:
            Array des labels de clusters prédits
        """
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné avant de faire des prédictions")
        
        # Préparation des nouvelles données
        processed_df = self._prepare_features(df)
        new_texts = processed_df['processed_content'].tolist()
        
        if len(new_texts) == 0:
            return np.array([])
        
        # Création des embeddings pour les nouveaux textes
        new_embeddings = self._create_embeddings(new_texts)
        
        # Réduction de dimensionnalité
        new_reduced = self.umap_model.transform(new_embeddings)
        
        # Prédiction des clusters
        predicted_labels, _ = hdbscan.approximate_predict(self.hdbscan_model, new_reduced)
        
        logger.info(
            "Prédictions NLP effectuées",
            prediction_count=len(predicted_labels)
        )
        
        return predicted_labels
    
    def get_cluster_analysis(self) -> pd.DataFrame:
        """
        Retourne une analyse détaillée des clusters découverts.
        
        Returns:
            DataFrame avec l'analyse des clusters
        """
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné")
        
        analysis_data = []
        
        for cluster_id, topic_info in self.cluster_topics.items():
            keywords_str = ", ".join([kw for kw, _ in topic_info['keywords'][:5]])
            
            analysis_data.append({
                'cluster_id': cluster_id,
                'size': topic_info['size'],
                'top_keywords': keywords_str,
                'representative_text': topic_info['representative_text'],
                'avg_text_length': topic_info['avg_length']
            })
        
        return pd.DataFrame(analysis_data).sort_values('size', ascending=False)
    
    def _fit_model(self, X_train, y_train, X_val=None, y_val=None):
        """Implémentation requise par BaseModel (non utilisée pour NLP)."""
        pass
