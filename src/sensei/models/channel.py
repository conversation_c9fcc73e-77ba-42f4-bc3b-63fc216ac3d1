"""
Modèle de recommandation de canal et timing avec CatBoost multiclasses.
"""

from typing import Any, Dict, List, Optional, Tuple
import warnings

import pandas as pd
import numpy as np
from catboost import CatBoostClassifier
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import optuna
import structlog

from .base import BaseModel, register_model
from ..utils.logging import get_logger

logger = get_logger(__name__)

# Suppression des warnings CatBoost
warnings.filterwarnings('ignore', category=UserWarning, module='catboost')


@register_model("channel")
class ChannelModel(BaseModel):
    """
    Modèle de recommandation de canal et timing optimal avec CatBoost.
    
    Prédit le meilleur canal de contact (email, appel, réunion) et 
    le timing optimal (matin, après-midi, soir) pour maximiser 
    les chances de conversion.
    """
    
    model_name = "channel"
    version = "1.0.0"
    
    # Mapping des canaux et timings
    CHANNELS = {
        'email': 0,
        'appel': 1, 
        'reunion': 2,
        'mixte': 3
    }
    
    TIMINGS = {
        'matin': 0,      # 8h-12h
        'apres_midi': 1, # 12h-17h
        'soir': 2        # 17h-20h
    }
    
    def __init__(self, model_params: Optional[Dict[str, Any]] = None):
        """
        Initialise le modèle de canal/timing.
        
        Args:
            model_params: Paramètres CatBoost personnalisés
        """
        # Paramètres avec forte régularisation anti-overfitting
        default_params = {
            'loss_function': 'MultiClass',
            'eval_metric': 'Accuracy',
            'iterations': 200,  # Réduit de 1000 à 200
            'learning_rate': 0.03,  # Réduit de 0.1 à 0.03
            'depth': 3,  # Réduit de 6 à 3
            'l2_leaf_reg': 10,  # Augmenté de 3 à 10
            'min_data_in_leaf': 20,  # Ajouté pour régularisation
            'random_seed': 42,
            'verbose': False,
            'early_stopping_rounds': 30,  # Réduit de 100 à 30
            'use_best_model': True,
            'bootstrap_type': 'Bernoulli',  # Compatible avec subsample
            'subsample': 0.7,  # Ajouté pour régularisation
            'colsample_bylevel': 0.8  # Ajouté pour régularisation
        }
        
        if model_params:
            default_params.update(model_params)
        
        super().__init__(default_params)
        self.target_encoder = LabelEncoder()
        self.categorical_features: List[str] = []
        self.numerical_features: List[str] = []
        self.channel_timing_combinations: List[str] = []
    
    def _create_model(self) -> CatBoostClassifier:
        """
        Crée l'instance du modèle CatBoost.
        
        Returns:
            Instance de CatBoostClassifier
        """
        return CatBoostClassifier(**self.model_params)
    
    def _create_channel_timing_target(self, df: pd.DataFrame) -> pd.Series:
        """
        Crée la variable target combinée canal+timing basée sur les données réelles.

        Args:
            df: DataFrame avec les données historiques

        Returns:
            Série avec les labels canal+timing
        """
        # Utiliser la colonne canal_timing_optimal si elle existe (données réelles)
        if 'canal_timing_optimal' in df.columns:
            target_labels = df['canal_timing_optimal'].tolist()
            logger.info(
                "Utilisation des targets réels depuis canal_timing_optimal",
                unique_combinations=len(set(target_labels)),
                distribution=pd.Series(target_labels).value_counts().to_dict()
            )
        else:
            # Fallback vers l'ancienne logique si pas de données réelles
            target_labels = []

            for _, row in df.iterrows():
                # Logique de détermination du canal optimal basée sur l'activité
                nb_emails = row.get('nb_emails_30j', 0)
                nb_appels = row.get('nb_appels_30j', 0) + row.get('nb_appels_modjo_30j', 0)
                nb_reunions = row.get('nb_reunions_30j', 0)

                # Détermination du canal principal
                if nb_reunions > 0:
                    canal = 'reunion'
                elif nb_appels > nb_emails:
                    canal = 'appel'
                elif nb_emails > 0:
                    canal = 'email'
                else:
                    canal = 'mixte'  # Pas d'historique clair

                # Détermination du timing optimal basé sur l'heure de soumission
                heure_soumission = row.get('heure_soumission', 14)  # Défaut après-midi

                if 8 <= heure_soumission < 12:
                    timing = 'matin'
                elif 12 <= heure_soumission < 17:
                    timing = 'apres_midi'
                else:
                    timing = 'soir'

                # Combinaison canal+timing
                label = f"{canal}_{timing}"
                target_labels.append(label)

        # Création de la liste des combinaisons possibles
        self.channel_timing_combinations = [
            f"{canal}_{timing}"
            for canal in self.CHANNELS.keys()
            for timing in self.TIMINGS.keys()
        ]

        logger.info(
            "Target canal+timing créé",
            unique_combinations=len(set(target_labels)),
            total_combinations=len(self.channel_timing_combinations),
            distribution=pd.Series(target_labels).value_counts().to_dict()
        )

        return pd.Series(target_labels, index=df.index)
    
    def _identify_feature_types(self, df: pd.DataFrame) -> tuple[list[str], list[str]]:
        """
        Identifie les types de features pour CatBoost.
        
        Args:
            df: DataFrame avec les features
        """
        # Features catégorielles connues
        categorical_patterns = [
            'statut_du_lead', 'relationship_status', 'ip_country',
            'vitesse_reponse', 'budget_declare', 'secteur_activite',
            'taille_entreprise', 'urgence_projet', 'experience_anterieure',
            'jour_semaine_soumission'
        ]
        
        self.categorical_features = []
        self.numerical_features = []
        
        for col in df.columns:
            if any(pattern in col for pattern in categorical_patterns):
                self.categorical_features.append(col)
            elif df[col].dtype in ['object', 'category']:
                self.categorical_features.append(col)
            else:
                self.numerical_features.append(col)
        
        logger.info(
            "Types de features identifiés pour CatBoost",
            categorical_count=len(self.categorical_features),
            numerical_count=len(self.numerical_features)
        )

        return self.categorical_features, self.numerical_features

    def _identify_feature_types_catboost(self, df: pd.DataFrame) -> tuple[list[str], list[str]]:
        """
        Alias pour _identify_feature_types pour compatibilité.

        Args:
            df: DataFrame avec les features

        Returns:
            Tuple (categorical_features, numerical_features)
        """
        return self._identify_feature_types(df)

    def _prepare_features_catboost(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Alias pour _prepare_features pour compatibilité.

        Args:
            df: DataFrame avec les features

        Returns:
            DataFrame avec features préparées
        """
        return self._prepare_features(df)
    
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prépare les features pour CatBoost.
        
        Args:
            df: DataFrame avec les données brutes
            
        Returns:
            DataFrame avec les features préparées
        """
        # Copie pour éviter de modifier l'original
        features_df = df.copy()
        
        # Suppression des colonnes non-features
        non_feature_cols = [
            'id_prospect', 'date_features', 'y_converted_90j',
            'date_premiere_conversion', 'montant_max_conversion',
            'montant_total_conversions', 'dt_creation', 'feature_hash',
            'date_soumission'  # Gardons seulement heure_soumission
        ]
        
        for col in non_feature_cols:
            if col in features_df.columns:
                features_df = features_df.drop(columns=[col])
        
        # Identification des types de features si pas encore fait
        if not self.categorical_features and not self.numerical_features:
            self._identify_feature_types(features_df)
        
        # Features d'interaction spécifiques au canal/timing
        if 'heure_soumission' in features_df.columns:
            # Catégorisation de l'heure
            features_df['periode_soumission'] = features_df['heure_soumission'].apply(
                lambda h: 'matin' if 8 <= h < 12 else ('apres_midi' if 12 <= h < 17 else 'soir')
            )
            if 'periode_soumission' not in self.categorical_features:
                self.categorical_features.append('periode_soumission')
        
        if 'jour_semaine_soumission' in features_df.columns:
            # Catégorisation jour semaine vs weekend
            features_df['type_jour'] = features_df['jour_semaine_soumission'].apply(
                lambda d: 'weekend' if d in [1, 7] else 'semaine'  # 1=Dimanche, 7=Samedi
            )
            if 'type_jour' not in self.categorical_features:
                self.categorical_features.append('type_jour')
        
        # Ratio d'activité par canal
        if all(col in features_df.columns for col in ['nb_emails_30j', 'nb_appels_30j', 'nb_reunions_30j']):
            total_activite = (
                features_df['nb_emails_30j'] + 
                features_df['nb_appels_30j'] + 
                features_df['nb_reunions_30j']
            )
            
            # Éviter la division par zéro
            total_activite = total_activite.replace(0, 1)
            
            features_df['ratio_emails'] = features_df['nb_emails_30j'] / total_activite
            features_df['ratio_appels'] = features_df['nb_appels_30j'] / total_activite
            features_df['ratio_reunions'] = features_df['nb_reunions_30j'] / total_activite
            
            self.numerical_features.extend(['ratio_emails', 'ratio_appels', 'ratio_reunions'])
        
        # Traitement des valeurs manquantes pour les features catégorielles
        for col in self.categorical_features:
            if col in features_df.columns:
                features_df[col] = features_df[col].fillna('unknown').astype(str)
        
        # Traitement des valeurs manquantes pour les features numériques
        for col in self.numerical_features:
            if col in features_df.columns:
                # Gestion des valeurs manquantes avec la médiane
                if features_df[col].isnull().any():
                    median_val = features_df[col].median()
                    features_df[col] = features_df[col].fillna(median_val)
                
                # Gestion des valeurs infinies
                features_df[col] = features_df[col].replace([np.inf, -np.inf], np.nan)
                if features_df[col].isnull().any():
                    features_df[col] = features_df[col].fillna(0)
        
        logger.info(
            "Features préparées pour CatBoost",
            shape=features_df.shape,
            categorical_features=len([c for c in self.categorical_features if c in features_df.columns]),
            numerical_features=len([c for c in self.numerical_features if c in features_df.columns])
        )
        
        return features_df
    
    def train(
        self,
        train_df: pd.DataFrame,
        val_df: Optional[pd.DataFrame] = None,
        target_column: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Entraîne le modèle de canal/timing.
        
        Args:
            train_df: Données d'entraînement
            val_df: Données de validation
            target_column: Ignoré, le target est créé automatiquement
            
        Returns:
            Métriques d'entraînement
        """
        logger.info(
            "Début de l'entraînement du modèle canal/timing",
            train_size=len(train_df),
            val_size=len(val_df) if val_df is not None else 0
        )
        
        # Création du target canal+timing
        y_train = self._create_channel_timing_target(train_df)
        self.target_column = "channel_timing_optimal"
        
        # Encodage du target
        y_train_encoded = self.target_encoder.fit_transform(y_train)
        
        # Validation des données
        self._validate_data(train_df, require_target=False)
        if val_df is not None:
            self._validate_data(val_df, require_target=False)
        
        # Préparation des features
        train_features = self._prepare_features(train_df)
        self.feature_columns = list(train_features.columns)
        
        X_train = train_features
        
        X_val, y_val_encoded = None, None
        if val_df is not None:
            y_val = self._create_channel_timing_target(val_df)
            y_val_encoded = self.target_encoder.transform(y_val)
            val_features = self._prepare_features(val_df)
            X_val = val_features
        
        # Création et entraînement du modèle
        self.model = self._create_model()
        
        from datetime import datetime
        start_time = datetime.now()
        self._fit_model(X_train, y_train_encoded, X_val, y_val_encoded)
        training_time = (datetime.now() - start_time).total_seconds()
        
        # Évaluation
        metrics = self._evaluate_channel_model(X_train, y_train_encoded, X_val, y_val_encoded)
        
        # Métadonnées d'entraînement
        self.training_metadata = {
            "training_time_seconds": training_time,
            "train_size": len(train_df),
            "val_size": len(val_df) if val_df is not None else 0,
            "feature_count": len(self.feature_columns),
            "target_classes": len(self.target_encoder.classes_),
            "target_column": self.target_column,
            "model_params": self.model_params,
            "trained_at": datetime.now().isoformat(),
            **metrics
        }
        
        self.is_trained = True
        
        logger.info(
            "Entraînement canal/timing terminé",
            training_time=training_time,
            target_classes=len(self.target_encoder.classes_),
            metrics=metrics
        )
        
        return self.training_metadata
    
    def _fit_model(
        self,
        X_train: pd.DataFrame,
        y_train: np.ndarray,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[np.ndarray] = None
    ) -> None:
        """
        Entraîne le modèle CatBoost.
        
        Args:
            X_train: Features d'entraînement
            y_train: Target d'entraînement (encodé)
            X_val: Features de validation
            y_val: Target de validation (encodé)
        """
        # Identification des features catégorielles pour CatBoost
        categorical_feature_indices = [
            i for i, col in enumerate(X_train.columns)
            if col in self.categorical_features
        ]
        
        # Paramètres d'entraînement
        fit_params = {
            'cat_features': categorical_feature_indices,
        }
        
        # Ajout de la validation si disponible
        if X_val is not None and y_val is not None:
            fit_params['eval_set'] = (X_val, y_val)
        else:
            # Si pas de validation, désactiver use_best_model
            self.model.set_params(use_best_model=False)

        # Entraînement
        self.model.fit(X_train, y_train, **fit_params)
        
        logger.info(
            "Modèle CatBoost entraîné",
            iterations=self.model.get_param('iterations'),
            categorical_features=len(categorical_feature_indices)
        )
    
    def _evaluate_channel_model(
        self,
        X_train: pd.DataFrame,
        y_train: np.ndarray,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """
        Évalue les performances du modèle canal/timing.
        
        Args:
            X_train: Features d'entraînement
            y_train: Target d'entraînement
            X_val: Features de validation
            y_val: Target de validation
            
        Returns:
            Dictionnaire des métriques
        """
        metrics = {}
        
        # Évaluation sur le train
        train_pred = self.model.predict(X_train)
        metrics["train_accuracy"] = accuracy_score(y_train, train_pred)
        
        # Évaluation sur la validation si disponible
        if X_val is not None and y_val is not None:
            val_pred = self.model.predict(X_val)
            metrics["val_accuracy"] = accuracy_score(y_val, val_pred)
            
            # Rapport de classification détaillé
            class_names = self.target_encoder.classes_
            report = classification_report(
                y_val, val_pred, 
                target_names=class_names,
                output_dict=True,
                zero_division=0
            )
            
            # Métriques moyennes
            metrics["val_precision_macro"] = report['macro avg']['precision']
            metrics["val_recall_macro"] = report['macro avg']['recall']
            metrics["val_f1_macro"] = report['macro avg']['f1-score']
        
        return metrics
    
    def predict(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prédit le canal et timing optimaux.
        
        Args:
            df: DataFrame avec les données à prédire
            
        Returns:
            Tuple (prédictions_encodées, probabilités)
        """
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné avant de faire des prédictions")
        
        self._validate_data(df, require_target=False)
        
        # Préparation des features
        features = self._prepare_features(df)
        
        # Prédictions
        predictions_encoded = self.model.predict(features)
        probabilities = self.model.predict_proba(features)
        
        logger.info(
            "Prédictions canal/timing effectuées",
            prediction_count=len(predictions_encoded)
        )
        
        return predictions_encoded, probabilities
    
    def predict_channel_timing(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prédit le canal et timing avec labels décodés.
        
        Args:
            df: DataFrame avec les données à prédire
            
        Returns:
            DataFrame avec les prédictions décodées
        """
        predictions_encoded, probabilities = self.predict(df)
        
        # Décodage des prédictions
        predictions_decoded = self.target_encoder.inverse_transform(predictions_encoded)
        
        # Extraction du canal et timing
        results = []
        for i, pred in enumerate(predictions_decoded):
            canal, timing = pred.split('_', 1)
            max_prob = probabilities[i].max()
            
            results.append({
                'canal_optimal': canal,
                'timing_optimal': timing,
                'combinaison': pred,
                'confiance': max_prob
            })
        
        return pd.DataFrame(results)
