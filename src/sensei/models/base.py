"""
Classe de base abstraite pour tous les modèles ML de Sensei AI.
"""

import abc
import json
import pickle
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import hashlib

import pandas as pd
import numpy as np
from sklearn.metrics import classification_report, roc_auc_score
import structlog

from ..utils.logging import get_logger

logger = get_logger(__name__)


# Registre global des modèles
MODEL_REGISTRY: Dict[str, type] = {}


def register_model(name: str):
    """
    Décorateur pour enregistrer un modèle dans le registre global.
    
    Args:
        name: Nom unique du modèle
    """
    def decorator(cls):
        MODEL_REGISTRY[name] = cls
        cls.model_name = name
        return cls
    return decorator


def get_model_class(name: str) -> type:
    """
    Récupère une classe de modèle depuis le registre.
    
    Args:
        name: Nom du modèle
        
    Returns:
        Classe du modèle
        
    Raises:
        KeyError: Si le modèle n'existe pas
    """
    if name not in MODEL_REGISTRY:
        raise KeyError(f"Modèle '{name}' non trouvé. Modèles disponibles: {list(MODEL_REGISTRY.keys())}")
    return MODEL_REGISTRY[name]


class BaseModel(abc.ABC):
    """
    Classe de base abstraite pour tous les modèles ML.
    
    Définit l'interface commune pour l'entraînement, la prédiction,
    la sauvegarde et l'évaluation des modèles.
    """
    
    model_name: str = "base"
    version: str = "1.0.0"
    
    def __init__(self, model_params: Optional[Dict[str, Any]] = None):
        """
        Initialise le modèle de base.
        
        Args:
            model_params: Paramètres spécifiques au modèle
        """
        self.model_params = model_params or {}
        self.model = None
        self.feature_columns: Optional[List[str]] = None
        self.target_column: Optional[str] = None
        self.is_trained = False
        self.training_metadata: Dict[str, Any] = {}
        
        logger.info(
            "Modèle initialisé",
            model_name=self.model_name,
            version=self.version,
            params=self.model_params
        )
    
    @abc.abstractmethod
    def _create_model(self) -> Any:
        """
        Crée l'instance du modèle ML spécifique.
        
        Returns:
            Instance du modèle (ex: LGBMClassifier, CatBoostClassifier)
        """
        pass
    
    @abc.abstractmethod
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prépare les features pour l'entraînement/prédiction.
        
        Args:
            df: DataFrame avec les données brutes
            
        Returns:
            DataFrame avec les features préparées
        """
        pass
    
    def _validate_data(self, df: pd.DataFrame, require_target: bool = True) -> None:
        """
        Valide les données d'entrée.
        
        Args:
            df: DataFrame à valider
            require_target: Si True, vérifie la présence de la colonne target
            
        Raises:
            ValueError: Si les données ne sont pas valides
        """
        if df.empty:
            raise ValueError("DataFrame vide")
        
        if require_target and self.target_column and self.target_column not in df.columns:
            raise ValueError(f"Colonne target '{self.target_column}' manquante")
        
        if self.feature_columns:
            missing_features = set(self.feature_columns) - set(df.columns)
            if missing_features:
                raise ValueError(f"Features manquantes: {missing_features}")
    
    def train(
        self,
        train_df: pd.DataFrame,
        val_df: Optional[pd.DataFrame] = None,
        target_column: str = "y_converted_90j"
    ) -> Dict[str, Any]:
        """
        Entraîne le modèle.
        
        Args:
            train_df: Données d'entraînement
            val_df: Données de validation (optionnel)
            target_column: Nom de la colonne target
            
        Returns:
            Métriques d'entraînement
        """
        logger.info(
            "Début de l'entraînement",
            model_name=self.model_name,
            train_size=len(train_df),
            val_size=len(val_df) if val_df is not None else 0
        )
        
        self.target_column = target_column
        
        # Validation des données
        self._validate_data(train_df, require_target=True)
        if val_df is not None:
            self._validate_data(val_df, require_target=True)
        
        # Préparation des features
        train_features = self._prepare_features(train_df)
        self.feature_columns = list(train_features.columns)
        
        # Extraction des features et target
        X_train = train_features
        y_train = train_df[target_column]
        
        X_val, y_val = None, None
        if val_df is not None:
            val_features = self._prepare_features(val_df)
            X_val = val_features
            y_val = val_df[target_column]
        
        # Création et entraînement du modèle
        self.model = self._create_model()
        
        start_time = datetime.now()
        self._fit_model(X_train, y_train, X_val, y_val)
        training_time = (datetime.now() - start_time).total_seconds()
        
        # Évaluation
        metrics = self._evaluate_model(X_train, y_train, X_val, y_val)
        
        # Métadonnées d'entraînement
        self.training_metadata = {
            "training_time_seconds": training_time,
            "train_size": len(train_df),
            "val_size": len(val_df) if val_df is not None else 0,
            "feature_count": len(self.feature_columns),
            "target_column": target_column,
            "model_params": self.model_params,
            "trained_at": datetime.now().isoformat(),
            **metrics
        }
        
        self.is_trained = True
        
        logger.info(
            "Entraînement terminé",
            model_name=self.model_name,
            training_time=training_time,
            metrics=metrics
        )
        
        return self.training_metadata
    
    @abc.abstractmethod
    def _fit_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None
    ) -> None:
        """
        Entraîne le modèle spécifique.
        
        Args:
            X_train: Features d'entraînement
            y_train: Target d'entraînement
            X_val: Features de validation
            y_val: Target de validation
        """
        pass
    
    def predict(self, df: pd.DataFrame) -> np.ndarray:
        """
        Effectue des prédictions.
        
        Args:
            df: DataFrame avec les données à prédire
            
        Returns:
            Array des prédictions
            
        Raises:
            ValueError: Si le modèle n'est pas entraîné
        """
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné avant de faire des prédictions")
        
        self._validate_data(df, require_target=False)
        
        # Préparation des features
        features = self._prepare_features(df)
        
        # Prédiction
        predictions = self.model.predict_proba(features)[:, 1]  # Probabilité de la classe positive
        
        logger.info(
            "Prédictions effectuées",
            model_name=self.model_name,
            prediction_count=len(predictions)
        )
        
        return predictions
    
    def _evaluate_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None
    ) -> Dict[str, float]:
        """
        Évalue les performances du modèle.
        
        Args:
            X_train: Features d'entraînement
            y_train: Target d'entraînement
            X_val: Features de validation
            y_val: Target de validation
            
        Returns:
            Dictionnaire des métriques
        """
        metrics = {}
        
        # Évaluation sur le train
        train_pred = self.model.predict_proba(X_train)[:, 1]
        metrics["train_auc"] = roc_auc_score(y_train, train_pred)
        
        # Évaluation sur la validation si disponible
        if X_val is not None and y_val is not None:
            val_pred = self.model.predict_proba(X_val)[:, 1]
            metrics["val_auc"] = roc_auc_score(y_val, val_pred)
            
            # Precision@200 (métrique business importante)
            val_pred_df = pd.DataFrame({
                'prediction': val_pred,
                'actual': y_val
            }).sort_values('prediction', ascending=False)
            
            top_200 = val_pred_df.head(200)
            if len(top_200) > 0:
                metrics["precision_at_200"] = top_200['actual'].mean()
        
        return metrics
    
    def save(self, path: Union[str, Path]) -> None:
        """
        Sauvegarde le modèle.
        
        Args:
            path: Chemin de sauvegarde
        """
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné avant d'être sauvegardé")
        
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        
        # Sauvegarde du modèle
        model_path = path / "model.pkl"
        with open(model_path, "wb") as f:
            pickle.dump(self.model, f)
        
        # Sauvegarde des métadonnées
        metadata = {
            "model_name": self.model_name,
            "version": self.version,
            "feature_columns": self.feature_columns,
            "target_column": self.target_column,
            "model_params": self.model_params,
            "training_metadata": self.training_metadata,
            "is_trained": self.is_trained
        }
        
        metadata_path = path / "metadata.json"
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(
            "Modèle sauvegardé",
            model_name=self.model_name,
            path=str(path)
        )
    
    def load(self, path: Union[str, Path]) -> None:
        """
        Charge un modèle sauvegardé.
        
        Args:
            path: Chemin du modèle
        """
        path = Path(path)
        
        # Chargement du modèle
        model_path = path / "model.pkl"
        with open(model_path, "rb") as f:
            self.model = pickle.load(f)
        
        # Chargement des métadonnées
        metadata_path = path / "metadata.json"
        with open(metadata_path, "r") as f:
            metadata = json.load(f)
        
        self.feature_columns = metadata["feature_columns"]
        self.target_column = metadata["target_column"]
        self.model_params = metadata["model_params"]
        self.training_metadata = metadata["training_metadata"]
        self.is_trained = metadata["is_trained"]
        
        logger.info(
            "Modèle chargé",
            model_name=self.model_name,
            path=str(path)
        )
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """
        Retourne l'importance des features si disponible.
        
        Returns:
            DataFrame avec l'importance des features
        """
        if not self.is_trained or not hasattr(self.model, 'feature_importances_'):
            return None
        
        importance_df = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        return importance_df
