"""
Modèle de scoring de conversion avec LightGBM et optimisation bayésienne.
"""

from typing import Any, Dict, List, Optional
import warnings

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder
import optuna
import structlog

from .base import BaseModel, register_model
from ..utils.logging import get_logger

logger = get_logger(__name__)

# Suppression des warnings LightGBM
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')


@register_model("conversion")
class ConversionModel(BaseModel):
    """
    Modèle de prédiction de conversion avec LightGBM.
    
    Prédit la probabilité qu'un prospect se convertisse en client
    dans les 90 jours suivants.
    """
    
    model_name = "conversion"
    version = "1.0.0"
    
    def __init__(self, model_params: Optional[Dict[str, Any]] = None):
        """
        Initialise le modèle de conversion.
        
        Args:
            model_params: Paramètres LightGBM personnalisés
        """
        # Paramètres avec forte régularisation anti-overfitting
        default_params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': 15,  # Réduit de 31 à 15
            'learning_rate': 0.01,  # Réduit de 0.05 à 0.01
            'feature_fraction': 0.7,  # Réduit de 0.9 à 0.7
            'bagging_fraction': 0.7,  # Réduit de 0.8 à 0.7
            'bagging_freq': 5,
            'min_child_samples': 50,  # Ajouté pour régularisation
            'reg_alpha': 0.1,  # L1 regularization
            'reg_lambda': 0.1,  # L2 regularization
            'max_depth': 5,  # Limité à 5
            'verbose': -1,
            'random_state': 42,
            'n_estimators': 100,  # Réduit de 1000 à 100
            'early_stopping_rounds': 20  # Réduit de 100 à 20
        }
        
        if model_params:
            default_params.update(model_params)
        
        super().__init__(default_params)
        self.label_encoders: Dict[str, LabelEncoder] = {}
        self.categorical_features: List[str] = []
        self.numerical_features: List[str] = []
    
    def _create_model(self) -> lgb.LGBMClassifier:
        """
        Crée l'instance du modèle LightGBM.
        
        Returns:
            Instance de LGBMClassifier
        """
        return lgb.LGBMClassifier(**self.model_params)
    
    def _identify_feature_types(self, df: pd.DataFrame) -> tuple[list[str], list[str]]:
        """
        Identifie les types de features (catégorielles vs numériques).

        Args:
            df: DataFrame avec les features

        Returns:
            Tuple (categorical_features, numerical_features)
        """
        # Features catégorielles connues
        categorical_patterns = [
            'statut_du_lead', 'relationship_status', 'ip_country',
            'vitesse_reponse', 'budget_declare', 'secteur_activite',
            'taille_entreprise', 'urgence_projet', 'experience_anterieure'
        ]

        self.categorical_features = []
        self.numerical_features = []

        for col in df.columns:
            if any(pattern in col for pattern in categorical_patterns):
                self.categorical_features.append(col)
            elif df[col].dtype in ['object', 'category']:
                self.categorical_features.append(col)
            else:
                self.numerical_features.append(col)

        logger.info(
            "Types de features identifiés",
            categorical_count=len(self.categorical_features),
            numerical_count=len(self.numerical_features),
            categorical_features=self.categorical_features[:10]  # Premiers 10 pour le log
        )

        return self.categorical_features, self.numerical_features
    
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prépare les features pour LightGBM.
        
        Args:
            df: DataFrame avec les données brutes
            
        Returns:
            DataFrame avec les features préparées
        """
        # Copie pour éviter de modifier l'original
        features_df = df.copy()
        
        # Suppression des colonnes non-features
        non_feature_cols = [
            'id_prospect', 'date_features', 'y_converted_90j',
            'date_premiere_conversion', 'montant_max_conversion',
            'montant_total_conversions', 'dt_creation', 'feature_hash'
        ]
        
        for col in non_feature_cols:
            if col in features_df.columns:
                features_df = features_df.drop(columns=[col])
        
        # Identification des types de features si pas encore fait
        if not self.categorical_features and not self.numerical_features:
            self._identify_feature_types(features_df)
        
        # Traitement des features catégorielles
        for col in self.categorical_features:
            if col in features_df.columns:
                # Gestion des valeurs manquantes
                features_df[col] = features_df[col].fillna('unknown')
                
                # Encodage label pour les nouvelles features
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features_df[col] = self.label_encoders[col].fit_transform(features_df[col].astype(str))
                else:
                    # Gestion des nouvelles catégories non vues à l'entraînement
                    known_classes = set(self.label_encoders[col].classes_)
                    features_df[col] = features_df[col].astype(str)
                    
                    # Remplacement des nouvelles catégories par 'unknown'
                    mask = ~features_df[col].isin(known_classes)
                    if mask.any():
                        features_df.loc[mask, col] = 'unknown'
                        
                        # Ajout de 'unknown' aux classes si nécessaire
                        if 'unknown' not in known_classes:
                            # Réentraînement de l'encodeur avec 'unknown'
                            all_values = list(known_classes) + ['unknown']
                            self.label_encoders[col].fit(all_values)
                    
                    features_df[col] = self.label_encoders[col].transform(features_df[col])
        
        # Traitement des features numériques
        for col in self.numerical_features:
            if col in features_df.columns:
                # Gestion des valeurs manquantes avec la médiane
                if features_df[col].isnull().any():
                    median_val = features_df[col].median()
                    features_df[col] = features_df[col].fillna(median_val)
                
                # Gestion des valeurs infinies
                features_df[col] = features_df[col].replace([np.inf, -np.inf], np.nan)
                if features_df[col].isnull().any():
                    features_df[col] = features_df[col].fillna(0)
        
        # Conversion en types appropriés pour LightGBM
        for col in self.categorical_features:
            if col in features_df.columns:
                features_df[col] = features_df[col].astype('category')
        
        logger.info(
            "Features préparées",
            shape=features_df.shape,
            categorical_features=len([c for c in self.categorical_features if c in features_df.columns]),
            numerical_features=len([c for c in self.numerical_features if c in features_df.columns])
        )
        
        return features_df
    
    def _fit_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None
    ) -> None:
        """
        Entraîne le modèle LightGBM.
        
        Args:
            X_train: Features d'entraînement
            y_train: Target d'entraînement
            X_val: Features de validation
            y_val: Target de validation
        """
        # Configuration des features catégorielles pour LightGBM
        categorical_feature_indices = [
            i for i, col in enumerate(X_train.columns)
            if col in self.categorical_features
        ]
        
        # Paramètres d'entraînement
        fit_params = {
            'categorical_feature': categorical_feature_indices,
        }
        
        # Ajout de la validation si disponible
        if X_val is not None and y_val is not None:
            fit_params['eval_set'] = [(X_val, y_val)]
            fit_params['eval_names'] = ['validation']
            fit_params['callbacks'] = [lgb.early_stopping(self.model_params.get('early_stopping_rounds', 100))]
        
        # Entraînement
        self.model.fit(X_train, y_train, **fit_params)
        
        logger.info(
            "Modèle LightGBM entraîné",
            n_estimators=self.model.n_estimators,
            best_iteration=getattr(self.model, 'best_iteration_', 'N/A'),
            categorical_features=len(categorical_feature_indices)
        )
    
    def optimize_hyperparameters(
        self,
        train_df: pd.DataFrame,
        target_column: str = "y_converted_90j",
        n_trials: int = 100,
        cv_folds: int = 5
    ) -> Dict[str, Any]:
        """
        Optimise les hyperparamètres avec Optuna.
        
        Args:
            train_df: Données d'entraînement
            target_column: Colonne target
            n_trials: Nombre d'essais Optuna
            cv_folds: Nombre de folds pour la validation croisée
            
        Returns:
            Meilleurs paramètres trouvés
        """
        logger.info(
            "Début de l'optimisation des hyperparamètres",
            n_trials=n_trials,
            cv_folds=cv_folds
        )
        
        # Préparation des données
        self.target_column = target_column
        X = self._prepare_features(train_df)
        y = train_df[target_column]
        
        self.feature_columns = list(X.columns)
        
        def objective(trial):
            # Paramètres à optimiser
            params = {
                'objective': 'binary',
                'metric': 'auc',
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'verbose': -1,
                'random_state': 42,
                'n_estimators': 1000,
                'early_stopping_rounds': 100
            }
            
            # Validation croisée
            cv_scores = []
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
            
            for train_idx, val_idx in skf.split(X, y):
                X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
                y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
                
                # Modèle temporaire
                temp_model = lgb.LGBMClassifier(**params)
                
                # Features catégorielles
                categorical_feature_indices = [
                    i for i, col in enumerate(X_train_fold.columns)
                    if col in self.categorical_features
                ]
                
                temp_model.fit(
                    X_train_fold, y_train_fold,
                    eval_set=[(X_val_fold, y_val_fold)],
                    categorical_feature=categorical_feature_indices,
                    callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
                )
                
                # Prédiction et score
                val_pred = temp_model.predict_proba(X_val_fold)[:, 1]
                from sklearn.metrics import roc_auc_score
                score = roc_auc_score(y_val_fold, val_pred)
                cv_scores.append(score)
            
            return np.mean(cv_scores)
        
        # Optimisation
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials)
        
        best_params = study.best_params
        best_score = study.best_value
        
        logger.info(
            "Optimisation terminée",
            best_score=best_score,
            best_params=best_params,
            n_trials=len(study.trials)
        )
        
        # Mise à jour des paramètres du modèle
        self.model_params.update(best_params)
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'n_trials': len(study.trials)
        }
