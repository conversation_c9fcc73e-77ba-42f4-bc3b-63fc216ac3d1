#!/usr/bin/env python3
"""
Simple API for Sensei AI Suite validation.

Minimal FastAPI server for testing basic functionality.
"""

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# Simple logging
def log_info(message: str, **kwargs):
    """Simple logging function."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
    print(f"[{timestamp}] INFO: {message} {extra}")


# Pydantic models for API
class ConversionRequest(BaseModel):
    """Request model for conversion prediction."""
    prospect_id: str
    nb_interactions: int = 0
    response_speed: str = "medium"
    declared_budget: str = "medium"
    activity_sector: str = "other"


class ChannelRequest(BaseModel):
    """Request model for channel optimization."""
    prospect_id: str
    nb_appels_historique: int = 0
    has_phone: bool = True
    has_email: bool = True
    response_rate: float = 0.5


class PredictionResponse(BaseModel):
    """Response model for predictions."""
    prediction: float
    confidence: float
    model_version: str
    processing_time_ms: int
    timestamp: str


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str
    timestamp: str
    version: str
    models_loaded: int
    uptime_seconds: float


# Initialize FastAPI app
app = FastAPI(
    title="Sensei AI Suite - Validation API",
    description="Simple API for validation and testing",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Global state
app_start_time = time.time()
models_status = {
    "conversion": {"loaded": False, "version": "validation"},
    "channel": {"loaded": False, "version": "validation"}
}


@app.on_event("startup")
async def startup_event():
    """Initialize API on startup."""
    log_info("Starting Sensei AI Suite Validation API")
    
    # Simulate model loading
    models_status["conversion"]["loaded"] = True
    models_status["channel"]["loaded"] = True
    
    log_info("API startup complete", models_loaded=len(models_status))


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint."""
    return {
        "message": "Sensei AI Suite - Validation API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    uptime = time.time() - app_start_time
    models_loaded = sum(1 for model in models_status.values() if model["loaded"])
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        models_loaded=models_loaded,
        uptime_seconds=uptime
    )


@app.get("/models/status")
async def models_status_endpoint():
    """Get status of all models."""
    return {
        "models": models_status,
        "total_models": len(models_status),
        "loaded_models": sum(1 for model in models_status.values() if model["loaded"]),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/predict/conversion", response_model=PredictionResponse)
async def predict_conversion(request: ConversionRequest):
    """Predict conversion probability."""
    start_time = time.time()
    
    # Check if model is loaded
    if not models_status["conversion"]["loaded"]:
        raise HTTPException(status_code=503, detail="Conversion model not loaded")
    
    log_info("Conversion prediction request", prospect_id=request.prospect_id)
    
    # Simple prediction logic (for validation)
    # Base probability around 0.1 (10% conversion rate)
    base_prob = 0.1
    
    # Adjust based on features
    prob_adjustment = 0.0
    
    if request.nb_interactions > 5:
        prob_adjustment += 0.05
    if request.response_speed == "fast":
        prob_adjustment += 0.03
    if request.declared_budget == "large":
        prob_adjustment += 0.04
    if request.activity_sector == "tech":
        prob_adjustment += 0.02
    
    # Final probability (clamped between 0 and 1)
    prediction = max(0.0, min(1.0, base_prob + prob_adjustment))
    
    # Confidence based on number of interactions
    confidence = min(0.9, 0.5 + (request.nb_interactions * 0.05))
    
    processing_time = int((time.time() - start_time) * 1000)
    
    return PredictionResponse(
        prediction=prediction,
        confidence=confidence,
        model_version=models_status["conversion"]["version"],
        processing_time_ms=processing_time,
        timestamp=datetime.now().isoformat()
    )


@app.post("/predict/channel", response_model=Dict[str, Any])
async def predict_channel(request: ChannelRequest):
    """Predict optimal channel and timing."""
    start_time = time.time()
    
    # Check if model is loaded
    if not models_status["channel"]["loaded"]:
        raise HTTPException(status_code=503, detail="Channel model not loaded")
    
    log_info("Channel prediction request", prospect_id=request.prospect_id)
    
    # Simple channel prediction logic
    channels = ["email_morning", "email_afternoon", "call_morning", "call_afternoon", "call_evening"]
    
    # Logic based on features
    if not request.has_phone:
        # Email only
        if request.response_rate > 0.7:
            predicted_channel = "email_morning"
        else:
            predicted_channel = "email_afternoon"
    elif not request.has_email:
        # Call only
        if request.nb_appels_historique > 10:
            predicted_channel = "call_afternoon"
        else:
            predicted_channel = "call_morning"
    else:
        # Both available - choose based on response rate
        if request.response_rate > 0.6:
            predicted_channel = "call_morning"
        elif request.response_rate > 0.3:
            predicted_channel = "email_morning"
        else:
            predicted_channel = "email_afternoon"
    
    # Confidence based on historical data
    confidence = min(0.9, 0.4 + (request.nb_appels_historique * 0.02))
    
    processing_time = int((time.time() - start_time) * 1000)
    
    return {
        "prediction": predicted_channel,
        "confidence": confidence,
        "probabilities": {channel: 0.2 for channel in channels},  # Simplified
        "model_version": models_status["channel"]["version"],
        "processing_time_ms": processing_time,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/predict/nlp")
async def predict_nlp():
    """NLP signals endpoint (placeholder)."""
    return {
        "message": "NLP signals endpoint - not implemented in validation API",
        "status": "placeholder",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/metrics")
async def get_metrics():
    """Get API metrics."""
    uptime = time.time() - app_start_time
    
    return {
        "uptime_seconds": uptime,
        "models_loaded": sum(1 for model in models_status.values() if model["loaded"]),
        "total_models": len(models_status),
        "api_version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


def main():
    """Run the API server."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Sensei AI Validation API")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    
    args = parser.parse_args()
    
    log_info("Starting Sensei AI Validation API", 
             host=args.host, port=args.port, workers=args.workers)
    
    uvicorn.run(
        "api_simple:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers if not args.reload else 1,
        log_level="info"
    )


if __name__ == "__main__":
    main()
