# 🚀 Guide de Déploiement Production - Sensei AI Suite v1.0

**Version** : 1.0.0  
**Date** : 22 juillet 2025  
**Statut** : PRODUCTION READY  

---

## 📋 **Prérequis**

### **🔧 Environnement Technique**
- **Python** : 3.11+
- **Docker** : 20.10+
- **Google Cloud SDK** : Latest
- **Accès BigQuery** : Service account avec permissions read-only
- **Ressources** : 2 CPU, 4GB RAM minimum

### **☁️ Infrastructure GCP**
- **Projet GCP** : `datalake-sensei`
- **BigQuery** : Accès aux tables `serving_layer.*`
- **Cloud Run** : Pour déploiement API
- **Container Registry** : Pour images Docker
- **Cloud Logging** : Pour monitoring

---

## 🏗️ **Architecture de Déploiement**

```mermaid
graph TB
    subgraph "Production Environment"
        LB[Load Balancer<br/>Cloud Run]
        API1[API Instance 1<br/>Container]
        API2[API Instance 2<br/>Container]
        API3[API Instance N<br/>Auto-scaling]
        
        LB --> API1
        LB --> API2
        LB --> API3
    end
    
    subgraph "Data Sources"
        BQ[BigQuery<br/>datalake-sensei]
        MODELS[Cloud Storage<br/>Model Registry]
    end
    
    subgraph "Monitoring"
        LOGS[Cloud Logging]
        METRICS[Cloud Monitoring]
        ALERTS[Alerting]
    end
    
    API1 --> BQ
    API2 --> BQ
    API3 --> BQ
    API1 --> MODELS
    API2 --> MODELS
    API3 --> MODELS
    
    API1 --> LOGS
    API2 --> LOGS
    API3 --> LOGS
    LOGS --> METRICS
    METRICS --> ALERTS
```

---

## 📦 **Étape 1 : Préparation du Code**

### **🔍 Vérification Pré-déploiement**
```bash
# 1. Cloner le repository
git clone https://github.com/your-org/sensei-ai-suite.git
cd sensei-ai-suite

# 2. Vérifier la structure
ls -la
# Doit contenir : src/, models/, config/, docs/, scripts/

# 3. Vérifier les modèles entraînés
ls -la models/
# Doit contenir : conversion/, channel/, nlp_signals/, registry.json

# 4. Vérifier la configuration
cat config/settings.py
# Vérifier ENVIRONMENT = "production"
```

### **🧪 Tests Pré-déploiement**
```bash
# 1. Tests unitaires
python -m pytest tests/unit/ -v

# 2. Tests d'intégration
python -m pytest tests/integration/ -v

# 3. Test de l'API
python scripts/testing/test_system.py

# 4. Validation des modèles
python scripts/validation/validate_models.py
```

---

## 🐳 **Étape 2 : Containerisation**

### **📦 Build de l'Image Docker**
```bash
# 1. Build de l'image de production
docker build -f Dockerfile -t sensei-ai:latest .

# 2. Test local du container
docker run -p 8000:8000 \
  -e ENVIRONMENT=production \
  -e GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json \
  -v $(pwd)/credentials:/app/credentials:ro \
  sensei-ai:latest

# 3. Vérification health check
curl http://localhost:8000/health
# Réponse attendue : {"status": "healthy", "models_loaded": 4}
```

### **🏷️ Tag et Push vers Registry**
```bash
# 1. Tag pour GCP Container Registry
docker tag sensei-ai:latest gcr.io/datalake-sensei/sensei-ai:v1.0.0
docker tag sensei-ai:latest gcr.io/datalake-sensei/sensei-ai:latest

# 2. Push vers registry
docker push gcr.io/datalake-sensei/sensei-ai:v1.0.0
docker push gcr.io/datalake-sensei/sensei-ai:latest
```

---

## ☁️ **Étape 3 : Déploiement Cloud Run**

### **🚀 Déploiement Initial**
```bash
# 1. Déploiement sur Cloud Run
gcloud run deploy sensei-ai-api \
  --image gcr.io/datalake-sensei/sensei-ai:latest \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated \
  --memory 4Gi \
  --cpu 2 \
  --max-instances 10 \
  --min-instances 1 \
  --port 8000 \
  --timeout 300 \
  --concurrency 100 \
  --set-env-vars ENVIRONMENT=production \
  --service-account <EMAIL>

# 2. Vérification du déploiement
gcloud run services describe sensei-ai-api --region europe-west1
```

### **🔧 Configuration Avancée**
```yaml
# cloud-run-config.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: sensei-ai-api
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        autoscaling.knative.dev/minScale: "1"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/execution-environment: gen2
    spec:
      serviceAccountName: <EMAIL>
      containers:
      - image: gcr.io/datalake-sensei/sensei-ai:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

---

## 🔒 **Étape 4 : Configuration Sécurité**

### **🛡️ Service Account et Permissions**
```bash
# 1. Créer le service account (si pas déjà fait)
gcloud iam service-accounts create sensei-ai-service-account \
  --display-name "Sensei AI Service Account"

# 2. Attribuer les permissions BigQuery
gcloud projects add-iam-policy-binding datalake-sensei \
  --member "serviceAccount:<EMAIL>" \
  --role "roles/bigquery.dataViewer"

gcloud projects add-iam-policy-binding datalake-sensei \
  --member "serviceAccount:<EMAIL>" \
  --role "roles/bigquery.jobUser"

# 3. Permissions Cloud Run
gcloud projects add-iam-policy-binding datalake-sensei \
  --member "serviceAccount:<EMAIL>" \
  --role "roles/run.invoker"
```

### **🔐 Variables d'Environnement Sécurisées**
```bash
# Configuration des secrets
gcloud secrets create sensei-ai-config --data-file=config/production.env

# Attribution des permissions
gcloud secrets add-iam-policy-binding sensei-ai-config \
  --member "serviceAccount:<EMAIL>" \
  --role "roles/secretmanager.secretAccessor"
```

---

## 📊 **Étape 5 : Monitoring et Alertes**

### **📈 Configuration Monitoring**
```bash
# 1. Créer les métriques personnalisées
gcloud logging metrics create sensei_api_errors \
  --description "Erreurs API Sensei" \
  --log-filter 'resource.type="cloud_run_revision" AND severity>=ERROR'

gcloud logging metrics create sensei_api_latency \
  --description "Latence API Sensei" \
  --log-filter 'resource.type="cloud_run_revision" AND jsonPayload.processing_time_ms>0'

# 2. Créer les alertes
gcloud alpha monitoring policies create \
  --policy-from-file monitoring/alert-policies.yaml
```

### **🚨 Configuration Alertes**
```yaml
# monitoring/alert-policies.yaml
displayName: "Sensei AI - High Error Rate"
conditions:
- displayName: "Error rate > 1%"
  conditionThreshold:
    filter: 'resource.type="cloud_run_revision" AND resource.label.service_name="sensei-ai-api"'
    comparison: COMPARISON_GREATER_THAN
    thresholdValue: 0.01
    duration: 300s
notificationChannels:
- projects/datalake-sensei/notificationChannels/[CHANNEL_ID]
```

---

## 🔄 **Étape 6 : Validation Post-déploiement**

### **✅ Tests de Validation**
```bash
# 1. Health check
curl https://sensei-ai-api-[hash]-ew.a.run.app/health

# 2. Test des endpoints
curl -X POST https://sensei-ai-api-[hash]-ew.a.run.app/predict/conversion \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "TEST_001",
    "nb_interactions": 3,
    "vitesse_reponse": "rapide",
    "budget_declare": "moyen",
    "secteur_activite": "tech"
  }'

# 3. Test de charge
ab -n 1000 -c 10 https://sensei-ai-api-[hash]-ew.a.run.app/health

# 4. Vérification des logs
gcloud logging read 'resource.type="cloud_run_revision" AND resource.label.service_name="sensei-ai-api"' \
  --limit 50 --format json
```

### **📊 Métriques de Validation**
- **Latence P95** : < 100ms
- **Taux d'erreur** : < 0.1%
- **Disponibilité** : > 99.9%
- **Temps de démarrage** : < 30s

---

## 🔄 **Processus de Mise à Jour**

### **🚀 Déploiement Blue-Green**
```bash
# 1. Déployer nouvelle version avec tag
gcloud run deploy sensei-ai-api-staging \
  --image gcr.io/datalake-sensei/sensei-ai:v1.1.0 \
  --no-traffic

# 2. Tests sur staging
curl https://sensei-ai-api-staging-[hash]-ew.a.run.app/health

# 3. Basculement progressif du trafic
gcloud run services update-traffic sensei-ai-api \
  --to-revisions sensei-ai-api-staging=10

# 4. Validation et basculement complet
gcloud run services update-traffic sensei-ai-api \
  --to-revisions sensei-ai-api-staging=100
```

### **🔄 Rollback d'Urgence**
```bash
# Rollback immédiat vers version précédente
gcloud run services update-traffic sensei-ai-api \
  --to-revisions [PREVIOUS_REVISION]=100
```

---

## 📋 **Checklist de Déploiement**

### **✅ Pré-déploiement**
- [ ] Code testé et validé
- [ ] Modèles entraînés et validés
- [ ] Configuration production vérifiée
- [ ] Service account configuré
- [ ] Permissions BigQuery validées

### **✅ Déploiement**
- [ ] Image Docker buildée et pushée
- [ ] Service Cloud Run déployé
- [ ] Health checks passent
- [ ] Endpoints API fonctionnels
- [ ] Monitoring configuré

### **✅ Post-déploiement**
- [ ] Tests de charge réussis
- [ ] Métriques dans les seuils
- [ ] Alertes configurées
- [ ] Documentation mise à jour
- [ ] Équipe formée

---

## 🆘 **Troubleshooting**

### **🔧 Problèmes Courants**

#### **Container ne démarre pas**
```bash
# Vérifier les logs
gcloud logging read 'resource.type="cloud_run_revision"' --limit 50

# Tester localement
docker run -it sensei-ai:latest /bin/bash
```

#### **Erreurs BigQuery**
```bash
# Vérifier les permissions
gcloud projects get-iam-policy datalake-sensei

# Tester la connexion
python -c "from sensei.data.bq_client import SecureBigQueryClient; client = SecureBigQueryClient(); print('OK')"
```

#### **Performance dégradée**
```bash
# Augmenter les ressources
gcloud run services update sensei-ai-api \
  --memory 8Gi --cpu 4

# Vérifier les métriques
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com"
```

---

**📝 Note** : Ce guide doit être suivi étape par étape. En cas de problème, consulter la section troubleshooting ou contacter l'équipe DevOps.
