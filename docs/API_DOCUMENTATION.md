# 📚 Documentation API - Sensei AI Suite v1.0

**Version** : 1.0.0  
**Date** : 22 juillet 2025  
**Base URL** : `https://sensei-ai-api-[hash]-ew.a.run.app`  
**Statut** : PRODUCTION READY  

---

## 📋 **Vue d'Ensemble**

L'API Sensei AI fournit des **prédictions de machine learning** pour l'optimisation des ventes B2B. Elle expose 3 modèles principaux via des endpoints REST sécurisés.

### **🎯 Fonctionnalités**
- **Prédiction de conversion** : Probabilité qu'un prospect devienne client
- **Optimisation de canal** : Meilleur canal et timing pour contacter
- **Analyse NLP** : Extraction de signaux depuis transcriptions
- **Monitoring** : Health checks et métriques de performance

### **🔒 Sécurité**
- **Read-only** : Aucune modification de données
- **Rate limiting** : 1000 requêtes/minute par IP
- **Validation** : Schémas Pydantic stricts
- **Audit** : Logs complets de toutes les requêtes

---

## 🌐 **Endpoints Disponibles**

### **📊 Endpoints de Monitoring**

#### **GET /health**
Health check du service et des modèles.

**Réponse :**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-22T19:33:25.298206Z",
  "version": "1.0.0",
  "models_loaded": 4,
  "uptime_seconds": 3600,
  "memory_usage_mb": 1024
}
```

#### **GET /models/status**
Statut détaillé de tous les modèles chargés.

**Réponse :**
```json
{
  "models": {
    "conversion": {
      "status": "loaded",
      "version": "20250722_193325",
      "last_trained": "2025-07-22T19:33:25.298206Z",
      "performance": {
        "train_auc": 0.794,
        "val_auc": 0.520
      },
      "features_count": 23
    },
    "channel": {
      "status": "loaded",
      "version": "20250722_190725",
      "last_trained": "2025-07-22T19:07:25.441354Z",
      "performance": {
        "train_accuracy": 0.993,
        "val_accuracy": 0.990
      },
      "classes": ["appel_matin", "appel_apres_midi", "email_apres_midi", "email_matin", "reunion_matin"]
    },
    "nlp_signals": {
      "status": "loaded",
      "version": "20250722_190917",
      "clusters_detected": 1138,
      "quality_score": 0.98
    }
  },
  "total_models": 4,
  "all_healthy": true
}
```

---

## 🎯 **Endpoints de Prédiction**

### **POST /predict/conversion**
Prédit la probabilité de conversion d'un prospect.

#### **Schéma de Requête**
```json
{
  "id_prospect": "string",           // Identifiant unique du prospect
  "nb_interactions": "integer",      // Nombre d'interactions (0-100)
  "vitesse_reponse": "string",       // "lente" | "moyenne" | "rapide"
  "budget_declare": "string",        // "petit" | "moyen" | "grand"
  "secteur_activite": "string",      // Secteur d'activité
  "hubspot_score": "integer",        // Score HubSpot (0-100, optionnel)
  "nb_jours_actifs": "integer",      // Jours avec activité (optionnel)
  "duree_moyenne_appels": "number"   // Durée moyenne en minutes (optionnel)
}
```

#### **Exemple de Requête**
```bash
curl -X POST "https://api.sensei-ai.com/predict/conversion" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "PROSPECT_12345",
    "nb_interactions": 5,
    "vitesse_reponse": "rapide",
    "budget_declare": "grand",
    "secteur_activite": "tech",
    "hubspot_score": 75,
    "nb_jours_actifs": 8,
    "duree_moyenne_appels": 12.5
  }'
```

#### **Réponse**
```json
{
  "prediction": 0.23,                    // Probabilité de conversion (0-1)
  "confidence": "medium",                // "low" | "medium" | "high"
  "model_version": "20250722_193325",
  "processing_time_ms": 45,
  "features_used": 23,
  "explanation": {
    "top_factors": [
      {
        "feature": "hubspot_score",
        "importance": 0.35,
        "value": 75
      },
      {
        "feature": "nb_interactions",
        "importance": 0.28,
        "value": 5
      },
      {
        "feature": "vitesse_reponse",
        "importance": 0.15,
        "value": "rapide"
      }
    ]
  },
  "recommendation": {
    "action": "prioritize",
    "reason": "Score élevé avec engagement fort",
    "next_steps": ["Planifier démonstration", "Envoyer proposition"]
  }
}
```

---

### **POST /predict/channel**
Optimise le canal et timing de contact pour un prospect.

#### **Schéma de Requête**
```json
{
  "id_prospect": "string",           // Identifiant unique du prospect
  "email": "string",                 // Email du prospect
  "numero_telephone": "string",      // Téléphone (optionnel)
  "nb_appels_historique": "integer", // Nombre d'appels passés
  "nb_appels_matin": "integer",      // Appels le matin (optionnel)
  "nb_appels_apres_midi": "integer", // Appels l'après-midi (optionnel)
  "nb_appels_soir": "integer",       // Appels le soir (optionnel)
  "duree_moyenne_appels": "number"   // Durée moyenne (optionnel)
}
```

#### **Exemple de Requête**
```bash
curl -X POST "https://api.sensei-ai.com/predict/channel" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "PROSPECT_12345",
    "email": "<EMAIL>",
    "numero_telephone": "+33123456789",
    "nb_appels_historique": 3,
    "nb_appels_matin": 1,
    "nb_appels_apres_midi": 2,
    "nb_appels_soir": 0,
    "duree_moyenne_appels": 8.5
  }'
```

#### **Réponse**
```json
{
  "prediction": "appel_apres_midi",      // Canal/timing optimal
  "confidence": 0.78,                    // Confiance de la prédiction
  "model_version": "20250722_190725",
  "processing_time_ms": 32,
  "alternatives": [
    {
      "channel": "email_apres_midi",
      "probability": 0.15,
      "timing": "14h-17h"
    },
    {
      "channel": "appel_matin",
      "probability": 0.07,
      "timing": "9h-12h"
    }
  ],
  "recommendation": {
    "best_time": "14h30-16h30",
    "channel": "Appel téléphonique",
    "reason": "Historique de réponse favorable l'après-midi",
    "backup_option": "Email si pas de réponse après 2 tentatives"
  }
}
```

---

### **POST /predict/nlp**
Analyse une transcription d'appel pour extraire des signaux.

#### **Schéma de Requête**
```json
{
  "callId": "string",        // Identifiant de l'appel
  "speakerId": "integer",    // ID du locuteur (1=commercial, 2=prospect)
  "content": "string",       // Transcription textuelle
  "startDate": "integer"     // Timestamp Unix de l'appel
}
```

#### **Exemple de Requête**
```bash
curl -X POST "https://api.sensei-ai.com/predict/nlp" \
  -H "Content-Type: application/json" \
  -d '{
    "callId": "CALL_789",
    "speakerId": 1,
    "content": "Bonjour, je vous appelle suite à votre demande d informations sur nos solutions CRM. Nous avons des offres très intéressantes pour les entreprises tech comme la vôtre. Pouvez-vous me parler de vos besoins actuels en matière de gestion client ?",
    "startDate": 1642680000
  }'
```

#### **Réponse**
```json
{
  "cluster_id": 42,                      // ID du cluster détecté
  "cluster_label": "introduction_crm",   // Label du pattern
  "confidence": 0.89,                    // Confiance de classification
  "model_version": "20250722_190917",
  "processing_time_ms": 67,
  "signals": {
    "intent": "discovery",
    "sentiment": "positive",
    "topics": ["CRM", "besoins", "entreprise tech"],
    "keywords": ["solutions", "offres", "gestion client"],
    "urgency": "medium",
    "buying_signals": ["demande d'informations", "besoins actuels"]
  },
  "similar_calls": [
    {
      "callId": "CALL_456",
      "similarity": 0.92,
      "outcome": "qualified"
    }
  ],
  "recommendation": {
    "next_action": "Approfondir les besoins spécifiques",
    "follow_up": "Envoyer documentation technique",
    "priority": "high"
  }
}
```

---

## 🔧 **Gestion des Erreurs**

### **📊 Codes de Statut**
- **200** : Succès
- **400** : Erreur de validation des données
- **422** : Erreur de schéma Pydantic
- **429** : Rate limit dépassé
- **500** : Erreur interne du serveur
- **503** : Service temporairement indisponible

### **🚨 Format des Erreurs**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Le champ 'nb_interactions' doit être entre 0 et 100",
    "details": {
      "field": "nb_interactions",
      "value": 150,
      "constraint": "max_value=100"
    },
    "timestamp": "2025-07-22T19:33:25.298206Z",
    "request_id": "req_abc123"
  }
}
```

---

## 📊 **Limites et Quotas**

### **⚡ Rate Limiting**
- **Limite** : 1000 requêtes/minute par IP
- **Burst** : 100 requêtes/seconde max
- **Headers** : `X-RateLimit-Remaining`, `X-RateLimit-Reset`

### **📏 Limites de Taille**
- **Payload max** : 1MB par requête
- **Timeout** : 30 secondes par requête
- **Transcription max** : 10,000 caractères

---

## 🔍 **Monitoring et Debugging**

### **📊 Headers de Réponse**
```
X-Request-ID: req_abc123           # ID unique de la requête
X-Processing-Time: 45ms            # Temps de traitement
X-Model-Version: 20250722_193325   # Version du modèle utilisé
X-Cache-Status: miss               # Statut du cache
```

### **📈 Métriques Disponibles**
- **Latence** : P50, P95, P99 par endpoint
- **Throughput** : Requêtes/seconde
- **Erreurs** : Taux d'erreur par type
- **Modèles** : Performance et drift

---

## 🧪 **Environnements**

### **🔧 Développement**
- **URL** : `http://localhost:8000`
- **Auth** : Aucune
- **Rate limit** : Désactivé
- **Logs** : DEBUG level

### **🚀 Production**
- **URL** : `https://sensei-ai-api-[hash]-ew.a.run.app`
- **Auth** : Service account
- **Rate limit** : Activé
- **Logs** : INFO level

---

## 📚 **Ressources Supplémentaires**

### **🔗 Liens Utiles**
- **Swagger UI** : `/docs` (interface interactive)
- **ReDoc** : `/redoc` (documentation alternative)
- **OpenAPI Schema** : `/openapi.json`
- **Health Check** : `/health`

### **📞 Support**
- **Email** : <EMAIL>
- **Documentation** : https://docs.sensei-ai.com
- **Status Page** : https://status.sensei-ai.com

---

**📝 Note** : Cette API est en lecture seule et ne modifie aucune donnée. Toutes les prédictions sont basées sur des modèles entraînés sur des données historiques réelles.
