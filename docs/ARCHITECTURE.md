# 🏗️ Architecture Technique - Sensei AI Suite v1.0

**Version** : 1.0.0  
**Date** : 22 juillet 2025  
**Statut** : PRODUCTION READY  

---

## 📋 **Vue d'Ensemble**

Sensei AI Suite est une **plateforme de machine learning** modulaire et sécurisée pour la prédiction de conversion et l'optimisation des canaux de communication B2B.

### **🎯 Objectifs Architecturaux**
- **Modularité** : Composants indépendants et réutilisables
- **Sécurité** : Accès read-only, chiffrement, audit
- **Scalabilité** : Support de millions de prédictions
- **Maintenabilité** : Code propre, tests, documentation
- **Performance** : Réponse < 100ms, haute disponibilité

---

## 🏛️ **Architecture Globale**

```mermaid
graph TB
    subgraph "Data Layer"
        BQ[BigQuery<br/>datalake-sensei]
        BQ --> VW1[vw_dim_contact]
        BQ --> VW2[vw_fact_modjo_call]
        BQ --> VW3[vw_dim_modjo_transcript]
    end
    
    subgraph "ML Pipeline"
        TR[Training Script<br/>train_models.py]
        VL[Validation<br/>overfitting_detector.py]
        TR --> VL
    end
    
    subgraph "Model Layer"
        M1[Conversion Model<br/>LightGBM]
        M2[Channel Model<br/>CatBoost]
        M3[NLP Signals<br/>BERT + HDBSCAN]
        REG[Model Registry<br/>registry.json]
    end
    
    subgraph "API Layer"
        API[FastAPI Server<br/>main.py]
        AUTH[Authentication<br/>Security]
        PRED[Prediction Endpoints]
        API --> AUTH
        API --> PRED
    end
    
    subgraph "Infrastructure"
        DOCKER[Docker Container]
        GCP[Google Cloud Platform]
        MON[Monitoring & Logs]
    end
    
    BQ --> TR
    TR --> M1
    TR --> M2
    TR --> M3
    M1 --> REG
    M2 --> REG
    M3 --> REG
    REG --> API
    API --> DOCKER
    DOCKER --> GCP
    API --> MON
```

---

## 📊 **Couche de Données (Data Layer)**

### **🗄️ BigQuery - Source de Vérité**
```
datalake-sensei (Projet GCP)
├── serving_layer/
│   ├── vw_dim_contact          # Prospects et clients
│   ├── vw_fact_modjo_call      # Historique d'appels
│   └── vw_dim_modjo_transcript # Transcriptions
└── raw_data/                   # Données brutes (non utilisées)
```

### **🔒 Sécurité des Données**
- **Service Account** : `sensei-ai-service-account.json`
- **Permissions** : Read-only sur serving_layer
- **Chiffrement** : TLS 1.3 pour toutes les connexions
- **Audit** : Logs complets des requêtes

### **📈 Performance**
- **Partitioning** : Tables partitionnées par date
- **Clustering** : Index sur id_contact, callId
- **Caching** : Cache BigQuery activé
- **Limits** : 20GB max par requête

---

## 🤖 **Couche ML (Machine Learning)**

### **🎯 Modèles Disponibles**

#### **1. Conversion Model**
```python
Type: Classification Binaire
Algorithm: LightGBM
Features: 23 variables (10 cat + 13 num)
Target: y_converted_90j (0/1)
Performance: AUC = 0.520 (réaliste)
```

#### **2. Channel Model**
```python
Type: Classification Multiclasse
Algorithm: CatBoost
Features: 20 variables (10 cat + 10 num)
Target: canal_timing_optimal (5 classes)
Performance: Accuracy = 0.75 (réaliste)
```

#### **3. NLP Signals Model**
```python
Type: Clustering Non-supervisé
Algorithm: BERT + UMAP + HDBSCAN
Input: Transcriptions texte
Output: 1,138 clusters de signaux
Performance: 98% textes valides
```

### **🔧 Pipeline d'Entraînement**
```python
# 1. Extraction des données
data = SecureBigQueryClient().query_df(sql)

# 2. Préparation des features
X, y = model.prepare_features(data)

# 3. Split train/test (80/20)
X_train, X_test, y_train, y_test = train_test_split(...)

# 4. Entraînement avec validation
model.train(X_train, y_train, validation_data=(X_test, y_test))

# 5. Détection d'overfitting
overfitting_level = detector.detect_overfitting(metrics)

# 6. Sauvegarde si validé
if overfitting_level != CRITICAL:
    model.save(path)
    registry.update(model_info)
```

### **📊 Validation et Qualité**
- **Cross-validation** : 3-fold stratifiée
- **Split temporel** : 80% train / 20% test
- **Détection overfitting** : Seuils automatiques
- **Métriques** : AUC, Accuracy, Precision, Recall, F1

---

## 🚀 **Couche API (Application Layer)**

### **🌐 FastAPI Server**
```python
# Structure des endpoints
/health                 # Health check
/models/status         # Statut des modèles
/predict/conversion    # Prédiction conversion
/predict/channel       # Optimisation canal
/predict/nlp          # Analyse NLP
/docs                 # Documentation Swagger
```

### **🔐 Sécurité API**
- **Read-only mode** : Aucune modification de données
- **Rate limiting** : 1000 req/min par IP
- **Input validation** : Pydantic schemas
- **Error handling** : Gestion robuste des erreurs
- **Logging** : Audit complet des requêtes

### **📊 Format des Réponses**
```json
{
  "prediction": 0.23,
  "confidence": "medium",
  "model_version": "20250722_193325",
  "processing_time_ms": 45,
  "features_used": 23,
  "explanation": {
    "top_factors": ["hubspot_score", "nb_interactions"]
  }
}
```

---

## 🐳 **Couche Infrastructure**

### **📦 Containerisation**
```dockerfile
# Production Dockerfile
FROM python:3.11-slim
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY src/ /app/src/
COPY models/ /app/models/
COPY config/ /app/config/
WORKDIR /app
EXPOSE 8000
CMD ["uvicorn", "src.sensei.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### **☁️ Déploiement GCP**
```yaml
# Cloud Run Configuration
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: sensei-ai-api
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
    spec:
      containers:
      - image: gcr.io/datalake-sensei/sensei-ai:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
```

---

## 📁 **Structure du Projet**

```
sensei-AISuite-v1.0/
├── src/sensei/                 # Code source principal
│   ├── api/                    # API FastAPI
│   │   ├── main.py            # Point d'entrée API
│   │   ├── endpoints/         # Endpoints par domaine
│   │   └── middleware/        # Middleware sécurité
│   ├── models/                # Modèles ML
│   │   ├── base.py           # Classe de base
│   │   ├── conversion.py     # Modèle conversion
│   │   ├── channel.py        # Modèle canal
│   │   └── nlp_signals.py    # Modèle NLP
│   ├── data/                  # Accès aux données
│   │   └── bq_client.py      # Client BigQuery sécurisé
│   └── utils/                 # Utilitaires
│       ├── logging.py        # Logging structuré
│       └── overfitting_detector.py # Détection overfitting
├── scripts/                   # Scripts d'administration
│   ├── training/             # Entraînement des modèles
│   ├── deployment/           # Scripts de déploiement
│   └── monitoring/           # Scripts de monitoring
├── models/                   # Modèles entraînés
│   ├── conversion/          # Modèles conversion
│   ├── channel/             # Modèles canal
│   ├── nlp_signals/         # Modèles NLP
│   └── registry.json        # Registre des modèles
├── config/                   # Configuration
│   ├── settings.py          # Paramètres globaux
│   └── environments/        # Config par environnement
├── docs/                     # Documentation
├── tests/                    # Tests automatisés
└── deployment/               # Configuration déploiement
```

---

## 🔄 **Flux de Données**

### **📊 Entraînement (Batch)**
```
1. BigQuery → Extraction données (train_models.py)
2. Preprocessing → Features engineering
3. Training → Modèles ML avec validation
4. Validation → Détection overfitting
5. Storage → Sauvegarde modèles + registry
6. Deployment → Rechargement API
```

### **⚡ Prédiction (Real-time)**
```
1. Client → Requête API (/predict/*)
2. Validation → Schéma Pydantic
3. Loading → Modèle depuis registry
4. Preprocessing → Features engineering
5. Prediction → Inférence ML
6. Response → JSON formaté + métadonnées
```

---

## 📈 **Monitoring et Observabilité**

### **📊 Métriques Clés**
- **Performance** : Latence P95 < 100ms
- **Disponibilité** : Uptime > 99.9%
- **Précision** : Drift des prédictions < 5%
- **Volume** : Requêtes/seconde, erreurs/minute

### **🔍 Logging**
```python
# Format de log structuré
{
  "timestamp": "2025-07-22T19:33:25.298206Z",
  "level": "info",
  "service": "sensei-ai",
  "version": "1.0.0",
  "event": "prediction_made",
  "model_name": "conversion",
  "prediction": 0.23,
  "processing_time_ms": 45,
  "user_id": "anonymous"
}
```

### **🚨 Alertes**
- **Erreur rate** > 1% → Alerte immédiate
- **Latence** > 200ms → Investigation
- **Model drift** détecté → Réentraînement
- **Overfitting** détecté → Arrêt automatique

---

## 🔒 **Sécurité et Conformité**

### **🛡️ Mesures de Sécurité**
- **Authentication** : Service account GCP
- **Authorization** : Permissions minimales (read-only)
- **Encryption** : TLS 1.3 en transit, AES-256 au repos
- **Audit** : Logs complets, traçabilité
- **Isolation** : Containers isolés, réseau privé

### **📋 Conformité**
- **RGPD** : Données pseudonymisées, droit à l'oubli
- **SOC 2** : Contrôles de sécurité
- **ISO 27001** : Management sécurité information

---

**📝 Note** : Cette architecture est conçue pour évoluer. Les composants sont modulaires et peuvent être étendus selon les besoins business.
