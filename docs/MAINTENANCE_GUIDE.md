# 🛠️ Guide de Maintenance - Sensei AI Suite v1.0

**Version** : 1.0.0  
**Date** : 22 juillet 2025  
**Statut** : PRODUCTION READY  

---

## 📋 **Vue d'Ensemble**

Ce guide couvre la **maintenance opérationnelle** du système Sensei AI en production, incluant le monitoring, les mises à jour, la résolution d'incidents et les bonnes pratiques.

### **🎯 Objectifs de Maintenance**
- **Disponibilité** : >99.9% uptime
- **Performance** : Latence P95 <100ms
- **Qualité** : Drift des modèles <5%
- **Sécurité** : Zéro incident de sécurité

---

## 📊 **Monitoring et Alertes**

### **🔍 Métriques Clés à Surveiller**

#### **Performance API**
```bash
# Latence par endpoint
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com/request_latencies"

# Taux d'erreur
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com/request_count"

# Utilisation ressources
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com/container/memory/utilizations"
```

#### **Santé des Modèles**
```python
# Script de vérification quotidienne
import requests
import json

def check_model_health():
    response = requests.get("https://sensei-ai-api-[hash]-ew.a.run.app/models/status")
    status = response.json()
    
    for model_name, model_info in status["models"].items():
        if model_info["status"] != "loaded":
            send_alert(f"Modèle {model_name} non chargé")
        
        # Vérifier la fraîcheur des modèles
        last_trained = datetime.fromisoformat(model_info["last_trained"])
        if (datetime.now() - last_trained).days > 30:
            send_alert(f"Modèle {model_name} obsolète (>{30} jours)")

# Exécuter quotidiennement via cron
# 0 8 * * * python /scripts/monitoring/check_model_health.py
```

### **🚨 Alertes Configurées**

#### **Alertes Critiques (Immédiate)**
- **API Down** : Health check échoue >2 minutes
- **Taux d'erreur** : >5% sur 5 minutes
- **Latence élevée** : P95 >500ms sur 10 minutes
- **Mémoire** : >90% utilisation sur 5 minutes

#### **Alertes Warning (1 heure)**
- **Taux d'erreur** : >1% sur 15 minutes
- **Latence** : P95 >200ms sur 15 minutes
- **Modèle obsolète** : >30 jours sans réentraînement
- **Drift détecté** : Performance <seuil sur 24h

### **📈 Dashboard de Monitoring**
```yaml
# monitoring/dashboard.yaml
displayName: "Sensei AI - Production Dashboard"
mosaicLayout:
  tiles:
  - width: 6
    height: 4
    widget:
      title: "API Request Rate"
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: 'resource.type="cloud_run_revision"'
              aggregation:
                alignmentPeriod: 60s
                perSeriesAligner: ALIGN_RATE
  - width: 6
    height: 4
    widget:
      title: "Error Rate"
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: 'resource.type="cloud_run_revision" AND severity>=ERROR'
```

---

## 🔄 **Maintenance Préventive**

### **📅 Tâches Quotidiennes**
```bash
#!/bin/bash
# scripts/maintenance/daily_checks.sh

echo "=== Vérifications quotidiennes Sensei AI ==="

# 1. Health check
curl -f https://sensei-ai-api-[hash]-ew.a.run.app/health || echo "❌ API DOWN"

# 2. Vérification des modèles
python scripts/monitoring/check_model_health.py

# 3. Analyse des logs d'erreur
gcloud logging read 'resource.type="cloud_run_revision" AND severity>=ERROR' \
  --freshness=1d --limit=10

# 4. Vérification de l'utilisation BigQuery
bq show --format=prettyjson datalake-sensei | jq '.statistics.query'

# 5. Backup des métriques
python scripts/monitoring/backup_metrics.py

echo "=== Vérifications terminées ==="
```

### **📅 Tâches Hebdomadaires**
```bash
#!/bin/bash
# scripts/maintenance/weekly_checks.sh

echo "=== Maintenance hebdomadaire Sensei AI ==="

# 1. Analyse de performance
python scripts/monitoring/performance_analysis.py --period=7d

# 2. Vérification de la dérive des modèles
python scripts/monitoring/model_drift_detection.py

# 3. Nettoyage des logs anciens
gcloud logging sinks delete old-logs-sink --quiet

# 4. Mise à jour des dépendances de sécurité
pip-audit --desc --format=json

# 5. Test de charge
python scripts/testing/load_test.py --duration=300

echo "=== Maintenance hebdomadaire terminée ==="
```

### **📅 Tâches Mensuelles**
```bash
#!/bin/bash
# scripts/maintenance/monthly_checks.sh

echo "=== Maintenance mensuelle Sensei AI ==="

# 1. Réentraînement des modèles
python scripts/training/train_models.py --capacity medium --models conversion channel

# 2. Audit de sécurité
python scripts/security/security_audit.py

# 3. Optimisation des coûts BigQuery
python scripts/monitoring/cost_optimization.py

# 4. Backup complet
gsutil -m cp -r models/ gs://sensei-ai-backups/$(date +%Y%m%d)/

# 5. Rapport mensuel
python scripts/reporting/monthly_report.py

echo "=== Maintenance mensuelle terminée ==="
```

---

## 🚨 **Gestion des Incidents**

### **📋 Procédure d'Incident**

#### **1. Détection et Triage**
```bash
# Vérification rapide du statut
curl https://sensei-ai-api-[hash]-ew.a.run.app/health

# Analyse des logs récents
gcloud logging read 'resource.type="cloud_run_revision" AND severity>=ERROR' \
  --freshness=1h --limit=50

# Vérification des métriques
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com"
```

#### **2. Classification des Incidents**
- **P0 (Critique)** : API complètement down
- **P1 (Majeur)** : Taux d'erreur >10% ou latence >1s
- **P2 (Mineur)** : Dégradation performance <50%
- **P3 (Info)** : Alertes monitoring non critiques

#### **3. Actions de Résolution**

**P0 - API Down**
```bash
# 1. Vérifier le statut Cloud Run
gcloud run services describe sensei-ai-api --region europe-west1

# 2. Redémarrer le service si nécessaire
gcloud run services update sensei-ai-api --region europe-west1

# 3. Rollback si problème de déploiement
gcloud run services update-traffic sensei-ai-api \
  --to-revisions [PREVIOUS_REVISION]=100

# 4. Escalader si problème persiste
```

**P1 - Performance Dégradée**
```bash
# 1. Augmenter les ressources temporairement
gcloud run services update sensei-ai-api \
  --memory 8Gi --cpu 4 --max-instances 20

# 2. Analyser les requêtes lentes
gcloud logging read 'jsonPayload.processing_time_ms>1000' --freshness=1h

# 3. Vérifier BigQuery
bq show --format=prettyjson datalake-sensei
```

### **📞 Escalade et Communication**
```yaml
# Contacts d'escalade
P0_Contacts:
  - DevOps Lead: +33123456789
  - CTO: +33123456790
  - On-call Engineer: +33123456791

P1_Contacts:
  - Team Lead: +33123456792
  - Product Manager: +33123456793

Communication_Channels:
  - Slack: #sensei-ai-alerts
  - Email: <EMAIL>
  - Status Page: https://status.sensei-ai.com
```

---

## 🔄 **Mises à Jour et Déploiements**

### **🚀 Processus de Mise à Jour**

#### **1. Mise à Jour Mineure (Patch)**
```bash
# 1. Tests en local
python -m pytest tests/ -v

# 2. Build et push
docker build -t gcr.io/datalake-sensei/sensei-ai:v1.0.1 .
docker push gcr.io/datalake-sensei/sensei-ai:v1.0.1

# 3. Déploiement avec traffic splitting
gcloud run deploy sensei-ai-api \
  --image gcr.io/datalake-sensei/sensei-ai:v1.0.1 \
  --no-traffic

gcloud run services update-traffic sensei-ai-api \
  --to-revisions sensei-ai-api-v1-0-1=10

# 4. Validation et basculement complet
gcloud run services update-traffic sensei-ai-api \
  --to-revisions sensei-ai-api-v1-0-1=100
```

#### **2. Mise à Jour Majeure (Feature)**
```bash
# 1. Déploiement en staging
gcloud run deploy sensei-ai-api-staging \
  --image gcr.io/datalake-sensei/sensei-ai:v2.0.0

# 2. Tests complets
python scripts/testing/integration_tests.py --env staging

# 3. Blue-Green deployment
# Voir DEPLOYMENT_GUIDE.md pour détails
```

### **📊 Réentraînement des Modèles**

#### **Déclencheurs de Réentraînement**
- **Programmé** : Mensuel automatique
- **Drift détecté** : Performance <seuil
- **Nouvelles données** : Volume significatif
- **Feedback business** : Changement métier

#### **Processus de Réentraînement**
```bash
# 1. Validation des données
python scripts/validation/validate_data.py

# 2. Entraînement avec validation
python scripts/training/train_models.py --capacity medium --validate

# 3. Tests A/B
python scripts/testing/ab_test_models.py --duration 24h

# 4. Déploiement si validé
python scripts/deployment/deploy_models.py --models conversion,channel
```

---

## 📊 **Optimisation des Performances**

### **🔧 Optimisations BigQuery**
```sql
-- 1. Optimisation des requêtes
-- Utiliser le partitioning par date
SELECT * FROM `datalake-sensei.serving_layer.vw_dim_contact`
WHERE DATE(created_at) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)

-- 2. Mise en cache des résultats fréquents
-- Activer le cache BigQuery pour les requêtes répétitives

-- 3. Optimisation des coûts
-- Surveiller les bytes_billed et optimiser les requêtes
```

### **⚡ Optimisations API**
```python
# 1. Cache des prédictions
from functools import lru_cache

@lru_cache(maxsize=1000)
def predict_conversion(features_hash):
    # Cache des prédictions identiques
    pass

# 2. Batch processing
def predict_batch(requests):
    # Traitement par batch pour réduire la latence
    pass

# 3. Async processing
import asyncio

async def async_predict(request):
    # Traitement asynchrone pour améliorer le throughput
    pass
```

---

## 📋 **Checklist de Maintenance**

### **✅ Quotidien**
- [ ] Health check API
- [ ] Vérification logs d'erreur
- [ ] Monitoring métriques clés
- [ ] Backup métriques

### **✅ Hebdomadaire**
- [ ] Analyse performance
- [ ] Détection drift modèles
- [ ] Test de charge
- [ ] Nettoyage logs

### **✅ Mensuel**
- [ ] Réentraînement modèles
- [ ] Audit sécurité
- [ ] Optimisation coûts
- [ ] Rapport mensuel

### **✅ Trimestriel**
- [ ] Review architecture
- [ ] Mise à jour dépendances
- [ ] Disaster recovery test
- [ ] Capacity planning

---

## 📞 **Contacts et Ressources**

### **🆘 Support Technique**
- **Email** : <EMAIL>
- **Slack** : #sensei-ai-support
- **On-call** : +33123456789
- **Documentation** : https://docs.sensei-ai.com

### **📚 Ressources Utiles**
- **Runbooks** : `/docs/runbooks/`
- **Scripts** : `/scripts/monitoring/`
- **Dashboards** : Google Cloud Monitoring
- **Logs** : Google Cloud Logging

---

**📝 Note** : Ce guide doit être mis à jour à chaque évolution du système. Les procédures doivent être testées régulièrement en environnement de staging.
