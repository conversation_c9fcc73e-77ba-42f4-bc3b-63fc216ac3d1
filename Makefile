# Makefile pour Sensei AI Suite

.PHONY: help install install-dev test test-unit test-integration lint format type-check security build run clean docker-build docker-run

# Variables
PYTHON := python3.11
POETRY := poetry
DOCKER_IMAGE := sensei-ai
DOCKER_TAG := latest

# Couleurs pour l'affichage
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

help: ## Affiche cette aide
	@echo "$(BLUE)Sensei AI Suite - Commandes disponibles:$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

install: ## Installe les dépendances de production
	@echo "$(BLUE)Installation des dépendances de production...$(NC)"
	$(POETRY) install --only=main
	@echo "$(GREEN)✅ Installation terminée$(NC)"

install-dev: ## Installe toutes les dépendances (dev inclus)
	@echo "$(BLUE)Installation des dépendances de développement...$(NC)"
	$(POETRY) install
	@echo "$(GREEN)✅ Installation terminée$(NC)"

test: test-unit test-integration ## Lance tous les tests

test-unit: ## Lance les tests unitaires
	@echo "$(BLUE)Lancement des tests unitaires...$(NC)"
	$(POETRY) run pytest tests/unit/ -v --cov=src/sensei --cov-report=term-missing --cov-report=html
	@echo "$(GREEN)✅ Tests unitaires terminés$(NC)"

test-integration: ## Lance les tests d'intégration
	@echo "$(BLUE)Lancement des tests d'intégration...$(NC)"
	$(POETRY) run pytest tests/integration/ -v -m integration
	@echo "$(GREEN)✅ Tests d'intégration terminés$(NC)"

test-coverage: ## Génère le rapport de couverture
	@echo "$(BLUE)Génération du rapport de couverture...$(NC)"
	$(POETRY) run pytest --cov=src/sensei --cov-report=html --cov-report=term
	@echo "$(GREEN)✅ Rapport disponible dans htmlcov/index.html$(NC)"

lint: ## Vérifie le style de code
	@echo "$(BLUE)Vérification du style de code...$(NC)"
	$(POETRY) run black --check src/ tests/
	$(POETRY) run isort --check-only src/ tests/
	@echo "$(GREEN)✅ Style de code vérifié$(NC)"

format: ## Formate le code
	@echo "$(BLUE)Formatage du code...$(NC)"
	$(POETRY) run black src/ tests/
	$(POETRY) run isort src/ tests/
	@echo "$(GREEN)✅ Code formaté$(NC)"

type-check: ## Vérifie les types avec mypy
	@echo "$(BLUE)Vérification des types...$(NC)"
	$(POETRY) run mypy src/
	@echo "$(GREEN)✅ Types vérifiés$(NC)"

security: ## Vérifie la sécurité du code
	@echo "$(BLUE)Vérification de sécurité...$(NC)"
	$(POETRY) run safety check
	$(POETRY) run bandit -r src/ -f json -o bandit-report.json || true
	@echo "$(GREEN)✅ Vérification de sécurité terminée$(NC)"

quality: lint type-check security ## Lance toutes les vérifications de qualité

build: ## Construit le package
	@echo "$(BLUE)Construction du package...$(NC)"
	$(POETRY) build
	@echo "$(GREEN)✅ Package construit dans dist/$(NC)"

run: ## Lance l'application
	@echo "$(BLUE)Lancement de Sensei AI...$(NC)"
	$(POETRY) run sensei --help

# Commandes pour les features
build-features: ## Construit les features pour hier
	@echo "$(BLUE)Construction des features...$(NC)"
	$(POETRY) run sensei build-features
	@echo "$(GREEN)✅ Features construites$(NC)"

train-conversion: ## Entraîne le modèle de conversion
	@echo "$(BLUE)Entraînement du modèle de conversion...$(NC)"
	$(POETRY) run sensei train conversion --save-path ./models
	@echo "$(GREEN)✅ Modèle de conversion entraîné$(NC)"

train-channel: ## Entraîne le modèle de canal/timing
	@echo "$(BLUE)Entraînement du modèle de canal/timing...$(NC)"
	$(POETRY) run sensei train channel --save-path ./models
	@echo "$(GREEN)✅ Modèle de canal/timing entraîné$(NC)"

train-nlp: ## Entraîne le modèle NLP
	@echo "$(BLUE)Entraînement du modèle NLP...$(NC)"
	$(POETRY) run sensei train nlp_signals --save-path ./models
	@echo "$(GREEN)✅ Modèle NLP entraîné$(NC)"

status: ## Affiche le statut du système
	@echo "$(BLUE)Vérification du statut...$(NC)"
	$(POETRY) run sensei status

# Commandes Docker
docker-build: ## Construit l'image Docker
	@echo "$(BLUE)Construction de l'image Docker...$(NC)"
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	@echo "$(GREEN)✅ Image Docker construite: $(DOCKER_IMAGE):$(DOCKER_TAG)$(NC)"

docker-run: ## Lance le conteneur Docker
	@echo "$(BLUE)Lancement du conteneur Docker...$(NC)"
	docker run --rm -it $(DOCKER_IMAGE):$(DOCKER_TAG)

docker-dev: ## Lance l'environnement de développement Docker
	@echo "$(BLUE)Lancement de l'environnement de développement...$(NC)"
	docker-compose up -d sensei-dev
	@echo "$(GREEN)✅ Environnement de développement démarré$(NC)"

docker-test: ## Lance les tests dans Docker
	@echo "$(BLUE)Lancement des tests dans Docker...$(NC)"
	docker-compose run --rm sensei-test
	@echo "$(GREEN)✅ Tests terminés$(NC)"

docker-stop: ## Arrête tous les conteneurs
	@echo "$(BLUE)Arrêt des conteneurs...$(NC)"
	docker-compose down
	@echo "$(GREEN)✅ Conteneurs arrêtés$(NC)"

# Commandes de nettoyage
clean: ## Nettoie les fichiers temporaires
	@echo "$(BLUE)Nettoyage des fichiers temporaires...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	@echo "$(GREEN)✅ Nettoyage terminé$(NC)"

clean-models: ## Supprime les modèles sauvegardés
	@echo "$(YELLOW)⚠️  Suppression des modèles sauvegardés...$(NC)"
	rm -rf models/
	@echo "$(GREEN)✅ Modèles supprimés$(NC)"

clean-data: ## Supprime les données temporaires
	@echo "$(YELLOW)⚠️  Suppression des données temporaires...$(NC)"
	rm -rf data/
	rm -rf logs/
	@echo "$(GREEN)✅ Données supprimées$(NC)"

clean-all: clean clean-models clean-data ## Nettoyage complet

# Commandes de développement
dev-setup: install-dev ## Configuration complète de l'environnement de développement
	@echo "$(BLUE)Configuration de l'environnement de développement...$(NC)"
	$(POETRY) run pre-commit install || echo "$(YELLOW)⚠️  pre-commit non installé$(NC)"
	mkdir -p models logs data
	@echo "$(GREEN)✅ Environnement de développement configuré$(NC)"

check: quality test ## Vérifie la qualité et lance les tests

ci: check build ## Pipeline CI complète

# Commandes de déploiement
deploy-staging: docker-build ## Déploie en staging
	@echo "$(BLUE)Déploiement en staging...$(NC)"
	# Commandes de déploiement à personnaliser
	@echo "$(GREEN)✅ Déployé en staging$(NC)"

deploy-prod: docker-build ## Déploie en production
	@echo "$(BLUE)Déploiement en production...$(NC)"
	# Commandes de déploiement à personnaliser
	@echo "$(GREEN)✅ Déployé en production$(NC)"

# Commandes d'aide au développement
logs: ## Affiche les logs de l'application
	@echo "$(BLUE)Affichage des logs...$(NC)"
	tail -f logs/*.log 2>/dev/null || echo "$(YELLOW)Aucun fichier de log trouvé$(NC)"

monitor: ## Surveille les fichiers et relance les tests
	@echo "$(BLUE)Surveillance des fichiers...$(NC)"
	$(POETRY) run ptw --runner "pytest tests/unit/ -v"

docs: ## Génère la documentation
	@echo "$(BLUE)Génération de la documentation...$(NC)"
	# Commandes de génération de documentation
	@echo "$(GREEN)✅ Documentation générée$(NC)"

version: ## Affiche la version
	@echo "$(BLUE)Version de Sensei AI Suite:$(NC)"
	$(POETRY) version

# Commandes de base de données
db-migrate: ## Lance les migrations de base de données
	@echo "$(BLUE)Migration de la base de données...$(NC)"
	$(POETRY) run sensei build-features --setup-dataset
	@echo "$(GREEN)✅ Migration terminée$(NC)"

# Aide par défaut
.DEFAULT_GOAL := help
