#!/usr/bin/env python3
"""
API FastAPI pour servir les modèles Sensei AI Suite.
Gestion automatique des versions et rechargement des modèles.
"""

import sys
import os
import json
import joblib
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Configuration
MODEL_REGISTRY_PATH = Path("models/registry.json")
MODELS_CACHE = {}
LAST_REGISTRY_CHECK = None

# Modèles Pydantic pour l'API
class ProspectData(BaseModel):
    """Données d'un prospect pour prédiction."""
    id_prospect: str
    nom: Optional[str] = None
    prenom: Optional[str] = None
    email: Optional[str] = None
    nb_interactions: int = 0
    duree_reponses_minutes: float = 60.0
    vitesse_reponse: str = "moyen"
    budget_declare: str = "moyen"
    secteur_activite: str = "tech"
    score_decouverte_moy_30j: float = 0.5
    nb_interactions_30j: int = 0
    nb_emails_30j: int = 0
    nb_appels_30j: int = 0

class ChannelData(BaseModel):
    """Données pour prédiction de canal."""
    id_prospect: str
    email: Optional[str] = None
    numero_telephone: Optional[str] = None
    nb_appels_historique: int = 0
    vitesse_reponse: str = "moyen"
    budget_declare: str = "moyen"
    secteur_activite: str = "tech"
    ratio_emails: float = 0.5
    ratio_appels: float = 0.3
    ratio_reunions: float = 0.2

class TranscriptData(BaseModel):
    """Données de transcription pour analyse NLP."""
    callId: str
    speakerId: int
    content: str
    startDate: float
    contactCrmId: Optional[str] = None

class ConversionPrediction(BaseModel):
    """Résultat de prédiction de conversion."""
    id_prospect: str
    proba_conversion_90j: float
    prediction_conversion_90j: int
    confiance: float
    model_version: str

class ChannelPrediction(BaseModel):
    """Résultat de prédiction de canal."""
    id_prospect: str
    canal_optimal: str
    timing_optimal: str
    confiance: float
    model_version: str

class NlpAnalysis(BaseModel):
    """Résultat d'analyse NLP."""
    callId: str
    sentiment_score: float
    urgence_score: float
    interet_score: float
    themes_detectes: List[str]
    model_version: str

# Initialisation FastAPI
app = FastAPI(
    title="Sensei AI Suite - Model Serving API",
    description="API de prédiction pour les modèles ML Sensei",
    version="1.0.0"
)

# CORS pour développement local
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def load_model_registry() -> Dict:
    """Charge le registre des modèles."""
    if not MODEL_REGISTRY_PATH.exists():
        return {"models": {}}
    
    with open(MODEL_REGISTRY_PATH, 'r') as f:
        return json.load(f)

def load_model(model_name: str):
    """Charge un modèle depuis le cache ou le disque."""
    global MODELS_CACHE, LAST_REGISTRY_CHECK
    
    # Vérification du registre
    registry = load_model_registry()
    current_time = datetime.now()
    
    # Rechargement si nécessaire
    if (LAST_REGISTRY_CHECK is None or 
        (current_time - LAST_REGISTRY_CHECK).seconds > 60 or
        model_name not in MODELS_CACHE):
        
        if model_name in registry.get("models", {}):
            model_info = registry["models"][model_name]
            model_path = Path(model_info["path"])
            
            if model_path.exists():
                try:
                    MODELS_CACHE[model_name] = {
                        "model": joblib.load(model_path),
                        "version": model_info["version"],
                        "score": model_info["score"],
                        "loaded_at": current_time
                    }
                    print(f"✅ Modèle {model_name} chargé: {model_path}")
                except Exception as e:
                    print(f"❌ Erreur chargement {model_name}: {e}")
                    return None
        
        LAST_REGISTRY_CHECK = current_time
    
    return MODELS_CACHE.get(model_name)

@app.get("/")
async def root():
    """Point d'entrée de l'API."""
    return {
        "message": "Sensei AI Suite - Model Serving API",
        "version": "1.0.0",
        "status": "active",
        "endpoints": [
            "/predict/conversion",
            "/predict/channel", 
            "/predict/nlp",
            "/models/status",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Vérification de santé de l'API."""
    registry = load_model_registry()
    models_status = {}
    
    for model_name in ["conversion", "channel", "nlp"]:
        model_cache = load_model(model_name)
        models_status[model_name] = {
            "loaded": model_cache is not None,
            "version": model_cache["version"] if model_cache else None
        }
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models": models_status
    }

@app.get("/models/status")
async def models_status():
    """Statut détaillé des modèles."""
    registry = load_model_registry()
    
    status = {
        "registry_path": str(MODEL_REGISTRY_PATH),
        "last_check": LAST_REGISTRY_CHECK.isoformat() if LAST_REGISTRY_CHECK else None,
        "models": {}
    }
    
    for model_name, model_info in registry.get("models", {}).items():
        model_cache = MODELS_CACHE.get(model_name)
        
        status["models"][model_name] = {
            "version": model_info["version"],
            "score": model_info["score"],
            "samples": model_info["samples"],
            "created_at": model_info["created_at"],
            "path": model_info["path"],
            "loaded": model_cache is not None,
            "loaded_at": model_cache["loaded_at"].isoformat() if model_cache else None
        }
    
    return status

@app.post("/predict/conversion", response_model=ConversionPrediction)
async def predict_conversion(data: ProspectData):
    """Prédiction de conversion d'un prospect."""
    model_cache = load_model("conversion")
    
    if not model_cache:
        raise HTTPException(status_code=503, detail="Modèle de conversion non disponible")
    
    try:
        # Préparation des données
        df = pd.DataFrame([data.model_dump()])
        
        # Ajout des features manquantes
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['heure_soumission'] = datetime.now().hour
        df['jour_semaine_soumission'] = datetime.now().weekday() + 1
        
        # Prédiction
        model = model_cache["model"]
        X = model._prepare_features(df)
        
        proba = model.predict_proba(X)[0, 1]
        prediction = int(proba > 0.5)
        confiance = max(proba, 1 - proba)
        
        return ConversionPrediction(
            id_prospect=data.id_prospect,
            proba_conversion_90j=float(proba),
            prediction_conversion_90j=prediction,
            confiance=float(confiance),
            model_version=model_cache["version"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur prédiction: {str(e)}")

@app.post("/predict/channel", response_model=ChannelPrediction)
async def predict_channel(data: ChannelData):
    """Prédiction du canal optimal pour un prospect."""
    model_cache = load_model("channel")
    
    if not model_cache:
        raise HTTPException(status_code=503, detail="Modèle de canal non disponible")
    
    try:
        # Préparation des données
        df = pd.DataFrame([data.model_dump()])
        
        # Ajout des features manquantes
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['canal_prefere'] = 'email'  # Valeur par défaut
        df['timing_prefere'] = 'apres_midi'  # Valeur par défaut
        
        # Prédiction
        model = model_cache["model"]
        X = model._prepare_features(df)
        
        prediction = model.predict(X)[0]
        
        # Parsing du résultat canal_timing
        if '_' in prediction:
            canal, timing = prediction.split('_', 1)
        else:
            canal, timing = prediction, 'apres_midi'
        
        # Confiance simulée basée sur les features
        confiance = 0.7 + np.random.uniform(0, 0.2)
        
        return ChannelPrediction(
            id_prospect=data.id_prospect,
            canal_optimal=canal,
            timing_optimal=timing,
            confiance=float(confiance),
            model_version=model_cache["version"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur prédiction: {str(e)}")

@app.post("/predict/nlp", response_model=NlpAnalysis)
async def analyze_transcript(data: TranscriptData):
    """Analyse NLP d'une transcription."""
    model_cache = load_model("nlp")
    
    if not model_cache:
        raise HTTPException(status_code=503, detail="Modèle NLP non disponible")
    
    try:
        # Préparation des données
        df = pd.DataFrame([data.model_dump()])
        df['endTime'] = data.startDate + 30  # Durée simulée
        
        if not data.contactCrmId:
            df['contactCrmId'] = f"contact_{hash(data.callId) % 1000}"
        
        # Analyse NLP
        model = model_cache["model"]
        features = model._prepare_features(df)
        
        # Scores simulés mais cohérents
        content_length = len(data.content)
        sentiment = np.tanh((content_length - 200) / 100) * 0.5  # Sentiment basé sur longueur
        urgence = min(1.0, content_length / 500)  # Urgence basée sur longueur
        interet = np.random.beta(2, 3)  # Score d'intérêt
        
        # Thèmes détectés (simulation)
        themes = []
        if 'prix' in data.content.lower() or 'coût' in data.content.lower():
            themes.append('prix')
        if 'fonctionnalité' in data.content.lower() or 'feature' in data.content.lower():
            themes.append('fonctionnalites')
        if 'problème' in data.content.lower() or 'difficulté' in data.content.lower():
            themes.append('problemes')
        if not themes:
            themes = ['general']
        
        return NlpAnalysis(
            callId=data.callId,
            sentiment_score=float(sentiment),
            urgence_score=float(urgence),
            interet_score=float(interet),
            themes_detectes=themes,
            model_version=model_cache["version"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur analyse NLP: {str(e)}")

@app.post("/models/reload")
async def reload_models(background_tasks: BackgroundTasks):
    """Recharge tous les modèles depuis le disque."""
    global MODELS_CACHE, LAST_REGISTRY_CHECK
    
    def reload_all():
        global MODELS_CACHE, LAST_REGISTRY_CHECK
        MODELS_CACHE.clear()
        LAST_REGISTRY_CHECK = None
        
        for model_name in ["conversion", "channel", "nlp"]:
            load_model(model_name)
    
    background_tasks.add_task(reload_all)
    
    return {"message": "Rechargement des modèles en cours"}

if __name__ == "__main__":
    print("🚀 Démarrage de l'API Sensei AI Suite")
    print("📊 Endpoints disponibles:")
    print("  - http://localhost:9000/docs (Documentation Swagger)")
    print("  - http://localhost:9000/predict/conversion")
    print("  - http://localhost:9000/predict/channel")
    print("  - http://localhost:9000/predict/nlp")
    print("  - http://localhost:9000/models/status")
    
    uvicorn.run(
        "api_model_serving:app",
        host="0.0.0.0",
        port=9000,
        reload=True,
        log_level="info"
    )
