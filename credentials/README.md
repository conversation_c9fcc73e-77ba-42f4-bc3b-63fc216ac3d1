# 🔐 Credentials Configuration

## Service Account Setup

Pour tester avec BigQuery en production, placez votre fichier de service account ici :

```
credentials/
└── sensei-ai-service-account.json  # ← Votre fichier JSON ici
```

## Obtenir les Credentials

1. **Google Cloud Console** : https://console.cloud.google.com/
2. **Projet** : `sensei-ai-dev`
3. **IAM & Admin > Service Accounts**
4. **Créer/utiliser un Service Account** avec les rôles :
   - `BigQuery Data Viewer`
   - `BigQuery Job User` 
   - `BigQuery Metadata Viewer`
5. **Télécharger la clé JSON**

## Configuration

Une fois le fichier placé, exécutez :

```bash
# Configurer les credentials
source scripts/configure_credentials.sh

# Tester la connexion
python test_bigquery_production.py
```

## Sécurité

⚠️ **IMPORTANT** :
- Ne jamais commiter les fichiers de credentials
- Le répertoire `credentials/` est dans `.gitignore`
- Utiliser des permissions minimales
- Rotation régulière des clés
