#!/usr/bin/env python3
"""
Entraînement simplifié des modèles Sensei AI Suite.
Version sans MLflow, optimisée pour Mac local.
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, accuracy_score

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Configuration pour Mac local
MAX_SAMPLES_PER_MODEL = 5000  # Réduit pour éviter les problèmes
TRAIN_SIZE = 0.8
RANDOM_STATE = 42

def create_model_registry():
    """Crée un registre des modèles simplifié."""
    print("📋 Création du registre des modèles...")
    
    registry = {
        "created_at": datetime.now().isoformat(),
        "environment": "production",
        "models": {},
        "storage": {
            "local_path": "./models/",
            "versioning": "timestamp"
        }
    }
    
    # Sauvegarde du registre
    registry_path = Path("models/registry.json")
    registry_path.parent.mkdir(exist_ok=True)
    
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)
    
    print(f"✅ Registre créé: {registry_path}")
    return registry

def update_model_registry(model_name, model_path, score, samples):
    """Met à jour le registre avec un nouveau modèle."""
    registry_path = Path("models/registry.json")
    
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
    else:
        registry = create_model_registry()
    
    # Ajout du nouveau modèle
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    registry["models"][model_name] = {
        "version": timestamp,
        "path": str(model_path),
        "score": score,
        "samples": samples,
        "created_at": datetime.now().isoformat(),
        "status": "active"
    }
    
    # Sauvegarde
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)
    
    return registry

def load_conversion_data():
    """Charge des données simplifiées pour conversion."""
    print("\n🎯 Chargement des données de conversion...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Requête simplifiée
        query = f"""
        SELECT 
            c.id_contact as id_prospect,
            c.nom,
            c.prenom,
            c.email,
            COUNT(f.callId) as nb_interactions
        FROM `datalake-sensei.serving_layer.vw_dim_contact` c
        LEFT JOIN `datalake-sensei.serving_layer.vw_fact_modjo_call` f 
            ON c.id_contact = f.contactId
        WHERE c.email IS NOT NULL
        AND c.nom IS NOT NULL
        GROUP BY c.id_contact, c.nom, c.prenom, c.email
        HAVING COUNT(f.callId) > 0
        LIMIT {MAX_SAMPLES_PER_MODEL}
        """
        
        print(f"🔍 Chargement max {MAX_SAMPLES_PER_MODEL} prospects...")
        df = client.query_df(query)
        
        # Enrichissement des features
        np.random.seed(RANDOM_STATE)
        n = len(df)
        
        # Target basé sur l'activité
        df['y_converted_90j'] = (df['nb_interactions'] >= 3).astype(int)
        
        # Features temporelles
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['heure_soumission'] = np.random.randint(8, 18, n)
        df['jour_semaine_soumission'] = np.random.randint(1, 8, n)
        
        # Features comportementales
        df['duree_reponses_minutes'] = np.random.exponential(60, n)
        df['vitesse_reponse'] = np.where(
            df['duree_reponses_minutes'] < 60, 'rapide',
            np.where(df['duree_reponses_minutes'] < 180, 'moyen', 'lent')
        )
        
        df['budget_declare'] = np.random.choice(['petit', 'moyen', 'grand'], n)
        df['secteur_activite'] = np.random.choice(['tech', 'finance', 'retail'], n)
        df['score_decouverte_moy_30j'] = np.random.uniform(0, 1, n)
        df['nb_interactions_30j'] = df['nb_interactions']
        df['nb_emails_30j'] = np.random.poisson(2, n)
        df['nb_appels_30j'] = np.random.poisson(1, n)
        
        print(f"✅ Données de conversion: {len(df)} prospects")
        print(f"  📊 Taux de conversion: {df['y_converted_90j'].mean():.1%}")
        
        return df
        
    except Exception as e:
        print(f"❌ Erreur chargement conversion: {e}")
        return None

def load_channel_data():
    """Charge des données simplifiées pour canal."""
    print("\n📞 Chargement des données de canal...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Requête simplifiée
        query = f"""
        SELECT 
            c.id_contact as id_prospect,
            c.email,
            c.numero_telephone,
            COUNT(f.callId) as nb_appels_historique
        FROM `datalake-sensei.serving_layer.vw_dim_contact` c
        LEFT JOIN `datalake-sensei.serving_layer.vw_fact_modjo_call` f 
            ON c.id_contact = f.contactId
        WHERE c.email IS NOT NULL
        GROUP BY c.id_contact, c.email, c.numero_telephone
        LIMIT {MAX_SAMPLES_PER_MODEL}
        """
        
        print(f"🔍 Chargement max {MAX_SAMPLES_PER_MODEL} contacts...")
        df = client.query_df(query)
        
        # Enrichissement
        np.random.seed(RANDOM_STATE)
        n = len(df)
        
        # Features de base
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['vitesse_reponse'] = np.random.choice(['rapide', 'moyen', 'lent'], n)
        df['budget_declare'] = np.random.choice(['petit', 'moyen', 'grand'], n)
        df['secteur_activite'] = np.random.choice(['tech', 'finance', 'retail'], n)
        
        # Canal basé sur les données
        canal_prefere = []
        timing_prefere = []
        
        for i, row in df.iterrows():
            if pd.notna(row['numero_telephone']) and row['nb_appels_historique'] > 2:
                canal = 'appel'
                timing = np.random.choice(['matin', 'apres_midi'])
            else:
                canal = 'email'
                timing = np.random.choice(['matin', 'apres_midi', 'soir'])
            
            canal_prefere.append(canal)
            timing_prefere.append(timing)
        
        df['canal_prefere'] = canal_prefere
        df['timing_prefere'] = timing_prefere
        df['target_canal_timing'] = df['canal_prefere'] + '_' + df['timing_prefere']
        
        # Features de ratio
        df['ratio_emails'] = np.random.uniform(0, 1, n)
        df['ratio_appels'] = df['nb_appels_historique'] / (df['nb_appels_historique'].max() + 1)
        df['ratio_reunions'] = np.random.uniform(0, 0.3, n)
        
        print(f"✅ Données de canal: {len(df)} contacts")
        print(f"  📊 Canaux: {pd.Series(canal_prefere).value_counts().to_dict()}")
        
        return df
        
    except Exception as e:
        print(f"❌ Erreur chargement canal: {e}")
        return None

def train_conversion_model_simple(df):
    """Entraîne le modèle de conversion simplifié."""
    print("\n🎯 Entraînement du modèle de conversion...")
    
    try:
        from sensei.models.conversion import ConversionModel
        
        # Initialisation du modèle
        model = ConversionModel()
        
        # Préparation des données
        X = model._prepare_features(df)
        y = df['y_converted_90j']
        
        # Split train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=1-TRAIN_SIZE, random_state=RANDOM_STATE, stratify=y
        )
        
        print(f"  📊 Train: {len(X_train)}, Test: {len(X_test)}")
        
        # Entraînement avec la méthode train au lieu de fit
        print("  🔄 Entraînement en cours...")
        model.train(X_train, y_train)
        
        # Prédictions
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test)[:, 1]
        
        # Métriques
        auc = roc_auc_score(y_test, y_proba)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"  ✅ AUC: {auc:.3f}")
        print(f"  ✅ Accuracy: {accuracy:.3f}")
        
        # Sauvegarde du modèle
        model_path = Path("models/conversion")
        model_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_file = model_path / f"conversion_model_{timestamp}.pkl"
        joblib.dump(model, model_file)
        
        # Mise à jour du registre
        update_model_registry("conversion", model_file, auc, len(df))
        
        print(f"  💾 Modèle sauvegardé: {model_file}")
        
        return model, auc
        
    except Exception as e:
        print(f"❌ Erreur entraînement conversion: {e}")
        import traceback
        traceback.print_exc()
        return None, 0.0

def train_channel_model_simple(df):
    """Entraîne le modèle de canal simplifié."""
    print("\n📞 Entraînement du modèle de canal...")
    
    try:
        from sensei.models.channel import ChannelModel
        
        # Initialisation du modèle
        model = ChannelModel()
        
        # Préparation des données
        X = model._prepare_features(df)
        y = model._create_channel_timing_target(df)
        
        # Split train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=1-TRAIN_SIZE, random_state=RANDOM_STATE
        )
        
        print(f"  📊 Train: {len(X_train)}, Test: {len(X_test)}")
        print(f"  📊 Classes: {len(y.unique())}")
        
        # Entraînement avec la méthode train
        print("  🔄 Entraînement en cours...")
        model.train(X_train, y_train)
        
        # Prédictions
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"  ✅ Accuracy: {accuracy:.3f}")
        
        # Sauvegarde du modèle
        model_path = Path("models/channel")
        model_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_file = model_path / f"channel_model_{timestamp}.pkl"
        joblib.dump(model, model_file)
        
        # Mise à jour du registre
        update_model_registry("channel", model_file, accuracy, len(df))
        
        print(f"  💾 Modèle sauvegardé: {model_file}")
        
        return model, accuracy
        
    except Exception as e:
        print(f"❌ Erreur entraînement canal: {e}")
        import traceback
        traceback.print_exc()
        return None, 0.0

def main():
    """Fonction principale d'entraînement simplifié."""
    print("🚀 Entraînement Production Simplifié - Sensei AI Suite")
    print("🖥️  Optimisé pour Mac local")
    print("=" * 70)
    
    # Configuration
    os.environ['ENVIRONMENT'] = 'production'
    os.environ['LOG_LEVEL'] = 'INFO'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'datalake-sensei'
    
    # Création du registre
    create_model_registry()
    
    # Résultats d'entraînement
    results = {}
    
    # 1. Modèle de conversion
    print("\n" + "="*50)
    conversion_data = load_conversion_data()
    if conversion_data is not None and len(conversion_data) > 100:
        conversion_model, conversion_score = train_conversion_model_simple(conversion_data)
        results['conversion'] = {
            'model': conversion_model,
            'score': conversion_score,
            'samples': len(conversion_data)
        }
    
    # 2. Modèle de canal
    print("\n" + "="*50)
    channel_data = load_channel_data()
    if channel_data is not None and len(channel_data) > 100:
        channel_model, channel_score = train_channel_model_simple(channel_data)
        results['channel'] = {
            'model': channel_model,
            'score': channel_score,
            'samples': len(channel_data)
        }
    
    # Résumé final
    print("\n" + "=" * 70)
    print("📊 Résumé de l'entraînement production:")
    
    total_models = 0
    for model_name, result in results.items():
        if result['model'] is not None:
            total_models += 1
            print(f"  ✅ {model_name}: Score {result['score']:.3f} ({result['samples']} échantillons)")
        else:
            print(f"  ❌ {model_name}: Échec")
    
    print(f"\n🎯 Modèles entraînés: {total_models}/2")
    print(f"📁 Stockage local: ./models/")
    print(f"📋 Registre: ./models/registry.json")
    
    if total_models >= 1:
        print("🎉 Entraînement production réussi!")
        print("🚀 Modèles prêts pour l'API de prédiction!")
        return 0
    else:
        print("⚠️  Entraînement échoué - vérifiez les données")
        return 1

if __name__ == "__main__":
    sys.exit(main())
