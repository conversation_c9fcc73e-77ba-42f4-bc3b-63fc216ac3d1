# Production-ready Docker image for Sensei AI Suite
FROM python:3.11-slim

# Metadata
LABEL maintainer="Sensei AI Team"
LABEL version="1.0.0"
LABEL description="Sensei AI Suite - ML-powered sales optimization platform"

# Environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Create non-root user for security
RUN groupadd -r sensei && useradd -r -g sensei sensei

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY models/ ./models/
COPY config/ ./config/
COPY credentials/ ./credentials/

# Create necessary directories
RUN mkdir -p /app/logs && chown -R sensei:sensei /app

# Switch to non-root user
USER sensei

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "src.sensei.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
