# Dockerfile pour Sensei AI Suite
# Image de base Python 3.11 optimisée
FROM python:3.11-slim

# Métadonnées
LABEL maintainer="Sensei Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Sensei AI Suite - Optimisation de la prospection marketing & commerciale"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_VERSION=1.6.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_CACHE_DIR=/tmp/poetry_cache \
    POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    # Outils de base
    curl \
    git \
    # Dépendances pour les librairies ML
    gcc \
    g++ \
    libc6-dev \
    # Dépendances pour les librairies scientifiques
    libgomp1 \
    # Nettoyage
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Installation de Poetry
RUN pip install poetry==$POETRY_VERSION

# Configuration de Poetry
RUN poetry config virtualenvs.create false

# Création de l'utilisateur non-root pour la sécurité
RUN groupadd -r sensei && useradd -r -g sensei sensei

# Création des répertoires de travail
WORKDIR /app

# Copie des fichiers de configuration Poetry
COPY pyproject.toml poetry.lock* ./

# Installation des dépendances Python
RUN poetry install --only=main --no-dev \
    && rm -rf $POETRY_CACHE_DIR

# Copie du code source
COPY src/ ./src/
COPY README.md ./

# Installation du package en mode éditable
RUN pip install -e .

# Création des répertoires nécessaires
RUN mkdir -p /app/models /app/logs /app/data \
    && chown -R sensei:sensei /app

# Configuration des permissions
USER sensei

# Port par défaut (pour une éventuelle API)
EXPOSE 8080

# Variables d'environnement pour la production
ENV ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    PYTHONPATH=/app/src

# Healthcheck pour vérifier que l'application fonctionne
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD sensei status || exit 1

# Point d'entrée par défaut
ENTRYPOINT ["sensei"]

# Commande par défaut
CMD ["--help"]
