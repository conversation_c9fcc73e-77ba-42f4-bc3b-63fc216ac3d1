#!/usr/bin/env python3
"""
Test complet du système de logging Sensei AI Suite.
Valide le logging structuré, les niveaux, et le formatage JSON.
"""

import sys
import os
import json
import tempfile
from pathlib import Path
from io import StringIO
from contextlib import redirect_stderr, redirect_stdout

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_logging_import():
    """Test d'importation du système de logging."""
    print("📝 Test d'importation du système de logging...")
    
    try:
        from sensei.utils.logging import get_logger, configure_logging
        
        print("✅ Modules de logging importés avec succès")
        print(f"  📋 get_logger: {get_logger}")
        print(f"  📋 configure_logging: {configure_logging}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'importation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logger_creation():
    """Test de création de loggers."""
    print("\n🏗️ Test de création de loggers...")
    
    try:
        from sensei.utils.logging import get_logger
        
        # Test de création de logger simple
        logger1 = get_logger("test_module")
        print(f"✅ Logger créé: {logger1}")
        
        # Test de création de logger avec nom complet
        logger2 = get_logger("sensei.models.conversion")
        print(f"✅ Logger module créé: {logger2}")
        
        # Test de création de logger avec __name__
        logger3 = get_logger(__name__)
        print(f"✅ Logger __name__ créé: {logger3}")
        
        # Vérification que les loggers sont différents mais cohérents
        assert logger1 != logger2
        assert logger1 != logger3
        
        print("✅ Création de loggers validée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging_levels():
    """Test des différents niveaux de logging."""
    print("\n📊 Test des niveaux de logging...")
    
    try:
        from sensei.utils.logging import get_logger
        
        logger = get_logger("test_levels")
        
        # Test des différents niveaux
        print("✅ Test des niveaux de log:")
        
        # Debug
        logger.debug("Message de debug", extra_field="debug_value")
        print("  📋 DEBUG: Message envoyé")
        
        # Info
        logger.info("Message d'information", user_id="user123", action="test")
        print("  📋 INFO: Message envoyé")
        
        # Warning
        logger.warning("Message d'avertissement", warning_type="test", severity="low")
        print("  📋 WARNING: Message envoyé")
        
        # Error
        logger.error("Message d'erreur", error_code="TEST001", component="logging")
        print("  📋 ERROR: Message envoyé")
        
        print("✅ Tous les niveaux de log testés")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_structured_logging():
    """Test du logging structuré avec métadonnées."""
    print("\n🏗️ Test du logging structuré...")
    
    try:
        from sensei.utils.logging import get_logger
        
        logger = get_logger("test_structured")
        
        # Test avec métadonnées complexes
        logger.info(
            "Prédiction générée",
            model_name="conversion",
            model_version="1.0.0",
            prospect_id="prospect_123",
            prediction_score=0.75,
            features_count=25,
            processing_time_ms=150,
            environment="test"
        )
        
        print("✅ Log structuré avec métadonnées envoyé")
        
        # Test avec données business
        logger.info(
            "Feature Store mis à jour",
            date_features="2025-01-15",
            prospects_processed=1000,
            features_generated=15,
            execution_time_seconds=45.2,
            success_rate=0.98
        )
        
        print("✅ Log business avec métriques envoyé")
        
        # Test avec erreur structurée
        logger.error(
            "Échec de prédiction",
            error_type="ModelNotTrained",
            model_name="channel",
            prospect_id="prospect_456",
            retry_count=3,
            last_error="Model not fitted"
        )
        
        print("✅ Log d'erreur structuré envoyé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging_configuration():
    """Test de la configuration du logging."""
    print("\n⚙️ Test de la configuration du logging...")
    
    try:
        from sensei.utils.logging import configure_logging, get_logger
        
        # Test de configuration avec différents niveaux
        print("✅ Test des configurations:")
        
        # Configuration DEBUG
        configure_logging(level="DEBUG", format_json=True)
        logger_debug = get_logger("test_debug")
        logger_debug.debug("Message debug visible")
        print("  📋 Configuration DEBUG appliquée")
        
        # Configuration INFO
        configure_logging(level="INFO", format_json=True)
        logger_info = get_logger("test_info")
        logger_info.info("Message info visible")
        logger_info.debug("Message debug invisible")
        print("  📋 Configuration INFO appliquée")
        
        # Configuration WARNING
        configure_logging(level="WARNING", format_json=False)
        logger_warn = get_logger("test_warn")
        logger_warn.warning("Message warning visible")
        logger_warn.info("Message info invisible")
        print("  📋 Configuration WARNING appliquée")
        
        print("✅ Configuration du logging validée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_integration():
    """Test de l'intégration avec les variables d'environnement."""
    print("\n🌍 Test de l'intégration environnement...")
    
    try:
        from sensei.utils.logging import configure_logging, get_logger
        
        # Test avec variables d'environnement
        original_log_level = os.environ.get('LOG_LEVEL')
        original_environment = os.environ.get('ENVIRONMENT')
        
        # Configuration test
        os.environ['LOG_LEVEL'] = 'INFO'
        os.environ['ENVIRONMENT'] = 'test'
        
        # Reconfiguration
        configure_logging()
        logger = get_logger("test_env")
        
        logger.info(
            "Test avec environnement",
            log_level=os.environ.get('LOG_LEVEL'),
            environment=os.environ.get('ENVIRONMENT')
        )
        
        print("✅ Intégration environnement testée")
        
        # Restauration
        if original_log_level:
            os.environ['LOG_LEVEL'] = original_log_level
        else:
            os.environ.pop('LOG_LEVEL', None)
            
        if original_environment:
            os.environ['ENVIRONMENT'] = original_environment
        else:
            os.environ.pop('ENVIRONMENT', None)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_logging():
    """Test du logging de performance."""
    print("\n⚡ Test du logging de performance...")
    
    try:
        from sensei.utils.logging import get_logger
        import time
        
        logger = get_logger("test_performance")
        
        # Simulation d'opération avec timing
        start_time = time.time()
        
        # Simulation de traitement
        time.sleep(0.1)  # 100ms
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(
            "Opération terminée",
            operation="test_processing",
            processing_time_seconds=processing_time,
            processing_time_ms=processing_time * 1000,
            records_processed=100,
            throughput_per_second=100 / processing_time
        )
        
        print(f"✅ Log de performance: {processing_time*1000:.1f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test complet du système de logging Sensei AI Suite")
    print("=" * 60)
    
    # Configuration de l'environnement
    os.environ['ENVIRONMENT'] = 'test'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    tests = [
        ("Importation du logging", test_logging_import),
        ("Création de loggers", test_logger_creation),
        ("Niveaux de logging", test_logging_levels),
        ("Logging structuré", test_structured_logging),
        ("Configuration logging", test_logging_configuration),
        ("Intégration environnement", test_environment_integration),
        ("Logging de performance", test_performance_logging),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 Résumé des tests de logging:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Le système de logging fonctionne parfaitement!")
        return 0
    else:
        print("⚠️  Le système de logging nécessite des corrections")
        return 1

if __name__ == "__main__":
    sys.exit(main())
