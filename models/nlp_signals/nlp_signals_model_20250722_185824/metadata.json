{"model_name": "nlp_signals", "version": "1.0.0", "feature_columns": null, "target_column": null, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "training_metadata": {"training_time_seconds": 100.951698, "transcription_count": 40000, "valid_texts": 39167, "n_clusters": 1164, "noise_ratio": 0.49271070033446523, "embedding_dim": 384, "reduced_dim": 50, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "trained_at": "2025-07-22T18:58:24.981770", "train_samples": 40000, "test_samples": 10000, "train_test_ratio": 4.0}, "is_trained": true}