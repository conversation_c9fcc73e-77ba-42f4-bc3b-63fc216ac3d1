{"model_name": "conversion", "version": "1.0.0", "feature_columns": ["nom", "prenom", "email", "numero_telephone", "statut_du_lead", "hubspotscore", "source_personnalisee", "nb_interactions", "nb_jours_actifs", "duree_moyenne_appels", "activite_90j", "signaux_positifs", "discussions_prix", "duree_reponses_minutes", "vitesse_reponse", "budget_declare", "secteur_activite", "score_decouverte_moy_30j", "nb_interactions_30j", "nb_emails_30j", "nb_appels_30j", "jours_depuis_premier_contact", "jours_depuis_dernier_contact"], "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 15, "learning_rate": 0.01, "feature_fraction": 0.7, "bagging_fraction": 0.7, "bagging_freq": 5, "min_child_samples": 50, "reg_alpha": 0.1, "reg_lambda": 0.1, "max_depth": 5, "verbose": -1, "random_state": 42, "n_estimators": 100, "early_stopping_rounds": 20}, "training_metadata": {"training_time_seconds": 0.066319, "train_size": 1259, "val_size": 315, "feature_count": 23, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 15, "learning_rate": 0.01, "feature_fraction": 0.7, "bagging_fraction": 0.7, "bagging_freq": 5, "min_child_samples": 50, "reg_alpha": 0.1, "reg_lambda": 0.1, "max_depth": 5, "verbose": -1, "random_state": 42, "n_estimators": 100, "early_stopping_rounds": 20}, "trained_at": "2025-07-22T23:12:14.037236", "train_auc": 0.7645480408638303, "val_auc": 0.47704918032786886, "precision_at_200": 0.025, "error": "'ConversionModel' object has no attribute 'predict_proba'"}, "is_trained": true}