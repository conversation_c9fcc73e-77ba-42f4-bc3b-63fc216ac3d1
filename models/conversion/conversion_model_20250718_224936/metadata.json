{"model_name": "conversion", "version": "1.0.0", "feature_columns": ["nom", "prenom", "email", "nb_interactions", "heure_soumission", "jour_semaine_soumission", "duree_reponses_minutes", "vitesse_reponse", "budget_declare", "secteur_activite", "score_decouverte_moy_30j", "nb_interactions_30j", "nb_emails_30j", "nb_appels_30j"], "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "training_metadata": {"training_time_seconds": 0.914498, "train_size": 1556, "val_size": 1556, "feature_count": 14, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "trained_at": "2025-07-18T22:49:36.915448", "train_auc": 1.0, "val_auc": 1.0, "precision_at_200": 1.0}, "is_trained": true}