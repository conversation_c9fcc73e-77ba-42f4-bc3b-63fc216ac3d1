{"model_name": "conversion", "version": "1.0.0", "feature_columns": ["nom", "prenom", "email", "numero_telephone", "statut_du_lead", "hubspotscore", "source_personnalisee", "nb_interactions", "nb_jours_actifs", "duree_moyenne_appels", "activite_90j", "signaux_positifs", "discussions_prix", "duree_reponses_minutes", "vitesse_reponse", "budget_declare", "secteur_activite", "score_decouverte_moy_30j", "nb_interactions_30j", "nb_emails_30j", "nb_appels_30j", "jours_depuis_premier_contact", "jours_depuis_dernier_contact"], "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "training_metadata": {"training_time_seconds": 0.442993, "train_size": 1259, "val_size": 315, "feature_count": 23, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "trained_at": "2025-07-22T19:06:44.416885", "train_auc": 0.9996595698692317, "val_auc": 0.9999999999999999, "precision_at_200": 0.355}, "is_trained": true}