{"model_name": "channel", "version": "1.0.0", "feature_columns": ["email", "numero_telephone", "statut_du_lead", "source_personnalisee", "nb_appels_historique", "nb_appels_matin", "nb_appels_apres_midi", "nb_appels_soir", "duree_moyenne_appels", "canal_timing_optimal", "vitesse_reponse", "budget_declare", "secteur_activite", "has_phone", "call_history_score", "response_speed", "preferred_time", "ratio_emails", "ratio_appels", "ratio_reunions"], "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 20, "learning_rate": 0.005, "depth": 1, "l2_leaf_reg": 100, "min_data_in_leaf": 200, "random_seed": 42, "verbose": false, "early_stopping_rounds": 5, "use_best_model": true, "bootstrap_type": "<PERSON><PERSON><PERSON>", "subsample": 0.3, "colsample_bylevel": 0.3, "leaf_estimation_iterations": 1}, "training_metadata": {"training_time_seconds": 0.092149, "train_size": 1259, "val_size": 315, "feature_count": 20, "target_classes": 5, "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 20, "learning_rate": 0.005, "depth": 1, "l2_leaf_reg": 100, "min_data_in_leaf": 200, "random_seed": 42, "verbose": false, "early_stopping_rounds": 5, "use_best_model": true, "bootstrap_type": "<PERSON><PERSON><PERSON>", "subsample": 0.3, "colsample_bylevel": 0.3, "leaf_estimation_iterations": 1}, "trained_at": "2025-07-22T23:11:17.975336", "train_accuracy": 0.7934868943606036, "val_accuracy": 0.7841269841269841, "val_precision_macro": 0.3426160337552743, "val_recall_macro": 0.3368421052631579, "val_f1_macro": 0.3290024630541872, "error": "Features manquantes: {'canal_timing_optimal'}"}, "is_trained": true}