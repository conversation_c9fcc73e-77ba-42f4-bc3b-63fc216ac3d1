{"model_name": "channel", "version": "1.0.0", "feature_columns": ["email", "numero_telephone", "statut_du_lead", "source_personnalisee", "nb_appels_historique", "appels_matin", "appels_apres_midi", "appels_soir", "duree_moyenne_appels", "canal_timing_optimal", "vitesse_reponse", "budget_declare", "secteur_activite", "has_phone", "call_history_score", "response_speed", "preferred_time", "ratio_emails", "ratio_appels", "ratio_reunions"], "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 1000, "learning_rate": 0.1, "depth": 6, "l2_leaf_reg": 3, "random_seed": 42, "verbose": false, "early_stopping_rounds": 100, "use_best_model": true}, "training_metadata": {"training_time_seconds": 20.156821, "train_size": 80000, "val_size": 20000, "feature_count": 20, "target_classes": 6, "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 1000, "learning_rate": 0.1, "depth": 6, "l2_leaf_reg": 3, "random_seed": 42, "verbose": false, "early_stopping_rounds": 100, "use_best_model": true}, "trained_at": "2025-07-22T19:07:25.190640", "train_accuracy": 1.0, "val_accuracy": 1.0, "val_precision_macro": 1.0, "val_recall_macro": 1.0, "val_f1_macro": 1.0}, "is_trained": true}