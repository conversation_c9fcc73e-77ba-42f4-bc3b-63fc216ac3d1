{"created_at": "2025-07-18T20:48:05.283652", "environment": "production", "models": {"nlp": {"version": "20250718_204818", "path": "models/nlp/nlp_model_20250718_204818.pkl", "score": 0.85, "samples": 1000, "created_at": "2025-07-18T20:48:18.845999", "status": "active"}, "conversion": {"version": "20250718_224936", "path": "models/conversion/conversion_model_20250718_224936", "metrics": {"training_time_seconds": 0.914498, "train_size": 1556, "val_size": 1556, "feature_count": 14, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "trained_at": "2025-07-18T22:49:36.915448", "train_auc": 1.0, "val_auc": 1.0, "precision_at_200": 1.0}, "created_at": "2025-07-18T22:49:36.917774", "status": "active", "server_capacity": "medium"}, "channel": {"version": "20250718_204927", "path": "models/channel/channel_model_20250718_204927.pkl", "score": 0.75, "samples": 1000, "created_at": "2025-07-18T20:49:27.038064", "status": "active"}, "nlp_signals": {"version": "20250718_230208", "path": "models/nlp_signals/nlp_signals_model_20250718_230208", "metrics": {"training_time_seconds": 93.439208, "transcription_count": 40000, "valid_texts": 39131, "n_clusters": 1186, "noise_ratio": 0.5011883161687665, "embedding_dim": 384, "reduced_dim": 50, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "trained_at": "2025-07-18T23:02:08.905180", "train_samples": 40000, "test_samples": 10000, "train_test_ratio": 4.0}, "created_at": "2025-07-18T23:02:08.912701", "status": "active", "server_capacity": "medium"}}, "storage": {"local_path": "./models/"}}