{"created_at": "2025-07-18T20:48:05.283652", "environment": "production", "models": {"nlp": {"version": "20250718_204818", "path": "models/nlp/nlp_model_20250718_204818.pkl", "score": 0.85, "samples": 1000, "created_at": "2025-07-18T20:48:18.845999", "status": "active"}, "conversion": {"version": "20250722_231214", "path": "models/conversion/conversion_model_20250722_231214", "metrics": {"training_time_seconds": 0.066319, "train_size": 1259, "val_size": 315, "feature_count": 23, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 15, "learning_rate": 0.01, "feature_fraction": 0.7, "bagging_fraction": 0.7, "bagging_freq": 5, "min_child_samples": 50, "reg_alpha": 0.1, "reg_lambda": 0.1, "max_depth": 5, "verbose": -1, "random_state": 42, "n_estimators": 100, "early_stopping_rounds": 20}, "trained_at": "2025-07-22T23:12:14.037236", "train_auc": 0.7645480408638303, "val_auc": 0.47704918032786886, "precision_at_200": 0.025, "error": "'ConversionModel' object has no attribute 'predict_proba'"}, "created_at": "2025-07-22T23:12:14.046331", "status": "active", "server_capacity": "xlarge"}, "channel": {"version": "20250722_231222", "path": "models/channel/channel_model_20250722_231222", "metrics": {"training_time_seconds": 0.070087, "train_size": 1259, "val_size": 315, "feature_count": 20, "target_classes": 5, "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 20, "learning_rate": 0.005, "depth": 1, "l2_leaf_reg": 100, "min_data_in_leaf": 200, "random_seed": 42, "verbose": false, "early_stopping_rounds": 5, "use_best_model": true, "bootstrap_type": "<PERSON><PERSON><PERSON>", "subsample": 0.3, "colsample_bylevel": 0.3, "leaf_estimation_iterations": 1}, "trained_at": "2025-07-22T23:12:22.842486", "train_accuracy": 0.7617156473391581, "val_accuracy": 0.7682539682539683, "val_precision_macro": 0.3401639344262295, "val_recall_macro": 0.3267857142857143, "val_f1_macro": 0.32001053393903484, "error": "Features manquantes: {'canal_timing_optimal'}"}, "created_at": "2025-07-22T23:12:22.853377", "status": "active", "server_capacity": "xlarge"}, "nlp_signals": {"version": "20250722_233528", "path": "models/nlp_signals/nlp_signals_model_20250722_233528", "metrics": {"training_time_seconds": 1320.210787, "transcription_count": 400000, "valid_texts": 391855, "n_clusters": 8951, "noise_ratio": 0.5604955914815429, "embedding_dim": 384, "reduced_dim": 50, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "trained_at": "2025-07-22T23:35:28.256639", "train_samples": 400000, "test_samples": 100000, "train_test_ratio": 4.0}, "created_at": "2025-07-22T23:35:28.651212", "status": "active", "server_capacity": "xlarge"}}, "storage": {"local_path": "./models/"}}