{"created_at": "2025-07-18T20:48:05.283652", "environment": "production", "models": {"nlp": {"version": "20250718_204818", "path": "models/nlp/nlp_model_20250718_204818.pkl", "score": 0.85, "samples": 1000, "created_at": "2025-07-18T20:48:18.845999", "status": "active"}, "conversion": {"version": "20250722_192201", "path": "models/conversion/conversion_model_20250722_192201", "metrics": {"training_time_seconds": 0.074402, "train_size": 1259, "val_size": 315, "feature_count": 23, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 15, "learning_rate": 0.01, "feature_fraction": 0.7, "bagging_fraction": 0.7, "bagging_freq": 5, "min_child_samples": 50, "reg_alpha": 0.1, "reg_lambda": 0.1, "max_depth": 5, "verbose": -1, "random_state": 42, "n_estimators": 100, "early_stopping_rounds": 20}, "trained_at": "2025-07-22T19:22:01.139833", "train_auc": 0.8146595208070617, "val_auc": 0.6204918032786885, "precision_at_200": 0.04, "error": "'ConversionModel' object has no attribute 'predict_proba'"}, "created_at": "2025-07-22T19:22:01.147931", "status": "active", "server_capacity": "medium"}, "channel": {"version": "20250722_192324", "path": "models/channel/channel_model_20250722_192324", "metrics": {"training_time_seconds": 2.236885, "train_size": 80000, "val_size": 20000, "feature_count": 20, "target_classes": 6, "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 200, "learning_rate": 0.03, "depth": 3, "l2_leaf_reg": 10, "min_data_in_leaf": 20, "random_seed": 42, "verbose": false, "early_stopping_rounds": 30, "use_best_model": true, "bootstrap_type": "<PERSON><PERSON><PERSON>", "subsample": 0.7, "colsample_bylevel": 0.8}, "trained_at": "2025-07-22T19:23:24.228918", "train_accuracy": 1.0, "val_accuracy": 1.0, "val_precision_macro": 1.0, "val_recall_macro": 1.0, "val_f1_macro": 1.0, "error": "Features manquantes: {'canal_timing_optimal'}"}, "created_at": "2025-07-22T19:23:24.326058", "status": "active", "server_capacity": "medium"}, "nlp_signals": {"version": "20250722_190917", "path": "models/nlp_signals/nlp_signals_model_20250722_190917", "metrics": {"training_time_seconds": 103.241928, "transcription_count": 40000, "valid_texts": 39183, "n_clusters": 1138, "noise_ratio": 0.49011050710767423, "embedding_dim": 384, "reduced_dim": 50, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "trained_at": "2025-07-22T19:09:17.262306", "train_samples": 40000, "test_samples": 10000, "train_test_ratio": 4.0}, "created_at": "2025-07-22T19:09:17.267746", "status": "active", "server_capacity": "medium"}}, "storage": {"local_path": "./models/"}}