{"created_at": "2025-07-18T20:48:05.283652", "environment": "production", "models": {"nlp": {"version": "20250718_204818", "path": "models/nlp/nlp_model_20250718_204818.pkl", "score": 0.85, "samples": 1000, "created_at": "2025-07-18T20:48:18.845999", "status": "active"}, "conversion": {"version": "20250722_190644", "path": "models/conversion/conversion_model_20250722_190644", "metrics": {"training_time_seconds": 0.442993, "train_size": 1259, "val_size": 315, "feature_count": 23, "target_column": "y_converted_90j", "model_params": {"objective": "binary", "metric": "auc", "boosting_type": "gbdt", "num_leaves": 31, "learning_rate": 0.05, "feature_fraction": 0.9, "bagging_fraction": 0.8, "bagging_freq": 5, "verbose": -1, "random_state": 42, "n_estimators": 1000, "early_stopping_rounds": 100}, "trained_at": "2025-07-22T19:06:44.416885", "train_auc": 0.9996595698692317, "val_auc": 0.9999999999999999, "precision_at_200": 0.355}, "created_at": "2025-07-22T19:06:44.425548", "status": "active", "server_capacity": "medium"}, "channel": {"version": "20250722_190725", "path": "models/channel/channel_model_20250722_190725", "metrics": {"training_time_seconds": 20.156821, "train_size": 80000, "val_size": 20000, "feature_count": 20, "target_classes": 6, "target_column": "channel_timing_optimal", "model_params": {"loss_function": "MultiClass", "eval_metric": "Accuracy", "iterations": 1000, "learning_rate": 0.1, "depth": 6, "l2_leaf_reg": 3, "random_seed": 42, "verbose": false, "early_stopping_rounds": 100, "use_best_model": true}, "trained_at": "2025-07-22T19:07:25.190640", "train_accuracy": 1.0, "val_accuracy": 1.0, "val_precision_macro": 1.0, "val_recall_macro": 1.0, "val_f1_macro": 1.0}, "created_at": "2025-07-22T19:07:25.268253", "status": "active", "server_capacity": "medium"}, "nlp_signals": {"version": "20250722_190917", "path": "models/nlp_signals/nlp_signals_model_20250722_190917", "metrics": {"training_time_seconds": 103.241928, "transcription_count": 40000, "valid_texts": 39183, "n_clusters": 1138, "noise_ratio": 0.49011050710767423, "embedding_dim": 384, "reduced_dim": 50, "model_params": {"sentence_transformer_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2", "umap_n_components": 50, "umap_n_neighbors": 15, "umap_min_dist": 0.1, "umap_metric": "cosine", "hdbscan_min_cluster_size": 5, "hdbscan_min_samples": 3, "hdbscan_metric": "euclidean", "max_sequence_length": 512, "min_text_length": 50}, "trained_at": "2025-07-22T19:09:17.262306", "train_samples": 40000, "test_samples": 10000, "train_test_ratio": 4.0}, "created_at": "2025-07-22T19:09:17.267746", "status": "active", "server_capacity": "medium"}}, "storage": {"local_path": "./models/"}}