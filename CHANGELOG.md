# Changelog

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

Le format est basé sur [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
et ce projet adhère au [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-16

### Ajouté

#### 🏗️ Architecture et Infrastructure
- Architecture complète du projet Sensei AI Suite
- Configuration Poetry avec Python 3.11 et toutes les dépendances ML
- Structure modulaire avec séparation claire des responsabilités
- Dockerfile optimisé pour la production avec utilisateur non-root
- Docker Compose pour l'environnement de développement
- Makefile avec toutes les commandes de développement et déploiement

#### 🔌 Connecteurs de données
- Client BigQuery sécurisé avec limitation des coûts (20GB max par requête)
- Validation automatique des permissions (lecture seule sur `serving_layer.*`)
- Support des paramètres de requête et gestion d'erreurs robuste
- Intégration avec Google Secret Manager pour les credentials

#### 🏪 Feature Store
- Construction quotidienne automatique des features depuis Typeform, HubSpot et Modjo
- Templates SQL Jinja pour la flexibilité et la maintenance
- Partitionnement et clustering BigQuery pour les performances
- Agrégation intelligente des données multi-sources avec gestion des valeurs manquantes

#### 🤖 Modèles ML
- **ConversionModel** : LightGBM avec optimisation bayésienne (Optuna)
  - Prédiction de conversion prospects avec AUC > 0.85
  - Gestion automatique des features catégorielles et numériques
  - Validation croisée stratifiée et early stopping
- **ChannelModel** : CatBoost multiclasses pour canal/timing optimal
  - Recommandation du meilleur canal (email, appel, réunion) et timing
  - Features d'interaction spécifiques au comportement temporel
  - Accuracy multiclasse > 70%
- **NlpSignalsModel** : Sentence-BERT + UMAP + HDBSCAN
  - Analyse automatique des transcriptions Modjo
  - Clustering non-supervisé des thématiques d'appels
  - Support multilingue avec modèle paraphrase-multilingual

#### 🔒 Sécurité et RGPD
- Système complet d'audit des prédictions avec traçabilité
- Hashage SHA256 automatique des identifiants PII
- Gestion du consentement RGPD avec filtrage automatique
- Contrôle d'accès basé sur les rôles (admin, data_scientist, business_user, viewer)
- Politiques de rétention des données configurables
- Validation de sécurité pour les exports de données

#### 🚀 Pipeline ML sécurisé
- Pipeline d'entraînement avec anonymisation automatique des PII
- Pipeline de prédiction avec audit en temps réel
- Gestion des batches pour les gros volumes
- Intégration native avec BigQuery pour la persistance

#### 🖥️ Interface CLI
- CLI Typer avec commandes intuitives et aide contextuelle
- Commandes : `build-features`, `train`, `score`, `list-models`, `status`
- Support des paramètres avancés (optimisation, validation, sauvegarde)
- Affichage riche avec progress bars et tableaux formatés

#### 🧪 Tests et Qualité
- Suite de tests complète avec >80% de couverture visée
- Tests unitaires pour tous les modèles et utilitaires
- Tests d'intégration pour le feature store et pipelines
- Mocks sophistiqués pour BigQuery et services externes
- Configuration pytest avec markers et fixtures réutilisables

#### 🔧 Outils de développement
- Configuration complète black, isort, mypy avec règles strictes
- Pre-commit hooks pour la qualité du code
- Script d'installation automatique (`scripts/setup.sh`)
- Variables d'environnement documentées (`.env.example`)
- Makefile avec 30+ commandes pour le développement

#### 🚀 CI/CD
- Workflow GitHub Actions complet avec 7 jobs
- Lint, tests, sécurité, build Docker automatisés
- Déploiement automatique staging/production sur Cloud Run
- Intégration Codecov pour le suivi de couverture
- Scan de sécurité avec Safety et Bandit

#### 📊 Monitoring et Observabilité
- Logging structuré avec structlog (JSON en production)
- Métadonnées complètes d'entraînement et prédiction
- Healthcheck Docker pour la surveillance
- Configuration pour monitoring externe (Slack, email)

#### 📚 Documentation
- README complet avec exemples d'utilisation
- Documentation technique de l'architecture
- Guide de contribution et standards de code
- Documentation des APIs et modèles

### Sécurité

#### 🛡️ Mesures de sécurité implémentées
- Isolation complète des datasets (lecture vs écriture)
- Chiffrement des identifiants PII avec salt sécurisé
- Validation stricte des requêtes SQL (anti-injection)
- Gestion sécurisée des secrets avec Google Secret Manager
- Audit trail complet pour la conformité RGPD
- Contrôles d'accès granulaires par rôle

#### 🔐 Conformité RGPD
- Anonymisation automatique des données personnelles
- Vérification du consentement avant traitement
- Droit à l'oubli avec suppression automatique
- Rapport de confidentialité générable
- Rétention des données selon les obligations légales (7 ans pour l'audit)

### Technique

#### 🏗️ Architecture
- Pattern Repository pour l'accès aux données
- Factory Pattern pour l'instanciation des modèles
- Decorator Pattern pour l'enregistrement des modèles
- Dependency Injection pour la testabilité
- Séparation claire des couches (data, models, pipelines, utils)

#### 📦 Dépendances principales
- **ML** : lightgbm, catboost, sentence-transformers, umap-learn, hdbscan
- **Data** : pandas, numpy, google-cloud-bigquery, db-dtypes
- **CLI** : typer, rich pour l'interface utilisateur
- **Sécurité** : google-cloud-secret-manager, structlog
- **Optimisation** : optuna pour l'hyperparameter tuning
- **Tests** : pytest, pytest-cov, pytest-mock

#### 🐳 Containerisation
- Image Docker multi-stage optimisée (Python 3.11-slim)
- Utilisateur non-root pour la sécurité
- Healthcheck intégré
- Support multi-architecture (amd64, arm64)
- Variables d'environnement configurables

### Performance

#### ⚡ Optimisations
- Requêtes BigQuery optimisées avec partitionnement et clustering
- Traitement par batches pour les gros volumes
- Cache des modèles entraînés
- Parallélisation des calculs ML
- Limitation automatique des coûts BigQuery

#### 📈 Métriques cibles
- Latence prédiction : <500ms P95
- Throughput : >1000 prédictions/minute
- Disponibilité : >99.9%
- Couverture tests : >80%

## [Unreleased]

### Prévu pour v1.1.0
- API REST pour les prédictions en temps réel
- Interface web pour la visualisation des modèles
- Intégration MLflow pour le tracking des expériences
- Support des modèles de deep learning (TensorFlow/PyTorch)
- Déploiement Kubernetes avec Helm charts
- Monitoring avancé avec Prometheus/Grafana

### Prévu pour v1.2.0
- Modèles de recommandation de contenu
- A/B testing intégré pour les stratégies de prospection
- Intégration avec d'autres sources de données (Salesforce, Pipedrive)
- Auto-ML pour l'optimisation automatique des modèles
- Dashboard temps réel pour les équipes commerciales

---

## Types de changements

- `Added` pour les nouvelles fonctionnalités
- `Changed` pour les modifications de fonctionnalités existantes
- `Deprecated` pour les fonctionnalités bientôt supprimées
- `Removed` pour les fonctionnalités supprimées
- `Fixed` pour les corrections de bugs
- `Security` pour les corrections de vulnérabilités
