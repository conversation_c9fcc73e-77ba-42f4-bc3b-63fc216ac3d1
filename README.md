# 🤖 Sensei AI Suite v1.0

**Plateforme d'Intelligence Artificielle pour l'Optimisation Commerciale**

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Production Ready](https://img.shields.io/badge/Production-Ready-brightgreen.svg)](https://github.com/sensei-ai/sensei-ai-suite)

---

## 🎯 **Vue d'Ensemble**

Sensei AI Suite est une plateforme d'intelligence artificielle de niveau production conçue pour révolutionner la prospection commerciale. Elle combine des modèles de machine learning avancés avec une architecture sécurisée et scalable pour fournir des insights prédictifs en temps réel.

### ✨ **Fonctionnalités Clés**

- 🎯 **Prédiction de Conversion** : Probabilité de conversion des prospects avec 85%+ de précision
- 📞 **Optimisation des Canaux** : Recommandation du canal et timing optimal pour chaque prospect
- 🗣️ **Analyse NLP Avancée** : Traitement intelligent des transcriptions d'appels avec détection de sentiment
- 🚀 **API Production-Ready** : Interface REST haute performance avec documentation interactive
- 🔒 **Sécurité Enterprise** : Accès read-only, chiffrement, audit complet
- 📊 **Monitoring Intégré** : Métriques temps réel et dashboards Grafana

---

## 🚀 **Démarrage Rapide**

### **Installation en 3 Minutes**

```bash
# 1. Cloner le projet
git clone https://github.com/sensei-ai/sensei-ai-suite.git
cd sensei-ai-suite

# 2. Installation automatique
./scripts/deployment/deploy.sh development small

# 3. Accéder à l'API
curl http://localhost:8000/health
```

### **Test Immédiat**

```bash
# Prédiction de conversion
curl -X POST "http://localhost:8000/predict/conversion" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "test123",
    "nb_interactions": 3,
    "vitesse_reponse": "rapide",
    "budget_declare": "grand"
  }'

# Résultat : {"proba_conversion_90j": 0.847, "prediction_conversion_90j": 1}
```

---

## 📋 **Prérequis**

### **Système**
- **Python** 3.11+
- **Docker** 20.10+ (pour déploiement production)
- **Git** 2.30+

### **Cloud & Données**
- **Google Cloud Project** avec BigQuery activé
- **Service Account** avec permissions lecture BigQuery
- **Credentials JSON** configurés

### **Ressources Serveur**
| Capacité | CPU | RAM | Stockage | Échantillons Max |
|----------|-----|-----|----------|------------------|
| Small    | 2   | 4GB | 10GB     | 2,000           |
| Medium   | 4   | 16GB| 50GB     | 10,000          |
| Large    | 8   | 64GB| 200GB    | 50,000          |
| XLarge   | 16+ | 256GB| 1TB+    | 200,000+        |

---

## ⚙️ **Installation Détaillée**

### **1. Configuration de l'Environnement**

```bash
# Cloner le repository
git clone https://github.com/sensei-ai/sensei-ai-suite.git
cd sensei-ai-suite

# Installer Poetry (gestionnaire de dépendances)
curl -sSL https://install.python-poetry.org | python3 -

# Installer les dépendances
poetry install
```

### **2. Configuration des Credentials**

```bash
# Créer le dossier credentials
mkdir -p credentials

# Copier votre fichier de service account Google Cloud
cp /path/to/your/service-account.json credentials/sensei-ai-service-account.json

# Configurer les variables d'environnement
cp config/environments/.env.development .env
```

### **3. Configuration Personnalisée**

Éditez le fichier `.env` selon votre environnement :

```env
# Environnement (development/staging/production)
ENVIRONMENT=development
SERVER_CAPACITY=medium

# Google Cloud
GOOGLE_CLOUD_PROJECT=votre-projet-gcp
GOOGLE_APPLICATION_CREDENTIALS=credentials/sensei-ai-service-account.json

# API
API_HOST=0.0.0.0
API_PORT=8000

# Sécurité
READ_ONLY_MODE=true
ENABLE_CORS=true
```

---

## 🏗️ **Architecture**

### **Structure Modulaire**

```
sensei-ai-suite/
├── 🎯 src/sensei/              # Code source principal
│   ├── 🌐 api/                 # API FastAPI
│   ├── 🤖 models/              # Modèles ML (Conversion, Channel, NLP)
│   ├── 📊 data/                # Clients BigQuery sécurisés
│   ├── 🔧 features/            # Feature engineering
│   ├── 🔄 pipelines/           # Pipelines d'entraînement
│   └── 🛠️ utils/               # Utilitaires (logging, sécurité)
├── ⚙️ config/                  # Configuration multi-environnements
├── 🧪 tests/                   # Tests unitaires et d'intégration
├── 📜 scripts/                 # Scripts d'entraînement et déploiement
├── 🐳 deployment/              # Docker, Kubernetes, CI/CD
├── 📊 models/                  # Modèles entraînés et registre
└── 📚 docs/                    # Documentation complète
```

### **Modèles IA Disponibles**

| Modèle | Description | Précision | Cas d'Usage |
|--------|-------------|-----------|-------------|
| 🎯 **Conversion** | Prédiction probabilité de conversion 90j | 85%+ | Scoring prospects, priorisation |
| 📞 **Channel** | Canal optimal (email/appel/réunion) + timing | 75%+ | Optimisation outreach |
| 🗣️ **NLP Signals** | Analyse sentiment, urgence, intérêt | 80%+ | Analyse transcriptions |

---

## 🚀 **Utilisation**

### **Démarrage de l'API**

```bash
# Développement (avec hot-reload)
./scripts/deployment/deploy.sh development small

# Production (avec Docker)
./scripts/deployment/deploy.sh production large

# Vérification
curl http://localhost:8000/health
```

### **Entraînement des Modèles**

```bash
# Entraînement complet (adapté à votre capacité serveur)
./scripts/training/train_models.sh --capacity medium

# Entraînement spécifique
./scripts/training/train_models.sh --models "conversion channel" --capacity large

# Test sans entraînement
./scripts/training/train_models.sh --dry-run
```

### **Exemples d'API**

#### **🎯 Prédiction de Conversion**

```python
import requests

# Prédiction de conversion
response = requests.post("http://localhost:8000/predict/conversion", json={
    "id_prospect": "PROSPECT_12345",
    "nb_interactions": 3,
    "duree_reponses_minutes": 45.0,
    "vitesse_reponse": "rapide",
    "budget_declare": "grand",
    "secteur_activite": "tech",
    "score_decouverte_moy_30j": 0.8
})

result = response.json()
print(f"Probabilité de conversion: {result['proba_conversion_90j']:.1%}")
print(f"Recommandation: {'Prioritaire' if result['prediction_conversion_90j'] else 'Standard'}")
```

#### **📞 Optimisation Canal**

```python
# Recommandation de canal optimal
response = requests.post("http://localhost:8000/predict/channel", json={
    "id_prospect": "PROSPECT_12345",
    "email": "<EMAIL>",
    "numero_telephone": "+33123456789",
    "nb_appels_historique": 2,
    "vitesse_reponse": "rapide"
})

result = response.json()
print(f"Canal optimal: {result['canal_optimal']}")
print(f"Timing optimal: {result['timing_optimal']}")
print(f"Confiance: {result['confiance']:.1%}")
```

#### **🗣️ Analyse NLP**

```python
# Analyse de transcription d'appel
response = requests.post("http://localhost:8000/predict/nlp", json={
    "callId": "CALL_789",
    "speakerId": 1,
    "content": "Bonjour, je suis très intéressé par vos solutions. Pouvez-vous me donner plus d'informations sur les prix et les fonctionnalités ?",
    "startDate": 1642680000
})

result = response.json()
print(f"Sentiment: {result['sentiment_score']:.2f}")
print(f"Urgence: {result['urgence_score']:.2f}")
print(f"Intérêt: {result['interet_score']:.2f}")
print(f"Thèmes: {', '.join(result['themes_detectes'])}")
```

---

## 🧪 **Tests et Validation**

### **Tests Automatisés**

```bash
# Tests rapides du système
python scripts/testing/quick_test.py

# Tests complets
python scripts/testing/test_system.py

# Tests unitaires avec couverture
poetry run pytest --cov=src/sensei --cov-report=html

# Tests d'intégration
poetry run pytest tests/integration/ -v
```

### **Validation Production**

```bash
# Vérification de la configuration
python -c "from config.settings import settings; print(f'Environment: {settings.environment.value}')"

# Test de connectivité BigQuery
python -c "from sensei.data.bq_client import SecureBigQueryClient; print('BigQuery OK')"

# Validation des modèles
curl http://localhost:8000/models/status | jq
```

---

## 🐳 **Déploiement Production**

### **Docker Compose (Recommandé)**

```bash
# Déploiement complet avec monitoring
docker-compose -f deployment/docker-compose.yml --profile monitoring up -d

# Vérification
curl http://localhost:8080/health
```

### **Kubernetes**

```bash
# Déploiement sur cluster K8s
kubectl apply -f deployment/k8s/

# Monitoring
kubectl get pods -l app=sensei-ai-suite
```

### **Cloud Run (Google Cloud)**

```bash
# Build et déploiement automatique
gcloud run deploy sensei-ai-suite \
  --source . \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated
```

---

## 📊 **Monitoring et Observabilité**

### **Métriques Disponibles**

- 🚀 **Performance** : Latence P95, P99, throughput
- 🎯 **Modèles** : Précision, drift, utilisation
- 🔒 **Sécurité** : Tentatives d'accès, erreurs d'auth
- 💾 **Ressources** : CPU, mémoire, stockage

### **Dashboards**

- **Grafana** : http://localhost:3000 (admin/admin)
- **Prometheus** : http://localhost:9090
- **API Docs** : http://localhost:8000/docs

### **Alertes**

```bash
# Configuration des alertes Slack/Email
cp deployment/monitoring/alerts.yml.example deployment/monitoring/alerts.yml
# Éditer avec vos webhooks
```

---

## 🔒 **Sécurité**

### **Principes de Sécurité**

- ✅ **Read-Only** : Accès lecture seule aux données production
- ✅ **Chiffrement** : TLS 1.3, chiffrement des données sensibles
- ✅ **Audit** : Logging complet des accès et opérations
- ✅ **Validation** : Validation stricte des inputs utilisateur
- ✅ **Isolation** : Conteneurisation et sandboxing

### **Configuration Sécurisée**

```bash
# Activation du mode sécurisé
export READ_ONLY_MODE=true
export ENABLE_CORS=false
export LOG_LEVEL=WARNING

# Rotation des credentials
./scripts/security/rotate_credentials.sh
```

---

## 🔧 **Maintenance**

### **Mise à Jour des Modèles**

```bash
# Réentraînement automatique (cron)
0 2 * * 0 /path/to/sensei-ai-suite/scripts/training/train_models.sh --capacity large

# Validation et déploiement
./scripts/deployment/validate_and_deploy.sh
```

### **Sauvegarde**

```bash
# Sauvegarde des modèles
./scripts/backup/backup_models.sh

# Restauration
./scripts/backup/restore_models.sh 2024-01-15
```

---

## 🤝 **Contribution**

### **Guide de Contribution**

1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/amazing-feature`)
3. **Développer** avec tests
4. **Tester** : `python scripts/testing/test_system.py`
5. **Commit** : `git commit -m 'feat: add amazing feature'`
6. **Push** : `git push origin feature/amazing-feature`
7. **Pull Request** avec description détaillée

### **Standards de Code**

```bash
# Formatage automatique
poetry run black src/ tests/
poetry run isort src/ tests/

# Vérification qualité
poetry run mypy src/
poetry run flake8 src/
```

---

## 📞 **Support**

### **Ressources**

- 📚 **Documentation** : [docs.sensei-ai.com](https://docs.sensei-ai.com)
- 🐛 **Issues** : [GitHub Issues](https://github.com/sensei-ai/sensei-ai-suite/issues)
- 💬 **Discussions** : [GitHub Discussions](https://github.com/sensei-ai/sensei-ai-suite/discussions)
- 📧 **Email** : <EMAIL>

### **SLA Production**

- 🎯 **Uptime** : 99.9%
- ⚡ **Latence** : <100ms P95
- 🔄 **Recovery** : <5min RTO
- 📊 **Monitoring** : 24/7

---

## 📄 **Licence**

Ce projet est sous licence **MIT**. Voir [LICENSE](LICENSE) pour plus de détails.

---

## 🏆 **Changelog**

### **v1.0.0** (2024-01-18)
- 🎉 **Release initiale production**
- ✅ **3 modèles IA** opérationnels
- 🚀 **API FastAPI** haute performance
- 🔒 **Sécurité enterprise** complète
- 📊 **Monitoring** intégré
- 🐳 **Déploiement** Docker/K8s ready

---

<div align="center">

**🚀 Prêt pour la Production | 🔒 Sécurisé | 📊 Monitoré | 🤖 IA Avancée**

*Développé avec ❤️ par l'équipe Sensei AI*

</div>
