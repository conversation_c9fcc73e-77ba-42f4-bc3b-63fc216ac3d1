# Sensei AI Suite v1.0

Optimisation de la prospection marketing & commerciale grâce à l'intelligence artificielle.

## 🎯 Objectifs

Ce projet implémente trois algorithmes d'IA pour optimiser la prospection :

1. **Scoring de conversion** (LightGBM) - Prédiction de la probabilité de conversion des prospects
2. **Recommandation de canal & timing** (CatBoost multiclasses) - Optimisation du canal et du moment de contact
3. **Analyse NLP des transcriptions** (Sentence-BERT + UMAP/HDBSCAN) - Extraction d'insights des appels Modjo

## 🏗️ Architecture

```
sensei-ai/
├── src/sensei/
│   ├── cli.py                # Interface CLI Typer
│   ├── data/                 # Connecteurs BigQuery
│   ├── features/             # Construction feature store
│   ├── models/               # Plugins ML
│   ├── pipelines/            # Scripts train/score
│   └── utils/                # Logging, secrets
├── tests/                    # Tests unitaires & intégration
├── pyproject.toml            # Configuration Poetry
├── Dockerfile                # Image production
└── .github/workflows/        # CI/CD
```

## 🔧 Installation

```bash
# Installation des dépendances
poetry install

# Configuration des secrets (Google Secret Manager)
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"
```

## 🚀 Utilisation

```bash
# Construction du feature store
sensei build-features --date 2024-01-15

# Entraînement d'un modèle
sensei train conversion

# Scoring
sensei score conversion --date 2024-01-15
```

## 🔒 Sécurité & RGPD

- Lecture seule sur `serving_layer.*`
- Écriture isolée sur `serving_layer_ml.*`
- Hashage SHA256 des identifiants PII
- Audit complet des prédictions
- Logs structurés avec rétention 30j

## 📊 Sources de données

- **Typeform** : Réponses prospects et abandons
- **HubSpot** : Activité CRM et scores
- **Modjo** : Transcriptions et scores IA d'appels

## 🎯 Objectifs de performance

- **Conversion Model** : AUC > 0.85, Precision@200 > 25%
- **Channel Model** : Accuracy multiclasse > 70%
- **NLP Signals** : Clustering cohérent des thématiques d'appels
