# 🚀 Guide de Démarrage Rapide - Sensei AI Suite

Ce guide vous permet de démarrer Sensei AI Suite en moins de 10 minutes.

## 📋 **Prérequis (2 minutes)**

### **Système**
```bash
# Vérifier Python 3.11+
python3 --version  # Doit être >= 3.11

# Vérifier Git
git --version

# Optionnel : Docker pour production
docker --version
```

### **Google Cloud**
- Projet GCP avec BigQuery activé
- Service Account avec permissions lecture BigQuery
- Fichier JSON des credentials

## ⚡ **Installation Express (3 minutes)**

### **1. <PERSON><PERSON><PERSON> et Installer**
```bash
# Cloner le projet
git clone https://github.com/sensei-ai/sensei-ai-suite.git
cd sensei-ai-suite

# Installation automatique
./scripts/deployment/deploy.sh development small
```

### **2. Configuration Minimale**
```bash
# Copier les credentials Google Cloud
mkdir -p credentials
cp /path/to/your/service-account.json credentials/sensei-ai-service-account.json

# Configuration de base
cp config/environments/.env.development .env

# Éditer le projet GCP (obligatoire)
echo "GOOGLE_CLOUD_PROJECT=votre-projet-gcp" >> .env
```

## 🧪 **Test Rapide (2 minutes)**

### **1. Vérifier l'Installation**
```bash
# Test du système
python scripts/testing/quick_test.py

# Résultat attendu : 4/4 tests passed
```

### **2. Démarrer l'API**
```bash
# Démarrage en mode développement
source venv/bin/activate
source .env
python -m uvicorn src.sensei.api.main:app --host 127.0.0.1 --port 8000 --reload
```

### **3. Test de l'API**
```bash
# Dans un autre terminal
curl http://localhost:8000/health

# Résultat attendu : {"status":"healthy",...}
```

## 🎯 **Premier Test de Prédiction (3 minutes)**

### **1. Prédiction de Conversion**
```bash
curl -X POST "http://localhost:8000/predict/conversion" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "TEST_001",
    "nb_interactions": 3,
    "vitesse_reponse": "rapide",
    "budget_declare": "grand",
    "secteur_activite": "tech"
  }'
```

**Résultat attendu :**
```json
{
  "id_prospect": "TEST_001",
  "proba_conversion_90j": 0.847,
  "prediction_conversion_90j": 1,
  "confiance": 0.847,
  "model_version": "20250118_123456",
  "predicted_at": "2025-01-18T12:34:56"
}
```

### **2. Analyse NLP**
```bash
curl -X POST "http://localhost:8000/predict/nlp" \
  -H "Content-Type: application/json" \
  -d '{
    "callId": "CALL_001",
    "speakerId": 1,
    "content": "Bonjour, je suis très intéressé par vos solutions. Pouvez-vous me donner plus d informations sur les prix ?",
    "startDate": **********
  }'
```

**Résultat attendu :**
```json
{
  "callId": "CALL_001",
  "sentiment_score": 0.65,
  "urgence_score": 0.4,
  "interet_score": 0.8,
  "themes_detectes": ["prix", "fonctionnalites"],
  "model_version": "20250118_123456",
  "analyzed_at": "2025-01-18T12:34:56"
}
```

### **3. Optimisation Canal**
```bash
curl -X POST "http://localhost:8000/predict/channel" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "TEST_001",
    "email": "<EMAIL>",
    "numero_telephone": "+33123456789",
    "nb_appels_historique": 2
  }'
```

**Résultat attendu :**
```json
{
  "id_prospect": "TEST_001",
  "canal_optimal": "appel",
  "timing_optimal": "matin",
  "confiance": 0.75,
  "model_version": "20250118_123456",
  "predicted_at": "2025-01-18T12:34:56"
}
```

## 📊 **Documentation Interactive**

Une fois l'API démarrée, accédez à :

- **Documentation Swagger** : http://localhost:8000/docs
- **Statut des modèles** : http://localhost:8000/models/status
- **Health check** : http://localhost:8000/health

## 🔧 **Résolution de Problèmes**

### **Erreur : Module 'config' not found**
```bash
# Vérifier le PYTHONPATH
export PYTHONPATH="${PWD}/src:${PWD}:${PYTHONPATH}"
```

### **Erreur : BigQuery permissions**
```bash
# Vérifier les credentials
python -c "from sensei.data.bq_client import SecureBigQueryClient; print('OK')"

# Vérifier le projet
echo $GOOGLE_CLOUD_PROJECT
```

### **Erreur : Port déjà utilisé**
```bash
# Changer le port
python -m uvicorn src.sensei.api.main:app --port 8001
```

### **Modèles non chargés**
```bash
# Vérifier le registre des modèles
cat models/registry.json

# Forcer le rechargement
curl -X POST http://localhost:8000/models/reload
```

## 🚀 **Prochaines Étapes**

### **1. Entraînement avec Vraies Données**
```bash
# Entraînement complet (nécessite accès BigQuery)
./scripts/training/train_models.sh --capacity medium

# Vérifier les résultats
curl http://localhost:8000/models/status | jq
```

### **2. Déploiement Production**
```bash
# Docker Compose
./scripts/deployment/deploy.sh production large

# Ou Kubernetes
kubectl apply -f deployment/k8s/
```

### **3. Monitoring**
```bash
# Démarrer avec monitoring
docker-compose -f deployment/docker-compose.yml --profile monitoring up -d

# Accéder aux dashboards
# Grafana: http://localhost:3000 (admin/admin)
# Prometheus: http://localhost:9090
```

## 📚 **Ressources Utiles**

- **README complet** : [README.md](README.md)
- **Documentation API** : http://localhost:8000/docs
- **Tests système** : `python scripts/testing/test_system.py`
- **Configuration** : [config/settings.py](config/settings.py)
- **Exemples** : [examples/](examples/)

## 🆘 **Support**

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** : `tail -f logs/sensei-ai.log`
2. **Testez le système** : `python scripts/testing/quick_test.py`
3. **Consultez les issues** : [GitHub Issues](https://github.com/sensei-ai/sensei-ai-suite/issues)
4. **Contactez l'équipe** : <EMAIL>

---

**🎉 Félicitations ! Sensei AI Suite est maintenant opérationnel !**

Vous pouvez maintenant :
- ✅ Faire des prédictions de conversion
- ✅ Optimiser vos canaux de communication  
- ✅ Analyser vos transcriptions d'appels
- ✅ Intégrer l'API dans vos applications
