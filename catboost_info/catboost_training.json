{"meta": {"test_sets": ["test"], "test_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "learn_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 20, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [0.7569499603, 1.603791887], "iteration": 0, "passed_time": 0.05853190282, "remaining_time": 1.112106154, "test": [0.7619047619, 1.603735194]}, {"learn": [0.7569499603, 1.600545163], "iteration": 1, "passed_time": 0.05931831427, "remaining_time": 0.5338648284, "test": [0.7619047619, 1.600381668]}, {"learn": [0.7569499603, 1.597412513], "iteration": 2, "passed_time": 0.05988539383, "remaining_time": 0.339350565, "test": [0.7619047619, 1.597187443]}, {"learn": [0.7926926132, 1.589115465], "iteration": 3, "passed_time": 0.06077547125, "remaining_time": 0.243101885, "test": [0.7841269841, 1.588797262]}, {"learn": [0.7561556791, 1.583625339], "iteration": 4, "passed_time": 0.06175921472, "remaining_time": 0.1852776441, "test": [0.7619047619, 1.583250868]}, {"learn": [0.7569499603, 1.578183582], "iteration": 5, "passed_time": 0.0622490448, "remaining_time": 0.1452477712, "test": [0.7619047619, 1.577752932]}, {"learn": [0.7569499603, 1.574391254], "iteration": 6, "passed_time": 0.06283633256, "remaining_time": 0.1166960462, "test": [0.7619047619, 1.573917496]}, {"learn": [0.7569499603, 1.570631478], "iteration": 7, "passed_time": 0.06331045441, "remaining_time": 0.09496568162, "test": [0.7619047619, 1.570114712]}, {"learn": [0.7569499603, 1.565150307], "iteration": 8, "passed_time": 0.0637534098, "remaining_time": 0.0779208342, "test": [0.7619047619, 1.564391132]}]}