{"meta": {"test_sets": ["test"], "test_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "learn_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 20, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [0.7617156473, 1.603679351], "iteration": 0, "passed_time": 0.05578155123, "remaining_time": 1.059849473, "test": [0.7682539683, 1.603627903]}, {"learn": [0.7617156473, 1.600381593], "iteration": 1, "passed_time": 0.05661304019, "remaining_time": 0.5095173617, "test": [0.7682539683, 1.600288764]}, {"learn": [0.7617156473, 1.597180659], "iteration": 2, "passed_time": 0.05716674118, "remaining_time": 0.3239448667, "test": [0.7682539683, 1.597011438]}, {"learn": [0.7609213662, 1.588714112], "iteration": 3, "passed_time": 0.05794477252, "remaining_time": 0.2317790901, "test": [0.7682539683, 1.588538151]}, {"learn": [0.7609213662, 1.58311516], "iteration": 4, "passed_time": 0.05849743185, "remaining_time": 0.1754922955, "test": [0.7682539683, 1.582888648]}, {"learn": [0.7617156473, 1.577559011], "iteration": 5, "passed_time": 0.059113882, "remaining_time": 0.1379323913, "test": [0.7682539683, 1.576923868]}]}