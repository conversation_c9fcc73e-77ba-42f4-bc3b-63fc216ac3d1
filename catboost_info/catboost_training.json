{"meta": {"test_sets": ["test"], "test_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "learn_metrics": [{"best_value": "Max", "name": "Accuracy"}, {"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 20, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [0.7688641779, 1.603551822], "iteration": 0, "passed_time": 0.05748315348, "remaining_time": 1.092179916, "test": [0.726984127, 1.603926331]}, {"learn": [0.7688641779, 1.600194068], "iteration": 1, "passed_time": 0.05903677813, "remaining_time": 0.5313310031, "test": [0.726984127, 1.60068701]}, {"learn": [0.7688641779, 1.596927522], "iteration": 2, "passed_time": 0.0598066736, "remaining_time": 0.3389044837, "test": [0.726984127, 1.597638078]}, {"learn": [0.8030182685, 1.588421453], "iteration": 3, "passed_time": 0.06087211859, "remaining_time": 0.2434884744, "test": [0.7555555556, 1.58939056]}, {"learn": [0.7680698967, 1.582698935], "iteration": 4, "passed_time": 0.06175772548, "remaining_time": 0.1852731764, "test": [0.726984127, 1.58403509]}, {"learn": [0.7688641779, 1.576887337], "iteration": 5, "passed_time": 0.06247774462, "remaining_time": 0.1457814041, "test": [0.726984127, 1.57860187]}, {"learn": [0.7680698967, 1.572962291], "iteration": 6, "passed_time": 0.06388657375, "remaining_time": 0.1186464941, "test": [0.726984127, 1.574852288]}, {"learn": [0.7680698967, 1.569070992], "iteration": 7, "passed_time": 0.06443883843, "remaining_time": 0.09665825765, "test": [0.726984127, 1.571135419]}, {"learn": [0.7688641779, 1.563368754], "iteration": 8, "passed_time": 0.06508856404, "remaining_time": 0.07955268939, "test": [0.726984127, 1.565805449]}]}