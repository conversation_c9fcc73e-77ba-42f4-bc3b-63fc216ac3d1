{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 50, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [1.599468494], "iteration": 0, "passed_time": 0.0001662477851, "remaining_time": 0.008146141472}, {"learn": [1.589710399], "iteration": 1, "passed_time": 0.0002821212414, "remaining_time": 0.006770909793}, {"learn": [1.581914477], "iteration": 2, "passed_time": 0.0005539926193, "remaining_time": 0.008679217703}, {"learn": [1.573726842], "iteration": 3, "passed_time": 0.0007732396984, "remaining_time": 0.008892256531}, {"learn": [1.566369658], "iteration": 4, "passed_time": 0.001054485951, "remaining_time": 0.009490373563}, {"learn": [1.560243692], "iteration": 5, "passed_time": 0.001248566699, "remaining_time": 0.009156155793}, {"learn": [1.556274278], "iteration": 6, "passed_time": 0.001420897737, "remaining_time": 0.00872837181}, {"learn": [1.551260534], "iteration": 7, "passed_time": 0.001667977778, "remaining_time": 0.008756883335}, {"learn": [1.546632906], "iteration": 8, "passed_time": 0.00191055788, "remaining_time": 0.008703652563}, {"learn": [1.543450492], "iteration": 9, "passed_time": 0.002178429311, "remaining_time": 0.008713717243}, {"learn": [1.53987414], "iteration": 10, "passed_time": 0.002416176143, "remaining_time": 0.00856644269}, {"learn": [1.537107462], "iteration": 11, "passed_time": 0.00260629861, "remaining_time": 0.008253278933}, {"learn": [1.534097066], "iteration": 12, "passed_time": 0.002820462424, "remaining_time": 0.008027469976}, {"learn": [1.529745118], "iteration": 13, "passed_time": 0.003073667384, "remaining_time": 0.00790371613}, {"learn": [1.527362945], "iteration": 14, "passed_time": 0.003290289498, "remaining_time": 0.007677342162}, {"learn": [1.524226985], "iteration": 15, "passed_time": 0.003489203514, "remaining_time": 0.007414557468}, {"learn": [1.522284696], "iteration": 16, "passed_time": 0.003728575325, "remaining_time": 0.00723782269}, {"learn": [1.520619473], "iteration": 17, "passed_time": 0.003921697752, "remaining_time": 0.006971907116}, {"learn": [1.519435289], "iteration": 18, "passed_time": 0.004173902726, "remaining_time": 0.006810051816}, {"learn": [1.518084981], "iteration": 19, "passed_time": 0.004472732078, "remaining_time": 0.006709098117}, {"learn": [1.516433201], "iteration": 20, "passed_time": 0.004695854105, "remaining_time": 0.006484750907}, {"learn": [1.515738115], "iteration": 21, "passed_time": 0.004873226742, "remaining_time": 0.006202288581}, {"learn": [1.513491018], "iteration": 22, "passed_time": 0.005100598713, "remaining_time": 0.005987659359}, {"learn": [1.510752995], "iteration": 23, "passed_time": 0.00535726196, "remaining_time": 0.005803700457}, {"learn": [1.509548224], "iteration": 24, "passed_time": 0.005569509133, "remaining_time": 0.005569509133}, {"learn": [1.508842189], "iteration": 25, "passed_time": 0.005738465215, "remaining_time": 0.005297044814}, {"learn": [1.507912483], "iteration": 26, "passed_time": 0.005888254886, "remaining_time": 0.005015920829}, {"learn": [1.505545661], "iteration": 27, "passed_time": 0.006106876973, "remaining_time": 0.004798260479}, {"learn": [1.504202703], "iteration": 28, "passed_time": 0.006372748431, "remaining_time": 0.004614748864}, {"learn": [1.503553037], "iteration": 29, "passed_time": 0.006543871151, "remaining_time": 0.004362580768}, {"learn": [1.502728866], "iteration": 30, "passed_time": 0.006783117964, "remaining_time": 0.004157394881}, {"learn": [1.50138045], "iteration": 31, "passed_time": 0.007061280925, "remaining_time": 0.00397197052}, {"learn": [1.500762829], "iteration": 32, "passed_time": 0.00723652859, "remaining_time": 0.003727908668}, {"learn": [1.500072399], "iteration": 33, "passed_time": 0.007395234809, "remaining_time": 0.003480110498}, {"learn": [1.499477844], "iteration": 34, "passed_time": 0.007576899055, "remaining_time": 0.003247242452}, {"learn": [1.498589854], "iteration": 35, "passed_time": 0.007754230026, "remaining_time": 0.003015533899}, {"learn": [1.495824396], "iteration": 36, "passed_time": 0.007958143976, "remaining_time": 0.00279610464}, {"learn": [1.495256866], "iteration": 37, "passed_time": 0.008205390682, "remaining_time": 0.002591176005}, {"learn": [1.494049623], "iteration": 38, "passed_time": 0.008453637375, "remaining_time": 0.00238435926}, {"learn": [1.493053507], "iteration": 39, "passed_time": 0.008659759629, "remaining_time": 0.002164939907}, {"learn": [1.49240145], "iteration": 40, "passed_time": 0.0088470488, "remaining_time": 0.001942035103}, {"learn": [1.491984006], "iteration": 41, "passed_time": 0.009118378519, "remaining_time": 0.001736834004}, {"learn": [1.491064729], "iteration": 42, "passed_time": 0.009376125085, "remaining_time": 0.001526345944}, {"learn": [1.489204323], "iteration": 43, "passed_time": 0.009626788412, "remaining_time": 0.001312743874}, {"learn": [1.488687372], "iteration": 44, "passed_time": 0.009815119236, "remaining_time": 0.001090568804}, {"learn": [1.487784726], "iteration": 45, "passed_time": 0.01006469924, "remaining_time": 0.0008751912387}, {"learn": [1.485065337], "iteration": 46, "passed_time": 0.01025361339, "remaining_time": 0.0006544859613}, {"learn": [1.483021519], "iteration": 47, "passed_time": 0.0104135696, "remaining_time": 0.0004338987332}, {"learn": [1.482646889], "iteration": 48, "passed_time": 0.01056769254, "remaining_time": 0.0002156671948}, {"learn": [1.481808507], "iteration": 49, "passed_time": 0.01081518925, "remaining_time": 0}]}