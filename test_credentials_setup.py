#!/usr/bin/env python3
"""
Test de validation des credentials Google Cloud.
Vérifie que les credentials sont correctement configurés avant les tests BigQuery.
"""

import os
import sys
import json
from pathlib import Path

def test_credentials_file():
    """Test de présence et validité du fichier de credentials."""
    print("🔐 Test du fichier de credentials...")
    
    # Vérification de la variable d'environnement
    creds_path = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
    
    if not creds_path:
        print("❌ Variable GOOGLE_APPLICATION_CREDENTIALS non définie")
        print("💡 Exécutez : source scripts/configure_credentials.sh")
        return False
    
    print(f"✅ Variable définie: {creds_path}")
    
    # Vérification de l'existence du fichier
    if not os.path.exists(creds_path):
        print(f"❌ Fichier non trouvé: {creds_path}")
        return False
    
    print(f"✅ Fichier trouvé: {os.path.basename(creds_path)}")
    
    # Validation du JSON
    try:
        with open(creds_path, 'r') as f:
            creds_data = json.load(f)
        
        # Vérification des champs requis
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in creds_data]
        
        if missing_fields:
            print(f"❌ Champs manquants: {missing_fields}")
            return False
        
        print("✅ Structure JSON valide")
        print(f"  📋 Type: {creds_data.get('type')}")
        print(f"  📋 Projet: {creds_data.get('project_id')}")
        print(f"  📧 Email: {creds_data.get('client_email')}")
        
        # Vérification du projet
        if creds_data.get('project_id') != 'sensei-ai-dev':
            print(f"⚠️  Projet différent: {creds_data.get('project_id')} (attendu: sensei-ai-dev)")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON invalide: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur lecture: {e}")
        return False

def test_google_auth():
    """Test d'authentification Google."""
    print("\n🔑 Test d'authentification Google...")
    
    try:
        import google.auth
        from google.auth import default
        
        # Tentative d'authentification
        credentials, project = default()
        
        print("✅ Authentification réussie")
        print(f"  📋 Projet détecté: {project}")
        print(f"  🔑 Type credentials: {type(credentials).__name__}")
        
        # Vérification du projet
        if project != 'sensei-ai-dev':
            print(f"⚠️  Projet différent: {project} (attendu: sensei-ai-dev)")
        
        return True
        
    except Exception as e:
        print(f"❌ Échec authentification: {e}")
        return False

def test_bigquery_client():
    """Test de création du client BigQuery."""
    print("\n🗄️ Test du client BigQuery...")
    
    try:
        from google.cloud import bigquery
        
        # Création du client
        client = bigquery.Client()
        
        print("✅ Client BigQuery créé")
        print(f"  📋 Projet: {client.project}")
        print(f"  🌍 Location: {client.location}")
        
        return True
        
    except Exception as e:
        print(f"❌ Échec client BigQuery: {e}")
        return False

def test_basic_query():
    """Test d'une requête BigQuery basique."""
    print("\n🔍 Test de requête basique...")
    
    try:
        from google.cloud import bigquery
        
        client = bigquery.Client()
        
        # Requête simple pour tester la connexion
        query = "SELECT 1 as test_value, CURRENT_TIMESTAMP() as test_time"
        
        print("🔍 Exécution de la requête de test...")
        
        # Exécution avec timeout court
        query_job = client.query(query)
        results = query_job.result(timeout=30)
        
        # Récupération du résultat
        for row in results:
            print(f"✅ Résultat: test_value={row.test_value}, time={row.test_time}")
        
        print("✅ Requête BigQuery réussie")
        
        return True
        
    except Exception as e:
        print(f"❌ Échec requête: {e}")
        return False

def test_sensei_project_access():
    """Test d'accès spécifique au projet Sensei."""
    print("\n🏢 Test d'accès projet Sensei...")
    
    try:
        from google.cloud import bigquery
        
        client = bigquery.Client()
        
        # Vérification de l'accès aux datasets
        datasets = list(client.list_datasets(max_results=10))
        
        print(f"✅ Accès aux datasets: {len(datasets)} trouvés")
        
        dataset_names = [ds.dataset_id for ds in datasets]
        print(f"  📊 Datasets: {dataset_names}")
        
        # Vérification des datasets attendus
        expected_datasets = ['raw_data', 'serving_layer_ml', 'analytics']
        found_expected = [ds for ds in expected_datasets if ds in dataset_names]
        
        print(f"  ✅ Datasets Sensei trouvés: {found_expected}")
        
        if len(found_expected) == 0:
            print("⚠️  Aucun dataset Sensei trouvé - vérifiez les permissions")
        
        return len(found_expected) > 0
        
    except Exception as e:
        print(f"❌ Échec accès projet: {e}")
        return False

def main():
    """Fonction principale de validation."""
    print("🚀 Validation des credentials Google Cloud - Sensei AI Suite")
    print("=" * 70)
    
    tests = [
        ("Fichier de credentials", test_credentials_file),
        ("Authentification Google", test_google_auth),
        ("Client BigQuery", test_bigquery_client),
        ("Requête basique", test_basic_query),
        ("Accès projet Sensei", test_sensei_project_access),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 Résumé de la validation:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Credentials configurés et validés!")
        print("🚀 Prêt pour les tests BigQuery en production!")
        return 0
    elif passed >= 3:
        print("⚠️  Configuration partielle - certains tests peuvent échouer")
        print("💡 Vérifiez les permissions du service account")
        return 0
    else:
        print("❌ Configuration incomplète")
        print("🔧 Suivez les instructions dans credentials/README.md")
        return 1

if __name__ == "__main__":
    sys.exit(main())
