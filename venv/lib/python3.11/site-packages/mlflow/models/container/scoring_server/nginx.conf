worker_processes 1;
daemon off; # Prevent forking


pid /tmp/nginx.pid;
error_log /var/log/nginx/error.log;

events {
  # defaults
}

http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;
  access_log /var/log/nginx/access.log combined;
  
  upstream uvicorn {
    server 127.0.0.1:8000;
  }

  server {
    listen 8080 deferred;
    client_max_body_size 5m;

    keepalive_timeout 75;

    location ~ ^/(ping|invocations) {
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Host $http_host;
      proxy_redirect off;
      proxy_pass http://uvicorn;
      client_max_body_size 100m;
    }

    location / {
      return 404 "{}";
    }
  }
}
