
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: service.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2
  from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
  from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from . import assessments_pb2 as assessments__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rservice.proto\x12\x06mlflow\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\x1a\x1egoogle/protobuf/duration.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x11\x61ssessments.proto\"H\n\x06Metric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\"#\n\x05Param\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"f\n\x03Run\x12\x1d\n\x04info\x18\x01 \x01(\x0b\x32\x0f.mlflow.RunInfo\x12\x1d\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x0f.mlflow.RunData\x12!\n\x06inputs\x18\x03 \x01(\x0b\x32\x11.mlflow.RunInputs\"g\n\x07RunData\x12\x1f\n\x07metrics\x18\x01 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x1d\n\x06params\x18\x02 \x03(\x0b\x32\r.mlflow.Param\x12\x1c\n\x04tags\x18\x03 \x03(\x0b\x32\x0e.mlflow.RunTag\"9\n\tRunInputs\x12,\n\x0e\x64\x61taset_inputs\x18\x01 \x03(\x0b\x32\x14.mlflow.DatasetInput\"$\n\x06RunTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"+\n\rExperimentTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xdd\x01\n\x07RunInfo\x12\x0e\n\x06run_id\x18\x0f \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x10\n\x08run_name\x18\x03 \x01(\t\x12\x15\n\rexperiment_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x06 \x01(\t\x12!\n\x06status\x18\x07 \x01(\x0e\x32\x11.mlflow.RunStatus\x12\x12\n\nstart_time\x18\x08 \x01(\x03\x12\x10\n\x08\x65nd_time\x18\t \x01(\x03\x12\x14\n\x0c\x61rtifact_uri\x18\r \x01(\t\x12\x17\n\x0flifecycle_stage\x18\x0e \x01(\t\"\xbb\x01\n\nExperiment\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x19\n\x11\x61rtifact_location\x18\x03 \x01(\t\x12\x17\n\x0flifecycle_stage\x18\x04 \x01(\t\x12\x18\n\x10last_update_time\x18\x05 \x01(\x03\x12\x15\n\rcreation_time\x18\x06 \x01(\x03\x12#\n\x04tags\x18\x07 \x03(\x0b\x32\x15.mlflow.ExperimentTag\"V\n\x0c\x44\x61tasetInput\x12\x1e\n\x04tags\x18\x01 \x03(\x0b\x32\x10.mlflow.InputTag\x12&\n\x07\x64\x61taset\x18\x02 \x01(\x0b\x32\x0f.mlflow.DatasetB\x04\xf8\x86\x19\x01\"2\n\x08InputTag\x12\x11\n\x03key\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\"\x85\x01\n\x07\x44\x61taset\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06\x64igest\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x19\n\x0bsource_type\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x04 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06schema\x18\x05 \x01(\t\x12\x0f\n\x07profile\x18\x06 \x01(\t\"\xb6\x01\n\x10\x43reateExperiment\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x19\n\x11\x61rtifact_location\x18\x02 \x01(\t\x12#\n\x04tags\x18\x03 \x03(\x0b\x32\x15.mlflow.ExperimentTag\x1a!\n\x08Response\x12\x15\n\rexperiment_id\x18\x01 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xfe\x01\n\x11SearchExperiments\x12\x13\n\x0bmax_results\x18\x01 \x01(\x03\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x0e\n\x06\x66ilter\x18\x03 \x01(\t\x12\x10\n\x08order_by\x18\x04 \x03(\t\x12#\n\tview_type\x18\x05 \x01(\x0e\x32\x10.mlflow.ViewType\x1aL\n\x08Response\x12\'\n\x0b\x65xperiments\x18\x01 \x03(\x0b\x32\x12.mlflow.Experiment\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8d\x01\n\rGetExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x32\n\x08Response\x12&\n\nexperiment\x18\x01 \x01(\x0b\x32\x12.mlflow.Experiment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"h\n\x10\x44\x65leteExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"i\n\x11RestoreExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"z\n\x10UpdateExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xca\x01\n\tCreateRun\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08run_name\x18\x03 \x01(\t\x12\x12\n\nstart_time\x18\x07 \x01(\x03\x12\x1c\n\x04tags\x18\t \x03(\x0b\x32\x0e.mlflow.RunTag\x1a$\n\x08Response\x12\x18\n\x03run\x18\x01 \x01(\x0b\x32\x0b.mlflow.Run:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xd0\x01\n\tUpdateRun\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12!\n\x06status\x18\x02 \x01(\x0e\x32\x11.mlflow.RunStatus\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\x03\x12\x10\n\x08run_name\x18\x05 \x01(\t\x1a-\n\x08Response\x12!\n\x08run_info\x18\x01 \x01(\x0b\x32\x0f.mlflow.RunInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"Z\n\tDeleteRun\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"[\n\nRestoreRun\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xb8\x01\n\tLogMetric\x12\x0e\n\x06run_id\x18\x06 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\x01\x42\x04\xf8\x86\x19\x01\x12\x17\n\ttimestamp\x18\x04 \x01(\x03\x42\x04\xf8\x86\x19\x01\x12\x0f\n\x04step\x18\x05 \x01(\x03:\x01\x30\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8d\x01\n\x08LogParam\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x90\x01\n\x10SetExperimentTag\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8b\x01\n\x06SetTag\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"m\n\tDeleteTag\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"}\n\x06GetRun\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x1a$\n\x08Response\x12\x18\n\x03run\x18\x01 \x01(\x0b\x32\x0b.mlflow.Run:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x98\x02\n\nSearchRuns\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x12\x0e\n\x06\x66ilter\x18\x04 \x01(\t\x12\x34\n\rrun_view_type\x18\x03 \x01(\x0e\x32\x10.mlflow.ViewType:\x0b\x41\x43TIVE_ONLY\x12\x19\n\x0bmax_results\x18\x05 \x01(\x05:\x04\x31\x30\x30\x30\x12\x10\n\x08order_by\x18\x06 \x03(\t\x12\x12\n\npage_token\x18\x07 \x01(\t\x1a>\n\x08Response\x12\x19\n\x04runs\x18\x01 \x03(\x0b\x32\x0b.mlflow.Run\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xd8\x01\n\rListArtifacts\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aV\n\x08Response\x12\x10\n\x08root_uri\x18\x01 \x01(\t\x12\x1f\n\x05\x66iles\x18\x02 \x03(\x0b\x32\x10.mlflow.FileInfo\x12\x17\n\x0fnext_page_token\x18\x03 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\";\n\x08\x46ileInfo\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x0e\n\x06is_dir\x18\x02 \x01(\x08\x12\x11\n\tfile_size\x18\x03 \x01(\x03\"\xea\x01\n\x10GetMetricHistory\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x18\n\nmetric_key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\npage_token\x18\x04 \x01(\t\x12\x13\n\x0bmax_results\x18\x05 \x01(\x05\x1a\x44\n\x08Response\x12\x1f\n\x07metrics\x18\x01 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"a\n\x0fMetricWithRunId\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x0e\n\x06run_id\x18\x05 \x01(\t\"\xe7\x01\n\x1cGetMetricHistoryBulkInterval\x12\x0f\n\x07run_ids\x18\x01 \x03(\t\x12\x18\n\nmetric_key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\nstart_step\x18\x03 \x01(\x05\x12\x10\n\x08\x65nd_step\x18\x04 \x01(\x05\x12\x13\n\x0bmax_results\x18\x05 \x01(\x05\x1a\x34\n\x08Response\x12(\n\x07metrics\x18\x01 \x03(\x0b\x32\x17.mlflow.MetricWithRunId:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xb1\x01\n\x08LogBatch\x12\x0e\n\x06run_id\x18\x01 \x01(\t\x12\x1f\n\x07metrics\x18\x02 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x1d\n\x06params\x18\x03 \x03(\x0b\x32\r.mlflow.Param\x12\x1c\n\x04tags\x18\x04 \x03(\x0b\x32\x0e.mlflow.RunTag\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"g\n\x08LogModel\x12\x0e\n\x06run_id\x18\x01 \x01(\t\x12\x12\n\nmodel_json\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x82\x01\n\tLogInputs\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12&\n\x08\x64\x61tasets\x18\x02 \x03(\x0b\x32\x14.mlflow.DatasetInput\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x95\x01\n\x13GetExperimentByName\x12\x1d\n\x0f\x65xperiment_name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x32\n\x08Response\x12&\n\nexperiment\x18\x01 \x01(\x0b\x32\x12.mlflow.Experiment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xba\x01\n\x10\x41ssessmentSource\x12>\n\x0bsource_type\x18\x01 \x01(\x0e\x32#.mlflow.AssessmentSource.SourceTypeB\x04\xf8\x86\x19\x01\x12\x17\n\tsource_id\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\"M\n\nSourceType\x12\x1b\n\x17SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05HUMAN\x10\x01\x12\r\n\tLLM_JUDGE\x10\x02\x12\x08\n\x04\x43ODE\x10\x03\"<\n\x0f\x41ssessmentError\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\"\xb9\x01\n\x10\x43reateAssessment\x12\x38\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.AssessmentB\x04\xf8\x86\x19\x01\x1a>\n\x08Response\x12\x32\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.Assessment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xf0\x01\n\x10UpdateAssessment\x12\x38\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.AssessmentB\x04\xf8\x86\x19\x01\x12\x35\n\x0bupdate_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMaskB\x04\xf8\x86\x19\x01\x1a>\n\x08Response\x12\x32\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.Assessment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x80\x01\n\x10\x44\x65leteAssessment\x12\x16\n\x08trace_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x1b\n\rassessment_id\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xe4\x01\n\tTraceInfo\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x15\n\rexperiment_id\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\x12\x19\n\x11\x65xecution_time_ms\x18\x04 \x01(\x03\x12#\n\x06status\x18\x05 \x01(\x0e\x32\x13.mlflow.TraceStatus\x12\x36\n\x10request_metadata\x18\x06 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x07 \x03(\x0b\x32\x10.mlflow.TraceTag\"2\n\x14TraceRequestMetadata\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"&\n\x08TraceTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xf1\x01\n\nStartTrace\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x36\n\x10request_metadata\x18\x03 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x04 \x03(\x0b\x32\x10.mlflow.TraceTag\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x91\x02\n\x08\x45ndTrace\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12#\n\x06status\x18\x03 \x01(\x0e\x32\x13.mlflow.TraceStatus\x12\x36\n\x10request_metadata\x18\x04 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x05 \x03(\x0b\x32\x10.mlflow.TraceTag\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x82\x01\n\x0cGetTraceInfo\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"y\n\x0eGetTraceInfoV3\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x1a(\n\x08Response\x12\x1c\n\x05trace\x18\x01 \x01(\x0b\x32\r.mlflow.Trace:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xeb\x01\n\x0cSearchTraces\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x12\x0e\n\x06\x66ilter\x18\x02 \x01(\t\x12\x18\n\x0bmax_results\x18\x03 \x01(\x05:\x03\x31\x30\x30\x12\x10\n\x08order_by\x18\x04 \x03(\t\x12\x12\n\npage_token\x18\x05 \x01(\t\x1a\x46\n\x08Response\x12!\n\x06traces\x18\x01 \x03(\x0b\x32\x11.mlflow.TraceInfo\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xc3\x01\n\x0c\x44\x65leteTraces\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x1c\n\x14max_timestamp_millis\x18\x02 \x01(\x03\x12\x12\n\nmax_traces\x18\x03 \x01(\x05\x12\x13\n\x0brequest_ids\x18\x04 \x03(\t\x1a\"\n\x08Response\x12\x16\n\x0etraces_deleted\x18\x01 \x01(\x05:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"v\n\x0bSetTraceTag\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"j\n\x0e\x44\x65leteTraceTag\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"0\n\x05Trace\x12\'\n\ntrace_info\x18\x01 \x01(\x0b\x32\x13.mlflow.TraceInfoV3\"\xb6\x03\n\rTraceLocation\x12\x35\n\x04type\x18\x01 \x01(\x0e\x32\'.mlflow.TraceLocation.TraceLocationType\x12K\n\x11mlflow_experiment\x18\x02 \x01(\x0b\x32..mlflow.TraceLocation.MlflowExperimentLocationH\x00\x12G\n\x0finference_table\x18\x03 \x01(\x0b\x32,.mlflow.TraceLocation.InferenceTableLocationH\x00\x1a\x31\n\x18MlflowExperimentLocation\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x1a\x31\n\x16InferenceTableLocation\x12\x17\n\x0f\x66ull_table_name\x18\x01 \x01(\t\"d\n\x11TraceLocationType\x12#\n\x1fTRACE_LOCATION_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11MLFLOW_EXPERIMENT\x10\x01\x12\x13\n\x0fINFERENCE_TABLE\x10\x02\x42\x0c\n\nidentifier\"\xe8\x04\n\x0bTraceInfoV3\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x19\n\x11\x63lient_request_id\x18\x02 \x01(\t\x12-\n\x0etrace_location\x18\x03 \x01(\x0b\x32\x15.mlflow.TraceLocation\x12\x0f\n\x07request\x18\x04 \x01(\t\x12\x10\n\x08response\x18\x05 \x01(\t\x12\x30\n\x0crequest_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x12\x65xecution_duration\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12(\n\x05state\x18\x08 \x01(\x0e\x32\x19.mlflow.TraceInfoV3.State\x12>\n\x0etrace_metadata\x18\t \x03(\x0b\x32&.mlflow.TraceInfoV3.TraceMetadataEntry\x12\x33\n\x0b\x61ssessments\x18\n \x03(\x0b\x32\x1e.mlflow.assessments.Assessment\x12+\n\x04tags\x18\x0b \x03(\x0b\x32\x1d.mlflow.TraceInfoV3.TagsEntry\x1a\x34\n\x12TraceMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\"h\n\x0e\x44\x61tasetSummary\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\x04name\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06\x64igest\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0f\n\x07\x63ontext\x18\x04 \x01(\t\"\x94\x01\n\x0eSearchDatasets\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x1a=\n\x08Response\x12\x31\n\x11\x64\x61taset_summaries\x18\x01 \x03(\x0b\x32\x16.mlflow.DatasetSummary:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]*6\n\x08ViewType\x12\x0f\n\x0b\x41\x43TIVE_ONLY\x10\x01\x12\x10\n\x0c\x44\x45LETED_ONLY\x10\x02\x12\x07\n\x03\x41LL\x10\x03*I\n\nSourceType\x12\x0c\n\x08NOTEBOOK\x10\x01\x12\x07\n\x03JOB\x10\x02\x12\x0b\n\x07PROJECT\x10\x03\x12\t\n\x05LOCAL\x10\x04\x12\x0c\n\x07UNKNOWN\x10\xe8\x07*M\n\tRunStatus\x12\x0b\n\x07RUNNING\x10\x01\x12\r\n\tSCHEDULED\x10\x02\x12\x0c\n\x08\x46INISHED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\n\n\x06KILLED\x10\x05*O\n\x0bTraceStatus\x12\x1c\n\x18TRACE_STATUS_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\x32\xde\'\n\rMlflowService\x12\xa6\x01\n\x13getExperimentByName\x12\x1b.mlflow.GetExperimentByName\x1a$.mlflow.GetExperimentByName.Response\"L\xf2\x86\x19H\n,\n\x03GET\x12\x1f/mlflow/experiments/get-by-name\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Get Experiment By Name\x12\x94\x01\n\x10\x63reateExperiment\x12\x18.mlflow.CreateExperiment\x1a!.mlflow.CreateExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x11\x43reate Experiment\x12\xc1\x01\n\x11searchExperiments\x12\x19.mlflow.SearchExperiments\x1a\".mlflow.SearchExperiments.Response\"m\xf2\x86\x19i\n(\n\x04POST\x12\x1a/mlflow/experiments/search\x1a\x04\x08\x02\x10\x00\n\'\n\x03GET\x12\x1a/mlflow/experiments/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Search Experiments\x12\x88\x01\n\rgetExperiment\x12\x15.mlflow.GetExperiment\x1a\x1e.mlflow.GetExperiment.Response\"@\xf2\x86\x19\x38\n$\n\x03GET\x12\x17/mlflow/experiments/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x0eGet Experiment\xba\x8c\x19\x00\x12\x94\x01\n\x10\x64\x65leteExperiment\x12\x18.mlflow.DeleteExperiment\x1a!.mlflow.DeleteExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x11\x44\x65lete Experiment\x12\x99\x01\n\x11restoreExperiment\x12\x19.mlflow.RestoreExperiment\x1a\".mlflow.RestoreExperiment.Response\"E\xf2\x86\x19\x41\n)\n\x04POST\x12\x1b/mlflow/experiments/restore\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Restore Experiment\x12\x94\x01\n\x10updateExperiment\x12\x18.mlflow.UpdateExperiment\x1a!.mlflow.UpdateExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x11Update Experiment\x12q\n\tcreateRun\x12\x11.mlflow.CreateRun\x1a\x1a.mlflow.CreateRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/create\x1a\x04\x08\x02\x10\x00\x10\x01*\nCreate Run\x12q\n\tupdateRun\x12\x11.mlflow.UpdateRun\x1a\x1a.mlflow.UpdateRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/update\x1a\x04\x08\x02\x10\x00\x10\x01*\nUpdate Run\x12q\n\tdeleteRun\x12\x11.mlflow.DeleteRun\x1a\x1a.mlflow.DeleteRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\nDelete Run\x12v\n\nrestoreRun\x12\x12.mlflow.RestoreRun\x1a\x1b.mlflow.RestoreRun.Response\"7\xf2\x86\x19\x33\n\"\n\x04POST\x12\x14/mlflow/runs/restore\x1a\x04\x08\x02\x10\x00\x10\x01*\x0bRestore Run\x12u\n\tlogMetric\x12\x11.mlflow.LogMetric\x1a\x1a.mlflow.LogMetric.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/log-metric\x1a\x04\x08\x02\x10\x00\x10\x01*\nLog Metric\x12t\n\x08logParam\x12\x10.mlflow.LogParam\x1a\x19.mlflow.LogParam.Response\";\xf2\x86\x19\x37\n(\n\x04POST\x12\x1a/mlflow/runs/log-parameter\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Param\x12\xa1\x01\n\x10setExperimentTag\x12\x18.mlflow.SetExperimentTag\x1a!.mlflow.SetExperimentTag.Response\"P\xf2\x86\x19L\n4\n\x04POST\x12&/mlflow/experiments/set-experiment-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Set Experiment Tag\x12\x66\n\x06setTag\x12\x0e.mlflow.SetTag\x1a\x17.mlflow.SetTag.Response\"3\xf2\x86\x19/\n\"\n\x04POST\x12\x14/mlflow/runs/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x07Set Tag\x12\x88\x01\n\x0bsetTraceTag\x12\x13.mlflow.SetTraceTag\x1a\x1c.mlflow.SetTraceTag.Response\"F\xf2\x86\x19\x42\n/\n\x05PATCH\x12 /mlflow/traces/{request_id}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\rSet Trace Tag\x12\x95\x01\n\x0e\x64\x65leteTraceTag\x12\x16.mlflow.DeleteTraceTag\x1a\x1f.mlflow.DeleteTraceTag.Response\"J\xf2\x86\x19\x46\n0\n\x06\x44\x45LETE\x12 /mlflow/traces/{request_id}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\x10\x44\x65lete Trace Tag\x12u\n\tdeleteTag\x12\x11.mlflow.DeleteTag\x1a\x1a.mlflow.DeleteTag.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\nDelete Tag\x12\x65\n\x06getRun\x12\x0e.mlflow.GetRun\x1a\x17.mlflow.GetRun.Response\"2\xf2\x86\x19*\n\x1d\n\x03GET\x12\x10/mlflow/runs/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x07Get Run\xba\x8c\x19\x00\x12y\n\nsearchRuns\x12\x12.mlflow.SearchRuns\x1a\x1b.mlflow.SearchRuns.Response\":\xf2\x86\x19\x32\n!\n\x04POST\x12\x13/mlflow/runs/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x0bSearch Runs\xba\x8c\x19\x00\x12\x87\x01\n\rlistArtifacts\x12\x15.mlflow.ListArtifacts\x1a\x1e.mlflow.ListArtifacts.Response\"?\xf2\x86\x19\x37\n#\n\x03GET\x12\x16/mlflow/artifacts/list\x1a\x04\x08\x02\x10\x00\x10\x01*\x0eList Artifacts\xba\x8c\x19\x00\x12\x95\x01\n\x10getMetricHistory\x12\x18.mlflow.GetMetricHistory\x1a!.mlflow.GetMetricHistory.Response\"D\xf2\x86\x19@\n(\n\x03GET\x12\x1b/mlflow/metrics/get-history\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Get Metric History\x12\xb7\x01\n\x1cgetMetricHistoryBulkInterval\x12$.mlflow.GetMetricHistoryBulkInterval\x1a-.mlflow.GetMetricHistoryBulkInterval.Response\"B\xf2\x86\x19:\n6\n\x03GET\x12)/mlflow/metrics/get-history-bulk-interval\x1a\x04\x08\x02\x10\x0b\x10\x03\xba\x8c\x19\x00\x12p\n\x08logBatch\x12\x10.mlflow.LogBatch\x1a\x19.mlflow.LogBatch.Response\"7\xf2\x86\x19\x33\n$\n\x04POST\x12\x16/mlflow/runs/log-batch\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Batch\x12p\n\x08logModel\x12\x10.mlflow.LogModel\x1a\x19.mlflow.LogModel.Response\"7\xf2\x86\x19\x33\n$\n\x04POST\x12\x16/mlflow/runs/log-model\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Model\x12u\n\tlogInputs\x12\x11.mlflow.LogInputs\x1a\x1a.mlflow.LogInputs.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/log-inputs\x1a\x04\x08\x02\x10\x00\x10\x01*\nLog Inputs\x12\x87\x01\n\x0esearchDatasets\x12\x16.mlflow.SearchDatasets\x1a\x1f.mlflow.SearchDatasets.Response\"<\xf2\x86\x19\x34\n0\n\x04POST\x12\"mlflow/experiments/search-datasets\x1a\x04\x08\x02\x10\x00\x10\x03\xba\x8c\x19\x00\x12p\n\nstartTrace\x12\x12.mlflow.StartTrace\x1a\x1b.mlflow.StartTrace.Response\"1\xf2\x86\x19-\n\x1c\n\x04POST\x12\x0e/mlflow/traces\x1a\x04\x08\x02\x10\x00\x10\x03*\x0bStart Trace\x12v\n\x08\x65ndTrace\x12\x10.mlflow.EndTrace\x1a\x19.mlflow.EndTrace.Response\"=\xf2\x86\x19\x39\n*\n\x05PATCH\x12\x1b/mlflow/traces/{request_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\tEnd Trace\x12\x89\x01\n\x0cgetTraceInfo\x12\x14.mlflow.GetTraceInfo\x1a\x1d.mlflow.GetTraceInfo.Response\"D\xf2\x86\x19@\n-\n\x03GET\x12 /mlflow/traces/{request_id}/info\x1a\x04\x08\x02\x10\x00\x10\x03*\rGet TraceInfo\x12\x8b\x01\n\x0egetTraceInfoV3\x12\x16.mlflow.GetTraceInfoV3\x1a\x1f.mlflow.GetTraceInfoV3.Response\"@\xf2\x86\x19<\n&\n\x03GET\x12\x19/mlflow/traces/{trace_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\x10Get TraceInfo v3\x12w\n\x0csearchTraces\x12\x14.mlflow.SearchTraces\x1a\x1d.mlflow.SearchTraces.Response\"2\xf2\x86\x19.\n\x1b\n\x03GET\x12\x0e/mlflow/traces\x1a\x04\x08\x02\x10\x00\x10\x03*\rSearch Traces\x12\x86\x01\n\x0c\x64\x65leteTraces\x12\x14.mlflow.DeleteTraces\x1a\x1d.mlflow.DeleteTraces.Response\"A\xf2\x86\x19=\n*\n\x04POST\x12\x1c/mlflow/traces/delete-traces\x1a\x04\x08\x02\x10\x00\x10\x03*\rDelete Traces\x12\xdf\x01\n\x10\x63reateAssessment\x12\x18.mlflow.CreateAssessment\x1a!.mlflow.CreateAssessment.Response\"\x8d\x01\xf2\x86\x19\x88\x01\n>\n\x04POST\x12\x30/mlflow/traces/{assessment.trace_id}/assessments\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xee\x07\x18\x0c\x18\x01*:Create an assessment of a trace or a span within the trace\x12\xd0\x01\n\x10updateAssessment\x12\x18.mlflow.UpdateAssessment\x1a!.mlflow.UpdateAssessment.Response\"\x7f\xf2\x86\x19{\nD\n\x05PATCH\x12\x35/mlflow/traces/{trace_id}/assessments/{assessment_id}\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xee\x07\x18\x01*)Update an existing assessment on a trace.\x12\xb1\x01\n\x10\x64\x65leteAssessment\x12\x18.mlflow.DeleteAssessment\x1a!.mlflow.DeleteAssessment.Response\"`\xf2\x86\x19\\\nE\n\x06\x44\x45LETE\x12\x35/mlflow/traces/{trace_id}/assessments/{assessment_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\x11\x44\x65lete AssessmentB\x1e\n\x14org.mlflow.api.proto\x90\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'service_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n\024org.mlflow.api.proto\220\001\001\342?\002\020\001'
    _globals['_DATASETINPUT'].fields_by_name['dataset']._loaded_options = None
    _globals['_DATASETINPUT'].fields_by_name['dataset']._serialized_options = b'\370\206\031\001'
    _globals['_INPUTTAG'].fields_by_name['key']._loaded_options = None
    _globals['_INPUTTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_INPUTTAG'].fields_by_name['value']._loaded_options = None
    _globals['_INPUTTAG'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_DATASET'].fields_by_name['name']._loaded_options = None
    _globals['_DATASET'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DATASET'].fields_by_name['digest']._loaded_options = None
    _globals['_DATASET'].fields_by_name['digest']._serialized_options = b'\370\206\031\001'
    _globals['_DATASET'].fields_by_name['source_type']._loaded_options = None
    _globals['_DATASET'].fields_by_name['source_type']._serialized_options = b'\370\206\031\001'
    _globals['_DATASET'].fields_by_name['source']._loaded_options = None
    _globals['_DATASET'].fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEEXPERIMENT'].fields_by_name['name']._loaded_options = None
    _globals['_CREATEEXPERIMENT'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEEXPERIMENT']._loaded_options = None
    _globals['_CREATEEXPERIMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SEARCHEXPERIMENTS']._loaded_options = None
    _globals['_SEARCHEXPERIMENTS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETEXPERIMENT'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_GETEXPERIMENT'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_GETEXPERIMENT']._loaded_options = None
    _globals['_GETEXPERIMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEEXPERIMENT'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_DELETEEXPERIMENT'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEEXPERIMENT']._loaded_options = None
    _globals['_DELETEEXPERIMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_RESTOREEXPERIMENT'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_RESTOREEXPERIMENT'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_RESTOREEXPERIMENT']._loaded_options = None
    _globals['_RESTOREEXPERIMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_UPDATEEXPERIMENT'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_UPDATEEXPERIMENT'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEEXPERIMENT']._loaded_options = None
    _globals['_UPDATEEXPERIMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_CREATERUN']._loaded_options = None
    _globals['_CREATERUN']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_UPDATERUN']._loaded_options = None
    _globals['_UPDATERUN']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETERUN'].fields_by_name['run_id']._loaded_options = None
    _globals['_DELETERUN'].fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETERUN']._loaded_options = None
    _globals['_DELETERUN']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_RESTORERUN'].fields_by_name['run_id']._loaded_options = None
    _globals['_RESTORERUN'].fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _globals['_RESTORERUN']._loaded_options = None
    _globals['_RESTORERUN']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LOGMETRIC'].fields_by_name['key']._loaded_options = None
    _globals['_LOGMETRIC'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_LOGMETRIC'].fields_by_name['value']._loaded_options = None
    _globals['_LOGMETRIC'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_LOGMETRIC'].fields_by_name['timestamp']._loaded_options = None
    _globals['_LOGMETRIC'].fields_by_name['timestamp']._serialized_options = b'\370\206\031\001'
    _globals['_LOGMETRIC']._loaded_options = None
    _globals['_LOGMETRIC']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LOGPARAM'].fields_by_name['key']._loaded_options = None
    _globals['_LOGPARAM'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_LOGPARAM'].fields_by_name['value']._loaded_options = None
    _globals['_LOGPARAM'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_LOGPARAM']._loaded_options = None
    _globals['_LOGPARAM']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SETEXPERIMENTTAG'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_SETEXPERIMENTTAG'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_SETEXPERIMENTTAG'].fields_by_name['key']._loaded_options = None
    _globals['_SETEXPERIMENTTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETEXPERIMENTTAG'].fields_by_name['value']._loaded_options = None
    _globals['_SETEXPERIMENTTAG'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_SETEXPERIMENTTAG']._loaded_options = None
    _globals['_SETEXPERIMENTTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SETTAG'].fields_by_name['key']._loaded_options = None
    _globals['_SETTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETTAG'].fields_by_name['value']._loaded_options = None
    _globals['_SETTAG'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_SETTAG']._loaded_options = None
    _globals['_SETTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETETAG'].fields_by_name['run_id']._loaded_options = None
    _globals['_DELETETAG'].fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETETAG'].fields_by_name['key']._loaded_options = None
    _globals['_DELETETAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_DELETETAG']._loaded_options = None
    _globals['_DELETETAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETRUN']._loaded_options = None
    _globals['_GETRUN']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SEARCHRUNS']._loaded_options = None
    _globals['_SEARCHRUNS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LISTARTIFACTS']._loaded_options = None
    _globals['_LISTARTIFACTS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETMETRICHISTORY'].fields_by_name['metric_key']._loaded_options = None
    _globals['_GETMETRICHISTORY'].fields_by_name['metric_key']._serialized_options = b'\370\206\031\001'
    _globals['_GETMETRICHISTORY']._loaded_options = None
    _globals['_GETMETRICHISTORY']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETMETRICHISTORYBULKINTERVAL'].fields_by_name['metric_key']._loaded_options = None
    _globals['_GETMETRICHISTORYBULKINTERVAL'].fields_by_name['metric_key']._serialized_options = b'\370\206\031\001'
    _globals['_GETMETRICHISTORYBULKINTERVAL']._loaded_options = None
    _globals['_GETMETRICHISTORYBULKINTERVAL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LOGBATCH']._loaded_options = None
    _globals['_LOGBATCH']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LOGMODEL']._loaded_options = None
    _globals['_LOGMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_LOGINPUTS'].fields_by_name['run_id']._loaded_options = None
    _globals['_LOGINPUTS'].fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _globals['_LOGINPUTS']._loaded_options = None
    _globals['_LOGINPUTS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETEXPERIMENTBYNAME'].fields_by_name['experiment_name']._loaded_options = None
    _globals['_GETEXPERIMENTBYNAME'].fields_by_name['experiment_name']._serialized_options = b'\370\206\031\001'
    _globals['_GETEXPERIMENTBYNAME']._loaded_options = None
    _globals['_GETEXPERIMENTBYNAME']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_ASSESSMENTSOURCE'].fields_by_name['source_type']._loaded_options = None
    _globals['_ASSESSMENTSOURCE'].fields_by_name['source_type']._serialized_options = b'\370\206\031\001'
    _globals['_ASSESSMENTSOURCE'].fields_by_name['source_id']._loaded_options = None
    _globals['_ASSESSMENTSOURCE'].fields_by_name['source_id']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEASSESSMENT'].fields_by_name['assessment']._loaded_options = None
    _globals['_CREATEASSESSMENT'].fields_by_name['assessment']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEASSESSMENT']._loaded_options = None
    _globals['_CREATEASSESSMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_UPDATEASSESSMENT'].fields_by_name['assessment']._loaded_options = None
    _globals['_UPDATEASSESSMENT'].fields_by_name['assessment']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEASSESSMENT'].fields_by_name['update_mask']._loaded_options = None
    _globals['_UPDATEASSESSMENT'].fields_by_name['update_mask']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEASSESSMENT']._loaded_options = None
    _globals['_UPDATEASSESSMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEASSESSMENT'].fields_by_name['trace_id']._loaded_options = None
    _globals['_DELETEASSESSMENT'].fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEASSESSMENT'].fields_by_name['assessment_id']._loaded_options = None
    _globals['_DELETEASSESSMENT'].fields_by_name['assessment_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEASSESSMENT']._loaded_options = None
    _globals['_DELETEASSESSMENT']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_STARTTRACE']._loaded_options = None
    _globals['_STARTTRACE']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_ENDTRACE']._loaded_options = None
    _globals['_ENDTRACE']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETTRACEINFO']._loaded_options = None
    _globals['_GETTRACEINFO']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETTRACEINFOV3']._loaded_options = None
    _globals['_GETTRACEINFOV3']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SEARCHTRACES']._loaded_options = None
    _globals['_SEARCHTRACES']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETETRACES'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_DELETETRACES'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_DELETETRACES']._loaded_options = None
    _globals['_DELETETRACES']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SETTRACETAG']._loaded_options = None
    _globals['_SETTRACETAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETETRACETAG']._loaded_options = None
    _globals['_DELETETRACETAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_TRACEINFOV3_TRACEMETADATAENTRY']._loaded_options = None
    _globals['_TRACEINFOV3_TRACEMETADATAENTRY']._serialized_options = b'8\001'
    _globals['_TRACEINFOV3_TAGSENTRY']._loaded_options = None
    _globals['_TRACEINFOV3_TAGSENTRY']._serialized_options = b'8\001'
    _globals['_DATASETSUMMARY'].fields_by_name['experiment_id']._loaded_options = None
    _globals['_DATASETSUMMARY'].fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _globals['_DATASETSUMMARY'].fields_by_name['name']._loaded_options = None
    _globals['_DATASETSUMMARY'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DATASETSUMMARY'].fields_by_name['digest']._loaded_options = None
    _globals['_DATASETSUMMARY'].fields_by_name['digest']._serialized_options = b'\370\206\031\001'
    _globals['_SEARCHDATASETS']._loaded_options = None
    _globals['_SEARCHDATASETS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_MLFLOWSERVICE'].methods_by_name['getExperimentByName']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getExperimentByName']._serialized_options = b'\362\206\031H\n,\n\003GET\022\037/mlflow/experiments/get-by-name\032\004\010\002\020\000\020\001*\026Get Experiment By Name'
    _globals['_MLFLOWSERVICE'].methods_by_name['createExperiment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['createExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/create\032\004\010\002\020\000\020\001*\021Create Experiment'
    _globals['_MLFLOWSERVICE'].methods_by_name['searchExperiments']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['searchExperiments']._serialized_options = b'\362\206\031i\n(\n\004POST\022\032/mlflow/experiments/search\032\004\010\002\020\000\n\'\n\003GET\022\032/mlflow/experiments/search\032\004\010\002\020\000\020\001*\022Search Experiments'
    _globals['_MLFLOWSERVICE'].methods_by_name['getExperiment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getExperiment']._serialized_options = b'\362\206\0318\n$\n\003GET\022\027/mlflow/experiments/get\032\004\010\002\020\000\020\001*\016Get Experiment\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteExperiment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/delete\032\004\010\002\020\000\020\001*\021Delete Experiment'
    _globals['_MLFLOWSERVICE'].methods_by_name['restoreExperiment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['restoreExperiment']._serialized_options = b'\362\206\031A\n)\n\004POST\022\033/mlflow/experiments/restore\032\004\010\002\020\000\020\001*\022Restore Experiment'
    _globals['_MLFLOWSERVICE'].methods_by_name['updateExperiment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['updateExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/update\032\004\010\002\020\000\020\001*\021Update Experiment'
    _globals['_MLFLOWSERVICE'].methods_by_name['createRun']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['createRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/create\032\004\010\002\020\000\020\001*\nCreate Run'
    _globals['_MLFLOWSERVICE'].methods_by_name['updateRun']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['updateRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/update\032\004\010\002\020\000\020\001*\nUpdate Run'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteRun']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/delete\032\004\010\002\020\000\020\001*\nDelete Run'
    _globals['_MLFLOWSERVICE'].methods_by_name['restoreRun']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['restoreRun']._serialized_options = b'\362\206\0313\n\"\n\004POST\022\024/mlflow/runs/restore\032\004\010\002\020\000\020\001*\013Restore Run'
    _globals['_MLFLOWSERVICE'].methods_by_name['logMetric']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['logMetric']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/log-metric\032\004\010\002\020\000\020\001*\nLog Metric'
    _globals['_MLFLOWSERVICE'].methods_by_name['logParam']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['logParam']._serialized_options = b'\362\206\0317\n(\n\004POST\022\032/mlflow/runs/log-parameter\032\004\010\002\020\000\020\001*\tLog Param'
    _globals['_MLFLOWSERVICE'].methods_by_name['setExperimentTag']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['setExperimentTag']._serialized_options = b'\362\206\031L\n4\n\004POST\022&/mlflow/experiments/set-experiment-tag\032\004\010\002\020\000\020\001*\022Set Experiment Tag'
    _globals['_MLFLOWSERVICE'].methods_by_name['setTag']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['setTag']._serialized_options = b'\362\206\031/\n\"\n\004POST\022\024/mlflow/runs/set-tag\032\004\010\002\020\000\020\001*\007Set Tag'
    _globals['_MLFLOWSERVICE'].methods_by_name['setTraceTag']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['setTraceTag']._serialized_options = b'\362\206\031B\n/\n\005PATCH\022 /mlflow/traces/{request_id}/tags\032\004\010\002\020\000\020\003*\rSet Trace Tag'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTraceTag']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTraceTag']._serialized_options = b'\362\206\031F\n0\n\006DELETE\022 /mlflow/traces/{request_id}/tags\032\004\010\002\020\000\020\003*\020Delete Trace Tag'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTag']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTag']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/delete-tag\032\004\010\002\020\000\020\001*\nDelete Tag'
    _globals['_MLFLOWSERVICE'].methods_by_name['getRun']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getRun']._serialized_options = b'\362\206\031*\n\035\n\003GET\022\020/mlflow/runs/get\032\004\010\002\020\000\020\001*\007Get Run\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['searchRuns']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['searchRuns']._serialized_options = b'\362\206\0312\n!\n\004POST\022\023/mlflow/runs/search\032\004\010\002\020\000\020\001*\013Search Runs\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['listArtifacts']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['listArtifacts']._serialized_options = b'\362\206\0317\n#\n\003GET\022\026/mlflow/artifacts/list\032\004\010\002\020\000\020\001*\016List Artifacts\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['getMetricHistory']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getMetricHistory']._serialized_options = b'\362\206\031@\n(\n\003GET\022\033/mlflow/metrics/get-history\032\004\010\002\020\000\020\001*\022Get Metric History'
    _globals['_MLFLOWSERVICE'].methods_by_name['getMetricHistoryBulkInterval']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getMetricHistoryBulkInterval']._serialized_options = b'\362\206\031:\n6\n\003GET\022)/mlflow/metrics/get-history-bulk-interval\032\004\010\002\020\013\020\003\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['logBatch']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['logBatch']._serialized_options = b'\362\206\0313\n$\n\004POST\022\026/mlflow/runs/log-batch\032\004\010\002\020\000\020\001*\tLog Batch'
    _globals['_MLFLOWSERVICE'].methods_by_name['logModel']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['logModel']._serialized_options = b'\362\206\0313\n$\n\004POST\022\026/mlflow/runs/log-model\032\004\010\002\020\000\020\001*\tLog Model'
    _globals['_MLFLOWSERVICE'].methods_by_name['logInputs']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['logInputs']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/log-inputs\032\004\010\002\020\000\020\001*\nLog Inputs'
    _globals['_MLFLOWSERVICE'].methods_by_name['searchDatasets']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['searchDatasets']._serialized_options = b'\362\206\0314\n0\n\004POST\022\"mlflow/experiments/search-datasets\032\004\010\002\020\000\020\003\272\214\031\000'
    _globals['_MLFLOWSERVICE'].methods_by_name['startTrace']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['startTrace']._serialized_options = b'\362\206\031-\n\034\n\004POST\022\016/mlflow/traces\032\004\010\002\020\000\020\003*\013Start Trace'
    _globals['_MLFLOWSERVICE'].methods_by_name['endTrace']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['endTrace']._serialized_options = b'\362\206\0319\n*\n\005PATCH\022\033/mlflow/traces/{request_id}\032\004\010\002\020\000\020\003*\tEnd Trace'
    _globals['_MLFLOWSERVICE'].methods_by_name['getTraceInfo']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getTraceInfo']._serialized_options = b'\362\206\031@\n-\n\003GET\022 /mlflow/traces/{request_id}/info\032\004\010\002\020\000\020\003*\rGet TraceInfo'
    _globals['_MLFLOWSERVICE'].methods_by_name['getTraceInfoV3']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['getTraceInfoV3']._serialized_options = b'\362\206\031<\n&\n\003GET\022\031/mlflow/traces/{trace_id}\032\004\010\002\020\000\020\003*\020Get TraceInfo v3'
    _globals['_MLFLOWSERVICE'].methods_by_name['searchTraces']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['searchTraces']._serialized_options = b'\362\206\031.\n\033\n\003GET\022\016/mlflow/traces\032\004\010\002\020\000\020\003*\rSearch Traces'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTraces']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteTraces']._serialized_options = b'\362\206\031=\n*\n\004POST\022\034/mlflow/traces/delete-traces\032\004\010\002\020\000\020\003*\rDelete Traces'
    _globals['_MLFLOWSERVICE'].methods_by_name['createAssessment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['createAssessment']._serialized_options = b'\362\206\031\210\001\n>\n\004POST\0220/mlflow/traces/{assessment.trace_id}/assessments\032\004\010\002\020\000\020\003\030\350\007\030\356\007\030\014\030\001*:Create an assessment of a trace or a span within the trace'
    _globals['_MLFLOWSERVICE'].methods_by_name['updateAssessment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['updateAssessment']._serialized_options = b'\362\206\031{\nD\n\005PATCH\0225/mlflow/traces/{trace_id}/assessments/{assessment_id}\032\004\010\002\020\000\020\003\030\350\007\030\356\007\030\001*)Update an existing assessment on a trace.'
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteAssessment']._loaded_options = None
    _globals['_MLFLOWSERVICE'].methods_by_name['deleteAssessment']._serialized_options = b'\362\206\031\\\nE\n\006DELETE\0225/mlflow/traces/{trace_id}/assessments/{assessment_id}\032\004\010\002\020\000\020\003*\021Delete Assessment'
    _globals['_VIEWTYPE']._serialized_start=9364
    _globals['_VIEWTYPE']._serialized_end=9418
    _globals['_SOURCETYPE']._serialized_start=9420
    _globals['_SOURCETYPE']._serialized_end=9493
    _globals['_RUNSTATUS']._serialized_start=9495
    _globals['_RUNSTATUS']._serialized_end=9572
    _globals['_TRACESTATUS']._serialized_start=9574
    _globals['_TRACESTATUS']._serialized_end=9653
    _globals['_METRIC']._serialized_start=184
    _globals['_METRIC']._serialized_end=256
    _globals['_PARAM']._serialized_start=258
    _globals['_PARAM']._serialized_end=293
    _globals['_RUN']._serialized_start=295
    _globals['_RUN']._serialized_end=397
    _globals['_RUNDATA']._serialized_start=399
    _globals['_RUNDATA']._serialized_end=502
    _globals['_RUNINPUTS']._serialized_start=504
    _globals['_RUNINPUTS']._serialized_end=561
    _globals['_RUNTAG']._serialized_start=563
    _globals['_RUNTAG']._serialized_end=599
    _globals['_EXPERIMENTTAG']._serialized_start=601
    _globals['_EXPERIMENTTAG']._serialized_end=644
    _globals['_RUNINFO']._serialized_start=647
    _globals['_RUNINFO']._serialized_end=868
    _globals['_EXPERIMENT']._serialized_start=871
    _globals['_EXPERIMENT']._serialized_end=1058
    _globals['_DATASETINPUT']._serialized_start=1060
    _globals['_DATASETINPUT']._serialized_end=1146
    _globals['_INPUTTAG']._serialized_start=1148
    _globals['_INPUTTAG']._serialized_end=1198
    _globals['_DATASET']._serialized_start=1201
    _globals['_DATASET']._serialized_end=1334
    _globals['_CREATEEXPERIMENT']._serialized_start=1337
    _globals['_CREATEEXPERIMENT']._serialized_end=1519
    _globals['_CREATEEXPERIMENT_RESPONSE']._serialized_start=1441
    _globals['_CREATEEXPERIMENT_RESPONSE']._serialized_end=1474
    _globals['_SEARCHEXPERIMENTS']._serialized_start=1522
    _globals['_SEARCHEXPERIMENTS']._serialized_end=1776
    _globals['_SEARCHEXPERIMENTS_RESPONSE']._serialized_start=1655
    _globals['_SEARCHEXPERIMENTS_RESPONSE']._serialized_end=1731
    _globals['_GETEXPERIMENT']._serialized_start=1779
    _globals['_GETEXPERIMENT']._serialized_end=1920
    _globals['_GETEXPERIMENT_RESPONSE']._serialized_start=1825
    _globals['_GETEXPERIMENT_RESPONSE']._serialized_end=1875
    _globals['_DELETEEXPERIMENT']._serialized_start=1922
    _globals['_DELETEEXPERIMENT']._serialized_end=2026
    _globals['_DELETEEXPERIMENT_RESPONSE']._serialized_start=1441
    _globals['_DELETEEXPERIMENT_RESPONSE']._serialized_end=1451
    _globals['_RESTOREEXPERIMENT']._serialized_start=2028
    _globals['_RESTOREEXPERIMENT']._serialized_end=2133
    _globals['_RESTOREEXPERIMENT_RESPONSE']._serialized_start=1441
    _globals['_RESTOREEXPERIMENT_RESPONSE']._serialized_end=1451
    _globals['_UPDATEEXPERIMENT']._serialized_start=2135
    _globals['_UPDATEEXPERIMENT']._serialized_end=2257
    _globals['_UPDATEEXPERIMENT_RESPONSE']._serialized_start=1441
    _globals['_UPDATEEXPERIMENT_RESPONSE']._serialized_end=1451
    _globals['_CREATERUN']._serialized_start=2260
    _globals['_CREATERUN']._serialized_end=2462
    _globals['_CREATERUN_RESPONSE']._serialized_start=2381
    _globals['_CREATERUN_RESPONSE']._serialized_end=2417
    _globals['_UPDATERUN']._serialized_start=2465
    _globals['_UPDATERUN']._serialized_end=2673
    _globals['_UPDATERUN_RESPONSE']._serialized_start=2583
    _globals['_UPDATERUN_RESPONSE']._serialized_end=2628
    _globals['_DELETERUN']._serialized_start=2675
    _globals['_DELETERUN']._serialized_end=2765
    _globals['_DELETERUN_RESPONSE']._serialized_start=1441
    _globals['_DELETERUN_RESPONSE']._serialized_end=1451
    _globals['_RESTORERUN']._serialized_start=2767
    _globals['_RESTORERUN']._serialized_end=2858
    _globals['_RESTORERUN_RESPONSE']._serialized_start=1441
    _globals['_RESTORERUN_RESPONSE']._serialized_end=1451
    _globals['_LOGMETRIC']._serialized_start=2861
    _globals['_LOGMETRIC']._serialized_end=3045
    _globals['_LOGMETRIC_RESPONSE']._serialized_start=1441
    _globals['_LOGMETRIC_RESPONSE']._serialized_end=1451
    _globals['_LOGPARAM']._serialized_start=3048
    _globals['_LOGPARAM']._serialized_end=3189
    _globals['_LOGPARAM_RESPONSE']._serialized_start=1441
    _globals['_LOGPARAM_RESPONSE']._serialized_end=1451
    _globals['_SETEXPERIMENTTAG']._serialized_start=3192
    _globals['_SETEXPERIMENTTAG']._serialized_end=3336
    _globals['_SETEXPERIMENTTAG_RESPONSE']._serialized_start=1441
    _globals['_SETEXPERIMENTTAG_RESPONSE']._serialized_end=1451
    _globals['_SETTAG']._serialized_start=3339
    _globals['_SETTAG']._serialized_end=3478
    _globals['_SETTAG_RESPONSE']._serialized_start=1441
    _globals['_SETTAG_RESPONSE']._serialized_end=1451
    _globals['_DELETETAG']._serialized_start=3480
    _globals['_DELETETAG']._serialized_end=3589
    _globals['_DELETETAG_RESPONSE']._serialized_start=1441
    _globals['_DELETETAG_RESPONSE']._serialized_end=1451
    _globals['_GETRUN']._serialized_start=3591
    _globals['_GETRUN']._serialized_end=3716
    _globals['_GETRUN_RESPONSE']._serialized_start=2381
    _globals['_GETRUN_RESPONSE']._serialized_end=2417
    _globals['_SEARCHRUNS']._serialized_start=3719
    _globals['_SEARCHRUNS']._serialized_end=3999
    _globals['_SEARCHRUNS_RESPONSE']._serialized_start=3892
    _globals['_SEARCHRUNS_RESPONSE']._serialized_end=3954
    _globals['_LISTARTIFACTS']._serialized_start=4002
    _globals['_LISTARTIFACTS']._serialized_end=4218
    _globals['_LISTARTIFACTS_RESPONSE']._serialized_start=4087
    _globals['_LISTARTIFACTS_RESPONSE']._serialized_end=4173
    _globals['_FILEINFO']._serialized_start=4220
    _globals['_FILEINFO']._serialized_end=4279
    _globals['_GETMETRICHISTORY']._serialized_start=4282
    _globals['_GETMETRICHISTORY']._serialized_end=4516
    _globals['_GETMETRICHISTORY_RESPONSE']._serialized_start=4403
    _globals['_GETMETRICHISTORY_RESPONSE']._serialized_end=4471
    _globals['_METRICWITHRUNID']._serialized_start=4518
    _globals['_METRICWITHRUNID']._serialized_end=4615
    _globals['_GETMETRICHISTORYBULKINTERVAL']._serialized_start=4618
    _globals['_GETMETRICHISTORYBULKINTERVAL']._serialized_end=4849
    _globals['_GETMETRICHISTORYBULKINTERVAL_RESPONSE']._serialized_start=4752
    _globals['_GETMETRICHISTORYBULKINTERVAL_RESPONSE']._serialized_end=4804
    _globals['_LOGBATCH']._serialized_start=4852
    _globals['_LOGBATCH']._serialized_end=5029
    _globals['_LOGBATCH_RESPONSE']._serialized_start=1441
    _globals['_LOGBATCH_RESPONSE']._serialized_end=1451
    _globals['_LOGMODEL']._serialized_start=5031
    _globals['_LOGMODEL']._serialized_end=5134
    _globals['_LOGMODEL_RESPONSE']._serialized_start=1441
    _globals['_LOGMODEL_RESPONSE']._serialized_end=1451
    _globals['_LOGINPUTS']._serialized_start=5137
    _globals['_LOGINPUTS']._serialized_end=5267
    _globals['_LOGINPUTS_RESPONSE']._serialized_start=1441
    _globals['_LOGINPUTS_RESPONSE']._serialized_end=1451
    _globals['_GETEXPERIMENTBYNAME']._serialized_start=5270
    _globals['_GETEXPERIMENTBYNAME']._serialized_end=5419
    _globals['_GETEXPERIMENTBYNAME_RESPONSE']._serialized_start=1825
    _globals['_GETEXPERIMENTBYNAME_RESPONSE']._serialized_end=1875
    _globals['_ASSESSMENTSOURCE']._serialized_start=5422
    _globals['_ASSESSMENTSOURCE']._serialized_end=5608
    _globals['_ASSESSMENTSOURCE_SOURCETYPE']._serialized_start=5531
    _globals['_ASSESSMENTSOURCE_SOURCETYPE']._serialized_end=5608
    _globals['_ASSESSMENTERROR']._serialized_start=5610
    _globals['_ASSESSMENTERROR']._serialized_end=5670
    _globals['_CREATEASSESSMENT']._serialized_start=5673
    _globals['_CREATEASSESSMENT']._serialized_end=5858
    _globals['_CREATEASSESSMENT_RESPONSE']._serialized_start=5751
    _globals['_CREATEASSESSMENT_RESPONSE']._serialized_end=5813
    _globals['_UPDATEASSESSMENT']._serialized_start=5861
    _globals['_UPDATEASSESSMENT']._serialized_end=6101
    _globals['_UPDATEASSESSMENT_RESPONSE']._serialized_start=5751
    _globals['_UPDATEASSESSMENT_RESPONSE']._serialized_end=5813
    _globals['_DELETEASSESSMENT']._serialized_start=6104
    _globals['_DELETEASSESSMENT']._serialized_end=6232
    _globals['_DELETEASSESSMENT_RESPONSE']._serialized_start=1441
    _globals['_DELETEASSESSMENT_RESPONSE']._serialized_end=1451
    _globals['_TRACEINFO']._serialized_start=6235
    _globals['_TRACEINFO']._serialized_end=6463
    _globals['_TRACEREQUESTMETADATA']._serialized_start=6465
    _globals['_TRACEREQUESTMETADATA']._serialized_end=6515
    _globals['_TRACETAG']._serialized_start=6517
    _globals['_TRACETAG']._serialized_end=6555
    _globals['_STARTTRACE']._serialized_start=6558
    _globals['_STARTTRACE']._serialized_end=6799
    _globals['_STARTTRACE_RESPONSE']._serialized_start=6705
    _globals['_STARTTRACE_RESPONSE']._serialized_end=6754
    _globals['_ENDTRACE']._serialized_start=6802
    _globals['_ENDTRACE']._serialized_end=7075
    _globals['_ENDTRACE_RESPONSE']._serialized_start=6705
    _globals['_ENDTRACE_RESPONSE']._serialized_end=6754
    _globals['_GETTRACEINFO']._serialized_start=7078
    _globals['_GETTRACEINFO']._serialized_end=7208
    _globals['_GETTRACEINFO_RESPONSE']._serialized_start=6705
    _globals['_GETTRACEINFO_RESPONSE']._serialized_end=6754
    _globals['_GETTRACEINFOV3']._serialized_start=7210
    _globals['_GETTRACEINFOV3']._serialized_end=7331
    _globals['_GETTRACEINFOV3_RESPONSE']._serialized_start=7246
    _globals['_GETTRACEINFOV3_RESPONSE']._serialized_end=7286
    _globals['_SEARCHTRACES']._serialized_start=7334
    _globals['_SEARCHTRACES']._serialized_end=7569
    _globals['_SEARCHTRACES_RESPONSE']._serialized_start=7454
    _globals['_SEARCHTRACES_RESPONSE']._serialized_end=7524
    _globals['_DELETETRACES']._serialized_start=7572
    _globals['_DELETETRACES']._serialized_end=7767
    _globals['_DELETETRACES_RESPONSE']._serialized_start=7688
    _globals['_DELETETRACES_RESPONSE']._serialized_end=7722
    _globals['_SETTRACETAG']._serialized_start=7769
    _globals['_SETTRACETAG']._serialized_end=7887
    _globals['_SETTRACETAG_RESPONSE']._serialized_start=1441
    _globals['_SETTRACETAG_RESPONSE']._serialized_end=1451
    _globals['_DELETETRACETAG']._serialized_start=7889
    _globals['_DELETETRACETAG']._serialized_end=7995
    _globals['_DELETETRACETAG_RESPONSE']._serialized_start=1441
    _globals['_DELETETRACETAG_RESPONSE']._serialized_end=1451
    _globals['_TRACE']._serialized_start=7997
    _globals['_TRACE']._serialized_end=8045
    _globals['_TRACELOCATION']._serialized_start=8048
    _globals['_TRACELOCATION']._serialized_end=8486
    _globals['_TRACELOCATION_MLFLOWEXPERIMENTLOCATION']._serialized_start=8270
    _globals['_TRACELOCATION_MLFLOWEXPERIMENTLOCATION']._serialized_end=8319
    _globals['_TRACELOCATION_INFERENCETABLELOCATION']._serialized_start=8321
    _globals['_TRACELOCATION_INFERENCETABLELOCATION']._serialized_end=8370
    _globals['_TRACELOCATION_TRACELOCATIONTYPE']._serialized_start=8372
    _globals['_TRACELOCATION_TRACELOCATIONTYPE']._serialized_end=8472
    _globals['_TRACEINFOV3']._serialized_start=8489
    _globals['_TRACEINFOV3']._serialized_end=9105
    _globals['_TRACEINFOV3_TRACEMETADATAENTRY']._serialized_start=8940
    _globals['_TRACEINFOV3_TRACEMETADATAENTRY']._serialized_end=8992
    _globals['_TRACEINFOV3_TAGSENTRY']._serialized_start=8994
    _globals['_TRACEINFOV3_TAGSENTRY']._serialized_end=9037
    _globals['_TRACEINFOV3_STATE']._serialized_start=9039
    _globals['_TRACEINFOV3_STATE']._serialized_end=9105
    _globals['_DATASETSUMMARY']._serialized_start=9107
    _globals['_DATASETSUMMARY']._serialized_end=9211
    _globals['_SEARCHDATASETS']._serialized_start=9214
    _globals['_SEARCHDATASETS']._serialized_end=9362
    _globals['_SEARCHDATASETS_RESPONSE']._serialized_start=9256
    _globals['_SEARCHDATASETS_RESPONSE']._serialized_end=9317
    _globals['_MLFLOWSERVICE']._serialized_start=9656
    _globals['_MLFLOWSERVICE']._serialized_end=14742
  _builder.BuildServices(DESCRIPTOR, 'service_pb2', _globals)
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: service.proto
  """Generated protocol buffer code."""
  from google.protobuf.internal import enum_type_wrapper
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf import service as _service
  from google.protobuf import service_reflection
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2
  from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
  from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from . import assessments_pb2 as assessments__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rservice.proto\x12\x06mlflow\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\x1a\x1egoogle/protobuf/duration.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x11\x61ssessments.proto\"H\n\x06Metric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\"#\n\x05Param\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"f\n\x03Run\x12\x1d\n\x04info\x18\x01 \x01(\x0b\x32\x0f.mlflow.RunInfo\x12\x1d\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x0f.mlflow.RunData\x12!\n\x06inputs\x18\x03 \x01(\x0b\x32\x11.mlflow.RunInputs\"g\n\x07RunData\x12\x1f\n\x07metrics\x18\x01 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x1d\n\x06params\x18\x02 \x03(\x0b\x32\r.mlflow.Param\x12\x1c\n\x04tags\x18\x03 \x03(\x0b\x32\x0e.mlflow.RunTag\"9\n\tRunInputs\x12,\n\x0e\x64\x61taset_inputs\x18\x01 \x03(\x0b\x32\x14.mlflow.DatasetInput\"$\n\x06RunTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"+\n\rExperimentTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xdd\x01\n\x07RunInfo\x12\x0e\n\x06run_id\x18\x0f \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x10\n\x08run_name\x18\x03 \x01(\t\x12\x15\n\rexperiment_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x06 \x01(\t\x12!\n\x06status\x18\x07 \x01(\x0e\x32\x11.mlflow.RunStatus\x12\x12\n\nstart_time\x18\x08 \x01(\x03\x12\x10\n\x08\x65nd_time\x18\t \x01(\x03\x12\x14\n\x0c\x61rtifact_uri\x18\r \x01(\t\x12\x17\n\x0flifecycle_stage\x18\x0e \x01(\t\"\xbb\x01\n\nExperiment\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x19\n\x11\x61rtifact_location\x18\x03 \x01(\t\x12\x17\n\x0flifecycle_stage\x18\x04 \x01(\t\x12\x18\n\x10last_update_time\x18\x05 \x01(\x03\x12\x15\n\rcreation_time\x18\x06 \x01(\x03\x12#\n\x04tags\x18\x07 \x03(\x0b\x32\x15.mlflow.ExperimentTag\"V\n\x0c\x44\x61tasetInput\x12\x1e\n\x04tags\x18\x01 \x03(\x0b\x32\x10.mlflow.InputTag\x12&\n\x07\x64\x61taset\x18\x02 \x01(\x0b\x32\x0f.mlflow.DatasetB\x04\xf8\x86\x19\x01\"2\n\x08InputTag\x12\x11\n\x03key\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\"\x85\x01\n\x07\x44\x61taset\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06\x64igest\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x19\n\x0bsource_type\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x04 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06schema\x18\x05 \x01(\t\x12\x0f\n\x07profile\x18\x06 \x01(\t\"\xb6\x01\n\x10\x43reateExperiment\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x19\n\x11\x61rtifact_location\x18\x02 \x01(\t\x12#\n\x04tags\x18\x03 \x03(\x0b\x32\x15.mlflow.ExperimentTag\x1a!\n\x08Response\x12\x15\n\rexperiment_id\x18\x01 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xfe\x01\n\x11SearchExperiments\x12\x13\n\x0bmax_results\x18\x01 \x01(\x03\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x0e\n\x06\x66ilter\x18\x03 \x01(\t\x12\x10\n\x08order_by\x18\x04 \x03(\t\x12#\n\tview_type\x18\x05 \x01(\x0e\x32\x10.mlflow.ViewType\x1aL\n\x08Response\x12\'\n\x0b\x65xperiments\x18\x01 \x03(\x0b\x32\x12.mlflow.Experiment\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8d\x01\n\rGetExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x32\n\x08Response\x12&\n\nexperiment\x18\x01 \x01(\x0b\x32\x12.mlflow.Experiment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"h\n\x10\x44\x65leteExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"i\n\x11RestoreExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"z\n\x10UpdateExperiment\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xca\x01\n\tCreateRun\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08run_name\x18\x03 \x01(\t\x12\x12\n\nstart_time\x18\x07 \x01(\x03\x12\x1c\n\x04tags\x18\t \x03(\x0b\x32\x0e.mlflow.RunTag\x1a$\n\x08Response\x12\x18\n\x03run\x18\x01 \x01(\x0b\x32\x0b.mlflow.Run:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xd0\x01\n\tUpdateRun\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12!\n\x06status\x18\x02 \x01(\x0e\x32\x11.mlflow.RunStatus\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\x03\x12\x10\n\x08run_name\x18\x05 \x01(\t\x1a-\n\x08Response\x12!\n\x08run_info\x18\x01 \x01(\x0b\x32\x0f.mlflow.RunInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"Z\n\tDeleteRun\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"[\n\nRestoreRun\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xb8\x01\n\tLogMetric\x12\x0e\n\x06run_id\x18\x06 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\x01\x42\x04\xf8\x86\x19\x01\x12\x17\n\ttimestamp\x18\x04 \x01(\x03\x42\x04\xf8\x86\x19\x01\x12\x0f\n\x04step\x18\x05 \x01(\x03:\x01\x30\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8d\x01\n\x08LogParam\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x90\x01\n\x10SetExperimentTag\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8b\x01\n\x06SetTag\x12\x0e\n\x06run_id\x18\x04 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"m\n\tDeleteTag\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"}\n\x06GetRun\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x1a$\n\x08Response\x12\x18\n\x03run\x18\x01 \x01(\x0b\x32\x0b.mlflow.Run:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x98\x02\n\nSearchRuns\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x12\x0e\n\x06\x66ilter\x18\x04 \x01(\t\x12\x34\n\rrun_view_type\x18\x03 \x01(\x0e\x32\x10.mlflow.ViewType:\x0b\x41\x43TIVE_ONLY\x12\x19\n\x0bmax_results\x18\x05 \x01(\x05:\x04\x31\x30\x30\x30\x12\x10\n\x08order_by\x18\x06 \x03(\t\x12\x12\n\npage_token\x18\x07 \x01(\t\x1a>\n\x08Response\x12\x19\n\x04runs\x18\x01 \x03(\x0b\x32\x0b.mlflow.Run\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xd8\x01\n\rListArtifacts\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aV\n\x08Response\x12\x10\n\x08root_uri\x18\x01 \x01(\t\x12\x1f\n\x05\x66iles\x18\x02 \x03(\x0b\x32\x10.mlflow.FileInfo\x12\x17\n\x0fnext_page_token\x18\x03 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\";\n\x08\x46ileInfo\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x0e\n\x06is_dir\x18\x02 \x01(\x08\x12\x11\n\tfile_size\x18\x03 \x01(\x03\"\xea\x01\n\x10GetMetricHistory\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x10\n\x08run_uuid\x18\x01 \x01(\t\x12\x18\n\nmetric_key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\npage_token\x18\x04 \x01(\t\x12\x13\n\x0bmax_results\x18\x05 \x01(\x05\x1a\x44\n\x08Response\x12\x1f\n\x07metrics\x18\x01 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"a\n\x0fMetricWithRunId\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x0e\n\x06run_id\x18\x05 \x01(\t\"\xe7\x01\n\x1cGetMetricHistoryBulkInterval\x12\x0f\n\x07run_ids\x18\x01 \x03(\t\x12\x18\n\nmetric_key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\nstart_step\x18\x03 \x01(\x05\x12\x10\n\x08\x65nd_step\x18\x04 \x01(\x05\x12\x13\n\x0bmax_results\x18\x05 \x01(\x05\x1a\x34\n\x08Response\x12(\n\x07metrics\x18\x01 \x03(\x0b\x32\x17.mlflow.MetricWithRunId:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xb1\x01\n\x08LogBatch\x12\x0e\n\x06run_id\x18\x01 \x01(\t\x12\x1f\n\x07metrics\x18\x02 \x03(\x0b\x32\x0e.mlflow.Metric\x12\x1d\n\x06params\x18\x03 \x03(\x0b\x32\r.mlflow.Param\x12\x1c\n\x04tags\x18\x04 \x03(\x0b\x32\x0e.mlflow.RunTag\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"g\n\x08LogModel\x12\x0e\n\x06run_id\x18\x01 \x01(\t\x12\x12\n\nmodel_json\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x82\x01\n\tLogInputs\x12\x14\n\x06run_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12&\n\x08\x64\x61tasets\x18\x02 \x03(\x0b\x32\x14.mlflow.DatasetInput\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x95\x01\n\x13GetExperimentByName\x12\x1d\n\x0f\x65xperiment_name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x32\n\x08Response\x12&\n\nexperiment\x18\x01 \x01(\x0b\x32\x12.mlflow.Experiment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xba\x01\n\x10\x41ssessmentSource\x12>\n\x0bsource_type\x18\x01 \x01(\x0e\x32#.mlflow.AssessmentSource.SourceTypeB\x04\xf8\x86\x19\x01\x12\x17\n\tsource_id\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\"M\n\nSourceType\x12\x1b\n\x17SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05HUMAN\x10\x01\x12\r\n\tLLM_JUDGE\x10\x02\x12\x08\n\x04\x43ODE\x10\x03\"<\n\x0f\x41ssessmentError\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\"\xb9\x01\n\x10\x43reateAssessment\x12\x38\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.AssessmentB\x04\xf8\x86\x19\x01\x1a>\n\x08Response\x12\x32\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.Assessment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xf0\x01\n\x10UpdateAssessment\x12\x38\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.AssessmentB\x04\xf8\x86\x19\x01\x12\x35\n\x0bupdate_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMaskB\x04\xf8\x86\x19\x01\x1a>\n\x08Response\x12\x32\n\nassessment\x18\x01 \x01(\x0b\x32\x1e.mlflow.assessments.Assessment:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x80\x01\n\x10\x44\x65leteAssessment\x12\x16\n\x08trace_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x1b\n\rassessment_id\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xe4\x01\n\tTraceInfo\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x15\n\rexperiment_id\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\x12\x19\n\x11\x65xecution_time_ms\x18\x04 \x01(\x03\x12#\n\x06status\x18\x05 \x01(\x0e\x32\x13.mlflow.TraceStatus\x12\x36\n\x10request_metadata\x18\x06 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x07 \x03(\x0b\x32\x10.mlflow.TraceTag\"2\n\x14TraceRequestMetadata\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"&\n\x08TraceTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xf1\x01\n\nStartTrace\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x36\n\x10request_metadata\x18\x03 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x04 \x03(\x0b\x32\x10.mlflow.TraceTag\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x91\x02\n\x08\x45ndTrace\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12#\n\x06status\x18\x03 \x01(\x0e\x32\x13.mlflow.TraceStatus\x12\x36\n\x10request_metadata\x18\x04 \x03(\x0b\x32\x1c.mlflow.TraceRequestMetadata\x12\x1e\n\x04tags\x18\x05 \x03(\x0b\x32\x10.mlflow.TraceTag\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x82\x01\n\x0cGetTraceInfo\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x1a\x31\n\x08Response\x12%\n\ntrace_info\x18\x01 \x01(\x0b\x32\x11.mlflow.TraceInfo:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"y\n\x0eGetTraceInfoV3\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x1a(\n\x08Response\x12\x1c\n\x05trace\x18\x01 \x01(\x0b\x32\r.mlflow.Trace:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xeb\x01\n\x0cSearchTraces\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x12\x0e\n\x06\x66ilter\x18\x02 \x01(\t\x12\x18\n\x0bmax_results\x18\x03 \x01(\x05:\x03\x31\x30\x30\x12\x10\n\x08order_by\x18\x04 \x03(\t\x12\x12\n\npage_token\x18\x05 \x01(\t\x1a\x46\n\x08Response\x12!\n\x06traces\x18\x01 \x03(\x0b\x32\x11.mlflow.TraceInfo\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xc3\x01\n\x0c\x44\x65leteTraces\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x1c\n\x14max_timestamp_millis\x18\x02 \x01(\x03\x12\x12\n\nmax_traces\x18\x03 \x01(\x05\x12\x13\n\x0brequest_ids\x18\x04 \x03(\t\x1a\"\n\x08Response\x12\x16\n\x0etraces_deleted\x18\x01 \x01(\x05:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"v\n\x0bSetTraceTag\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"j\n\x0e\x44\x65leteTraceTag\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"0\n\x05Trace\x12\'\n\ntrace_info\x18\x01 \x01(\x0b\x32\x13.mlflow.TraceInfoV3\"\xb6\x03\n\rTraceLocation\x12\x35\n\x04type\x18\x01 \x01(\x0e\x32\'.mlflow.TraceLocation.TraceLocationType\x12K\n\x11mlflow_experiment\x18\x02 \x01(\x0b\x32..mlflow.TraceLocation.MlflowExperimentLocationH\x00\x12G\n\x0finference_table\x18\x03 \x01(\x0b\x32,.mlflow.TraceLocation.InferenceTableLocationH\x00\x1a\x31\n\x18MlflowExperimentLocation\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x1a\x31\n\x16InferenceTableLocation\x12\x17\n\x0f\x66ull_table_name\x18\x01 \x01(\t\"d\n\x11TraceLocationType\x12#\n\x1fTRACE_LOCATION_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11MLFLOW_EXPERIMENT\x10\x01\x12\x13\n\x0fINFERENCE_TABLE\x10\x02\x42\x0c\n\nidentifier\"\xe8\x04\n\x0bTraceInfoV3\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x19\n\x11\x63lient_request_id\x18\x02 \x01(\t\x12-\n\x0etrace_location\x18\x03 \x01(\x0b\x32\x15.mlflow.TraceLocation\x12\x0f\n\x07request\x18\x04 \x01(\t\x12\x10\n\x08response\x18\x05 \x01(\t\x12\x30\n\x0crequest_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x12\x65xecution_duration\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12(\n\x05state\x18\x08 \x01(\x0e\x32\x19.mlflow.TraceInfoV3.State\x12>\n\x0etrace_metadata\x18\t \x03(\x0b\x32&.mlflow.TraceInfoV3.TraceMetadataEntry\x12\x33\n\x0b\x61ssessments\x18\n \x03(\x0b\x32\x1e.mlflow.assessments.Assessment\x12+\n\x04tags\x18\x0b \x03(\x0b\x32\x1d.mlflow.TraceInfoV3.TagsEntry\x1a\x34\n\x12TraceMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\"h\n\x0e\x44\x61tasetSummary\x12\x1b\n\rexperiment_id\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x12\n\x04name\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06\x64igest\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0f\n\x07\x63ontext\x18\x04 \x01(\t\"\x94\x01\n\x0eSearchDatasets\x12\x16\n\x0e\x65xperiment_ids\x18\x01 \x03(\t\x1a=\n\x08Response\x12\x31\n\x11\x64\x61taset_summaries\x18\x01 \x03(\x0b\x32\x16.mlflow.DatasetSummary:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]*6\n\x08ViewType\x12\x0f\n\x0b\x41\x43TIVE_ONLY\x10\x01\x12\x10\n\x0c\x44\x45LETED_ONLY\x10\x02\x12\x07\n\x03\x41LL\x10\x03*I\n\nSourceType\x12\x0c\n\x08NOTEBOOK\x10\x01\x12\x07\n\x03JOB\x10\x02\x12\x0b\n\x07PROJECT\x10\x03\x12\t\n\x05LOCAL\x10\x04\x12\x0c\n\x07UNKNOWN\x10\xe8\x07*M\n\tRunStatus\x12\x0b\n\x07RUNNING\x10\x01\x12\r\n\tSCHEDULED\x10\x02\x12\x0c\n\x08\x46INISHED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\n\n\x06KILLED\x10\x05*O\n\x0bTraceStatus\x12\x1c\n\x18TRACE_STATUS_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\x32\xde\'\n\rMlflowService\x12\xa6\x01\n\x13getExperimentByName\x12\x1b.mlflow.GetExperimentByName\x1a$.mlflow.GetExperimentByName.Response\"L\xf2\x86\x19H\n,\n\x03GET\x12\x1f/mlflow/experiments/get-by-name\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Get Experiment By Name\x12\x94\x01\n\x10\x63reateExperiment\x12\x18.mlflow.CreateExperiment\x1a!.mlflow.CreateExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x11\x43reate Experiment\x12\xc1\x01\n\x11searchExperiments\x12\x19.mlflow.SearchExperiments\x1a\".mlflow.SearchExperiments.Response\"m\xf2\x86\x19i\n(\n\x04POST\x12\x1a/mlflow/experiments/search\x1a\x04\x08\x02\x10\x00\n\'\n\x03GET\x12\x1a/mlflow/experiments/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Search Experiments\x12\x88\x01\n\rgetExperiment\x12\x15.mlflow.GetExperiment\x1a\x1e.mlflow.GetExperiment.Response\"@\xf2\x86\x19\x38\n$\n\x03GET\x12\x17/mlflow/experiments/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x0eGet Experiment\xba\x8c\x19\x00\x12\x94\x01\n\x10\x64\x65leteExperiment\x12\x18.mlflow.DeleteExperiment\x1a!.mlflow.DeleteExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x11\x44\x65lete Experiment\x12\x99\x01\n\x11restoreExperiment\x12\x19.mlflow.RestoreExperiment\x1a\".mlflow.RestoreExperiment.Response\"E\xf2\x86\x19\x41\n)\n\x04POST\x12\x1b/mlflow/experiments/restore\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Restore Experiment\x12\x94\x01\n\x10updateExperiment\x12\x18.mlflow.UpdateExperiment\x1a!.mlflow.UpdateExperiment.Response\"C\xf2\x86\x19?\n(\n\x04POST\x12\x1a/mlflow/experiments/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x11Update Experiment\x12q\n\tcreateRun\x12\x11.mlflow.CreateRun\x1a\x1a.mlflow.CreateRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/create\x1a\x04\x08\x02\x10\x00\x10\x01*\nCreate Run\x12q\n\tupdateRun\x12\x11.mlflow.UpdateRun\x1a\x1a.mlflow.UpdateRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/update\x1a\x04\x08\x02\x10\x00\x10\x01*\nUpdate Run\x12q\n\tdeleteRun\x12\x11.mlflow.DeleteRun\x1a\x1a.mlflow.DeleteRun.Response\"5\xf2\x86\x19\x31\n!\n\x04POST\x12\x13/mlflow/runs/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\nDelete Run\x12v\n\nrestoreRun\x12\x12.mlflow.RestoreRun\x1a\x1b.mlflow.RestoreRun.Response\"7\xf2\x86\x19\x33\n\"\n\x04POST\x12\x14/mlflow/runs/restore\x1a\x04\x08\x02\x10\x00\x10\x01*\x0bRestore Run\x12u\n\tlogMetric\x12\x11.mlflow.LogMetric\x1a\x1a.mlflow.LogMetric.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/log-metric\x1a\x04\x08\x02\x10\x00\x10\x01*\nLog Metric\x12t\n\x08logParam\x12\x10.mlflow.LogParam\x1a\x19.mlflow.LogParam.Response\";\xf2\x86\x19\x37\n(\n\x04POST\x12\x1a/mlflow/runs/log-parameter\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Param\x12\xa1\x01\n\x10setExperimentTag\x12\x18.mlflow.SetExperimentTag\x1a!.mlflow.SetExperimentTag.Response\"P\xf2\x86\x19L\n4\n\x04POST\x12&/mlflow/experiments/set-experiment-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Set Experiment Tag\x12\x66\n\x06setTag\x12\x0e.mlflow.SetTag\x1a\x17.mlflow.SetTag.Response\"3\xf2\x86\x19/\n\"\n\x04POST\x12\x14/mlflow/runs/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x07Set Tag\x12\x88\x01\n\x0bsetTraceTag\x12\x13.mlflow.SetTraceTag\x1a\x1c.mlflow.SetTraceTag.Response\"F\xf2\x86\x19\x42\n/\n\x05PATCH\x12 /mlflow/traces/{request_id}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\rSet Trace Tag\x12\x95\x01\n\x0e\x64\x65leteTraceTag\x12\x16.mlflow.DeleteTraceTag\x1a\x1f.mlflow.DeleteTraceTag.Response\"J\xf2\x86\x19\x46\n0\n\x06\x44\x45LETE\x12 /mlflow/traces/{request_id}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\x10\x44\x65lete Trace Tag\x12u\n\tdeleteTag\x12\x11.mlflow.DeleteTag\x1a\x1a.mlflow.DeleteTag.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\nDelete Tag\x12\x65\n\x06getRun\x12\x0e.mlflow.GetRun\x1a\x17.mlflow.GetRun.Response\"2\xf2\x86\x19*\n\x1d\n\x03GET\x12\x10/mlflow/runs/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x07Get Run\xba\x8c\x19\x00\x12y\n\nsearchRuns\x12\x12.mlflow.SearchRuns\x1a\x1b.mlflow.SearchRuns.Response\":\xf2\x86\x19\x32\n!\n\x04POST\x12\x13/mlflow/runs/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x0bSearch Runs\xba\x8c\x19\x00\x12\x87\x01\n\rlistArtifacts\x12\x15.mlflow.ListArtifacts\x1a\x1e.mlflow.ListArtifacts.Response\"?\xf2\x86\x19\x37\n#\n\x03GET\x12\x16/mlflow/artifacts/list\x1a\x04\x08\x02\x10\x00\x10\x01*\x0eList Artifacts\xba\x8c\x19\x00\x12\x95\x01\n\x10getMetricHistory\x12\x18.mlflow.GetMetricHistory\x1a!.mlflow.GetMetricHistory.Response\"D\xf2\x86\x19@\n(\n\x03GET\x12\x1b/mlflow/metrics/get-history\x1a\x04\x08\x02\x10\x00\x10\x01*\x12Get Metric History\x12\xb7\x01\n\x1cgetMetricHistoryBulkInterval\x12$.mlflow.GetMetricHistoryBulkInterval\x1a-.mlflow.GetMetricHistoryBulkInterval.Response\"B\xf2\x86\x19:\n6\n\x03GET\x12)/mlflow/metrics/get-history-bulk-interval\x1a\x04\x08\x02\x10\x0b\x10\x03\xba\x8c\x19\x00\x12p\n\x08logBatch\x12\x10.mlflow.LogBatch\x1a\x19.mlflow.LogBatch.Response\"7\xf2\x86\x19\x33\n$\n\x04POST\x12\x16/mlflow/runs/log-batch\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Batch\x12p\n\x08logModel\x12\x10.mlflow.LogModel\x1a\x19.mlflow.LogModel.Response\"7\xf2\x86\x19\x33\n$\n\x04POST\x12\x16/mlflow/runs/log-model\x1a\x04\x08\x02\x10\x00\x10\x01*\tLog Model\x12u\n\tlogInputs\x12\x11.mlflow.LogInputs\x1a\x1a.mlflow.LogInputs.Response\"9\xf2\x86\x19\x35\n%\n\x04POST\x12\x17/mlflow/runs/log-inputs\x1a\x04\x08\x02\x10\x00\x10\x01*\nLog Inputs\x12\x87\x01\n\x0esearchDatasets\x12\x16.mlflow.SearchDatasets\x1a\x1f.mlflow.SearchDatasets.Response\"<\xf2\x86\x19\x34\n0\n\x04POST\x12\"mlflow/experiments/search-datasets\x1a\x04\x08\x02\x10\x00\x10\x03\xba\x8c\x19\x00\x12p\n\nstartTrace\x12\x12.mlflow.StartTrace\x1a\x1b.mlflow.StartTrace.Response\"1\xf2\x86\x19-\n\x1c\n\x04POST\x12\x0e/mlflow/traces\x1a\x04\x08\x02\x10\x00\x10\x03*\x0bStart Trace\x12v\n\x08\x65ndTrace\x12\x10.mlflow.EndTrace\x1a\x19.mlflow.EndTrace.Response\"=\xf2\x86\x19\x39\n*\n\x05PATCH\x12\x1b/mlflow/traces/{request_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\tEnd Trace\x12\x89\x01\n\x0cgetTraceInfo\x12\x14.mlflow.GetTraceInfo\x1a\x1d.mlflow.GetTraceInfo.Response\"D\xf2\x86\x19@\n-\n\x03GET\x12 /mlflow/traces/{request_id}/info\x1a\x04\x08\x02\x10\x00\x10\x03*\rGet TraceInfo\x12\x8b\x01\n\x0egetTraceInfoV3\x12\x16.mlflow.GetTraceInfoV3\x1a\x1f.mlflow.GetTraceInfoV3.Response\"@\xf2\x86\x19<\n&\n\x03GET\x12\x19/mlflow/traces/{trace_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\x10Get TraceInfo v3\x12w\n\x0csearchTraces\x12\x14.mlflow.SearchTraces\x1a\x1d.mlflow.SearchTraces.Response\"2\xf2\x86\x19.\n\x1b\n\x03GET\x12\x0e/mlflow/traces\x1a\x04\x08\x02\x10\x00\x10\x03*\rSearch Traces\x12\x86\x01\n\x0c\x64\x65leteTraces\x12\x14.mlflow.DeleteTraces\x1a\x1d.mlflow.DeleteTraces.Response\"A\xf2\x86\x19=\n*\n\x04POST\x12\x1c/mlflow/traces/delete-traces\x1a\x04\x08\x02\x10\x00\x10\x03*\rDelete Traces\x12\xdf\x01\n\x10\x63reateAssessment\x12\x18.mlflow.CreateAssessment\x1a!.mlflow.CreateAssessment.Response\"\x8d\x01\xf2\x86\x19\x88\x01\n>\n\x04POST\x12\x30/mlflow/traces/{assessment.trace_id}/assessments\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xee\x07\x18\x0c\x18\x01*:Create an assessment of a trace or a span within the trace\x12\xd0\x01\n\x10updateAssessment\x12\x18.mlflow.UpdateAssessment\x1a!.mlflow.UpdateAssessment.Response\"\x7f\xf2\x86\x19{\nD\n\x05PATCH\x12\x35/mlflow/traces/{trace_id}/assessments/{assessment_id}\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xee\x07\x18\x01*)Update an existing assessment on a trace.\x12\xb1\x01\n\x10\x64\x65leteAssessment\x12\x18.mlflow.DeleteAssessment\x1a!.mlflow.DeleteAssessment.Response\"`\xf2\x86\x19\\\nE\n\x06\x44\x45LETE\x12\x35/mlflow/traces/{trace_id}/assessments/{assessment_id}\x1a\x04\x08\x02\x10\x00\x10\x03*\x11\x44\x65lete AssessmentB\x1e\n\x14org.mlflow.api.proto\x90\x01\x01\xe2?\x02\x10\x01')

  _VIEWTYPE = DESCRIPTOR.enum_types_by_name['ViewType']
  ViewType = enum_type_wrapper.EnumTypeWrapper(_VIEWTYPE)
  _SOURCETYPE = DESCRIPTOR.enum_types_by_name['SourceType']
  SourceType = enum_type_wrapper.EnumTypeWrapper(_SOURCETYPE)
  _RUNSTATUS = DESCRIPTOR.enum_types_by_name['RunStatus']
  RunStatus = enum_type_wrapper.EnumTypeWrapper(_RUNSTATUS)
  _TRACESTATUS = DESCRIPTOR.enum_types_by_name['TraceStatus']
  TraceStatus = enum_type_wrapper.EnumTypeWrapper(_TRACESTATUS)
  ACTIVE_ONLY = 1
  DELETED_ONLY = 2
  ALL = 3
  NOTEBOOK = 1
  JOB = 2
  PROJECT = 3
  LOCAL = 4
  UNKNOWN = 1000
  RUNNING = 1
  SCHEDULED = 2
  FINISHED = 3
  FAILED = 4
  KILLED = 5
  TRACE_STATUS_UNSPECIFIED = 0
  OK = 1
  ERROR = 2
  IN_PROGRESS = 3


  _METRIC = DESCRIPTOR.message_types_by_name['Metric']
  _PARAM = DESCRIPTOR.message_types_by_name['Param']
  _RUN = DESCRIPTOR.message_types_by_name['Run']
  _RUNDATA = DESCRIPTOR.message_types_by_name['RunData']
  _RUNINPUTS = DESCRIPTOR.message_types_by_name['RunInputs']
  _RUNTAG = DESCRIPTOR.message_types_by_name['RunTag']
  _EXPERIMENTTAG = DESCRIPTOR.message_types_by_name['ExperimentTag']
  _RUNINFO = DESCRIPTOR.message_types_by_name['RunInfo']
  _EXPERIMENT = DESCRIPTOR.message_types_by_name['Experiment']
  _DATASETINPUT = DESCRIPTOR.message_types_by_name['DatasetInput']
  _INPUTTAG = DESCRIPTOR.message_types_by_name['InputTag']
  _DATASET = DESCRIPTOR.message_types_by_name['Dataset']
  _CREATEEXPERIMENT = DESCRIPTOR.message_types_by_name['CreateExperiment']
  _CREATEEXPERIMENT_RESPONSE = _CREATEEXPERIMENT.nested_types_by_name['Response']
  _SEARCHEXPERIMENTS = DESCRIPTOR.message_types_by_name['SearchExperiments']
  _SEARCHEXPERIMENTS_RESPONSE = _SEARCHEXPERIMENTS.nested_types_by_name['Response']
  _GETEXPERIMENT = DESCRIPTOR.message_types_by_name['GetExperiment']
  _GETEXPERIMENT_RESPONSE = _GETEXPERIMENT.nested_types_by_name['Response']
  _DELETEEXPERIMENT = DESCRIPTOR.message_types_by_name['DeleteExperiment']
  _DELETEEXPERIMENT_RESPONSE = _DELETEEXPERIMENT.nested_types_by_name['Response']
  _RESTOREEXPERIMENT = DESCRIPTOR.message_types_by_name['RestoreExperiment']
  _RESTOREEXPERIMENT_RESPONSE = _RESTOREEXPERIMENT.nested_types_by_name['Response']
  _UPDATEEXPERIMENT = DESCRIPTOR.message_types_by_name['UpdateExperiment']
  _UPDATEEXPERIMENT_RESPONSE = _UPDATEEXPERIMENT.nested_types_by_name['Response']
  _CREATERUN = DESCRIPTOR.message_types_by_name['CreateRun']
  _CREATERUN_RESPONSE = _CREATERUN.nested_types_by_name['Response']
  _UPDATERUN = DESCRIPTOR.message_types_by_name['UpdateRun']
  _UPDATERUN_RESPONSE = _UPDATERUN.nested_types_by_name['Response']
  _DELETERUN = DESCRIPTOR.message_types_by_name['DeleteRun']
  _DELETERUN_RESPONSE = _DELETERUN.nested_types_by_name['Response']
  _RESTORERUN = DESCRIPTOR.message_types_by_name['RestoreRun']
  _RESTORERUN_RESPONSE = _RESTORERUN.nested_types_by_name['Response']
  _LOGMETRIC = DESCRIPTOR.message_types_by_name['LogMetric']
  _LOGMETRIC_RESPONSE = _LOGMETRIC.nested_types_by_name['Response']
  _LOGPARAM = DESCRIPTOR.message_types_by_name['LogParam']
  _LOGPARAM_RESPONSE = _LOGPARAM.nested_types_by_name['Response']
  _SETEXPERIMENTTAG = DESCRIPTOR.message_types_by_name['SetExperimentTag']
  _SETEXPERIMENTTAG_RESPONSE = _SETEXPERIMENTTAG.nested_types_by_name['Response']
  _SETTAG = DESCRIPTOR.message_types_by_name['SetTag']
  _SETTAG_RESPONSE = _SETTAG.nested_types_by_name['Response']
  _DELETETAG = DESCRIPTOR.message_types_by_name['DeleteTag']
  _DELETETAG_RESPONSE = _DELETETAG.nested_types_by_name['Response']
  _GETRUN = DESCRIPTOR.message_types_by_name['GetRun']
  _GETRUN_RESPONSE = _GETRUN.nested_types_by_name['Response']
  _SEARCHRUNS = DESCRIPTOR.message_types_by_name['SearchRuns']
  _SEARCHRUNS_RESPONSE = _SEARCHRUNS.nested_types_by_name['Response']
  _LISTARTIFACTS = DESCRIPTOR.message_types_by_name['ListArtifacts']
  _LISTARTIFACTS_RESPONSE = _LISTARTIFACTS.nested_types_by_name['Response']
  _FILEINFO = DESCRIPTOR.message_types_by_name['FileInfo']
  _GETMETRICHISTORY = DESCRIPTOR.message_types_by_name['GetMetricHistory']
  _GETMETRICHISTORY_RESPONSE = _GETMETRICHISTORY.nested_types_by_name['Response']
  _METRICWITHRUNID = DESCRIPTOR.message_types_by_name['MetricWithRunId']
  _GETMETRICHISTORYBULKINTERVAL = DESCRIPTOR.message_types_by_name['GetMetricHistoryBulkInterval']
  _GETMETRICHISTORYBULKINTERVAL_RESPONSE = _GETMETRICHISTORYBULKINTERVAL.nested_types_by_name['Response']
  _LOGBATCH = DESCRIPTOR.message_types_by_name['LogBatch']
  _LOGBATCH_RESPONSE = _LOGBATCH.nested_types_by_name['Response']
  _LOGMODEL = DESCRIPTOR.message_types_by_name['LogModel']
  _LOGMODEL_RESPONSE = _LOGMODEL.nested_types_by_name['Response']
  _LOGINPUTS = DESCRIPTOR.message_types_by_name['LogInputs']
  _LOGINPUTS_RESPONSE = _LOGINPUTS.nested_types_by_name['Response']
  _GETEXPERIMENTBYNAME = DESCRIPTOR.message_types_by_name['GetExperimentByName']
  _GETEXPERIMENTBYNAME_RESPONSE = _GETEXPERIMENTBYNAME.nested_types_by_name['Response']
  _ASSESSMENTSOURCE = DESCRIPTOR.message_types_by_name['AssessmentSource']
  _ASSESSMENTERROR = DESCRIPTOR.message_types_by_name['AssessmentError']
  _CREATEASSESSMENT = DESCRIPTOR.message_types_by_name['CreateAssessment']
  _CREATEASSESSMENT_RESPONSE = _CREATEASSESSMENT.nested_types_by_name['Response']
  _UPDATEASSESSMENT = DESCRIPTOR.message_types_by_name['UpdateAssessment']
  _UPDATEASSESSMENT_RESPONSE = _UPDATEASSESSMENT.nested_types_by_name['Response']
  _DELETEASSESSMENT = DESCRIPTOR.message_types_by_name['DeleteAssessment']
  _DELETEASSESSMENT_RESPONSE = _DELETEASSESSMENT.nested_types_by_name['Response']
  _TRACEINFO = DESCRIPTOR.message_types_by_name['TraceInfo']
  _TRACEREQUESTMETADATA = DESCRIPTOR.message_types_by_name['TraceRequestMetadata']
  _TRACETAG = DESCRIPTOR.message_types_by_name['TraceTag']
  _STARTTRACE = DESCRIPTOR.message_types_by_name['StartTrace']
  _STARTTRACE_RESPONSE = _STARTTRACE.nested_types_by_name['Response']
  _ENDTRACE = DESCRIPTOR.message_types_by_name['EndTrace']
  _ENDTRACE_RESPONSE = _ENDTRACE.nested_types_by_name['Response']
  _GETTRACEINFO = DESCRIPTOR.message_types_by_name['GetTraceInfo']
  _GETTRACEINFO_RESPONSE = _GETTRACEINFO.nested_types_by_name['Response']
  _GETTRACEINFOV3 = DESCRIPTOR.message_types_by_name['GetTraceInfoV3']
  _GETTRACEINFOV3_RESPONSE = _GETTRACEINFOV3.nested_types_by_name['Response']
  _SEARCHTRACES = DESCRIPTOR.message_types_by_name['SearchTraces']
  _SEARCHTRACES_RESPONSE = _SEARCHTRACES.nested_types_by_name['Response']
  _DELETETRACES = DESCRIPTOR.message_types_by_name['DeleteTraces']
  _DELETETRACES_RESPONSE = _DELETETRACES.nested_types_by_name['Response']
  _SETTRACETAG = DESCRIPTOR.message_types_by_name['SetTraceTag']
  _SETTRACETAG_RESPONSE = _SETTRACETAG.nested_types_by_name['Response']
  _DELETETRACETAG = DESCRIPTOR.message_types_by_name['DeleteTraceTag']
  _DELETETRACETAG_RESPONSE = _DELETETRACETAG.nested_types_by_name['Response']
  _TRACE = DESCRIPTOR.message_types_by_name['Trace']
  _TRACELOCATION = DESCRIPTOR.message_types_by_name['TraceLocation']
  _TRACELOCATION_MLFLOWEXPERIMENTLOCATION = _TRACELOCATION.nested_types_by_name['MlflowExperimentLocation']
  _TRACELOCATION_INFERENCETABLELOCATION = _TRACELOCATION.nested_types_by_name['InferenceTableLocation']
  _TRACEINFOV3 = DESCRIPTOR.message_types_by_name['TraceInfoV3']
  _TRACEINFOV3_TRACEMETADATAENTRY = _TRACEINFOV3.nested_types_by_name['TraceMetadataEntry']
  _TRACEINFOV3_TAGSENTRY = _TRACEINFOV3.nested_types_by_name['TagsEntry']
  _DATASETSUMMARY = DESCRIPTOR.message_types_by_name['DatasetSummary']
  _SEARCHDATASETS = DESCRIPTOR.message_types_by_name['SearchDatasets']
  _SEARCHDATASETS_RESPONSE = _SEARCHDATASETS.nested_types_by_name['Response']
  _ASSESSMENTSOURCE_SOURCETYPE = _ASSESSMENTSOURCE.enum_types_by_name['SourceType']
  _TRACELOCATION_TRACELOCATIONTYPE = _TRACELOCATION.enum_types_by_name['TraceLocationType']
  _TRACEINFOV3_STATE = _TRACEINFOV3.enum_types_by_name['State']
  Metric = _reflection.GeneratedProtocolMessageType('Metric', (_message.Message,), {
    'DESCRIPTOR' : _METRIC,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Metric)
    })
  _sym_db.RegisterMessage(Metric)

  Param = _reflection.GeneratedProtocolMessageType('Param', (_message.Message,), {
    'DESCRIPTOR' : _PARAM,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Param)
    })
  _sym_db.RegisterMessage(Param)

  Run = _reflection.GeneratedProtocolMessageType('Run', (_message.Message,), {
    'DESCRIPTOR' : _RUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Run)
    })
  _sym_db.RegisterMessage(Run)

  RunData = _reflection.GeneratedProtocolMessageType('RunData', (_message.Message,), {
    'DESCRIPTOR' : _RUNDATA,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RunData)
    })
  _sym_db.RegisterMessage(RunData)

  RunInputs = _reflection.GeneratedProtocolMessageType('RunInputs', (_message.Message,), {
    'DESCRIPTOR' : _RUNINPUTS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RunInputs)
    })
  _sym_db.RegisterMessage(RunInputs)

  RunTag = _reflection.GeneratedProtocolMessageType('RunTag', (_message.Message,), {
    'DESCRIPTOR' : _RUNTAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RunTag)
    })
  _sym_db.RegisterMessage(RunTag)

  ExperimentTag = _reflection.GeneratedProtocolMessageType('ExperimentTag', (_message.Message,), {
    'DESCRIPTOR' : _EXPERIMENTTAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ExperimentTag)
    })
  _sym_db.RegisterMessage(ExperimentTag)

  RunInfo = _reflection.GeneratedProtocolMessageType('RunInfo', (_message.Message,), {
    'DESCRIPTOR' : _RUNINFO,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RunInfo)
    })
  _sym_db.RegisterMessage(RunInfo)

  Experiment = _reflection.GeneratedProtocolMessageType('Experiment', (_message.Message,), {
    'DESCRIPTOR' : _EXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Experiment)
    })
  _sym_db.RegisterMessage(Experiment)

  DatasetInput = _reflection.GeneratedProtocolMessageType('DatasetInput', (_message.Message,), {
    'DESCRIPTOR' : _DATASETINPUT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DatasetInput)
    })
  _sym_db.RegisterMessage(DatasetInput)

  InputTag = _reflection.GeneratedProtocolMessageType('InputTag', (_message.Message,), {
    'DESCRIPTOR' : _INPUTTAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.InputTag)
    })
  _sym_db.RegisterMessage(InputTag)

  Dataset = _reflection.GeneratedProtocolMessageType('Dataset', (_message.Message,), {
    'DESCRIPTOR' : _DATASET,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Dataset)
    })
  _sym_db.RegisterMessage(Dataset)

  CreateExperiment = _reflection.GeneratedProtocolMessageType('CreateExperiment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _CREATEEXPERIMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.CreateExperiment.Response)
      })
    ,
    'DESCRIPTOR' : _CREATEEXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.CreateExperiment)
    })
  _sym_db.RegisterMessage(CreateExperiment)
  _sym_db.RegisterMessage(CreateExperiment.Response)

  SearchExperiments = _reflection.GeneratedProtocolMessageType('SearchExperiments', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHEXPERIMENTS_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchExperiments.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHEXPERIMENTS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchExperiments)
    })
  _sym_db.RegisterMessage(SearchExperiments)
  _sym_db.RegisterMessage(SearchExperiments.Response)

  GetExperiment = _reflection.GeneratedProtocolMessageType('GetExperiment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETEXPERIMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetExperiment.Response)
      })
    ,
    'DESCRIPTOR' : _GETEXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetExperiment)
    })
  _sym_db.RegisterMessage(GetExperiment)
  _sym_db.RegisterMessage(GetExperiment.Response)

  DeleteExperiment = _reflection.GeneratedProtocolMessageType('DeleteExperiment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEEXPERIMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteExperiment.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEEXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteExperiment)
    })
  _sym_db.RegisterMessage(DeleteExperiment)
  _sym_db.RegisterMessage(DeleteExperiment.Response)

  RestoreExperiment = _reflection.GeneratedProtocolMessageType('RestoreExperiment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _RESTOREEXPERIMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.RestoreExperiment.Response)
      })
    ,
    'DESCRIPTOR' : _RESTOREEXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RestoreExperiment)
    })
  _sym_db.RegisterMessage(RestoreExperiment)
  _sym_db.RegisterMessage(RestoreExperiment.Response)

  UpdateExperiment = _reflection.GeneratedProtocolMessageType('UpdateExperiment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _UPDATEEXPERIMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.UpdateExperiment.Response)
      })
    ,
    'DESCRIPTOR' : _UPDATEEXPERIMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.UpdateExperiment)
    })
  _sym_db.RegisterMessage(UpdateExperiment)
  _sym_db.RegisterMessage(UpdateExperiment.Response)

  CreateRun = _reflection.GeneratedProtocolMessageType('CreateRun', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _CREATERUN_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.CreateRun.Response)
      })
    ,
    'DESCRIPTOR' : _CREATERUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.CreateRun)
    })
  _sym_db.RegisterMessage(CreateRun)
  _sym_db.RegisterMessage(CreateRun.Response)

  UpdateRun = _reflection.GeneratedProtocolMessageType('UpdateRun', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _UPDATERUN_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.UpdateRun.Response)
      })
    ,
    'DESCRIPTOR' : _UPDATERUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.UpdateRun)
    })
  _sym_db.RegisterMessage(UpdateRun)
  _sym_db.RegisterMessage(UpdateRun.Response)

  DeleteRun = _reflection.GeneratedProtocolMessageType('DeleteRun', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETERUN_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteRun.Response)
      })
    ,
    'DESCRIPTOR' : _DELETERUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteRun)
    })
  _sym_db.RegisterMessage(DeleteRun)
  _sym_db.RegisterMessage(DeleteRun.Response)

  RestoreRun = _reflection.GeneratedProtocolMessageType('RestoreRun', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _RESTORERUN_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.RestoreRun.Response)
      })
    ,
    'DESCRIPTOR' : _RESTORERUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RestoreRun)
    })
  _sym_db.RegisterMessage(RestoreRun)
  _sym_db.RegisterMessage(RestoreRun.Response)

  LogMetric = _reflection.GeneratedProtocolMessageType('LogMetric', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LOGMETRIC_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.LogMetric.Response)
      })
    ,
    'DESCRIPTOR' : _LOGMETRIC,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.LogMetric)
    })
  _sym_db.RegisterMessage(LogMetric)
  _sym_db.RegisterMessage(LogMetric.Response)

  LogParam = _reflection.GeneratedProtocolMessageType('LogParam', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LOGPARAM_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.LogParam.Response)
      })
    ,
    'DESCRIPTOR' : _LOGPARAM,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.LogParam)
    })
  _sym_db.RegisterMessage(LogParam)
  _sym_db.RegisterMessage(LogParam.Response)

  SetExperimentTag = _reflection.GeneratedProtocolMessageType('SetExperimentTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETEXPERIMENTTAG_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetExperimentTag.Response)
      })
    ,
    'DESCRIPTOR' : _SETEXPERIMENTTAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetExperimentTag)
    })
  _sym_db.RegisterMessage(SetExperimentTag)
  _sym_db.RegisterMessage(SetExperimentTag.Response)

  SetTag = _reflection.GeneratedProtocolMessageType('SetTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETTAG_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetTag.Response)
      })
    ,
    'DESCRIPTOR' : _SETTAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetTag)
    })
  _sym_db.RegisterMessage(SetTag)
  _sym_db.RegisterMessage(SetTag.Response)

  DeleteTag = _reflection.GeneratedProtocolMessageType('DeleteTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETETAG_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteTag.Response)
      })
    ,
    'DESCRIPTOR' : _DELETETAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteTag)
    })
  _sym_db.RegisterMessage(DeleteTag)
  _sym_db.RegisterMessage(DeleteTag.Response)

  GetRun = _reflection.GeneratedProtocolMessageType('GetRun', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETRUN_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetRun.Response)
      })
    ,
    'DESCRIPTOR' : _GETRUN,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetRun)
    })
  _sym_db.RegisterMessage(GetRun)
  _sym_db.RegisterMessage(GetRun.Response)

  SearchRuns = _reflection.GeneratedProtocolMessageType('SearchRuns', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHRUNS_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchRuns.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHRUNS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchRuns)
    })
  _sym_db.RegisterMessage(SearchRuns)
  _sym_db.RegisterMessage(SearchRuns.Response)

  ListArtifacts = _reflection.GeneratedProtocolMessageType('ListArtifacts', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LISTARTIFACTS_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.ListArtifacts.Response)
      })
    ,
    'DESCRIPTOR' : _LISTARTIFACTS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ListArtifacts)
    })
  _sym_db.RegisterMessage(ListArtifacts)
  _sym_db.RegisterMessage(ListArtifacts.Response)

  FileInfo = _reflection.GeneratedProtocolMessageType('FileInfo', (_message.Message,), {
    'DESCRIPTOR' : _FILEINFO,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.FileInfo)
    })
  _sym_db.RegisterMessage(FileInfo)

  GetMetricHistory = _reflection.GeneratedProtocolMessageType('GetMetricHistory', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETMETRICHISTORY_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetMetricHistory.Response)
      })
    ,
    'DESCRIPTOR' : _GETMETRICHISTORY,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetMetricHistory)
    })
  _sym_db.RegisterMessage(GetMetricHistory)
  _sym_db.RegisterMessage(GetMetricHistory.Response)

  MetricWithRunId = _reflection.GeneratedProtocolMessageType('MetricWithRunId', (_message.Message,), {
    'DESCRIPTOR' : _METRICWITHRUNID,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.MetricWithRunId)
    })
  _sym_db.RegisterMessage(MetricWithRunId)

  GetMetricHistoryBulkInterval = _reflection.GeneratedProtocolMessageType('GetMetricHistoryBulkInterval', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETMETRICHISTORYBULKINTERVAL_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetMetricHistoryBulkInterval.Response)
      })
    ,
    'DESCRIPTOR' : _GETMETRICHISTORYBULKINTERVAL,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetMetricHistoryBulkInterval)
    })
  _sym_db.RegisterMessage(GetMetricHistoryBulkInterval)
  _sym_db.RegisterMessage(GetMetricHistoryBulkInterval.Response)

  LogBatch = _reflection.GeneratedProtocolMessageType('LogBatch', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LOGBATCH_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.LogBatch.Response)
      })
    ,
    'DESCRIPTOR' : _LOGBATCH,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.LogBatch)
    })
  _sym_db.RegisterMessage(LogBatch)
  _sym_db.RegisterMessage(LogBatch.Response)

  LogModel = _reflection.GeneratedProtocolMessageType('LogModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LOGMODEL_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.LogModel.Response)
      })
    ,
    'DESCRIPTOR' : _LOGMODEL,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.LogModel)
    })
  _sym_db.RegisterMessage(LogModel)
  _sym_db.RegisterMessage(LogModel.Response)

  LogInputs = _reflection.GeneratedProtocolMessageType('LogInputs', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _LOGINPUTS_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.LogInputs.Response)
      })
    ,
    'DESCRIPTOR' : _LOGINPUTS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.LogInputs)
    })
  _sym_db.RegisterMessage(LogInputs)
  _sym_db.RegisterMessage(LogInputs.Response)

  GetExperimentByName = _reflection.GeneratedProtocolMessageType('GetExperimentByName', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETEXPERIMENTBYNAME_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetExperimentByName.Response)
      })
    ,
    'DESCRIPTOR' : _GETEXPERIMENTBYNAME,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetExperimentByName)
    })
  _sym_db.RegisterMessage(GetExperimentByName)
  _sym_db.RegisterMessage(GetExperimentByName.Response)

  AssessmentSource = _reflection.GeneratedProtocolMessageType('AssessmentSource', (_message.Message,), {
    'DESCRIPTOR' : _ASSESSMENTSOURCE,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.AssessmentSource)
    })
  _sym_db.RegisterMessage(AssessmentSource)

  AssessmentError = _reflection.GeneratedProtocolMessageType('AssessmentError', (_message.Message,), {
    'DESCRIPTOR' : _ASSESSMENTERROR,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.AssessmentError)
    })
  _sym_db.RegisterMessage(AssessmentError)

  CreateAssessment = _reflection.GeneratedProtocolMessageType('CreateAssessment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _CREATEASSESSMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.CreateAssessment.Response)
      })
    ,
    'DESCRIPTOR' : _CREATEASSESSMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.CreateAssessment)
    })
  _sym_db.RegisterMessage(CreateAssessment)
  _sym_db.RegisterMessage(CreateAssessment.Response)

  UpdateAssessment = _reflection.GeneratedProtocolMessageType('UpdateAssessment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _UPDATEASSESSMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.UpdateAssessment.Response)
      })
    ,
    'DESCRIPTOR' : _UPDATEASSESSMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.UpdateAssessment)
    })
  _sym_db.RegisterMessage(UpdateAssessment)
  _sym_db.RegisterMessage(UpdateAssessment.Response)

  DeleteAssessment = _reflection.GeneratedProtocolMessageType('DeleteAssessment', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEASSESSMENT_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteAssessment.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEASSESSMENT,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteAssessment)
    })
  _sym_db.RegisterMessage(DeleteAssessment)
  _sym_db.RegisterMessage(DeleteAssessment.Response)

  TraceInfo = _reflection.GeneratedProtocolMessageType('TraceInfo', (_message.Message,), {
    'DESCRIPTOR' : _TRACEINFO,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TraceInfo)
    })
  _sym_db.RegisterMessage(TraceInfo)

  TraceRequestMetadata = _reflection.GeneratedProtocolMessageType('TraceRequestMetadata', (_message.Message,), {
    'DESCRIPTOR' : _TRACEREQUESTMETADATA,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TraceRequestMetadata)
    })
  _sym_db.RegisterMessage(TraceRequestMetadata)

  TraceTag = _reflection.GeneratedProtocolMessageType('TraceTag', (_message.Message,), {
    'DESCRIPTOR' : _TRACETAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TraceTag)
    })
  _sym_db.RegisterMessage(TraceTag)

  StartTrace = _reflection.GeneratedProtocolMessageType('StartTrace', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _STARTTRACE_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.StartTrace.Response)
      })
    ,
    'DESCRIPTOR' : _STARTTRACE,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.StartTrace)
    })
  _sym_db.RegisterMessage(StartTrace)
  _sym_db.RegisterMessage(StartTrace.Response)

  EndTrace = _reflection.GeneratedProtocolMessageType('EndTrace', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _ENDTRACE_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.EndTrace.Response)
      })
    ,
    'DESCRIPTOR' : _ENDTRACE,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.EndTrace)
    })
  _sym_db.RegisterMessage(EndTrace)
  _sym_db.RegisterMessage(EndTrace.Response)

  GetTraceInfo = _reflection.GeneratedProtocolMessageType('GetTraceInfo', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETTRACEINFO_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetTraceInfo.Response)
      })
    ,
    'DESCRIPTOR' : _GETTRACEINFO,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetTraceInfo)
    })
  _sym_db.RegisterMessage(GetTraceInfo)
  _sym_db.RegisterMessage(GetTraceInfo.Response)

  GetTraceInfoV3 = _reflection.GeneratedProtocolMessageType('GetTraceInfoV3', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETTRACEINFOV3_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetTraceInfoV3.Response)
      })
    ,
    'DESCRIPTOR' : _GETTRACEINFOV3,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetTraceInfoV3)
    })
  _sym_db.RegisterMessage(GetTraceInfoV3)
  _sym_db.RegisterMessage(GetTraceInfoV3.Response)

  SearchTraces = _reflection.GeneratedProtocolMessageType('SearchTraces', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHTRACES_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchTraces.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHTRACES,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchTraces)
    })
  _sym_db.RegisterMessage(SearchTraces)
  _sym_db.RegisterMessage(SearchTraces.Response)

  DeleteTraces = _reflection.GeneratedProtocolMessageType('DeleteTraces', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETETRACES_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteTraces.Response)
      })
    ,
    'DESCRIPTOR' : _DELETETRACES,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteTraces)
    })
  _sym_db.RegisterMessage(DeleteTraces)
  _sym_db.RegisterMessage(DeleteTraces.Response)

  SetTraceTag = _reflection.GeneratedProtocolMessageType('SetTraceTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETTRACETAG_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetTraceTag.Response)
      })
    ,
    'DESCRIPTOR' : _SETTRACETAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetTraceTag)
    })
  _sym_db.RegisterMessage(SetTraceTag)
  _sym_db.RegisterMessage(SetTraceTag.Response)

  DeleteTraceTag = _reflection.GeneratedProtocolMessageType('DeleteTraceTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETETRACETAG_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteTraceTag.Response)
      })
    ,
    'DESCRIPTOR' : _DELETETRACETAG,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteTraceTag)
    })
  _sym_db.RegisterMessage(DeleteTraceTag)
  _sym_db.RegisterMessage(DeleteTraceTag.Response)

  Trace = _reflection.GeneratedProtocolMessageType('Trace', (_message.Message,), {
    'DESCRIPTOR' : _TRACE,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.Trace)
    })
  _sym_db.RegisterMessage(Trace)

  TraceLocation = _reflection.GeneratedProtocolMessageType('TraceLocation', (_message.Message,), {

    'MlflowExperimentLocation' : _reflection.GeneratedProtocolMessageType('MlflowExperimentLocation', (_message.Message,), {
      'DESCRIPTOR' : _TRACELOCATION_MLFLOWEXPERIMENTLOCATION,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.TraceLocation.MlflowExperimentLocation)
      })
    ,

    'InferenceTableLocation' : _reflection.GeneratedProtocolMessageType('InferenceTableLocation', (_message.Message,), {
      'DESCRIPTOR' : _TRACELOCATION_INFERENCETABLELOCATION,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.TraceLocation.InferenceTableLocation)
      })
    ,
    'DESCRIPTOR' : _TRACELOCATION,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TraceLocation)
    })
  _sym_db.RegisterMessage(TraceLocation)
  _sym_db.RegisterMessage(TraceLocation.MlflowExperimentLocation)
  _sym_db.RegisterMessage(TraceLocation.InferenceTableLocation)

  TraceInfoV3 = _reflection.GeneratedProtocolMessageType('TraceInfoV3', (_message.Message,), {

    'TraceMetadataEntry' : _reflection.GeneratedProtocolMessageType('TraceMetadataEntry', (_message.Message,), {
      'DESCRIPTOR' : _TRACEINFOV3_TRACEMETADATAENTRY,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.TraceInfoV3.TraceMetadataEntry)
      })
    ,

    'TagsEntry' : _reflection.GeneratedProtocolMessageType('TagsEntry', (_message.Message,), {
      'DESCRIPTOR' : _TRACEINFOV3_TAGSENTRY,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.TraceInfoV3.TagsEntry)
      })
    ,
    'DESCRIPTOR' : _TRACEINFOV3,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TraceInfoV3)
    })
  _sym_db.RegisterMessage(TraceInfoV3)
  _sym_db.RegisterMessage(TraceInfoV3.TraceMetadataEntry)
  _sym_db.RegisterMessage(TraceInfoV3.TagsEntry)

  DatasetSummary = _reflection.GeneratedProtocolMessageType('DatasetSummary', (_message.Message,), {
    'DESCRIPTOR' : _DATASETSUMMARY,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DatasetSummary)
    })
  _sym_db.RegisterMessage(DatasetSummary)

  SearchDatasets = _reflection.GeneratedProtocolMessageType('SearchDatasets', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHDATASETS_RESPONSE,
      '__module__' : 'service_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchDatasets.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHDATASETS,
    '__module__' : 'service_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchDatasets)
    })
  _sym_db.RegisterMessage(SearchDatasets)
  _sym_db.RegisterMessage(SearchDatasets.Response)

  _MLFLOWSERVICE = DESCRIPTOR.services_by_name['MlflowService']
  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n\024org.mlflow.api.proto\220\001\001\342?\002\020\001'
    _DATASETINPUT.fields_by_name['dataset']._options = None
    _DATASETINPUT.fields_by_name['dataset']._serialized_options = b'\370\206\031\001'
    _INPUTTAG.fields_by_name['key']._options = None
    _INPUTTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _INPUTTAG.fields_by_name['value']._options = None
    _INPUTTAG.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _DATASET.fields_by_name['name']._options = None
    _DATASET.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DATASET.fields_by_name['digest']._options = None
    _DATASET.fields_by_name['digest']._serialized_options = b'\370\206\031\001'
    _DATASET.fields_by_name['source_type']._options = None
    _DATASET.fields_by_name['source_type']._serialized_options = b'\370\206\031\001'
    _DATASET.fields_by_name['source']._options = None
    _DATASET.fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _CREATEEXPERIMENT.fields_by_name['name']._options = None
    _CREATEEXPERIMENT.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _CREATEEXPERIMENT._options = None
    _CREATEEXPERIMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SEARCHEXPERIMENTS._options = None
    _SEARCHEXPERIMENTS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETEXPERIMENT.fields_by_name['experiment_id']._options = None
    _GETEXPERIMENT.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _GETEXPERIMENT._options = None
    _GETEXPERIMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEEXPERIMENT.fields_by_name['experiment_id']._options = None
    _DELETEEXPERIMENT.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _DELETEEXPERIMENT._options = None
    _DELETEEXPERIMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _RESTOREEXPERIMENT.fields_by_name['experiment_id']._options = None
    _RESTOREEXPERIMENT.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _RESTOREEXPERIMENT._options = None
    _RESTOREEXPERIMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _UPDATEEXPERIMENT.fields_by_name['experiment_id']._options = None
    _UPDATEEXPERIMENT.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _UPDATEEXPERIMENT._options = None
    _UPDATEEXPERIMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _CREATERUN._options = None
    _CREATERUN._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _UPDATERUN._options = None
    _UPDATERUN._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETERUN.fields_by_name['run_id']._options = None
    _DELETERUN.fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _DELETERUN._options = None
    _DELETERUN._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _RESTORERUN.fields_by_name['run_id']._options = None
    _RESTORERUN.fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _RESTORERUN._options = None
    _RESTORERUN._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LOGMETRIC.fields_by_name['key']._options = None
    _LOGMETRIC.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _LOGMETRIC.fields_by_name['value']._options = None
    _LOGMETRIC.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _LOGMETRIC.fields_by_name['timestamp']._options = None
    _LOGMETRIC.fields_by_name['timestamp']._serialized_options = b'\370\206\031\001'
    _LOGMETRIC._options = None
    _LOGMETRIC._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LOGPARAM.fields_by_name['key']._options = None
    _LOGPARAM.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _LOGPARAM.fields_by_name['value']._options = None
    _LOGPARAM.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _LOGPARAM._options = None
    _LOGPARAM._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SETEXPERIMENTTAG.fields_by_name['experiment_id']._options = None
    _SETEXPERIMENTTAG.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _SETEXPERIMENTTAG.fields_by_name['key']._options = None
    _SETEXPERIMENTTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETEXPERIMENTTAG.fields_by_name['value']._options = None
    _SETEXPERIMENTTAG.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _SETEXPERIMENTTAG._options = None
    _SETEXPERIMENTTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SETTAG.fields_by_name['key']._options = None
    _SETTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETTAG.fields_by_name['value']._options = None
    _SETTAG.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _SETTAG._options = None
    _SETTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETETAG.fields_by_name['run_id']._options = None
    _DELETETAG.fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _DELETETAG.fields_by_name['key']._options = None
    _DELETETAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _DELETETAG._options = None
    _DELETETAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETRUN._options = None
    _GETRUN._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SEARCHRUNS._options = None
    _SEARCHRUNS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LISTARTIFACTS._options = None
    _LISTARTIFACTS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETMETRICHISTORY.fields_by_name['metric_key']._options = None
    _GETMETRICHISTORY.fields_by_name['metric_key']._serialized_options = b'\370\206\031\001'
    _GETMETRICHISTORY._options = None
    _GETMETRICHISTORY._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETMETRICHISTORYBULKINTERVAL.fields_by_name['metric_key']._options = None
    _GETMETRICHISTORYBULKINTERVAL.fields_by_name['metric_key']._serialized_options = b'\370\206\031\001'
    _GETMETRICHISTORYBULKINTERVAL._options = None
    _GETMETRICHISTORYBULKINTERVAL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LOGBATCH._options = None
    _LOGBATCH._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LOGMODEL._options = None
    _LOGMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _LOGINPUTS.fields_by_name['run_id']._options = None
    _LOGINPUTS.fields_by_name['run_id']._serialized_options = b'\370\206\031\001'
    _LOGINPUTS._options = None
    _LOGINPUTS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETEXPERIMENTBYNAME.fields_by_name['experiment_name']._options = None
    _GETEXPERIMENTBYNAME.fields_by_name['experiment_name']._serialized_options = b'\370\206\031\001'
    _GETEXPERIMENTBYNAME._options = None
    _GETEXPERIMENTBYNAME._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _ASSESSMENTSOURCE.fields_by_name['source_type']._options = None
    _ASSESSMENTSOURCE.fields_by_name['source_type']._serialized_options = b'\370\206\031\001'
    _ASSESSMENTSOURCE.fields_by_name['source_id']._options = None
    _ASSESSMENTSOURCE.fields_by_name['source_id']._serialized_options = b'\370\206\031\001'
    _CREATEASSESSMENT.fields_by_name['assessment']._options = None
    _CREATEASSESSMENT.fields_by_name['assessment']._serialized_options = b'\370\206\031\001'
    _CREATEASSESSMENT._options = None
    _CREATEASSESSMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _UPDATEASSESSMENT.fields_by_name['assessment']._options = None
    _UPDATEASSESSMENT.fields_by_name['assessment']._serialized_options = b'\370\206\031\001'
    _UPDATEASSESSMENT.fields_by_name['update_mask']._options = None
    _UPDATEASSESSMENT.fields_by_name['update_mask']._serialized_options = b'\370\206\031\001'
    _UPDATEASSESSMENT._options = None
    _UPDATEASSESSMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEASSESSMENT.fields_by_name['trace_id']._options = None
    _DELETEASSESSMENT.fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _DELETEASSESSMENT.fields_by_name['assessment_id']._options = None
    _DELETEASSESSMENT.fields_by_name['assessment_id']._serialized_options = b'\370\206\031\001'
    _DELETEASSESSMENT._options = None
    _DELETEASSESSMENT._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _STARTTRACE._options = None
    _STARTTRACE._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _ENDTRACE._options = None
    _ENDTRACE._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETTRACEINFO._options = None
    _GETTRACEINFO._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETTRACEINFOV3._options = None
    _GETTRACEINFOV3._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SEARCHTRACES._options = None
    _SEARCHTRACES._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETETRACES.fields_by_name['experiment_id']._options = None
    _DELETETRACES.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _DELETETRACES._options = None
    _DELETETRACES._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SETTRACETAG._options = None
    _SETTRACETAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETETRACETAG._options = None
    _DELETETRACETAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _TRACEINFOV3_TRACEMETADATAENTRY._options = None
    _TRACEINFOV3_TRACEMETADATAENTRY._serialized_options = b'8\001'
    _TRACEINFOV3_TAGSENTRY._options = None
    _TRACEINFOV3_TAGSENTRY._serialized_options = b'8\001'
    _DATASETSUMMARY.fields_by_name['experiment_id']._options = None
    _DATASETSUMMARY.fields_by_name['experiment_id']._serialized_options = b'\370\206\031\001'
    _DATASETSUMMARY.fields_by_name['name']._options = None
    _DATASETSUMMARY.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DATASETSUMMARY.fields_by_name['digest']._options = None
    _DATASETSUMMARY.fields_by_name['digest']._serialized_options = b'\370\206\031\001'
    _SEARCHDATASETS._options = None
    _SEARCHDATASETS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _MLFLOWSERVICE.methods_by_name['getExperimentByName']._options = None
    _MLFLOWSERVICE.methods_by_name['getExperimentByName']._serialized_options = b'\362\206\031H\n,\n\003GET\022\037/mlflow/experiments/get-by-name\032\004\010\002\020\000\020\001*\026Get Experiment By Name'
    _MLFLOWSERVICE.methods_by_name['createExperiment']._options = None
    _MLFLOWSERVICE.methods_by_name['createExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/create\032\004\010\002\020\000\020\001*\021Create Experiment'
    _MLFLOWSERVICE.methods_by_name['searchExperiments']._options = None
    _MLFLOWSERVICE.methods_by_name['searchExperiments']._serialized_options = b'\362\206\031i\n(\n\004POST\022\032/mlflow/experiments/search\032\004\010\002\020\000\n\'\n\003GET\022\032/mlflow/experiments/search\032\004\010\002\020\000\020\001*\022Search Experiments'
    _MLFLOWSERVICE.methods_by_name['getExperiment']._options = None
    _MLFLOWSERVICE.methods_by_name['getExperiment']._serialized_options = b'\362\206\0318\n$\n\003GET\022\027/mlflow/experiments/get\032\004\010\002\020\000\020\001*\016Get Experiment\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['deleteExperiment']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/delete\032\004\010\002\020\000\020\001*\021Delete Experiment'
    _MLFLOWSERVICE.methods_by_name['restoreExperiment']._options = None
    _MLFLOWSERVICE.methods_by_name['restoreExperiment']._serialized_options = b'\362\206\031A\n)\n\004POST\022\033/mlflow/experiments/restore\032\004\010\002\020\000\020\001*\022Restore Experiment'
    _MLFLOWSERVICE.methods_by_name['updateExperiment']._options = None
    _MLFLOWSERVICE.methods_by_name['updateExperiment']._serialized_options = b'\362\206\031?\n(\n\004POST\022\032/mlflow/experiments/update\032\004\010\002\020\000\020\001*\021Update Experiment'
    _MLFLOWSERVICE.methods_by_name['createRun']._options = None
    _MLFLOWSERVICE.methods_by_name['createRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/create\032\004\010\002\020\000\020\001*\nCreate Run'
    _MLFLOWSERVICE.methods_by_name['updateRun']._options = None
    _MLFLOWSERVICE.methods_by_name['updateRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/update\032\004\010\002\020\000\020\001*\nUpdate Run'
    _MLFLOWSERVICE.methods_by_name['deleteRun']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteRun']._serialized_options = b'\362\206\0311\n!\n\004POST\022\023/mlflow/runs/delete\032\004\010\002\020\000\020\001*\nDelete Run'
    _MLFLOWSERVICE.methods_by_name['restoreRun']._options = None
    _MLFLOWSERVICE.methods_by_name['restoreRun']._serialized_options = b'\362\206\0313\n\"\n\004POST\022\024/mlflow/runs/restore\032\004\010\002\020\000\020\001*\013Restore Run'
    _MLFLOWSERVICE.methods_by_name['logMetric']._options = None
    _MLFLOWSERVICE.methods_by_name['logMetric']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/log-metric\032\004\010\002\020\000\020\001*\nLog Metric'
    _MLFLOWSERVICE.methods_by_name['logParam']._options = None
    _MLFLOWSERVICE.methods_by_name['logParam']._serialized_options = b'\362\206\0317\n(\n\004POST\022\032/mlflow/runs/log-parameter\032\004\010\002\020\000\020\001*\tLog Param'
    _MLFLOWSERVICE.methods_by_name['setExperimentTag']._options = None
    _MLFLOWSERVICE.methods_by_name['setExperimentTag']._serialized_options = b'\362\206\031L\n4\n\004POST\022&/mlflow/experiments/set-experiment-tag\032\004\010\002\020\000\020\001*\022Set Experiment Tag'
    _MLFLOWSERVICE.methods_by_name['setTag']._options = None
    _MLFLOWSERVICE.methods_by_name['setTag']._serialized_options = b'\362\206\031/\n\"\n\004POST\022\024/mlflow/runs/set-tag\032\004\010\002\020\000\020\001*\007Set Tag'
    _MLFLOWSERVICE.methods_by_name['setTraceTag']._options = None
    _MLFLOWSERVICE.methods_by_name['setTraceTag']._serialized_options = b'\362\206\031B\n/\n\005PATCH\022 /mlflow/traces/{request_id}/tags\032\004\010\002\020\000\020\003*\rSet Trace Tag'
    _MLFLOWSERVICE.methods_by_name['deleteTraceTag']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteTraceTag']._serialized_options = b'\362\206\031F\n0\n\006DELETE\022 /mlflow/traces/{request_id}/tags\032\004\010\002\020\000\020\003*\020Delete Trace Tag'
    _MLFLOWSERVICE.methods_by_name['deleteTag']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteTag']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/delete-tag\032\004\010\002\020\000\020\001*\nDelete Tag'
    _MLFLOWSERVICE.methods_by_name['getRun']._options = None
    _MLFLOWSERVICE.methods_by_name['getRun']._serialized_options = b'\362\206\031*\n\035\n\003GET\022\020/mlflow/runs/get\032\004\010\002\020\000\020\001*\007Get Run\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['searchRuns']._options = None
    _MLFLOWSERVICE.methods_by_name['searchRuns']._serialized_options = b'\362\206\0312\n!\n\004POST\022\023/mlflow/runs/search\032\004\010\002\020\000\020\001*\013Search Runs\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['listArtifacts']._options = None
    _MLFLOWSERVICE.methods_by_name['listArtifacts']._serialized_options = b'\362\206\0317\n#\n\003GET\022\026/mlflow/artifacts/list\032\004\010\002\020\000\020\001*\016List Artifacts\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['getMetricHistory']._options = None
    _MLFLOWSERVICE.methods_by_name['getMetricHistory']._serialized_options = b'\362\206\031@\n(\n\003GET\022\033/mlflow/metrics/get-history\032\004\010\002\020\000\020\001*\022Get Metric History'
    _MLFLOWSERVICE.methods_by_name['getMetricHistoryBulkInterval']._options = None
    _MLFLOWSERVICE.methods_by_name['getMetricHistoryBulkInterval']._serialized_options = b'\362\206\031:\n6\n\003GET\022)/mlflow/metrics/get-history-bulk-interval\032\004\010\002\020\013\020\003\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['logBatch']._options = None
    _MLFLOWSERVICE.methods_by_name['logBatch']._serialized_options = b'\362\206\0313\n$\n\004POST\022\026/mlflow/runs/log-batch\032\004\010\002\020\000\020\001*\tLog Batch'
    _MLFLOWSERVICE.methods_by_name['logModel']._options = None
    _MLFLOWSERVICE.methods_by_name['logModel']._serialized_options = b'\362\206\0313\n$\n\004POST\022\026/mlflow/runs/log-model\032\004\010\002\020\000\020\001*\tLog Model'
    _MLFLOWSERVICE.methods_by_name['logInputs']._options = None
    _MLFLOWSERVICE.methods_by_name['logInputs']._serialized_options = b'\362\206\0315\n%\n\004POST\022\027/mlflow/runs/log-inputs\032\004\010\002\020\000\020\001*\nLog Inputs'
    _MLFLOWSERVICE.methods_by_name['searchDatasets']._options = None
    _MLFLOWSERVICE.methods_by_name['searchDatasets']._serialized_options = b'\362\206\0314\n0\n\004POST\022\"mlflow/experiments/search-datasets\032\004\010\002\020\000\020\003\272\214\031\000'
    _MLFLOWSERVICE.methods_by_name['startTrace']._options = None
    _MLFLOWSERVICE.methods_by_name['startTrace']._serialized_options = b'\362\206\031-\n\034\n\004POST\022\016/mlflow/traces\032\004\010\002\020\000\020\003*\013Start Trace'
    _MLFLOWSERVICE.methods_by_name['endTrace']._options = None
    _MLFLOWSERVICE.methods_by_name['endTrace']._serialized_options = b'\362\206\0319\n*\n\005PATCH\022\033/mlflow/traces/{request_id}\032\004\010\002\020\000\020\003*\tEnd Trace'
    _MLFLOWSERVICE.methods_by_name['getTraceInfo']._options = None
    _MLFLOWSERVICE.methods_by_name['getTraceInfo']._serialized_options = b'\362\206\031@\n-\n\003GET\022 /mlflow/traces/{request_id}/info\032\004\010\002\020\000\020\003*\rGet TraceInfo'
    _MLFLOWSERVICE.methods_by_name['getTraceInfoV3']._options = None
    _MLFLOWSERVICE.methods_by_name['getTraceInfoV3']._serialized_options = b'\362\206\031<\n&\n\003GET\022\031/mlflow/traces/{trace_id}\032\004\010\002\020\000\020\003*\020Get TraceInfo v3'
    _MLFLOWSERVICE.methods_by_name['searchTraces']._options = None
    _MLFLOWSERVICE.methods_by_name['searchTraces']._serialized_options = b'\362\206\031.\n\033\n\003GET\022\016/mlflow/traces\032\004\010\002\020\000\020\003*\rSearch Traces'
    _MLFLOWSERVICE.methods_by_name['deleteTraces']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteTraces']._serialized_options = b'\362\206\031=\n*\n\004POST\022\034/mlflow/traces/delete-traces\032\004\010\002\020\000\020\003*\rDelete Traces'
    _MLFLOWSERVICE.methods_by_name['createAssessment']._options = None
    _MLFLOWSERVICE.methods_by_name['createAssessment']._serialized_options = b'\362\206\031\210\001\n>\n\004POST\0220/mlflow/traces/{assessment.trace_id}/assessments\032\004\010\002\020\000\020\003\030\350\007\030\356\007\030\014\030\001*:Create an assessment of a trace or a span within the trace'
    _MLFLOWSERVICE.methods_by_name['updateAssessment']._options = None
    _MLFLOWSERVICE.methods_by_name['updateAssessment']._serialized_options = b'\362\206\031{\nD\n\005PATCH\0225/mlflow/traces/{trace_id}/assessments/{assessment_id}\032\004\010\002\020\000\020\003\030\350\007\030\356\007\030\001*)Update an existing assessment on a trace.'
    _MLFLOWSERVICE.methods_by_name['deleteAssessment']._options = None
    _MLFLOWSERVICE.methods_by_name['deleteAssessment']._serialized_options = b'\362\206\031\\\nE\n\006DELETE\0225/mlflow/traces/{trace_id}/assessments/{assessment_id}\032\004\010\002\020\000\020\003*\021Delete Assessment'
    _VIEWTYPE._serialized_start=9364
    _VIEWTYPE._serialized_end=9418
    _SOURCETYPE._serialized_start=9420
    _SOURCETYPE._serialized_end=9493
    _RUNSTATUS._serialized_start=9495
    _RUNSTATUS._serialized_end=9572
    _TRACESTATUS._serialized_start=9574
    _TRACESTATUS._serialized_end=9653
    _METRIC._serialized_start=184
    _METRIC._serialized_end=256
    _PARAM._serialized_start=258
    _PARAM._serialized_end=293
    _RUN._serialized_start=295
    _RUN._serialized_end=397
    _RUNDATA._serialized_start=399
    _RUNDATA._serialized_end=502
    _RUNINPUTS._serialized_start=504
    _RUNINPUTS._serialized_end=561
    _RUNTAG._serialized_start=563
    _RUNTAG._serialized_end=599
    _EXPERIMENTTAG._serialized_start=601
    _EXPERIMENTTAG._serialized_end=644
    _RUNINFO._serialized_start=647
    _RUNINFO._serialized_end=868
    _EXPERIMENT._serialized_start=871
    _EXPERIMENT._serialized_end=1058
    _DATASETINPUT._serialized_start=1060
    _DATASETINPUT._serialized_end=1146
    _INPUTTAG._serialized_start=1148
    _INPUTTAG._serialized_end=1198
    _DATASET._serialized_start=1201
    _DATASET._serialized_end=1334
    _CREATEEXPERIMENT._serialized_start=1337
    _CREATEEXPERIMENT._serialized_end=1519
    _CREATEEXPERIMENT_RESPONSE._serialized_start=1441
    _CREATEEXPERIMENT_RESPONSE._serialized_end=1474
    _SEARCHEXPERIMENTS._serialized_start=1522
    _SEARCHEXPERIMENTS._serialized_end=1776
    _SEARCHEXPERIMENTS_RESPONSE._serialized_start=1655
    _SEARCHEXPERIMENTS_RESPONSE._serialized_end=1731
    _GETEXPERIMENT._serialized_start=1779
    _GETEXPERIMENT._serialized_end=1920
    _GETEXPERIMENT_RESPONSE._serialized_start=1825
    _GETEXPERIMENT_RESPONSE._serialized_end=1875
    _DELETEEXPERIMENT._serialized_start=1922
    _DELETEEXPERIMENT._serialized_end=2026
    _DELETEEXPERIMENT_RESPONSE._serialized_start=1441
    _DELETEEXPERIMENT_RESPONSE._serialized_end=1451
    _RESTOREEXPERIMENT._serialized_start=2028
    _RESTOREEXPERIMENT._serialized_end=2133
    _RESTOREEXPERIMENT_RESPONSE._serialized_start=1441
    _RESTOREEXPERIMENT_RESPONSE._serialized_end=1451
    _UPDATEEXPERIMENT._serialized_start=2135
    _UPDATEEXPERIMENT._serialized_end=2257
    _UPDATEEXPERIMENT_RESPONSE._serialized_start=1441
    _UPDATEEXPERIMENT_RESPONSE._serialized_end=1451
    _CREATERUN._serialized_start=2260
    _CREATERUN._serialized_end=2462
    _CREATERUN_RESPONSE._serialized_start=2381
    _CREATERUN_RESPONSE._serialized_end=2417
    _UPDATERUN._serialized_start=2465
    _UPDATERUN._serialized_end=2673
    _UPDATERUN_RESPONSE._serialized_start=2583
    _UPDATERUN_RESPONSE._serialized_end=2628
    _DELETERUN._serialized_start=2675
    _DELETERUN._serialized_end=2765
    _DELETERUN_RESPONSE._serialized_start=1441
    _DELETERUN_RESPONSE._serialized_end=1451
    _RESTORERUN._serialized_start=2767
    _RESTORERUN._serialized_end=2858
    _RESTORERUN_RESPONSE._serialized_start=1441
    _RESTORERUN_RESPONSE._serialized_end=1451
    _LOGMETRIC._serialized_start=2861
    _LOGMETRIC._serialized_end=3045
    _LOGMETRIC_RESPONSE._serialized_start=1441
    _LOGMETRIC_RESPONSE._serialized_end=1451
    _LOGPARAM._serialized_start=3048
    _LOGPARAM._serialized_end=3189
    _LOGPARAM_RESPONSE._serialized_start=1441
    _LOGPARAM_RESPONSE._serialized_end=1451
    _SETEXPERIMENTTAG._serialized_start=3192
    _SETEXPERIMENTTAG._serialized_end=3336
    _SETEXPERIMENTTAG_RESPONSE._serialized_start=1441
    _SETEXPERIMENTTAG_RESPONSE._serialized_end=1451
    _SETTAG._serialized_start=3339
    _SETTAG._serialized_end=3478
    _SETTAG_RESPONSE._serialized_start=1441
    _SETTAG_RESPONSE._serialized_end=1451
    _DELETETAG._serialized_start=3480
    _DELETETAG._serialized_end=3589
    _DELETETAG_RESPONSE._serialized_start=1441
    _DELETETAG_RESPONSE._serialized_end=1451
    _GETRUN._serialized_start=3591
    _GETRUN._serialized_end=3716
    _GETRUN_RESPONSE._serialized_start=2381
    _GETRUN_RESPONSE._serialized_end=2417
    _SEARCHRUNS._serialized_start=3719
    _SEARCHRUNS._serialized_end=3999
    _SEARCHRUNS_RESPONSE._serialized_start=3892
    _SEARCHRUNS_RESPONSE._serialized_end=3954
    _LISTARTIFACTS._serialized_start=4002
    _LISTARTIFACTS._serialized_end=4218
    _LISTARTIFACTS_RESPONSE._serialized_start=4087
    _LISTARTIFACTS_RESPONSE._serialized_end=4173
    _FILEINFO._serialized_start=4220
    _FILEINFO._serialized_end=4279
    _GETMETRICHISTORY._serialized_start=4282
    _GETMETRICHISTORY._serialized_end=4516
    _GETMETRICHISTORY_RESPONSE._serialized_start=4403
    _GETMETRICHISTORY_RESPONSE._serialized_end=4471
    _METRICWITHRUNID._serialized_start=4518
    _METRICWITHRUNID._serialized_end=4615
    _GETMETRICHISTORYBULKINTERVAL._serialized_start=4618
    _GETMETRICHISTORYBULKINTERVAL._serialized_end=4849
    _GETMETRICHISTORYBULKINTERVAL_RESPONSE._serialized_start=4752
    _GETMETRICHISTORYBULKINTERVAL_RESPONSE._serialized_end=4804
    _LOGBATCH._serialized_start=4852
    _LOGBATCH._serialized_end=5029
    _LOGBATCH_RESPONSE._serialized_start=1441
    _LOGBATCH_RESPONSE._serialized_end=1451
    _LOGMODEL._serialized_start=5031
    _LOGMODEL._serialized_end=5134
    _LOGMODEL_RESPONSE._serialized_start=1441
    _LOGMODEL_RESPONSE._serialized_end=1451
    _LOGINPUTS._serialized_start=5137
    _LOGINPUTS._serialized_end=5267
    _LOGINPUTS_RESPONSE._serialized_start=1441
    _LOGINPUTS_RESPONSE._serialized_end=1451
    _GETEXPERIMENTBYNAME._serialized_start=5270
    _GETEXPERIMENTBYNAME._serialized_end=5419
    _GETEXPERIMENTBYNAME_RESPONSE._serialized_start=1825
    _GETEXPERIMENTBYNAME_RESPONSE._serialized_end=1875
    _ASSESSMENTSOURCE._serialized_start=5422
    _ASSESSMENTSOURCE._serialized_end=5608
    _ASSESSMENTSOURCE_SOURCETYPE._serialized_start=5531
    _ASSESSMENTSOURCE_SOURCETYPE._serialized_end=5608
    _ASSESSMENTERROR._serialized_start=5610
    _ASSESSMENTERROR._serialized_end=5670
    _CREATEASSESSMENT._serialized_start=5673
    _CREATEASSESSMENT._serialized_end=5858
    _CREATEASSESSMENT_RESPONSE._serialized_start=5751
    _CREATEASSESSMENT_RESPONSE._serialized_end=5813
    _UPDATEASSESSMENT._serialized_start=5861
    _UPDATEASSESSMENT._serialized_end=6101
    _UPDATEASSESSMENT_RESPONSE._serialized_start=5751
    _UPDATEASSESSMENT_RESPONSE._serialized_end=5813
    _DELETEASSESSMENT._serialized_start=6104
    _DELETEASSESSMENT._serialized_end=6232
    _DELETEASSESSMENT_RESPONSE._serialized_start=1441
    _DELETEASSESSMENT_RESPONSE._serialized_end=1451
    _TRACEINFO._serialized_start=6235
    _TRACEINFO._serialized_end=6463
    _TRACEREQUESTMETADATA._serialized_start=6465
    _TRACEREQUESTMETADATA._serialized_end=6515
    _TRACETAG._serialized_start=6517
    _TRACETAG._serialized_end=6555
    _STARTTRACE._serialized_start=6558
    _STARTTRACE._serialized_end=6799
    _STARTTRACE_RESPONSE._serialized_start=6705
    _STARTTRACE_RESPONSE._serialized_end=6754
    _ENDTRACE._serialized_start=6802
    _ENDTRACE._serialized_end=7075
    _ENDTRACE_RESPONSE._serialized_start=6705
    _ENDTRACE_RESPONSE._serialized_end=6754
    _GETTRACEINFO._serialized_start=7078
    _GETTRACEINFO._serialized_end=7208
    _GETTRACEINFO_RESPONSE._serialized_start=6705
    _GETTRACEINFO_RESPONSE._serialized_end=6754
    _GETTRACEINFOV3._serialized_start=7210
    _GETTRACEINFOV3._serialized_end=7331
    _GETTRACEINFOV3_RESPONSE._serialized_start=7246
    _GETTRACEINFOV3_RESPONSE._serialized_end=7286
    _SEARCHTRACES._serialized_start=7334
    _SEARCHTRACES._serialized_end=7569
    _SEARCHTRACES_RESPONSE._serialized_start=7454
    _SEARCHTRACES_RESPONSE._serialized_end=7524
    _DELETETRACES._serialized_start=7572
    _DELETETRACES._serialized_end=7767
    _DELETETRACES_RESPONSE._serialized_start=7688
    _DELETETRACES_RESPONSE._serialized_end=7722
    _SETTRACETAG._serialized_start=7769
    _SETTRACETAG._serialized_end=7887
    _SETTRACETAG_RESPONSE._serialized_start=1441
    _SETTRACETAG_RESPONSE._serialized_end=1451
    _DELETETRACETAG._serialized_start=7889
    _DELETETRACETAG._serialized_end=7995
    _DELETETRACETAG_RESPONSE._serialized_start=1441
    _DELETETRACETAG_RESPONSE._serialized_end=1451
    _TRACE._serialized_start=7997
    _TRACE._serialized_end=8045
    _TRACELOCATION._serialized_start=8048
    _TRACELOCATION._serialized_end=8486
    _TRACELOCATION_MLFLOWEXPERIMENTLOCATION._serialized_start=8270
    _TRACELOCATION_MLFLOWEXPERIMENTLOCATION._serialized_end=8319
    _TRACELOCATION_INFERENCETABLELOCATION._serialized_start=8321
    _TRACELOCATION_INFERENCETABLELOCATION._serialized_end=8370
    _TRACELOCATION_TRACELOCATIONTYPE._serialized_start=8372
    _TRACELOCATION_TRACELOCATIONTYPE._serialized_end=8472
    _TRACEINFOV3._serialized_start=8489
    _TRACEINFOV3._serialized_end=9105
    _TRACEINFOV3_TRACEMETADATAENTRY._serialized_start=8940
    _TRACEINFOV3_TRACEMETADATAENTRY._serialized_end=8992
    _TRACEINFOV3_TAGSENTRY._serialized_start=8994
    _TRACEINFOV3_TAGSENTRY._serialized_end=9037
    _TRACEINFOV3_STATE._serialized_start=9039
    _TRACEINFOV3_STATE._serialized_end=9105
    _DATASETSUMMARY._serialized_start=9107
    _DATASETSUMMARY._serialized_end=9211
    _SEARCHDATASETS._serialized_start=9214
    _SEARCHDATASETS._serialized_end=9362
    _SEARCHDATASETS_RESPONSE._serialized_start=9256
    _SEARCHDATASETS_RESPONSE._serialized_end=9317
    _MLFLOWSERVICE._serialized_start=9656
    _MLFLOWSERVICE._serialized_end=14742
  MlflowService = service_reflection.GeneratedServiceType('MlflowService', (_service.Service,), dict(
    DESCRIPTOR = _MLFLOWSERVICE,
    __module__ = 'service_pb2'
    ))

  MlflowService_Stub = service_reflection.GeneratedServiceStubType('MlflowService_Stub', (MlflowService,), dict(
    DESCRIPTOR = _MLFLOWSERVICE,
    __module__ = 'service_pb2'
    ))


  # @@protoc_insertion_point(module_scope)

