
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_trace_server.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from . import assessments_pb2 as assessments__pb2
  from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
  from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1d\x64\x61tabricks_trace_server.proto\x12\x12mlflow.traceserver\x1a\x10\x64\x61tabricks.proto\x1a\x11\x61ssessments.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x15scalapb/scalapb.proto\"\x8b\x01\n\x0b\x43reateTrace\x12+\n\x04info\x18\x01 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceInfo\x12+\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceData:\"\xe2?\x1f\n\x1d\x63om.databricks.rpc.RPC[Trace]\"a\n\x05Trace\x12+\n\x04info\x18\x01 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceInfo\x12+\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceData\"\x90\x05\n\tTraceInfo\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x19\n\x11\x63lient_request_id\x18\x02 \x01(\t\x12\x39\n\x0etrace_location\x18\x03 \x01(\x0b\x32!.mlflow.traceserver.TraceLocation\x12\x0f\n\x07request\x18\x04 \x01(\t\x12\x10\n\x08response\x18\x05 \x01(\t\x12\x30\n\x0crequest_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x12\x65xecution_duration\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x32\n\x05state\x18\x08 \x01(\x0e\x32#.mlflow.traceserver.TraceInfo.State\x12H\n\x0etrace_metadata\x18\t \x03(\x0b\x32\x30.mlflow.traceserver.TraceInfo.TraceMetadataEntry\x12\x33\n\x0b\x61ssessments\x18\n \x03(\x0b\x32\x1e.mlflow.assessments.Assessment\x12\x35\n\x04tags\x18\x0b \x03(\x0b\x32\'.mlflow.traceserver.TraceInfo.TagsEntry\x1a\x34\n\x12TraceMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\"\xda\x03\n\rTraceLocation\x12\x41\n\x04type\x18\x01 \x01(\x0e\x32\x33.mlflow.traceserver.TraceLocation.TraceLocationType\x12W\n\x11mlflow_experiment\x18\x02 \x01(\x0b\x32:.mlflow.traceserver.TraceLocation.MlflowExperimentLocationH\x00\x12S\n\x0finference_table\x18\x03 \x01(\x0b\x32\x38.mlflow.traceserver.TraceLocation.InferenceTableLocationH\x00\x1a\x31\n\x18MlflowExperimentLocation\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x1a\x31\n\x16InferenceTableLocation\x12\x17\n\x0f\x66ull_table_name\x18\x01 \x01(\t\"d\n\x11TraceLocationType\x12#\n\x1fTRACE_LOCATION_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11MLFLOW_EXPERIMENT\x10\x01\x12\x13\n\x0fINFERENCE_TABLE\x10\x02\x42\x0c\n\nidentifier\"4\n\tTraceData\x12\'\n\x05spans\x18\x01 \x03(\x0b\x32\x18.mlflow.traceserver.Span\"\xb2\x0b\n\x04Span\x12\x16\n\x08trace_id\x18\x01 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x15\n\x07span_id\x18\x02 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12\x16\n\x0eparent_span_id\x18\x04 \x01(\x0c\x12\r\n\x05\x66lags\x18\x10 \x01(\x07\x12\x12\n\x04name\x18\x05 \x01(\tB\x04\xf8\x86\x19\x01\x12/\n\x04kind\x18\x06 \x01(\x0e\x32!.mlflow.traceserver.Span.SpanKind\x12\"\n\x14start_time_unix_nano\x18\x07 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12 \n\x12\x65nd_time_unix_nano\x18\x08 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12<\n\nattributes\x18\t \x03(\x0b\x32(.mlflow.traceserver.Span.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\n \x01(\r\x12.\n\x06\x65vents\x18\x0b \x03(\x0b\x32\x1e.mlflow.traceserver.Span.Event\x12\x1c\n\x14\x64ropped_events_count\x18\x0c \x01(\r\x12,\n\x05links\x18\r \x03(\x0b\x32\x1d.mlflow.traceserver.Span.Link\x12\x1b\n\x13\x64ropped_links_count\x18\x0e \x01(\r\x12/\n\x06status\x18\x0f \x01(\x0b\x32\x1f.mlflow.traceserver.Span.Status\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\xea\x01\n\x05\x45vent\x12\x1c\n\x0etime_unix_nano\x18\x01 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12\x12\n\x04name\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x42\n\nattributes\x18\x03 \x03(\x0b\x32..mlflow.traceserver.Span.Event.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\x04 \x01(\r\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\x89\x02\n\x04Link\x12\x16\n\x08trace_id\x18\x01 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x15\n\x07span_id\x18\x02 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12\x41\n\nattributes\x18\x04 \x03(\x0b\x32-.mlflow.traceserver.Span.Link.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\x05 \x01(\r\x12\r\n\x05\x66lags\x18\x06 \x01(\x07\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\xa9\x01\n\x06Status\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x38\n\x04\x63ode\x18\x03 \x01(\x0e\x32*.mlflow.traceserver.Span.Status.StatusCode\"N\n\nStatusCode\x12\x15\n\x11STATUS_CODE_UNSET\x10\x00\x12\x12\n\x0eSTATUS_CODE_OK\x10\x01\x12\x15\n\x11STATUS_CODE_ERROR\x10\x02J\x04\x08\x01\x10\x02\"\x99\x01\n\x08SpanKind\x12\x19\n\x15SPAN_KIND_UNSPECIFIED\x10\x00\x12\x16\n\x12SPAN_KIND_INTERNAL\x10\x01\x12\x14\n\x10SPAN_KIND_SERVER\x10\x02\x12\x14\n\x10SPAN_KIND_CLIENT\x10\x03\x12\x16\n\x12SPAN_KIND_PRODUCER\x10\x04\x12\x16\n\x12SPAN_KIND_CONSUMER\x10\x05\x32\x8c\x01\n\x1e\x44\x61tabricksTracingServerService\x12j\n\x0b\x63reateTrace\x12\x1f.mlflow.traceserver.CreateTrace\x1a\x19.mlflow.traceserver.Trace\"\x1f\xf2\x86\x19\x1b\n\x17\n\x04POST\x12\x0f/tracing/traces\x10\x03\x42\x03\x90\x01\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'databricks_trace_server_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\220\001\001'
    _globals['_CREATETRACE']._loaded_options = None
    _globals['_CREATETRACE']._serialized_options = b'\342?\037\n\035com.databricks.rpc.RPC[Trace]'
    _globals['_TRACEINFO_TRACEMETADATAENTRY']._loaded_options = None
    _globals['_TRACEINFO_TRACEMETADATAENTRY']._serialized_options = b'8\001'
    _globals['_TRACEINFO_TAGSENTRY']._loaded_options = None
    _globals['_TRACEINFO_TAGSENTRY']._serialized_options = b'8\001'
    _globals['_SPAN_ATTRIBUTESENTRY']._loaded_options = None
    _globals['_SPAN_ATTRIBUTESENTRY']._serialized_options = b'8\001'
    _globals['_SPAN_EVENT_ATTRIBUTESENTRY']._loaded_options = None
    _globals['_SPAN_EVENT_ATTRIBUTESENTRY']._serialized_options = b'8\001'
    _globals['_SPAN_EVENT'].fields_by_name['time_unix_nano']._loaded_options = None
    _globals['_SPAN_EVENT'].fields_by_name['time_unix_nano']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN_EVENT'].fields_by_name['name']._loaded_options = None
    _globals['_SPAN_EVENT'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN_LINK_ATTRIBUTESENTRY']._loaded_options = None
    _globals['_SPAN_LINK_ATTRIBUTESENTRY']._serialized_options = b'8\001'
    _globals['_SPAN_LINK'].fields_by_name['trace_id']._loaded_options = None
    _globals['_SPAN_LINK'].fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN_LINK'].fields_by_name['span_id']._loaded_options = None
    _globals['_SPAN_LINK'].fields_by_name['span_id']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN'].fields_by_name['trace_id']._loaded_options = None
    _globals['_SPAN'].fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN'].fields_by_name['span_id']._loaded_options = None
    _globals['_SPAN'].fields_by_name['span_id']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN'].fields_by_name['name']._loaded_options = None
    _globals['_SPAN'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN'].fields_by_name['start_time_unix_nano']._loaded_options = None
    _globals['_SPAN'].fields_by_name['start_time_unix_nano']._serialized_options = b'\370\206\031\001'
    _globals['_SPAN'].fields_by_name['end_time_unix_nano']._loaded_options = None
    _globals['_SPAN'].fields_by_name['end_time_unix_nano']._serialized_options = b'\370\206\031\001'
    _globals['_DATABRICKSTRACINGSERVERSERVICE'].methods_by_name['createTrace']._loaded_options = None
    _globals['_DATABRICKSTRACINGSERVERSERVICE'].methods_by_name['createTrace']._serialized_options = b'\362\206\031\033\n\027\n\004POST\022\017/tracing/traces\020\003'
    _globals['_CREATETRACE']._serialized_start=209
    _globals['_CREATETRACE']._serialized_end=348
    _globals['_TRACE']._serialized_start=350
    _globals['_TRACE']._serialized_end=447
    _globals['_TRACEINFO']._serialized_start=450
    _globals['_TRACEINFO']._serialized_end=1106
    _globals['_TRACEINFO_TRACEMETADATAENTRY']._serialized_start=941
    _globals['_TRACEINFO_TRACEMETADATAENTRY']._serialized_end=993
    _globals['_TRACEINFO_TAGSENTRY']._serialized_start=995
    _globals['_TRACEINFO_TAGSENTRY']._serialized_end=1038
    _globals['_TRACEINFO_STATE']._serialized_start=1040
    _globals['_TRACEINFO_STATE']._serialized_end=1106
    _globals['_TRACELOCATION']._serialized_start=1109
    _globals['_TRACELOCATION']._serialized_end=1583
    _globals['_TRACELOCATION_MLFLOWEXPERIMENTLOCATION']._serialized_start=1367
    _globals['_TRACELOCATION_MLFLOWEXPERIMENTLOCATION']._serialized_end=1416
    _globals['_TRACELOCATION_INFERENCETABLELOCATION']._serialized_start=1418
    _globals['_TRACELOCATION_INFERENCETABLELOCATION']._serialized_end=1467
    _globals['_TRACELOCATION_TRACELOCATIONTYPE']._serialized_start=1469
    _globals['_TRACELOCATION_TRACELOCATIONTYPE']._serialized_end=1569
    _globals['_TRACEDATA']._serialized_start=1585
    _globals['_TRACEDATA']._serialized_end=1637
    _globals['_SPAN']._serialized_start=1640
    _globals['_SPAN']._serialized_end=3098
    _globals['_SPAN_ATTRIBUTESENTRY']._serialized_start=2192
    _globals['_SPAN_ATTRIBUTESENTRY']._serialized_end=2265
    _globals['_SPAN_EVENT']._serialized_start=2268
    _globals['_SPAN_EVENT']._serialized_end=2502
    _globals['_SPAN_EVENT_ATTRIBUTESENTRY']._serialized_start=2192
    _globals['_SPAN_EVENT_ATTRIBUTESENTRY']._serialized_end=2265
    _globals['_SPAN_LINK']._serialized_start=2505
    _globals['_SPAN_LINK']._serialized_end=2770
    _globals['_SPAN_LINK_ATTRIBUTESENTRY']._serialized_start=2192
    _globals['_SPAN_LINK_ATTRIBUTESENTRY']._serialized_end=2265
    _globals['_SPAN_STATUS']._serialized_start=2773
    _globals['_SPAN_STATUS']._serialized_end=2942
    _globals['_SPAN_STATUS_STATUSCODE']._serialized_start=2858
    _globals['_SPAN_STATUS_STATUSCODE']._serialized_end=2936
    _globals['_SPAN_SPANKIND']._serialized_start=2945
    _globals['_SPAN_SPANKIND']._serialized_end=3098
    _globals['_DATABRICKSTRACINGSERVERSERVICE']._serialized_start=3101
    _globals['_DATABRICKSTRACINGSERVERSERVICE']._serialized_end=3241
  _builder.BuildServices(DESCRIPTOR, 'databricks_trace_server_pb2', _globals)
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_trace_server.proto
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf import service as _service
  from google.protobuf import service_reflection
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from . import assessments_pb2 as assessments__pb2
  from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
  from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1d\x64\x61tabricks_trace_server.proto\x12\x12mlflow.traceserver\x1a\x10\x64\x61tabricks.proto\x1a\x11\x61ssessments.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x15scalapb/scalapb.proto\"\x8b\x01\n\x0b\x43reateTrace\x12+\n\x04info\x18\x01 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceInfo\x12+\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceData:\"\xe2?\x1f\n\x1d\x63om.databricks.rpc.RPC[Trace]\"a\n\x05Trace\x12+\n\x04info\x18\x01 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceInfo\x12+\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1d.mlflow.traceserver.TraceData\"\x90\x05\n\tTraceInfo\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x19\n\x11\x63lient_request_id\x18\x02 \x01(\t\x12\x39\n\x0etrace_location\x18\x03 \x01(\x0b\x32!.mlflow.traceserver.TraceLocation\x12\x0f\n\x07request\x18\x04 \x01(\t\x12\x10\n\x08response\x18\x05 \x01(\t\x12\x30\n\x0crequest_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x12\x65xecution_duration\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x32\n\x05state\x18\x08 \x01(\x0e\x32#.mlflow.traceserver.TraceInfo.State\x12H\n\x0etrace_metadata\x18\t \x03(\x0b\x32\x30.mlflow.traceserver.TraceInfo.TraceMetadataEntry\x12\x33\n\x0b\x61ssessments\x18\n \x03(\x0b\x32\x1e.mlflow.assessments.Assessment\x12\x35\n\x04tags\x18\x0b \x03(\x0b\x32\'.mlflow.traceserver.TraceInfo.TagsEntry\x1a\x34\n\x12TraceMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\"\xda\x03\n\rTraceLocation\x12\x41\n\x04type\x18\x01 \x01(\x0e\x32\x33.mlflow.traceserver.TraceLocation.TraceLocationType\x12W\n\x11mlflow_experiment\x18\x02 \x01(\x0b\x32:.mlflow.traceserver.TraceLocation.MlflowExperimentLocationH\x00\x12S\n\x0finference_table\x18\x03 \x01(\x0b\x32\x38.mlflow.traceserver.TraceLocation.InferenceTableLocationH\x00\x1a\x31\n\x18MlflowExperimentLocation\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x1a\x31\n\x16InferenceTableLocation\x12\x17\n\x0f\x66ull_table_name\x18\x01 \x01(\t\"d\n\x11TraceLocationType\x12#\n\x1fTRACE_LOCATION_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11MLFLOW_EXPERIMENT\x10\x01\x12\x13\n\x0fINFERENCE_TABLE\x10\x02\x42\x0c\n\nidentifier\"4\n\tTraceData\x12\'\n\x05spans\x18\x01 \x03(\x0b\x32\x18.mlflow.traceserver.Span\"\xb2\x0b\n\x04Span\x12\x16\n\x08trace_id\x18\x01 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x15\n\x07span_id\x18\x02 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12\x16\n\x0eparent_span_id\x18\x04 \x01(\x0c\x12\r\n\x05\x66lags\x18\x10 \x01(\x07\x12\x12\n\x04name\x18\x05 \x01(\tB\x04\xf8\x86\x19\x01\x12/\n\x04kind\x18\x06 \x01(\x0e\x32!.mlflow.traceserver.Span.SpanKind\x12\"\n\x14start_time_unix_nano\x18\x07 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12 \n\x12\x65nd_time_unix_nano\x18\x08 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12<\n\nattributes\x18\t \x03(\x0b\x32(.mlflow.traceserver.Span.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\n \x01(\r\x12.\n\x06\x65vents\x18\x0b \x03(\x0b\x32\x1e.mlflow.traceserver.Span.Event\x12\x1c\n\x14\x64ropped_events_count\x18\x0c \x01(\r\x12,\n\x05links\x18\r \x03(\x0b\x32\x1d.mlflow.traceserver.Span.Link\x12\x1b\n\x13\x64ropped_links_count\x18\x0e \x01(\r\x12/\n\x06status\x18\x0f \x01(\x0b\x32\x1f.mlflow.traceserver.Span.Status\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\xea\x01\n\x05\x45vent\x12\x1c\n\x0etime_unix_nano\x18\x01 \x01(\x06\x42\x04\xf8\x86\x19\x01\x12\x12\n\x04name\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x42\n\nattributes\x18\x03 \x03(\x0b\x32..mlflow.traceserver.Span.Event.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\x04 \x01(\r\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\x89\x02\n\x04Link\x12\x16\n\x08trace_id\x18\x01 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x15\n\x07span_id\x18\x02 \x01(\x0c\x42\x04\xf8\x86\x19\x01\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12\x41\n\nattributes\x18\x04 \x03(\x0b\x32-.mlflow.traceserver.Span.Link.AttributesEntry\x12 \n\x18\x64ropped_attributes_count\x18\x05 \x01(\r\x12\r\n\x05\x66lags\x18\x06 \x01(\x07\x1aI\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\x1a\xa9\x01\n\x06Status\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x38\n\x04\x63ode\x18\x03 \x01(\x0e\x32*.mlflow.traceserver.Span.Status.StatusCode\"N\n\nStatusCode\x12\x15\n\x11STATUS_CODE_UNSET\x10\x00\x12\x12\n\x0eSTATUS_CODE_OK\x10\x01\x12\x15\n\x11STATUS_CODE_ERROR\x10\x02J\x04\x08\x01\x10\x02\"\x99\x01\n\x08SpanKind\x12\x19\n\x15SPAN_KIND_UNSPECIFIED\x10\x00\x12\x16\n\x12SPAN_KIND_INTERNAL\x10\x01\x12\x14\n\x10SPAN_KIND_SERVER\x10\x02\x12\x14\n\x10SPAN_KIND_CLIENT\x10\x03\x12\x16\n\x12SPAN_KIND_PRODUCER\x10\x04\x12\x16\n\x12SPAN_KIND_CONSUMER\x10\x05\x32\x8c\x01\n\x1e\x44\x61tabricksTracingServerService\x12j\n\x0b\x63reateTrace\x12\x1f.mlflow.traceserver.CreateTrace\x1a\x19.mlflow.traceserver.Trace\"\x1f\xf2\x86\x19\x1b\n\x17\n\x04POST\x12\x0f/tracing/traces\x10\x03\x42\x03\x90\x01\x01')



  _CREATETRACE = DESCRIPTOR.message_types_by_name['CreateTrace']
  _TRACE = DESCRIPTOR.message_types_by_name['Trace']
  _TRACEINFO = DESCRIPTOR.message_types_by_name['TraceInfo']
  _TRACEINFO_TRACEMETADATAENTRY = _TRACEINFO.nested_types_by_name['TraceMetadataEntry']
  _TRACEINFO_TAGSENTRY = _TRACEINFO.nested_types_by_name['TagsEntry']
  _TRACELOCATION = DESCRIPTOR.message_types_by_name['TraceLocation']
  _TRACELOCATION_MLFLOWEXPERIMENTLOCATION = _TRACELOCATION.nested_types_by_name['MlflowExperimentLocation']
  _TRACELOCATION_INFERENCETABLELOCATION = _TRACELOCATION.nested_types_by_name['InferenceTableLocation']
  _TRACEDATA = DESCRIPTOR.message_types_by_name['TraceData']
  _SPAN = DESCRIPTOR.message_types_by_name['Span']
  _SPAN_ATTRIBUTESENTRY = _SPAN.nested_types_by_name['AttributesEntry']
  _SPAN_EVENT = _SPAN.nested_types_by_name['Event']
  _SPAN_EVENT_ATTRIBUTESENTRY = _SPAN_EVENT.nested_types_by_name['AttributesEntry']
  _SPAN_LINK = _SPAN.nested_types_by_name['Link']
  _SPAN_LINK_ATTRIBUTESENTRY = _SPAN_LINK.nested_types_by_name['AttributesEntry']
  _SPAN_STATUS = _SPAN.nested_types_by_name['Status']
  _TRACEINFO_STATE = _TRACEINFO.enum_types_by_name['State']
  _TRACELOCATION_TRACELOCATIONTYPE = _TRACELOCATION.enum_types_by_name['TraceLocationType']
  _SPAN_STATUS_STATUSCODE = _SPAN_STATUS.enum_types_by_name['StatusCode']
  _SPAN_SPANKIND = _SPAN.enum_types_by_name['SpanKind']
  CreateTrace = _reflection.GeneratedProtocolMessageType('CreateTrace', (_message.Message,), {
    'DESCRIPTOR' : _CREATETRACE,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.CreateTrace)
    })
  _sym_db.RegisterMessage(CreateTrace)

  Trace = _reflection.GeneratedProtocolMessageType('Trace', (_message.Message,), {
    'DESCRIPTOR' : _TRACE,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.Trace)
    })
  _sym_db.RegisterMessage(Trace)

  TraceInfo = _reflection.GeneratedProtocolMessageType('TraceInfo', (_message.Message,), {

    'TraceMetadataEntry' : _reflection.GeneratedProtocolMessageType('TraceMetadataEntry', (_message.Message,), {
      'DESCRIPTOR' : _TRACEINFO_TRACEMETADATAENTRY,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceInfo.TraceMetadataEntry)
      })
    ,

    'TagsEntry' : _reflection.GeneratedProtocolMessageType('TagsEntry', (_message.Message,), {
      'DESCRIPTOR' : _TRACEINFO_TAGSENTRY,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceInfo.TagsEntry)
      })
    ,
    'DESCRIPTOR' : _TRACEINFO,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceInfo)
    })
  _sym_db.RegisterMessage(TraceInfo)
  _sym_db.RegisterMessage(TraceInfo.TraceMetadataEntry)
  _sym_db.RegisterMessage(TraceInfo.TagsEntry)

  TraceLocation = _reflection.GeneratedProtocolMessageType('TraceLocation', (_message.Message,), {

    'MlflowExperimentLocation' : _reflection.GeneratedProtocolMessageType('MlflowExperimentLocation', (_message.Message,), {
      'DESCRIPTOR' : _TRACELOCATION_MLFLOWEXPERIMENTLOCATION,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceLocation.MlflowExperimentLocation)
      })
    ,

    'InferenceTableLocation' : _reflection.GeneratedProtocolMessageType('InferenceTableLocation', (_message.Message,), {
      'DESCRIPTOR' : _TRACELOCATION_INFERENCETABLELOCATION,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceLocation.InferenceTableLocation)
      })
    ,
    'DESCRIPTOR' : _TRACELOCATION,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceLocation)
    })
  _sym_db.RegisterMessage(TraceLocation)
  _sym_db.RegisterMessage(TraceLocation.MlflowExperimentLocation)
  _sym_db.RegisterMessage(TraceLocation.InferenceTableLocation)

  TraceData = _reflection.GeneratedProtocolMessageType('TraceData', (_message.Message,), {
    'DESCRIPTOR' : _TRACEDATA,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.TraceData)
    })
  _sym_db.RegisterMessage(TraceData)

  Span = _reflection.GeneratedProtocolMessageType('Span', (_message.Message,), {

    'AttributesEntry' : _reflection.GeneratedProtocolMessageType('AttributesEntry', (_message.Message,), {
      'DESCRIPTOR' : _SPAN_ATTRIBUTESENTRY,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.AttributesEntry)
      })
    ,

    'Event' : _reflection.GeneratedProtocolMessageType('Event', (_message.Message,), {

      'AttributesEntry' : _reflection.GeneratedProtocolMessageType('AttributesEntry', (_message.Message,), {
        'DESCRIPTOR' : _SPAN_EVENT_ATTRIBUTESENTRY,
        '__module__' : 'databricks_trace_server_pb2'
        # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.Event.AttributesEntry)
        })
      ,
      'DESCRIPTOR' : _SPAN_EVENT,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.Event)
      })
    ,

    'Link' : _reflection.GeneratedProtocolMessageType('Link', (_message.Message,), {

      'AttributesEntry' : _reflection.GeneratedProtocolMessageType('AttributesEntry', (_message.Message,), {
        'DESCRIPTOR' : _SPAN_LINK_ATTRIBUTESENTRY,
        '__module__' : 'databricks_trace_server_pb2'
        # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.Link.AttributesEntry)
        })
      ,
      'DESCRIPTOR' : _SPAN_LINK,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.Link)
      })
    ,

    'Status' : _reflection.GeneratedProtocolMessageType('Status', (_message.Message,), {
      'DESCRIPTOR' : _SPAN_STATUS,
      '__module__' : 'databricks_trace_server_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span.Status)
      })
    ,
    'DESCRIPTOR' : _SPAN,
    '__module__' : 'databricks_trace_server_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.traceserver.Span)
    })
  _sym_db.RegisterMessage(Span)
  _sym_db.RegisterMessage(Span.AttributesEntry)
  _sym_db.RegisterMessage(Span.Event)
  _sym_db.RegisterMessage(Span.Event.AttributesEntry)
  _sym_db.RegisterMessage(Span.Link)
  _sym_db.RegisterMessage(Span.Link.AttributesEntry)
  _sym_db.RegisterMessage(Span.Status)

  _DATABRICKSTRACINGSERVERSERVICE = DESCRIPTOR.services_by_name['DatabricksTracingServerService']
  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\220\001\001'
    _CREATETRACE._options = None
    _CREATETRACE._serialized_options = b'\342?\037\n\035com.databricks.rpc.RPC[Trace]'
    _TRACEINFO_TRACEMETADATAENTRY._options = None
    _TRACEINFO_TRACEMETADATAENTRY._serialized_options = b'8\001'
    _TRACEINFO_TAGSENTRY._options = None
    _TRACEINFO_TAGSENTRY._serialized_options = b'8\001'
    _SPAN_ATTRIBUTESENTRY._options = None
    _SPAN_ATTRIBUTESENTRY._serialized_options = b'8\001'
    _SPAN_EVENT_ATTRIBUTESENTRY._options = None
    _SPAN_EVENT_ATTRIBUTESENTRY._serialized_options = b'8\001'
    _SPAN_EVENT.fields_by_name['time_unix_nano']._options = None
    _SPAN_EVENT.fields_by_name['time_unix_nano']._serialized_options = b'\370\206\031\001'
    _SPAN_EVENT.fields_by_name['name']._options = None
    _SPAN_EVENT.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SPAN_LINK_ATTRIBUTESENTRY._options = None
    _SPAN_LINK_ATTRIBUTESENTRY._serialized_options = b'8\001'
    _SPAN_LINK.fields_by_name['trace_id']._options = None
    _SPAN_LINK.fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _SPAN_LINK.fields_by_name['span_id']._options = None
    _SPAN_LINK.fields_by_name['span_id']._serialized_options = b'\370\206\031\001'
    _SPAN.fields_by_name['trace_id']._options = None
    _SPAN.fields_by_name['trace_id']._serialized_options = b'\370\206\031\001'
    _SPAN.fields_by_name['span_id']._options = None
    _SPAN.fields_by_name['span_id']._serialized_options = b'\370\206\031\001'
    _SPAN.fields_by_name['name']._options = None
    _SPAN.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SPAN.fields_by_name['start_time_unix_nano']._options = None
    _SPAN.fields_by_name['start_time_unix_nano']._serialized_options = b'\370\206\031\001'
    _SPAN.fields_by_name['end_time_unix_nano']._options = None
    _SPAN.fields_by_name['end_time_unix_nano']._serialized_options = b'\370\206\031\001'
    _DATABRICKSTRACINGSERVERSERVICE.methods_by_name['createTrace']._options = None
    _DATABRICKSTRACINGSERVERSERVICE.methods_by_name['createTrace']._serialized_options = b'\362\206\031\033\n\027\n\004POST\022\017/tracing/traces\020\003'
    _CREATETRACE._serialized_start=209
    _CREATETRACE._serialized_end=348
    _TRACE._serialized_start=350
    _TRACE._serialized_end=447
    _TRACEINFO._serialized_start=450
    _TRACEINFO._serialized_end=1106
    _TRACEINFO_TRACEMETADATAENTRY._serialized_start=941
    _TRACEINFO_TRACEMETADATAENTRY._serialized_end=993
    _TRACEINFO_TAGSENTRY._serialized_start=995
    _TRACEINFO_TAGSENTRY._serialized_end=1038
    _TRACEINFO_STATE._serialized_start=1040
    _TRACEINFO_STATE._serialized_end=1106
    _TRACELOCATION._serialized_start=1109
    _TRACELOCATION._serialized_end=1583
    _TRACELOCATION_MLFLOWEXPERIMENTLOCATION._serialized_start=1367
    _TRACELOCATION_MLFLOWEXPERIMENTLOCATION._serialized_end=1416
    _TRACELOCATION_INFERENCETABLELOCATION._serialized_start=1418
    _TRACELOCATION_INFERENCETABLELOCATION._serialized_end=1467
    _TRACELOCATION_TRACELOCATIONTYPE._serialized_start=1469
    _TRACELOCATION_TRACELOCATIONTYPE._serialized_end=1569
    _TRACEDATA._serialized_start=1585
    _TRACEDATA._serialized_end=1637
    _SPAN._serialized_start=1640
    _SPAN._serialized_end=3098
    _SPAN_ATTRIBUTESENTRY._serialized_start=2192
    _SPAN_ATTRIBUTESENTRY._serialized_end=2265
    _SPAN_EVENT._serialized_start=2268
    _SPAN_EVENT._serialized_end=2502
    _SPAN_EVENT_ATTRIBUTESENTRY._serialized_start=2192
    _SPAN_EVENT_ATTRIBUTESENTRY._serialized_end=2265
    _SPAN_LINK._serialized_start=2505
    _SPAN_LINK._serialized_end=2770
    _SPAN_LINK_ATTRIBUTESENTRY._serialized_start=2192
    _SPAN_LINK_ATTRIBUTESENTRY._serialized_end=2265
    _SPAN_STATUS._serialized_start=2773
    _SPAN_STATUS._serialized_end=2942
    _SPAN_STATUS_STATUSCODE._serialized_start=2858
    _SPAN_STATUS_STATUSCODE._serialized_end=2936
    _SPAN_SPANKIND._serialized_start=2945
    _SPAN_SPANKIND._serialized_end=3098
    _DATABRICKSTRACINGSERVERSERVICE._serialized_start=3101
    _DATABRICKSTRACINGSERVERSERVICE._serialized_end=3241
  DatabricksTracingServerService = service_reflection.GeneratedServiceType('DatabricksTracingServerService', (_service.Service,), dict(
    DESCRIPTOR = _DATABRICKSTRACINGSERVERSERVICE,
    __module__ = 'databricks_trace_server_pb2'
    ))

  DatabricksTracingServerService_Stub = service_reflection.GeneratedServiceStubType('DatabricksTracingServerService_Stub', (DatabricksTracingServerService,), dict(
    DESCRIPTOR = _DATABRICKSTRACINGSERVERSERVICE,
    __module__ = 'databricks_trace_server_pb2'
    ))


  # @@protoc_insertion_point(module_scope)

