"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7419],{42550:function(e,t,n){n.d(t,{t:function(){return u}});var a=n(89555),r=n(77520),s=n(31014),i=n(15230),o=n(21879),l=n(50111);var d={name:"1h0bf8r",styles:"body, :host{user-select:none;}"},c={name:"5ob2ly",styles:"display:flex;position:relative"};const u=e=>{let{runListHidden:t,width:n,onResize:r,children:u,onHiddenChange:m}=e;const g=(0,o.e)(),[h,f]=(0,s.useState)(!1);return(0,l.FD)(l.FK,{children:[(0,l.Y)(i.ResizableBox,{css:c,style:{flex:`0 0 ${t?0:n}px`},width:n,axis:"x",resizeHandles:["e"],minConstraints:[250,0],handle:(0,l.Y)(p,{runListHidden:t,updateRunListHidden:e=>{m?m(e):g((t=>({...t,runListHidden:e})))}}),onResize:(e,n)=>{let{size:a}=n;t||r(a.width)},onResizeStart:()=>!t&&f(!0),onResizeStop:()=>f(!1),children:u}),h&&(0,l.Y)(a.mL,{styles:d})]})},p=s.forwardRef(((e,t)=>{let{updateRunListHidden:n,runListHidden:s,...i}=e;const{theme:o}=(0,r.u)();return(0,l.FD)("div",{ref:t,...i,css:(0,a.AH)({transition:"opacity 0.2s",width:0,overflow:"visible",height:"100%",position:"relative",zIndex:10,display:"flex",opacity:s?1:0,"&:hover":{opacity:1,".bar":{opacity:1},".button":{border:`2px solid ${o.colors.actionDefaultBorderHover}`}}},""),children:[(0,l.Y)("div",{css:(0,a.AH)({position:"absolute",left:-o.general.iconSize/2,width:o.general.iconSize,cursor:s?void 0:"ew-resize",height:"100%",top:0,bottom:0},""),children:(0,l.Y)("div",{className:"button",css:(0,a.AH)({top:"50%",transition:"border-color 0.2s",position:"absolute",width:o.general.iconSize,height:o.general.iconSize,backgroundColor:o.colors.backgroundPrimary,borderRadius:o.general.iconSize,overflow:"hidden",border:`1px solid ${o.colors.border}`,display:"flex",alignItems:"center",justifyContent:"center",zIndex:11},""),children:(0,l.Y)(r.B,{componentId:"mlflow.experiment_page.table_resizer.collapse",onClick:()=>n(!s),icon:s?(0,l.Y)(r.q,{}):(0,l.Y)(r.o,{}),size:"small"})})}),(0,l.Y)("div",{className:"bar",css:(0,a.AH)({position:"absolute",opacity:0,left:-1.5,width:3,height:"100%",top:0,bottom:0,backgroundColor:o.colors.actionPrimaryBackgroundDefault},"")})]})}))},72314:function(e,t,n){n.d(t,{L:function(){return i}});var a=n(56675),r=n(95947);const s=a.J1`
  query MlflowGetExperimentQuery($input: MlflowGetExperimentInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetExperiment(input: $input) {
      apiError {
        code
        message
      }
      experiment {
        artifactLocation
        creationTime
        experimentId
        lastUpdateTime
        lifecycleStage
        name
        tags {
          key
          value
        }
      }
    }
  }
`,i=e=>{var t;let{experimentId:n,options:a={}}=e;const{data:i,loading:o,error:l,refetch:d}=(0,r.I)(s,{variables:{input:{experimentId:n}},skip:!n,...a});return{loading:o,data:null===i||void 0===i||null===(t=i.mlflowGetExperiment)||void 0===t?void 0:t.experiment,refetch:d,apolloError:l,apiError:(()=>{var e;return null===i||void 0===i||null===(e=i.mlflowGetExperiment)||void 0===e?void 0:e.apiError})()}}},87877:function(e,t,n){n.d(t,{J:function(){return s},N:function(){return i}});var a=n(63528),r=n(46795);const s=e=>(t,n,r)=>{n?e().includes(n)?r(`Experiment "${n}" already exists.`):a.x.getExperimentByName({experiment_name:n}).then((e=>r(`Experiment "${n}" already exists in deleted state.\n                                 You can restore the experiment, or permanently delete the\n                                 experiment from the .trash folder (under tracking server's\n                                 root folder) in order to use this experiment name again.`))).catch((e=>r(void 0))):r(void 0)},i=(e,t,n)=>{t?r.x.getRegisteredModel({name:t}).then((()=>n(`Model "${t}" already exists.`))).catch((e=>n(void 0))):n(void 0)}},91170:function(e,t,n){n.r(t),n.d(t,{default:function(){return ts}});var a=n(89555),r=n(31014),s=n(10811),i=n(26809),o=n(93215),l=n(7204),d=n(53140),c=n(3293),u=n(42848),p=n(77520),m=n(65418),g=n(36698),h=(n(59764),n(58481)),f=n(80683),v=n.n(f),_=n(67245),x=n(64912),y=n(50111);const w="experimentName",b="artifactLocation";class Y extends r.Component{render(){return(0,y.FD)(c.SQ4,{ref:this.props.innerRef,layout:"vertical",children:[(0,y.Y)(c.SQ4.Item,{label:this.props.intl.formatMessage({id:"eyGoqW",defaultMessage:"Experiment Name"}),name:w,rules:[{required:!0,message:this.props.intl.formatMessage({id:"okQ1oB",defaultMessage:"Please input a new name for the new experiment."})},{validator:this.props.validator}],children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_createexperimentform.tsx_51",placeholder:this.props.intl.formatMessage({id:"FZubuU",defaultMessage:"Input an experiment name"}),autoFocus:!0})}),(0,y.Y)(c.SQ4.Item,{name:b,label:this.props.intl.formatMessage({id:"meoYKZ",defaultMessage:"Artifact Location"}),rules:[{required:!1}],children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_createexperimentform.tsx_71",placeholder:this.props.intl.formatMessage({id:"WvHAEg",defaultMessage:"Input an artifact location (optional)"})})})]})}}const C=(0,x.Ay)(Y);var I=n(87877),A=n(72877),k=n(25869);class S extends r.Component{constructor(){super(...arguments),this.handleCreateExperiment=async e=>{const t=e[w],n=e[b],a=await this.props.createExperimentApi(t,n);await this.props.searchExperimentsApi();const{value:{experiment_id:r}}=a;r&&this.props.navigate(h.h.getExperimentPageRoute(r))},this.debouncedExperimentNameValidator=v()((0,I.J)((()=>this.props.experimentNames)),400)}render(){const{isOpen:e}=this.props;return(0,y.Y)(_.B,{title:"Create Experiment",okText:"Create",isOpen:e,handleSubmit:this.handleCreateExperiment,onClose:this.props.onClose,children:(0,y.Y)(C,{validator:this.debouncedExperimentNameValidator})})}}const E={createExperimentApi:i.Gh,searchExperimentsApi:i.vF},M=(0,k.h)((0,s.Ng)((e=>({experimentNames:(0,A.DZ)(e).map((e=>e.name))})),E)(S));var R=n(97026),T=n(76010);class D extends r.Component{constructor(){super(...arguments),this.handleSubmit=()=>{const{experimentId:e,activeExperimentIds:t}=this.props,n=(0,l.yk)();return this.props.deleteExperimentApi(e,n).then((()=>{if(null!==t&&void 0!==t&&t.includes(e))if(1===t.length)this.props.navigate(h.h.rootRoute);else{const n=t.filter((t=>t!==e)),a=1===n.length?h.h.getExperimentPageRoute(n[0]):h.h.getCompareExperimentsPageRoute(n);this.props.navigate(a)}})).then((()=>this.props.searchExperimentsApi(n))).catch((e=>{T.A.logErrorAndNotifyUser(e)}))}}render(){return(0,y.Y)(R.u,{isOpen:this.props.isOpen,onClose:this.props.onClose,handleSubmit:this.handleSubmit,title:`Delete Experiment "${this.props.experimentName}"`,helpText:(0,y.FD)("div",{children:[(0,y.Y)("p",{children:(0,y.FD)("b",{children:['Experiment "',this.props.experimentName,'" (Experiment ID: ',this.props.experimentId,") will be deleted."]})}),""]}),confirmButtonText:"Delete"})}}const F={deleteExperimentApi:i.lJ,searchExperimentsApi:i.vF},L=(0,k.h)((0,s.Ng)(void 0,F)(D));var B=n(58645);class H extends r.Component{constructor(){super(...arguments),this.handleRenameExperiment=e=>{const t=e[B.m];return this.props.updateExperimentApi(this.props.experimentId,t).then((()=>this.props.getExperimentApi(this.props.experimentId))).catch((e=>T.A.logErrorAndNotifyUser(e)))},this.debouncedExperimentNameValidator=v()((0,I.J)((()=>this.props.experimentNames)),400)}render(){const{isOpen:e,experimentName:t}=this.props;return(0,y.Y)(_.B,{title:"Rename Experiment",okText:"Save",isOpen:e,handleSubmit:this.handleRenameExperiment,onClose:this.props.onClose,children:(0,y.Y)(B.P,{type:"experiment",name:t,visible:e,validator:this.debouncedExperimentNameValidator})})}}const P={updateExperimentApi:i.Td,getExperimentApi:i.yc},N=(0,s.Ng)((e=>({experimentNames:(0,A.DZ)(e).map((e=>e.name))})),P)(H);var O={name:"1d4xjjz",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1"},U={name:"15c25lm",styles:"svg{transform:rotate(-90deg);}"},K={name:"ddhbat",styles:"box-sizing:border-box;height:100%;margin-left:24px;margin-right:8px;padding-right:16px;width:100%;min-width:max(280px, 20vw);max-width:20vw;display:grid;grid-template-rows:auto auto 1fr"},V={name:"1uoxr6z",styles:"svg{transform:rotate(90deg);}"};class z extends r.Component{constructor(){super(...arguments),this.list=void 0,this.state={checkedKeys:this.props.activeExperimentIds,hidden:!1,searchInput:"",showCreateExperimentModal:!1,showDeleteExperimentModal:!1,showRenameExperimentModal:!1,selectedExperimentId:"0",selectedExperimentName:""},this.bindListRef=e=>{this.list=e},this.componentDidUpdate=()=>{this.list&&this.list.forceUpdateGrid()},this.filterExperiments=e=>{const{experiments:t}=this.props,n=e.toLowerCase();return""===n?this.props.experiments:t.filter((e=>{let{name:t}=e;return t.toLowerCase().includes(n)}))},this.handleSearchInputChange=e=>{this.setState({searchInput:e.target.value})},this.updateSelectedExperiment=(e,t)=>{this.setState({selectedExperimentId:e,selectedExperimentName:t})},this.handleCreateExperiment=()=>{this.setState({showCreateExperimentModal:!0})},this.handleDeleteExperiment=(e,t)=>()=>{this.setState({showDeleteExperimentModal:!0}),this.updateSelectedExperiment(e,t)},this.handleRenameExperiment=(e,t)=>()=>{this.setState({showRenameExperimentModal:!0}),this.updateSelectedExperiment(e,t)},this.handleCloseCreateExperimentModal=()=>{this.setState({showCreateExperimentModal:!1})},this.handleCloseDeleteExperimentModal=()=>{this.setState({showDeleteExperimentModal:!1}),this.updateSelectedExperiment("0","")},this.handleCloseRenameExperimentModal=()=>{this.setState({showRenameExperimentModal:!1}),this.updateSelectedExperiment("0","")},this.handleCheck=(e,t)=>{this.setState(((n,a)=>{let{checkedKeys:r}=n;return!0!==e||a.activeExperimentIds.includes(t)||(r=[t,...a.activeExperimentIds]),!1===e&&1!==a.activeExperimentIds.length&&(r=a.activeExperimentIds.filter((e=>e!==t))),{checkedKeys:r}}),this.pushExperimentRoute)},this.pushExperimentRoute=()=>{if(this.state.checkedKeys.length>0){const e=1===this.state.checkedKeys.length?h.h.getExperimentPageRoute(this.state.checkedKeys[0]):h.h.getCompareExperimentsPageRoute(this.state.checkedKeys);this.props.navigate(e)}},this.renderListItem=e=>{let{index:t,key:n,style:r,parent:s}=e;const i=s.props.data[t],{activeExperimentIds:l}=this.props,d=l.includes(i.experimentId),m=d?"active-experiment-list-item":"experiment-list-item",{theme:g}=this.props.designSystemThemeApi;return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",paddingLeft:g.spacing.xs,paddingRight:g.spacing.xs,borderLeft:d?`solid ${g.colors.primary}`:"solid transparent",borderLeftWidth:4,backgroundColor:d?g.colors.actionDefaultBackgroundPress:"transparent",fontSize:g.typography.fontSizeBase,svg:{width:14,height:14}},""),"data-testid":m,style:r,children:[(0,y.Y)(c.Sc0,{componentId:"mlflow.experiment_list_view.check_box",id:i.experimentId,onChange:e=>this.handleCheck(e,i.experimentId),isChecked:d,"data-testid":`${m}-check-box`},i.experimentId),(0,y.Y)(o.N_,{className:"experiment-link",css:O,to:h.h.getExperimentPageRoute(i.experimentId),onClick:()=>this.setState({checkedKeys:[i.experimentId]}),title:i.name,"data-testid":`${m}-link`,children:i.name}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.rename_experiment_button.tooltip",content:"Rename experiment",children:(0,y.Y)(p.B,{type:"link",componentId:"mlflow.experiment_list_view.rename_experiment_button",icon:(0,y.Y)(c.R2l,{}),onClick:this.handleRenameExperiment(i.experimentId,i.name),"data-testid":"rename-experiment-button",size:"small"})}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.delete_experiment_button.tooltip",content:"Delete experiment",children:(0,y.Y)(p.B,{type:"link",componentId:"mlflow.experiment_list_view.delete_experiment_button",icon:(0,y.Y)(c.ucK,{}),onClick:this.handleDeleteExperiment(i.experimentId,i.name),"data-testid":"delete-experiment-button",size:"small"})})]},n)},this.unHide=()=>this.setState({hidden:!1}),this.hide=()=>this.setState({hidden:!0})}render(){const{hidden:e}=this.state,{activeExperimentIds:t,designSystemThemeApi:n}=this.props,{theme:r}=n;if(e)return(0,y.Y)(u.T,{content:"Show experiment list",componentId:"mlflow.experiment_list_view.show_experiments.tooltip",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.show_experiments",icon:(0,y.Y)(c.iTX,{}),onClick:this.unHide,css:U,title:"Show experiment list"})});const{searchInput:s}=this.state,i=this.filterExperiments(s);return(0,y.FD)("div",{id:"experiment-list-outer-container",css:K,children:[(0,y.Y)(M,{isOpen:this.state.showCreateExperimentModal,onClose:this.handleCloseCreateExperimentModal}),(0,y.Y)(L,{isOpen:this.state.showDeleteExperimentModal,onClose:this.handleCloseDeleteExperimentModal,activeExperimentIds:t,experimentId:this.state.selectedExperimentId,experimentName:this.state.selectedExperimentName}),(0,y.Y)(N,{isOpen:this.state.showRenameExperimentModal,onClose:this.handleCloseRenameExperimentModal,experimentId:this.state.selectedExperimentId,experimentName:this.state.selectedExperimentName}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:r.spacing.sm},""),children:[(0,y.Y)(p.T.Title,{level:2,style:{margin:0},children:"Experiments"}),(0,y.FD)("div",{children:[(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.new_experiment_button.tooltip",content:"New experiment",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.new_experiment_button",icon:(0,y.Y)(c.GYj,{}),onClick:this.handleCreateExperiment,title:"New Experiment","data-testid":"create-experiment-button"})}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.hide_button.tooltip",content:"Hide experiment list",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.hide_button",icon:(0,y.Y)(c.iTX,{}),onClick:this.hide,css:V,title:"Hide experiment list"})})]})]}),(0,y.Y)(m.I,{componentId:"mlflow.experiment_list_view.search_input",placeholder:"Search experiments","aria-label":"search experiments",value:s,onChange:this.handleSearchInputChange,"data-testid":"search-experiment-input"}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:r.spacing.xs},""),children:(0,y.Y)(g.t$,{children:e=>{let{width:t,height:n}=e;return(0,y.Y)(g.B8,{rowRenderer:this.renderListItem,data:i,ref:this.bindListRef,rowHeight:32,overscanRowCount:10,height:n,width:t,rowCount:i.length})}})})]})}}var G=(0,k.h)((0,p.au)(z)),$=n(24947),j=n(9133);const q=(0,r.createContext)(null),W=e=>{let{children:t,actions:n}=e;const[a,i]=(0,r.useState)([]),[o,l]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),p=(0,s.wA)(),m=(0,r.useCallback)((e=>{u(null),(()=>{const t=e.map((e=>{const t=n.getExperimentApi(e);return p(t).catch((e=>{T.A.logErrorAndNotifyUser(e)})),t.meta.id}));i((e=>(0,j.isEqual)(t,e)?e:t))})()}),[n,p]),g=(0,r.useMemo)((()=>({fetchExperiments:m,isLoadingExperiment:o,requestError:c,actions:n})),[n,m,o,c]);return(0,y.Y)(q.Provider,{value:g,children:(0,y.Y)(d.Ay,{shouldOptimisticallyRender:!0,requestIds:a,children:(e,n,r)=>(l(r.some((e=>a.includes(e.id)&&e.active))),c||r.forEach((e=>{e.error&&u(e.error)})),t)})})};var Q=n(47664),J=n(64558);var Z=n.p+"static/media/permission-denied-lock.16036747d57cd663d7df223781a447b2.svg";function X(e){let{errorMessage:t}=e;const{theme:n}=(0,p.u)();return(0,y.FD)("div",{className:"center",children:[(0,y.Y)("img",{style:{height:300,marginTop:80},src:Z,alt:"permission denied"}),(0,y.Y)("h1",{style:{paddingTop:10},children:"Permission Denied"}),(0,y.Y)("h2",{"data-testid":"error-message",css:(0,a.AH)({color:n.colors.textSecondary},""),children:t||"The current user does not have permission to view this page."})]})}var ee=n(88443),te=n(79085),ne=n(56412);var ae={name:"490tlg",styles:"display:flex;gap:4px"};const re=e=>{let{copyText:t}=e;return(0,y.FD)("div",{css:ae,children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_shared_building_blocks_copybox.tsx_18",readOnly:!0,value:t,"data-testid":"copy-box"}),(0,y.Y)(ne.i,{copyText:t})]})},se=e=>{let{visible:t,onCancel:n,link:a}=e;return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_getlinkmodal.tsx_21",title:(0,y.Y)(ee.A,{id:"ktiuki",defaultMessage:"Get Link"}),visible:t,onCancel:n,children:(0,y.Y)(re,{copyText:a})})};var ie=n(45653),oe=n(25866),le=n(91144);const de="compareRunsMode",ce=()=>(0,le.xt)()?"CHART":"TABLE",ue={MODELS:e=>h.h.getExperimentPageTabRoute(e,oe.fM.Models)},pe=()=>{const[e,t]=(0,o.ok)(),n=(0,o.Zp)();return[e.get(de)||ce(),(e,a)=>{if(e in ue&&a){var r;const t=null===(r=ue[e])||void 0===r?void 0:r.call(ue,a);if(t)return void n(t)}t((t=>(t.set(de,e||""),t)),{replace:!1})}]};var me={name:"11jf4ye",styles:"display:flex;gap:8px"},ge={name:"82a6rk",styles:"flex:1"};const he=e=>{let{onCancel:t,visible:n,experimentIds:a,searchFacetsState:o,uiState:l}=e;const[d,p]=(0,r.useState)(""),[g,f]=(0,r.useState)(!0),[v,_]=(0,r.useState)(null),[x]=pe(),w=(0,s.wA)(),b=(0,r.useMemo)((()=>({...o,...l})),[o,l]),Y=(0,r.useCallback)((async e=>{if(a.length>1)return f(!1),_(e),void p(window.location.href);f(!0);const[t]=a;try{const n=await(async e=>(0,le.YP)()?(0,ie.z7)(JSON.stringify(e)):JSON.stringify(e))(e),a=await(0,ie.Xb)(n),r=`${oe.o6}${a}`;await w((0,i.EJ)(t,r,n)),f(!1),_(e),p(((e,t,n)=>{const a=h.h.getExperimentPageRoute(e),r=new URLSearchParams;r.set(oe.ex,t),n&&r.set(de,n);const s=r.toString(),i=`${a}${null!==s&&void 0!==s&&s.startsWith("?")?"":"?"}${s}`;return`${window.location.origin}${window.location.pathname}#${i}`})(t,a,x))}catch(n){throw T.A.logErrorAndNotifyUser("Failed to create shareable link for experiment"),n}}),[w,a,x]);return(0,r.useEffect)((()=>{n&&v!==b&&Y(b)}),[n,Y,v,b]),(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentgetsharelinkmodal.tsx_101",title:(0,y.Y)(ee.A,{id:"6KFQMl",defaultMessage:"Get shareable link"}),visible:n,onCancel:t,children:(0,y.FD)("div",{css:me,children:[g?(0,y.Y)(c.xUE,{css:ge}):(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentgetsharelinkmodal.tsx_115",placeholder:"Click button on the right to create shareable state",value:d,readOnly:!0}),(0,y.Y)(ne.i,{loading:g,copyText:d,"data-testid":"share-link-copy-button"})]})})},fe=e=>{let{searchFacetsState:t,uiState:n,experimentIds:a}=e;const[s,i]=(0,r.useState)(!1);return(0,y.FD)(y.FK,{children:[t&&n&&a?(0,y.Y)(he,{searchFacetsState:t,uiState:n,visible:s,onCancel:()=>i(!1),experimentIds:a}):(0,y.Y)(se,{link:window.location.href,visible:s,onCancel:()=>i(!1)}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheadersharebutton.tsx_44",type:"primary",onClick:()=>i(!0),"data-test-id":"share-button",children:(0,y.Y)(ee.A,{id:"2oFAO4",defaultMessage:"Share"})})]})},ve=r.memo((e=>{let{experiments:t}=e;const n=(0,r.useMemo)((()=>(0,y.Y)(ee.A,{id:"Dhn7Mb",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:t.length}})),[t.length]);return(0,y.Y)(te.z,{title:n,breadcrumbs:[],children:(0,y.Y)(fe,{})})}));var _e=n(21616);var xe=n(33946),ye=n(53677);class we extends r.Component{constructor(e){super(e),this.handleSubmit=this.handleSubmit.bind(this)}handleSubmit(){const e=[];return this.props.selectedRunIds.forEach((t=>{e.push(this.props.restoreRunApi(t))})),Promise.all(e).catch((e=>{let t="While restoring an experiment run, an error occurred.";e.textJson&&"RESOURCE_LIMIT_EXCEEDED"===e.textJson.error_code&&(t=t+" "+e.textJson.message),this.props.openErrorModal(t)})).then((()=>{var e,t;null===(e=(t=this.props).onSuccess)||void 0===e||e.call(t)}))}render(){const e=this.props.selectedRunIds.length;return(0,y.Y)(R.u,{isOpen:this.props.isOpen,onClose:this.props.onClose,handleSubmit:this.handleSubmit,title:`Restore Experiment ${T.A.pluralize("Run",e)}`,helpText:`${e} experiment ${T.A.pluralize("run",e)} will be restored.`,confirmButtonText:"Restore"})}}const be={restoreRunApi:i.iz,openErrorModal:i.Yi};var Ye=(0,s.Ng)(null,be)(we);const Ce=e=>{let{showDeleteRunModal:t,showRestoreRunModal:n,showRenameRunModal:a,runsSelected:r,onCloseDeleteRunModal:s,onCloseRestoreRunModal:i,onCloseRenameRunModal:o,renamedRunName:l,refreshRuns:d}=e;const c=Object.entries(r).filter((e=>{let[,t]=e;return t})).map((e=>{let[t]=e;return t}));return(0,y.FD)(y.FK,{children:[(0,y.Y)(xe.A,{isOpen:t,onClose:s,selectedRunIds:c,onSuccess:()=>{d()}}),(0,y.Y)(Ye,{isOpen:n,onClose:i,selectedRunIds:c,onSuccess:()=>{d()}}),(0,y.Y)(ye.j,{runUuid:c[0],onClose:o,runName:l,isOpen:a,onSuccess:()=>{d()}})]})};var Ie=n(98590);var Ae={name:"82a6rk",styles:"flex:1"},ke={name:"82a6rk",styles:"flex:1"};const Se=e=>{let{isOpen:t,setIsOpen:n,selectedRunsExistingTagKeys:s,addNewTag:i}=e;const{theme:o}=(0,p.u)(),[l,d]=(0,r.useState)(""),[g,h]=(0,r.useState)(""),f=""===l||/^[^,.:/=\-\s]+$/.test(l),v=s.includes(l),_=f&&!v,x=l.length>0&&g.length>0&&_;return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_34",title:(0,y.Y)(ee.A,{id:"VGJhVI",defaultMessage:"Add New Tag"}),visible:t,onCancel:()=>n(!1),onOk:()=>{x&&(i({key:l,value:g}),n(!1),d(""),h(""))},okText:(0,y.Y)(ee.A,{id:"oU/SPm",defaultMessage:"Add"}),cancelText:(0,y.Y)(ee.A,{id:"urvfNd",defaultMessage:"Cancel"}),okButtonProps:{disabled:!x},children:(0,y.Y)("form",{css:(0,a.AH)({display:"flex",alignItems:"flex-end",gap:o.spacing.md},""),children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:o.spacing.md,flex:1},""),children:[(0,y.FD)("div",{css:Ae,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"key",children:(0,y.Y)(ee.A,{id:"FYxQgz",defaultMessage:"Key"})}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_51",value:l,onChange:e=>d(e.target.value),validationState:_?void 0:"warning","data-testid":"add-new-tag-key-input"}),!f&&(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(ee.A,{id:"Sb0Z4Z",defaultMessage:", . : / - = and blank spaces are not allowed"})}),v&&(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(ee.A,{id:"xBoybr",defaultMessage:"Tag key already exists on one or more of the selected runs. Please choose a different key."})})]}),(0,y.FD)("div",{css:ke,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"value",children:(0,y.Y)(ee.A,{id:"OQsEJv",defaultMessage:"Value"})}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_78",value:g,onChange:e=>h(e.target.value),"data-testid":"add-new-tag-value-input"})]})]})})})};var Ee=n(52350);const Me=e=>`${e.key}: ${e.value}`,Re=e=>{const[t,...n]=e.split(": ");return{key:t,value:n.join(": ")}},Te=e=>{let{runInfos:t,runsSelected:n,tagsList:o,refreshRuns:l}=e;const{theme:d}=(0,p.u)(),[u,m]=(0,r.useState)({}),[g,h]=(0,r.useState)(!1),[f,v]=(0,r.useState)(!1),[_,x]=(0,r.useState)(!1),{allSelectedTags:w,allNotSelectedTags:b,indeterminateTags:Y,allTags:C}=((e,t,n)=>{const a=e.flatMap(((e,a)=>{if(t[e.runUuid]){const e=n[a];return[Object.keys(e).filter(Ie.oD).map((t=>Me(e[t])))]}return[]})),r=n.flatMap((e=>Object.keys(e).filter(Ie.oD).map((t=>Me(e[t]))))),s=r.filter((e=>a.every((t=>t.includes(e))))),i=r.filter((e=>a.every((t=>!t.includes(e))))),o=r.filter((e=>!s.includes(e)&&a.some((t=>t.includes(e)))));return{allSelectedTags:s,allNotSelectedTags:i,indeterminateTags:o,allTags:r}})(t,n,o),I=e=>{m((()=>{const t={...u};return C.forEach((e=>{w.includes(e)?t[e]=!0:b.includes(e)?t[e]=!1:Y.includes(e)&&(t[e]=void 0)})),void 0!==e&&(t[Me(e)]=!0),t})),v(!0)},A=e=>{m((t=>({...t,[e]:!t[e]})))},k=(0,s.wA)();return(0,y.FD)(y.FK,{children:[(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsselecttags.tsx_162",open:f,label:"Add tags",id:"runs-tag-multiselect",multiSelect:!0,children:[(0,y.Y)(c.gGe,{onClick:()=>{f?v(!1):I()},"data-testid":"runs-tag-multiselect-trigger"}),(0,y.FD)(c.dn6,{matchTriggerWidth:!0,children:[(0,y.Y)(c.HI_,{children:Object.keys(u).map((e=>{const t=void 0===u[e];return(0,y.Y)(c.jTC,{value:e,onChange:A,checked:u[e],indeterminate:t},e)}))}),(0,y.Y)(c.BU4,{children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",justifyContent:"flex-end",gap:d.spacing.sm},""),children:[(0,y.Y)(p.B,{componentId:"mlflow.experiment_page.runs.add_new_tag",onClick:()=>{h(!0),v(!1)},icon:(0,y.Y)(c.c11,{}),"data-testid":"runs-add-new-tag-button",children:(0,y.Y)(ee.A,{id:"2zy+D5",defaultMessage:"Add new tag"})}),(0,y.Y)(p.B,{type:"primary",componentId:"mlflow.experiment_page.runs.add_tags",onClick:()=>{x(!0);t.flatMap(((e,t)=>n[e.runUuid]?[t]:[])).forEach((e=>{const n=t[e].runUuid,a=Object.values(o[e]).filter((e=>(0,Ie.oD)(e.key))),r=Object.keys(u).filter((e=>void 0===u[e]?a.map((e=>Me(e))).includes(e):u[e])).map((e=>Re(e)));k((0,i.hD)(n,a,r)).then((()=>{l()})).catch((e=>{const t=e instanceof Ee.s?e.getMessageField():e.message;T.A.displayGlobalErrorNotification(t)})).finally((()=>{x(!1),v(!1)}))}))},disabled:0===Object.keys(u).length,loading:_,children:(0,y.Y)(ee.A,{id:"dl0TeT",defaultMessage:"Save"})})]})})]})]}),(0,y.Y)(Se,{isOpen:g,setIsOpen:h,selectedRunsExistingTagKeys:(0,j.uniq)(w.concat(Y).map((e=>Re(e).key))),addNewTag:e=>{I(e)}})]})},De=e=>{let{children:t}=e;return(0,y.Y)(y.FK,{children:t})},Fe=r.memo((e=>{let{viewState:t,runsData:n,searchFacetsState:a,refreshRuns:s}=e;const{runsSelected:i}=t,{runInfos:l,tagsList:d}=n,{lifecycleFilter:c}=a,u=(0,o.Zp)(),{theme:m}=(0,p.u)(),[g,f]=(0,r.useState)(!1),[v,_]=(0,r.useState)(!1),[x,w]=(0,r.useState)(!1),[b,Y]=(0,r.useState)(""),C=(0,r.useCallback)((()=>{const e=Object.keys(i),t=l.find((t=>t.runUuid===e[0]));t&&(Y(t.runName),w(!0))}),[l,i]),I=(0,r.useCallback)((()=>{const e=Object.keys(i),t=l.filter((t=>{let{runUuid:n}=t;return e.includes(n)})).map((e=>{let{experimentId:t}=e;return t}));u(h.h.getCompareRunPageRoute(e,[...new Set(t)].sort()))}),[u,l,i]),A=(0,r.useCallback)((()=>f(!0)),[]),k=(0,r.useCallback)((()=>_(!0)),[]),S=(0,r.useCallback)((()=>f(!1)),[]),E=(0,r.useCallback)((()=>_(!1)),[]),M=(0,r.useCallback)((()=>w(!1)),[]),R=Object.values(t.runsSelected).filter(Boolean).length,T=R>0,D=1===R,F=R>1;return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:Le.controlBar,children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_110","data-testid":"run-rename-button",onClick:C,disabled:!D,children:(0,y.Y)(ee.A,{id:"oWPgX7",defaultMessage:"Rename"})}),c===oe.gy.ACTIVE?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_117","data-testid":"runs-delete-button",disabled:!T,onClick:A,danger:!0,children:(0,y.Y)(ee.A,{id:"3Rb4sG",defaultMessage:"Delete"})}):null,c===oe.gy.DELETED?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_126","data-testid":"runs-restore-button",disabled:!T,onClick:k,children:(0,y.Y)(ee.A,{id:"Potju2",defaultMessage:"Restore"})}):null,(0,y.Y)("div",{css:Le.buttonSeparator}),(0,y.Y)(De,{children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_136","data-testid":"runs-compare-button",disabled:!F,onClick:I,children:(0,y.Y)(ee.A,{id:"RaGnOQ",defaultMessage:"Compare"})})}),(0,y.Y)("div",{css:Le.buttonSeparator}),(0,y.Y)(Te,{runsSelected:i,runInfos:l,tagsList:d,refreshRuns:s})]}),(0,y.Y)(Ce,{runsSelected:i,onCloseRenameRunModal:M,onCloseDeleteRunModal:S,onCloseRestoreRunModal:E,showDeleteRunModal:g,showRestoreRunModal:v,showRenameRunModal:x,renamedRunName:b,refreshRuns:s})]})})),Le={buttonSeparator:e=>({borderLeft:`1px solid ${e.colors.border}`,marginLeft:e.spacing.xs,marginRight:e.spacing.xs,height:"100%"}),controlBar:e=>({display:"flex",gap:e.spacing.sm,alignItems:"center"})};var Be=n(88464),He=n(14343);var Pe={name:"bjn8wh",styles:"position:relative"};const Ne=r.memo((e=>{const{runInfos:t}=e,{theme:n}=(0,p.u)(),{refreshRuns:s}=e,o=(0,$.z)(),[l,d]=(0,r.useState)(0),[u,m]=(0,r.useState)(0);return(0,r.useEffect)((()=>{m(0),d((()=>Date.now()))}),[t]),(0,r.useEffect)((()=>{if(!l)return;const e=setInterval((()=>{const e={experimentIds:o,filter:`attributes.start_time > ${l}`,maxResults:oe.ks};(0,i.bc)(e).then((e=>{var t;const n=(null===(t=e.runs)||void 0===t?void 0:t.length)||0;m(n)}))}),oe.a0);return()=>clearInterval(e)}),[l,o]),(0,y.FD)("div",{css:Pe,children:[u>0&&(0,y.Y)("div",{title:oe.ks>u?`${u}`:oe.ks-1+"+",css:(0,a.AH)({position:"absolute",top:0,right:0,transform:"translate(50%, -50%)",display:"flex",justifyContent:"center",alignItems:"center",width:u>9?28:20,height:20,borderRadius:10,border:`1px solid ${n.colors.white}`,backgroundColor:n.colors.lime,color:n.colors.white,fontSize:10,fontWeight:"bold",userSelect:"none",zIndex:1},""),children:oe.ks>u?u:oe.ks-1+"+"}),(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{id:"ag05Pe",defaultMessage:"Refresh"}),useAsLabel:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrefreshbutton.tsx_123",onClick:s,"data-testid":"runs-refresh-button",icon:(0,y.Y)(c.Fjq,{})})})]})})),Oe=(0,s.Ng)((e=>({runInfos:e.entities.runInfosByUuid})),void 0,void 0,{areStatesEqual:(e,t)=>e.entities.runInfosByUuid===t.entities.runInfosByUuid})(Ne);var Ue=n(32614);const Ke=e=>(0,r.useMemo)((()=>Ue.A.getStoreForComponent("ExperimentView",e)),[e]),Ve=()=>(0,y.FD)("div",{className:"search-input-tooltip-content",children:[(0,y.Y)(ee.A,{id:"kIlkgf",defaultMessage:"Search runs using a simplified version of the SQL {whereBold} clause.",values:{whereBold:(0,y.Y)("b",{children:"WHERE"})}})," ",(0,y.Y)(ee.A,{id:"nYKZy4",defaultMessage:"<link>Learn more</link>",values:{link:e=>(0,y.Y)("a",{href:Q.g2,target:"_blank",rel:"noopener noreferrer",children:e})}}),(0,y.Y)("br",{}),(0,y.Y)(ee.A,{id:"U3btBc",defaultMessage:"Examples:"}),(0,y.Y)("br",{}),"\u2022 metrics.rmse >= 0.8",(0,y.Y)("br",{}),"\u2022 metrics.`f1 score` < 1",(0,y.Y)("br",{}),"\u2022 params.model = 'tree'",(0,y.Y)("br",{}),"\u2022 attributes.run_name = 'my run'",(0,y.Y)("br",{}),"\u2022 tags.`mlflow.user` = 'myUser'",(0,y.Y)("br",{}),"\u2022 metric.f1_score > 0.9 AND params.model = 'tree'",(0,y.Y)("br",{}),"\u2022 dataset.name IN ('dataset1', 'dataset2')",(0,y.Y)("br",{}),"\u2022 attributes.run_id IN ('a1b2c3d4', 'e5f6g7h8')",(0,y.Y)("br",{}),"\u2022 tags.model_class LIKE 'sklearn.linear_model%'"]});const ze=["run_id","run_name","status","artifact_uri","user_id","start_time","end_time","created"].map((e=>({value:`attributes.${e}`})));var Ge={name:"lugakg",styles:"font-weight:normal"};const $e=(e,t)=>{const n=e.split(RegExp(t.replace(".","\\."),"ig")),a=e.match(RegExp(t.replace(".","\\."),"ig"));return(0,y.Y)("span",{css:Ge,"data-test-id":e,children:n.map(((e,t)=>(0,y.FD)(r.Fragment,{children:[e,t!==n.length-1&&a&&(0,y.Y)("b",{children:a[t]})]},t)))})};var je=n(6604);const qe="tooltipLastPopup";var We={name:"c61v0h",styles:".du-bois-light-select-item-option-active:not(.du-bois-light-select-item-option-disabled){background-color:#e6f1f5;}"},Qe={name:"9gxvqt",styles:"display:flex;gap:4px;align-items:center"};const Je=e=>{const{runsData:t,searchFilter:n,requestError:s,onSearchFilterChange:i,onClear:o}=e,{theme:l,getPrefixedClassName:d}=(0,p.u)(),g=(0,r.useRef)(null),h=(0,Be.A)(),[f,v]=(0,r.useState)(""),[_,x]=(0,r.useState)(void 0),[w,b]=(0,r.useState)(!1),[Y,C]=(0,r.useState)(!1),I=(0,r.useRef)({metricNames:[],paramNames:[],tagNames:[]}),[A,k]=(0,r.useState)({Metrics:10,Parameters:10,Tags:10}),S=(0,r.useRef)([]),[E,M]=(0,r.useState)(void 0);(0,r.useEffect)((()=>{v(n)}),[n]);const R=(0,r.useMemo)((()=>{const e=I.current,n=((e,t)=>{const n=(e,t)=>[...new Set([...e,...t])],a=n(t.metricNames,e.metricKeyList),r=n(t.paramNames,e.paramKeyList),s=(i=e.tagsList,i.flatMap((e=>Object.keys(e)))).filter((e=>!e.startsWith("mlflow."))).map((e=>e.includes('"')||e.includes(" ")||e.includes(".")||/^\d+$/.test(e)?`\`${e}\``:e.includes("`")?`"${e}"`:e));var i;return{metricNames:a,paramNames:r,tagNames:n(t.tagNames,s)}})(t,e);return I.current=n,[{label:"Metrics",options:(a=n).metricNames.map((e=>({value:`metrics.${e}`})))},{label:"Parameters",options:a.paramNames.map((e=>({value:`params.${e}`})))},{label:"Tags",options:a.tagNames.map((e=>({value:`tags.${e}`})))},{label:"Attributes",options:ze}];var a}),[t]);(0,r.useEffect)((()=>{const e=S.current,t=(e=>{const t=/>|<|>=|<=|=|!=|like|ilike/gi,n=(e=>{const t=/and[\s]+/gi,n=[];let a,r;for(;r=t.lastIndex,a=t.exec(e);)n.push({clause:e.substring(r,a.index),startIndex:r});return n.push({clause:e.substring(r),startIndex:r}),n})(e),a=[];return n.forEach((e=>{const n=e.clause.split(t)[0],{startIndex:r}=e;a.push({name:n,startIndex:0+r,endIndex:n.length+r})})),a})(f);if(S.current=t,Y)return void C(!1);const n=t.map((e=>e.name)),a=e.map((e=>e.name));if(!(0,j.isEqual)(n,a)&&t.length>=e.length){let n=0;for(;n<t.length;){if(n>=e.length||t[n].name.trim()!==e[n].name.trim())return x(!0),void M(t[n]);n++}}x(!1)}),[f]);const T=(0,r.useMemo)((()=>E?((e,t,n)=>e.map((e=>{const a=e.options.filter((e=>e.value.toLowerCase().includes(t.name.toLowerCase().trim()))).map((e=>({value:e.value,label:$e(e.value,t.name.trim())}))),r=n[e.label],s=[...a.slice(0,r),...a.length>r?[{label:"...",value:`..._${e.label}`}]:[]];return{label:e.label,options:s}})).filter((e=>e.options.length>0)))(R,E,A):[]),[R,E,A]),D=(0,r.useCallback)(((e,t)=>{if(E)if(e.startsWith("...")){x(!0);const e=t.value.split("_")[1];k((t=>({...t,[e]:t[e]+10})))}else{const t=f.substring(0,E.startIndex),n=f.substring(E.endIndex);v(t+e+" "+n),C(!0),x(!1)}}),[f,v,E,x]),F=Ke(qe),[L,B]=(0,r.useState)((()=>{const e=Math.floor(Date.now()/1e3),t=F.getItem(qe);return!t||parseInt(t,10)<e-604800})),H=r.useRef(null),P=(0,r.useMemo)((()=>{if((0,le.ey)()&&f.length>0&&!(0,je.cK)(f))return(0,je.jA)(f)}),[f]);(0,r.useEffect)((()=>{if(s&&L){var e;const t=Math.floor(Date.now()/1e3);F.setItem(qe,t),B(!1),null===(e=H.current)||void 0===e||e.click()}}),[s]);const N=0===T.flatMap((e=>e.options)).length,O=_&&w&&!N,U=(0,r.useCallback)((e=>{var t;const n=d("select-item-option-active"),a=Boolean(null===(t=g.current)||void 0===t?void 0:t.querySelector(`.${n}`));"Enter"===e.key&&(O&&x(!1),O&&a||i(f)),"Escape"===e.key&&(e.preventDefault(),O&&x(!1))}),[O,f,i,d]);return(0,y.Y)("div",{css:(0,a.AH)({display:"flex",gap:l.spacing.sm,width:430,[l.responsive.mediaQueries.xs]:{width:"auto"}},""),children:(0,y.Y)(c.j9R,{dropdownMatchSelectWidth:560,css:(0,a.AH)({width:560,[l.responsive.mediaQueries.xs]:{width:"auto"}},""),defaultOpen:!1,defaultActiveFirstOption:!(0,le.ey)(),open:O,options:T,onSelect:D,value:f,"data-test-id":"runs-search-autocomplete",dropdownRender:e=>(0,y.Y)("div",{css:We,ref:g,children:e}),children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_236",value:f,prefix:(0,y.Y)(m.S,{css:(0,a.AH)({svg:{width:l.general.iconFontSize,height:l.general.iconFontSize,color:l.colors.textSecondary}},"")}),onKeyDown:U,onClick:()=>b(!0),onBlur:()=>b(!1),onChange:e=>v(e.target.value),placeholder:'metrics.rmse < 1 and params.model = "tree"',"data-test-id":"search-box",suffix:(0,y.FD)("div",{css:Qe,children:[f&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_212",onClick:()=>{o(),v("")},type:"link","data-test-id":"clear-button",children:(0,y.Y)(p.C,{})}),P?(0,y.Y)(u.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_310",content:(0,y.Y)(ee.A,{id:"ESmLOR",defaultMessage:"Using regular expression quick filter. The following query will be used: {filterSample}",values:{filterSample:(0,y.Y)("div",{children:(0,y.Y)("code",{children:P})})}}),delayDuration:0,children:(0,y.Y)(c.VAR,{"aria-label":h.formatMessage({id:"ESmLOR",defaultMessage:"Using regular expression quick filter. The following query will be used: {filterSample}"},{filterSample:P}),css:(0,a.AH)({svg:{width:l.general.iconFontSize,height:l.general.iconFontSize,color:l.colors.actionPrimaryBackgroundDefault}},"")})}):(0,y.Y)(c.paO,{title:(0,y.Y)(Ve,{}),placement:"right",dangerouslySetAntdProps:{overlayInnerStyle:{width:"150%"},trigger:["focus","click"]},children:(0,y.Y)(p.B,{size:"small",ref:H,componentId:"mlflow.experiment_page.search_filter.tooltip",type:"link",css:(0,a.AH)({marginLeft:-l.spacing.xs,marginRight:-l.spacing.xs},""),icon:(0,y.Y)(u.I,{css:(0,a.AH)({svg:{width:l.general.iconFontSize,height:l.general.iconFontSize,color:l.colors.textSecondary}},"")})})})]})})})})};var Ze=n(18476),Xe=n(37616);var et={name:"1voqbl3",styles:"margin-top:0;font-weight:normal"};const tt=e=>{let{isOpen:t,closeModal:n,experimentId:r}=e;const{theme:s}=(0,p.u)(),i=s.isDarkMode?"duotoneDark":"light",o=`\nimport mlflow\nfrom sklearn.model_selection import train_test_split\nfrom sklearn.datasets import load_diabetes\nfrom sklearn.ensemble import RandomForestRegressor\n\n# set the experiment id\nmlflow.set_experiment(experiment_id="${r}")\n\nmlflow.autolog()\ndb = load_diabetes()\n\nX_train, X_test, y_train, y_test = train_test_split(db.data, db.target)\n\n# Create and train models.\nrf = RandomForestRegressor(n_estimators=100, max_depth=6, max_features=3)\nrf.fit(X_train, y_train)\n\n# Use the model to make predictions on the test dataset.\npredictions = rf.predict(X_test)\n`.trimStart(),l=`\nimport mlflow\nimport openai\nimport os\nimport pandas as pd\n\n# you must set the OPENAI_API_KEY environment variable\nassert (\n  "OPENAI_API_KEY" in os.environ\n), "Please set the OPENAI_API_KEY environment variable."\n\n# set the experiment id\nmlflow.set_experiment(experiment_id="${r}")\n\nsystem_prompt = (\n  "The following is a conversation with an AI assistant."\n  + "The assistant is helpful and very friendly."\n)\n\n# start a run\nmlflow.start_run()\nmlflow.log_param("system_prompt", system_prompt)\n\n# Create a question answering model using prompt engineering\n# with OpenAI. Log the model to MLflow Tracking\nlogged_model = mlflow.openai.log_model(\n    model="gpt-4o-mini",\n    task=openai.chat.completions,\n    artifact_path="model",\n    messages=[\n        {"role": "system", "content": system_prompt},\n        {"role": "user", "content": "{question}"},\n    ],\n)\n\n# Evaluate the model on some example questions\nquestions = pd.DataFrame(\n    {\n        "question": [\n            "How do you create a run with MLflow?",\n            "How do you log a model with MLflow?",\n            "What is the capital of France?",\n        ]\n    }\n)\nmlflow.evaluate(\n    model=logged_model.model_uri,\n    model_type="question-answering",\n    data=questions,\n)\nmlflow.end_run()\n`.trimStart(),d=18*(Math.min(...[o,l].map((e=>e.split("\n").length)))+1);return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_createnotebookrunmodal.tsx_111",visible:t,onCancel:n,onOk:n,footer:(0,y.Y)("div",{css:(0,a.AH)({display:"flex",gap:s.spacing.sm,justifyContent:"flex-end"},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_createnotebookrunmodal.tsx_117",onClick:n,type:"primary",children:(0,y.Y)(ee.A,{id:"ttyLD4",defaultMessage:"Okay"})})}),title:(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:s.spacing.sm,marginBottom:s.spacing.xs},""),children:(0,y.Y)(ee.A,{id:"iF1UfU",defaultMessage:"New run using notebook"})}),(0,y.Y)(p.T.Hint,{css:et,children:"Run this code snippet in a notebook or locally, to create an experiment run"})]}),children:(0,y.FD)(c.Y6f,{children:[(0,y.Y)(c.jyI,{tab:(0,y.Y)(ee.A,{id:"71dHIO",defaultMessage:"Classical ML"}),children:(0,y.FD)("div",{style:{position:"relative"},children:[(0,y.Y)(Xe.z7,{style:{padding:"5px",height:d},language:"python",theme:i,children:o}),(0,y.Y)(ne.i,{copyText:o,showLabel:!1,icon:(0,y.Y)(c.TdU,{}),style:{position:"absolute",top:s.spacing.xs,right:s.spacing.xs}})]})},"classical-ml"),(0,y.Y)(c.jyI,{tab:(0,y.Y)(ee.A,{id:"xBVMQz",defaultMessage:"LLM"}),children:(0,y.FD)("div",{style:{position:"relative"},children:[(0,y.Y)(Xe.z7,{style:{padding:"5px",height:d},language:"python",theme:i,children:l}),(0,y.Y)(ne.i,{copyText:l,showLabel:!1,icon:(0,y.Y)(c.TdU,{}),style:{position:"absolute",top:s.spacing.xs,right:s.spacing.xs}})]})},"llm")]})})};var nt=n(30214),at=n(4877),rt=n.n(at),st=n(63528),it=n(50361);class ot extends Ee.s{getGatewayErrorMessage(){var e,t,n,a;return(null===(e=this.textJson)||void 0===e||null===(t=e.error)||void 0===t?void 0:t.message)||(null===(n=this.textJson)||void 0===n?void 0:n.message)||(null===(a=this.textJson)||void 0===a?void 0:a.toString())||this.text}}var lt;class dt{static createEvaluationTextPayload(e,t){switch(t){case it.W8.LLM_V1_COMPLETIONS:return{prompt:e};case it.W8.LLM_V1_CHAT:return{messages:[{content:e,role:"user"}]};case it.W8.LLM_V1_EMBEDDINGS:throw new Error(`Unsupported served LLM model task "${t}"!`);default:throw new Error(`Unknown served LLM model task "${t}"!`)}}}lt=dt,dt.queryMLflowDeploymentEndpointRoute=async(e,t)=>{rt()(e.mlflowDeployment,"Trying to call a MLflow deployment route without a deployment_url");const{inputText:n}=t,a={...lt.createEvaluationTextPayload(n,e.task),...t.parameters};return st.x.gatewayProxyPost({gateway_path:e.mlflowDeployment.endpoint_url.substring(1),json_data:a})},dt.queryModelGatewayRoute=async(e,t)=>{if("mlflow_deployment_endpoint"===e.type){rt()(e.mlflowDeployment,"Trying to call a serving endpoint route without an endpoint");return((e,t)=>{if(t===it.W8.LLM_V1_COMPLETIONS){var n,a;const t=e,r=null===(n=t.choices)||void 0===n||null===(a=n[0])||void 0===a?void 0:a.text,{usage:s}=t;if(r&&s)return{text:r,metadata:{total_tokens:s.total_tokens,output_tokens:s.completion_tokens,input_tokens:s.prompt_tokens}}}if(t===it.W8.LLM_V1_CHAT){var r,s,i;const t=e,n=null===(r=t.choices)||void 0===r||null===(s=r[0])||void 0===s||null===(i=s.message)||void 0===i?void 0:i.content,{usage:a}=t;if(n&&a)return{text:n,metadata:{total_tokens:a.total_tokens,output_tokens:a.completion_tokens,input_tokens:a.prompt_tokens}}}throw new Error(`Unrecognizable AI gateway response metadata "${e.usage}"!`)})(await lt.queryMLflowDeploymentEndpointRoute(e,t),e.task)}throw new Error("Unknown route type")};var ct=n(23734),ut=n(65795),pt=n(69526);const mt=[{type:"slider",name:"temperature",string:(0,pt.zR)({id:"Jrri/Y",defaultMessage:"Temperature"}),helpString:(0,pt.zR)({id:"5yWkFd",defaultMessage:"Increase or decrease the confidence level of the language model."}),max:1,min:0,step:.01},{type:"input",name:"max_tokens",string:(0,pt.zR)({id:"qR3llD",defaultMessage:"Max tokens"}),helpString:(0,pt.zR)({id:"cEwSR8",defaultMessage:"Maximum number of language tokens returned from evaluation."}),max:65536,min:1,step:1},{type:"list",name:"stop",string:(0,pt.zR)({id:"FAfPOl",defaultMessage:"Stop Sequences"}),helpString:(0,pt.zR)({id:"J2XCE/",defaultMessage:"Specify sequences that signal the model to stop generating text."})}],gt={temperature:.01,max_tokens:100},ht=()=>{const[e,t]=(0,r.useState)(gt),n=(0,r.useCallback)(((e,n)=>{t((t=>({...t,[e]:n})))}),[]);return{parameterDefinitions:mt,parameters:e,updateParameter:n}};var ft=n(20193);var vt={name:"1d3w5wq",styles:"width:100%"};const _t=e=>{let{parameterValue:t,updateParameter:n,disabled:s}=e;const[i,o]=(0,r.useState)(""),{theme:l}=(0,p.u)();return(0,j.isArray)(t)?(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,a.AH)({marginTop:l.spacing.xs,marginBottom:l.spacing.sm},""),children:t.map(((e,a)=>(0,y.Y)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_28",closable:!0,onClose:()=>{n(t.filter((t=>t!==e)))},children:e},a)))}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_39",allowClear:!0,css:vt,disabled:s,onChange:e=>o(e.target.value),value:i,onKeyDown:e=>{"Enter"===e.key&&i.trim()&&(n((0,j.uniq)([...t,i])),o(""))}})]}):null};var xt={name:"151f7v8",styles:"span{font-weight:normal;}"};const yt=e=>{let{disabled:t=!1,parameters:n,updateParameter:r}=e;const{parameterDefinitions:s}=ht(),{theme:i}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({marginBottom:i.spacing.lg},""),children:[(0,y.Y)(c.D$Q.Label,{css:(0,a.AH)({marginBottom:i.spacing.md},""),children:(0,y.Y)(ee.A,{id:"KADUUT",defaultMessage:"Model parameters"})}),s.map((e=>{var s;return(0,y.Y)("div",{css:(0,a.AH)({marginBottom:i.spacing.md},""),children:(0,y.FD)(y.FK,{children:[(0,y.FD)(c.D$Q.Label,{htmlFor:e.name,css:xt,children:[(0,y.Y)(ee.A,{...e.string}),(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{...e.helpString}),placement:"right",children:(0,y.Y)(u.I,{css:(0,a.AH)({marginLeft:i.spacing.sm,verticalAlign:"text-top",color:i.colors.textSecondary},"")})})]}),(0,y.Y)(c.D$Q.Hint,{}),"temperature"===e.name&&(0,y.Y)(ft.o,{"data-testid":e.name,disabled:t,max:e.max,min:e.min,step:e.step,value:n[e.name]||0,onChange:t=>r(e.name,t)}),"input"===e.type&&(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_107","data-testid":e.name,type:"number",disabled:t,max:e.max,min:e.min,step:e.step,value:n[e.name]||0,onChange:t=>r(e.name,parseInt(t.target.value,10))}),"list"===e.type&&(0,y.Y)(_t,{parameterValue:null!==(s=n[e.name])&&void 0!==s?s:[],disabled:t,updateParameter:t=>r(e.name,t)})]})},e.name)}))]})},wt=" }}",bt="new_variable",Yt=()=>{const[e,t]=(0,r.useState)(ut.KH),n=(0,r.useRef)(),a=(0,r.useCallback)((()=>{t((e=>{const t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(!e.includes(bt))return bt;const t=(0,j.max)(e.map((e=>{var t;return parseInt((null===(t=e.match(/new_variable_(\d+)/))||void 0===t?void 0:t[1])||"1",10)})))||1;return`${bt}_${t+1}`}((0,ut.rl)(e)),a=`${e} {{ ${t}${wt}`;return requestAnimationFrame((()=>{const e=n.current;e&&(e.focus(),e.setSelectionRange(a.length-t.length-3,a.length-3))})),a}))}),[t]);return{savePromptTemplateInputRef:(0,r.useCallback)((e=>{var t;n.current=null===e||void 0===e||null===(t=e.resizableTextArea)||void 0===t?void 0:t.textArea}),[]),handleAddVariableToTemplate:a,promptTemplate:e,updatePromptTemplate:t}},Ct=(0,pt.zR)({id:"lM0z8k",defaultMessage:"The following variable names contain spaces which is disallowed: {invalidNames}"}),It=e=>{let{violations:t}=e;const{namesWithSpaces:n}=t,{formatMessage:a}=(0,Be.A)();return(0,y.Y)(y.FK,{children:n.length>0&&(0,y.Y)(p.T.Text,{color:"warning",size:"sm","aria-label":a(Ct,{invalidNames:n.join(", ")}),children:(0,y.Y)(ee.A,{...Ct,values:{invalidNames:(0,y.Y)(y.FK,{children:n.map((e=>(0,y.Y)("code",{children:e},e)))})}})})})},At=[{prompt:["You are a marketing consultant for a technology company. Develop a marketing strategy report for {{ company_name }} aiming to {{ company_goal }}"],variables:[{name:"company_name",value:"XYZ Company"},{name:"company_goal",value:"Increase top-line revenue"}]},{prompt:['You are a helpful and friendly customer support chatbot. Answer the users question "{{ user_question }}" clearly, based on the following documentation: {{ documentation }}'],variables:[{name:"user_question",value:"Is MLflow open source?"},{name:"documentation",value:"MLflow is an open source platform for managing the end-to-end machine learning lifecycle."}]},{prompt:['Summarize the given text "{{ text }}" into a concise and coherent summary, capturing the main ideas and key points. Make sure that the summary does not exceed {{ word_count }} words.'],variables:[{name:"text",value:"Although C. septempunctata larvae and adults mainly eat aphids, they also feed on Thysanoptera, Aleyrodidae, on the larvae of Psyllidae and Cicadellidae, and on eggs and larvae of some beetles and butterflies. There are one or two generations per year. Adults overwinter in ground litter in parks, gardens and forest edges and under tree bark and rocks. C. septempunctata has a broad ecological range, generally living wherever there are aphids for it to eat. This includes, amongst other biotopes, meadows, fields, Pontic\u2013Caspian steppe, parkland, gardens, Western European broadleaf forests and mixed forests. In the United Kingdom, there are fears that the seven-spot ladybird is being outcompeted for food by the harlequin ladybird. An adult seven-spot ladybird may reach a body length of 7.6\u201312.7 mm (0.3\u20130.5 in). Their distinctive spots and conspicuous colours warn of their toxicity, making them unappealing to predators. The species can secrete a fluid from joints in their legs which gives them a foul taste. A threatened ladybird may both play dead and secrete the unappetising substance to protect itself. The seven-spot ladybird synthesizes the toxic alkaloids, N-oxide coccinelline and its free base precoccinelline; depending on sex and diet, the spot size and coloration can provide some indication of how toxic the individual insect is to potential predators."},{name:"word_count",value:"75"}]},{prompt:["Generate a list of ten titles for my book. The book is about {{ topic }}. Each title should be between {{ word_range }} words long.","### Examples of great titles ###","{{ examples }}"],variables:[{name:"topic",value:"my journey as an adventurer who has lived an unconventional life, meeting many different personalities and finally finding peace in gardening."},{name:"word_range",value:"two to five"},{name:"examples",value:'"Long walk to freedom", "Wishful drinking", "I know why the caged bird sings"'}]},{prompt:["Generate a SQL query from a user\u2019s question, using the information from the table.","Question: {{ user_question }}","Table Information: {{ table_information }}"],variables:[{name:"user_question",value:"Which product generated the most sales this month?"},{name:"table_information",value:"CREATE TABLE Sales (SaleID INT PRIMARY KEY, ProductID INT, SaleDate DATE, CustomerID INT, QuantitySold INT, UnitPrice DECIMAL(10, 2));"}]}],{TextArea:kt}=m.I,St=e=>{let{isOpen:t,closeExamples:n,closeModal:r,updatePromptTemplate:s,updateInputVariableValue:i}=e;const{theme:o}=(0,p.u)();return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_42",verticalSizing:"maxed_out",visible:t,onCancel:r,title:(0,y.Y)("div",{children:(0,y.FD)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:o.spacing.sm,marginBottom:o.spacing.xs},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_48",css:(0,a.AH)({marginRight:o.spacing.sm,marginBottom:o.spacing.sm},""),icon:(0,y.Y)(c.A60,{}),onClick:n}),(0,y.Y)(ee.A,{id:"KzC+38",defaultMessage:"Prompt template examples"})]})}),dangerouslySetAntdProps:{width:1200},children:At.map((e=>(0,y.FD)("div",{css:(0,a.AH)({display:"flex",flexDirection:"column",gap:o.spacing.md},""),children:[(0,y.FD)("div",{css:(0,a.AH)({boxSizing:"border-box",border:`1px solid ${o.colors.actionDefaultBorderDefault}`,borderRadius:o.legacyBorders.borderRadiusMd,background:o.colors.backgroundPrimary,padding:o.spacing.md,margin:0,overflow:"hidden",display:"flex",flexDirection:"column",gap:`${o.spacing.xs}px`},""),children:[(0,y.FD)(p.T.Title,{level:4,children:[(0,y.Y)(ee.A,{id:"GDzD7I",defaultMessage:"Prompt Template"}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_90",type:"tertiary",size:"small",style:{float:"right"},onClick:()=>(e=>{s(e.prompt.join("\n")),e.variables.forEach((e=>{let{name:t,value:n}=e;i(t,n)})),n()})(e),children:(0,y.Y)(ee.A,{id:"Np53PR",defaultMessage:"Try this template"})})]}),e.prompt.map((e=>(0,y.Y)(p.T.Paragraph,{children:e},e))),(0,y.Y)("div",{css:(0,a.AH)({marginTop:o.spacing.xs,marginBottom:o.spacing.xs,borderTop:`1px solid ${o.colors.border}`,opacity:.5},"")}),e.variables.map((e=>{let{name:t,value:n}=e;return(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:4,children:t}),(0,y.Y)(p.T.Paragraph,{children:n})]},t)}))]}),(0,y.Y)(u.S,{})]},e.prompt.join("\n"))))})};const{TextArea:Et}=m.I;var Mt={name:"1ll9bqd",styles:"cursor:default"};const Rt=e=>{let{evaluationMetadata:t,isEvaluating:n,isOutputDirty:s,evaluationOutput:i,evaluationError:o,evaluateButtonTooltip:l,disabled:d,onEvaluateClick:u,onCancelClick:m}=e;const{theme:g}=(0,p.u)(),h=(0,r.useMemo)((()=>t?n?null:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:g.spacing.xs,alignItems:"center"},""),children:[ut.Ef in t&&(0,y.FD)(p.T.Hint,{size:"sm",children:[Math.round(Number(t[ut.Ef]))," ms","MLFLOW_total_tokens"in t?",":""]}),ut.jC in t&&(0,y.Y)(p.T.Hint,{size:"sm",children:(0,y.Y)(ee.A,{id:"/nPZbt",defaultMessage:"{totalTokens} total tokens",values:{totalTokens:t[ut.jC]}})})]}):null),[t,n,g]);return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({marginBottom:g.spacing.md},""),children:[(0,y.Y)(c.paO,{title:l,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_85","data-testid":"button-evaluate",icon:(0,y.Y)(c.udU,{}),onClick:u,disabled:d,loading:n,children:(0,y.Y)(ee.A,{id:"72uGpl",defaultMessage:"Evaluate"})})}),n&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_99","data-testid":"button-cancel",icon:(0,y.Y)(c.wFz,{}),onClick:m,css:(0,a.AH)({marginLeft:g.spacing.sm},""),children:(0,y.Y)(ee.A,{id:"CTEh+b",defaultMessage:"Cancel"})})]}),(0,y.FD)(c.D$Q.Label,{children:[(0,y.Y)(ee.A,{id:"ydoHrJ",defaultMessage:"Output"}),s&&(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{id:"gc+GrI",defaultMessage:"Model, input data or prompt have changed since last evaluation of the output"}),children:(0,y.Y)(p.W,{css:(0,a.AH)({marginLeft:g.spacing.xs},"")})})]}),(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(ee.A,{id:"JtQQMn",defaultMessage:"This is the output generated by the LLM using the prompt template and input values defined above."})}),!o&&n&&(0,y.Y)("div",{css:(0,a.AH)({marginTop:g.spacing.sm},""),children:(0,y.Y)(c.QvX,{lines:5})}),!n&&(0,y.Y)(Et,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_144",rows:5,css:Mt,"data-testid":"prompt-output",value:i,readOnly:!0}),!n&&o&&(0,y.Y)(c.D$Q.Message,{message:o,type:"error"}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:g.spacing.sm},""),children:h})]})};var Tt=n(17111);const Dt=e=>{let{routeName:t,routeType:n,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l}=e;return(e=>{let{routeName:t,routeType:n,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l}=e;return async(e,i)=>{const{modelGateway:d}=i();d.modelGatewayRoutesLoading.loading||0!==Object.keys(d.modelGatewayRoutes).length||await e(Ft());const c=i().modelGateway.modelGatewayRoutes[`${n}:${t}`];if(!c){const e=`MLflow deployment endpoint ${t} does not exist anymore!`;throw T.A.logErrorAndNotifyUser(e),new Error(e)}const u={inputText:a,parameters:s};return e({type:"EVALUATE_PROMPT_TABLE_VALUE",payload:dt.queryModelGatewayRoute(c,u),meta:{inputValues:r,run:l,compiledPrompt:a,rowKey:o,startTime:performance.now()}})}})({...{routeName:t,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l},routeType:n})},Ft=()=>async e=>e({type:"SEARCH_MLFLOW_DEPLOYMENTS_MODEL_ROUTES",payload:st.x.gatewayProxyGet({gateway_path:"api/2.0/endpoints/"}),meta:{id:(0,l.yk)()}}),Lt=e=>e instanceof ot?e.getGatewayErrorMessage():e instanceof Ee.s?e.getMessageField():e.message;const{TextArea:Bt}=m.I;var Ht={name:"1voqbl3",styles:"margin-top:0;font-weight:normal"},Pt={name:"t691yx",styles:"display:grid;grid-template-columns:300px 1fr;gap:48px"},Nt={name:"1d3w5wq",styles:"width:100%"},Ot={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};const Ut=e=>{let{isOpen:t,closeModal:n,runBeingDuplicated:o,visibleRuns:l=[],refreshRuns:d}=e;const[g]=(0,$.z)(),{theme:h}=(0,p.u)(),{parameters:f,updateParameter:v}=ht(),[,_]=pe(),[x,w]=(0,r.useState)(""),[b,Y]=(0,r.useState)(""),[C,I]=(0,r.useState)(!1),[A,k]=(0,r.useState)(!1),[S,E]=(0,r.useState)(null),[M,R]=(0,r.useState)(""),[D,F]=(0,r.useState)({}),[L,B]=(0,r.useState)(!1),[H,P]=(0,r.useState)(!1),N=(0,r.useRef)(null),O=(0,s.wA)();(0,r.useEffect)((()=>{O(Ft()).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e)}))}),[O]);const U=(0,Be.A)(),{updateInputVariables:K,inputVariables:V,inputVariableValues:z,updateInputVariableValue:G,inputVariableNameViolations:q,clearInputVariableValues:W}=(()=>{const[e,t]=(0,r.useState)((0,ut.rl)("")),[n,a]=(0,r.useState)({namesWithSpaces:[]}),[s,i]=(0,r.useState)(ut.kU),o=(0,r.useCallback)((()=>i({})),[]),l=(0,r.useMemo)((()=>(0,j.debounce)((e=>{t((t=>{const n=(0,ut.rl)(e);return(0,j.isEqual)(n,t)?t:n})),a((0,ut.Eb)(e))}),250)),[]),d=(0,r.useCallback)(((e,t)=>{i((n=>({...n,[e]:t})))}),[]),c=(0,r.useMemo)((()=>(0,j.fromPairs)(Object.entries(s).filter((t=>{let[n]=t;return e.includes(n)})))),[s,e]);return{updateInputVariables:l,inputVariables:e,inputVariableValues:c,updateInputVariableValue:d,inputVariableNameViolations:n,clearInputVariableValues:o}})(),{handleAddVariableToTemplate:Q,savePromptTemplateInputRef:J,promptTemplate:Z,updatePromptTemplate:X}=Yt();(0,r.useEffect)((()=>{t&&!o&&Y((0,ct.Lr)())}),[t,o]),(0,r.useEffect)((()=>{K(Z)}),[Z,K]),(0,r.useEffect)((()=>{if(o){const{promptTemplate:e,routeName:t,parameters:n}=(0,ut.vC)(o);(0,ut.xP)(o),e&&X(e),n.temperature&&v("temperature",n.temperature),n.max_tokens&&v("max_tokens",n.max_tokens),t&&w(t),R(""),B(!1);const a=(0,ct.gK)(o.runName,(0,j.compact)(l.map((e=>{let{runName:t}=e;return t}))));Y(a),W()}}),[o,W,v,X,l]);const te=(0,s.d4)((e=>{let{modelGateway:t}=e;return t.modelGatewayRoutes})),ne=(0,r.useMemo)((()=>(0,j.sortBy)(Object.values(te),"name")),[te]),ae=(0,s.d4)((e=>{let{modelGateway:t}=e;return t.modelGatewayRoutesLoading.loading}));(0,r.useEffect)((()=>{M&&B(!0)}),[z,Z,f,x]);const re=(0,r.useCallback)((()=>{const e=te[x],t=Math.random().toString(36);if(N.current=t,!e)throw new Error("No model route found!");E(null),k(!0);const n=(0,ut.TG)(Z,z),a=performance.now();dt.queryModelGatewayRoute(e,{inputText:n,parameters:f}).then((e=>{if(N.current===t){const{text:n,metadata:r}=e,s=performance.now()-a;R(n);const i={...r,latency:s},o=Object.entries(i).reduce(((e,t)=>{let[n,a]=t;return{...e,[`MLFLOW_${n}`]:a}}),{});F(o),B(!1),k(!1),N.current===t&&(N.current=null)}})).catch((e=>{const n=Lt(e),a=U.formatMessage({id:"nyHcAz",defaultMessage:'MLflow deployment returned the following error: "{errorMessage}"'},{errorMessage:n});T.A.displayGlobalErrorNotification(a),k(!1),E(a),N.current===t&&(N.current=null)}))}),[z,te,f,Z,x,U]),se=(0,r.useCallback)((()=>{N.current&&(k(!1),N.current=null)}),[k]),ie=U.formatMessage({id:"AyceYA",defaultMessage:"Served LLM model"}),oe=U.formatMessage({id:"pTDM+x",defaultMessage:"Select LLM model endpoint"}),le=Z.trim().length>0,de=(0,r.useMemo)((()=>V.every((e=>{var t;return null===(t=z[e])||void 0===t?void 0:t.trim()}))),[V,z]),ce=b.trim().length>0,ue=x&&le&&de,me=Boolean(x&&le&&de&&M&&!L&&V.length>0&&ce&&!S),ge=(0,r.useMemo)((()=>x?le?de?M?L?U.formatMessage({id:"/K6wBr",defaultMessage:"Input data or prompt template have changed since last evaluation of the output"}):0===V.length?U.formatMessage({id:"z+0t/R",defaultMessage:"You need to define at least one input variable"}):ce?null:U.formatMessage({id:"dYbJha",defaultMessage:"Please provide run name"}):U.formatMessage({id:"gUhc5g",defaultMessage:"You need to evaluate the resulting output first"}):U.formatMessage({id:"flJNTA",defaultMessage:"You need to provide values for all defined inputs"}):U.formatMessage({id:"dHAuWp",defaultMessage:"You need to provide a prompt template"}):U.formatMessage({id:"4HbEVz",defaultMessage:"You need to select a served model endpoint using dropdown first"})),[de,V.length,U,L,M,le,x,ce]),he=(0,r.useMemo)((()=>x?le?de?null:U.formatMessage({id:"flJNTA",defaultMessage:"You need to provide values for all defined inputs"}):U.formatMessage({id:"dHAuWp",defaultMessage:"You need to provide a prompt template"}):U.formatMessage({id:"4HbEVz",defaultMessage:"You need to select a served model endpoint using dropdown first"})),[de,U,le,x]);if(t&&H)return(0,y.Y)(St,{isOpen:t&&H,closeExamples:()=>P(!1),closeModal:n,updatePromptTemplate:X,updateInputVariableValue:G});return(0,y.FD)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_541",verticalSizing:"maxed_out",visible:t,onCancel:n,onOk:n,footer:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:h.spacing.sm,justifyContent:"flex-end"},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_589",onClick:n,children:(0,y.Y)(ee.A,{id:"mqTFL+",defaultMessage:"Cancel"})}),(0,y.Y)(c.paO,{title:ge,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_596",onClick:()=>{var e,t;I(!0);const a=null===(e=te[x])||void 0===e?void 0:e.name,r={...f,route_type:null===(t=te[x])||void 0===t?void 0:t.type},s=(0,ut.TG)(Z,z);O((0,i.$v)({experimentId:g,promptTemplate:Z,modelInput:s,modelParameters:r,modelRouteName:a,promptParameters:z,modelOutput:M,runName:b,modelOutputParameters:D})).then((()=>{d(),n(),I(!1),_("ARTIFACT")})).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e),I(!1)}))},"data-testid":"button-create-run",type:"primary",disabled:!me,children:(0,y.Y)(ee.A,{id:"8Uwpjw",defaultMessage:"Create run"})})})]}),title:(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:h.spacing.sm,marginBottom:h.spacing.xs},""),children:(0,y.Y)(ee.A,{id:"sLhmy7",defaultMessage:"New run"})}),(0,y.Y)(p.T.Hint,{css:Ht,children:"Create a new run using a large-language model by giving it a prompt template and model parameters"})]}),dangerouslySetAntdProps:{width:1200},children:[(0,y.FD)("div",{css:Pt,children:[(0,y.FD)("div",{children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"selected_model",css:(0,a.AH)({marginBottom:h.spacing.sm},""),children:ie}),(0,y.Y)("div",{css:(0,a.AH)({marginBottom:h.spacing.lg,display:"flex",alignItems:"center"},""),children:(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_597",label:ie,modal:!1,value:x?[(fe=x,fe.includes(":")?fe.split(":")[1]:fe)]:void 0,children:[(0,y.Y)(c.gGe,{id:"selected_model",css:Nt,allowClear:!1,placeholder:oe,withInlineLabel:!1}),(0,y.Y)(c.dn6,{loading:ae,maxHeight:400,matchTriggerWidth:!0,children:!ae&&(0,y.Y)(c.HI_,{children:(0,y.Y)(c.dhl,{autoFocus:!0,children:ne.map((e=>(0,y.FD)(c.crD,{value:e.key,onChange:e=>{w(e)},checked:x===e.key,children:[e.name,e.mlflowDeployment&&(0,y.Y)(c.b5C,{children:e.mlflowDeployment.model.name})]},e.key)))})})})]})}),x&&(0,y.Y)(yt,{parameters:f,updateParameter:v}),(0,y.Y)("div",{css:Kt.formItem,children:(0,y.FD)(y.FK,{children:[(0,y.FD)(c.D$Q.Label,{htmlFor:"new_run_name",children:[(0,y.Y)(ee.A,{id:"Zqj4VA",defaultMessage:"New run name"}),!b.trim()&&(0,y.Y)(c.D$Q.Message,{type:"error",message:U.formatMessage({id:"dYbJha",defaultMessage:"Please provide run name"})})]}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_638",id:"new_run_name","data-testid":"run-name-input",required:!0,value:b,onChange:e=>Y(e.target.value)})]})})]}),(0,y.FD)("div",{children:[(0,y.FD)("div",{css:Kt.formItem,children:[(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:Ot,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"prompt_template",children:(0,y.Y)(ee.A,{id:"uBTAtY",defaultMessage:"Prompt Template"})}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_695",onClick:()=>P(!0),style:{marginLeft:"auto"},size:"small",children:(0,y.Y)(ee.A,{id:"13vJft",defaultMessage:"View Examples"})})]}),(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(ee.A,{id:"gC2Kxm",defaultMessage:"Give instructions to the model. Use '{{ }}' or the \"Add new variable\" button to add variables to your prompt."})})]}),(0,y.Y)(Bt,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_678",id:"prompt_template",autoSize:{minRows:3},"data-testid":"prompt-template-input",value:Z,onChange:e=>X(e.target.value),ref:J}),(0,y.Y)(It,{violations:q})]}),V.map((e=>(0,y.Y)("div",{css:Kt.formItem,children:(0,y.FD)(y.FK,{children:[(0,y.Y)(c.D$Q.Label,{htmlFor:e,children:(0,y.Y)("span",{children:e})}),(0,y.Y)(Bt,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_694",id:e,autoSize:!0,value:z[e]?z[e]:"",onChange:t=>G(e,t.target.value)})]})},e))),(0,y.Y)("div",{css:(0,a.AH)({marginBottom:2*h.spacing.md},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_736",icon:(0,y.Y)(c.c11,{}),onClick:Q,children:(0,y.Y)(ee.A,{id:"71hvMu",defaultMessage:"Add new variable"})})}),(0,y.Y)(Rt,{evaluateButtonTooltip:he,evaluationMetadata:D,evaluationOutput:M,disabled:!ue,isEvaluating:A,isOutputDirty:L,onCancelClick:se,onEvaluateClick:re,evaluationError:S})]})]}),C&&(0,y.Y)("div",{css:(0,a.AH)({inset:0,backgroundColor:h.colors.overlayOverlay,position:"absolute",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1},""),children:(0,y.Y)(p.H,{})})]});var fe},Kt={formItem:{marginBottom:16}},Vt=r.createContext({createNewRun:()=>{}}),zt=e=>{let{children:t,visibleRuns:n,refreshRuns:a}=e;const[s,i]=(0,r.useState)(!1),[o,l]=(0,r.useState)(null),d=(0,r.useMemo)((()=>({createNewRun:e=>{i(!0),l(e||null)}})),[]);return(0,y.FD)(Vt.Provider,{value:d,children:[t,(0,le.Ii)()&&(0,y.Y)(Ut,{visibleRuns:n,isOpen:s,closeModal:()=>i(!1),runBeingDuplicated:o,refreshRuns:a})]})},Gt=()=>(0,r.useContext)(Vt);var $t=n(3546),jt=n(83028),qt=n(21879);function Wt(){return(0,le.XK)()}const Qt=r.memo((e=>{let{searchFacetsState:t,experimentId:n,runsData:i,viewState:o,updateViewState:l,onDownloadCsv:d,requestError:m,additionalControls:g,refreshRuns:h,viewMaximized:f,autoRefreshEnabled:v=!1,hideEmptyCharts:_=!1}=e;const x=(0,$t.Px)(),w=Wt(),[b,Y]=pe(),C=(0,qt.e)(),I=(0,$.z)().length>1,{startTime:A,lifecycleFilter:k,datasetsFilter:S,searchFilter:E}=t,M=b,R=(0,Be.A)(),{createNewRun:T}=Gt(),[D,F]=(0,r.useState)(!1),{theme:L}=(0,p.u)(),B=(0,r.useMemo)((()=>(0,_e.XU)(R)),[R]),H=k===oe.gy.ACTIVE?R.formatMessage({id:"0pdAuV",defaultMessage:"Active"}):R.formatMessage({id:"yJpGwW",defaultMessage:"Deleted"}),P=R.formatMessage({id:"+fb25b",defaultMessage:"Time created"}),N=void 0===M||"ARTIFACT"===M,O=(0,s.d4)((e=>e.entities.datasetsByExperimentId[n])),U=void 0!==O;return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,justifyContent:"space-between",[L.responsive.mediaQueries.xs]:{flexDirection:"column"}},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,alignItems:"center",flexWrap:"wrap"},""),children:[w&&"ARTIFACT"!==b&&(0,y.FD)(c.d98,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_184",name:"runs-view-mode",value:b,onChange:e=>{let{target:t}=e;const{value:n}=t;b!==n&&Y(n)},children:[(0,y.Y)(c.EPn,{value:"TABLE",children:(0,y.Y)(u.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_201",content:R.formatMessage({id:"AxyQXa",defaultMessage:"Table view"}),children:(0,y.Y)(u.L,{})})}),(0,y.Y)(c.EPn,{value:"CHART",children:(0,y.Y)(u.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_211",content:R.formatMessage({id:"Q6oN2U",defaultMessage:"Chart view"}),children:(0,y.Y)(c.SCH,{})})})]}),(0,y.Y)(Je,{runsData:i,searchFilter:E,onSearchFilterChange:e=>{x({searchFilter:e})},onClear:()=>{x((0,jt.G)())},requestError:m}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_217",label:P,value:"ALL"!==A?[B[A]]:[],children:[(0,y.Y)(c.gGe,{allowClear:"ALL"!==A,onClear:()=>{x({startTime:"ALL"})},"data-test-id":"start-time-select-dropdown"}),(0,y.Y)(c.dn6,{children:(0,y.Y)(c.HI_,{children:Object.keys(B).map((e=>(0,y.Y)(c.crD,{checked:e===A,title:B[e],"data-test-id":`start-time-select-${e}`,value:e,onChange:()=>{x({startTime:e})},children:B[e]},e)))})})]}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_248",label:R.formatMessage({id:"iLFoPb",defaultMessage:"State"}),value:[H],children:[(0,y.Y)(c.gGe,{allowClear:!1,"data-testid":"lifecycle-filter"}),(0,y.Y)(c.dn6,{children:(0,y.FD)(c.HI_,{children:[(0,y.Y)(c.crD,{checked:k===oe.gy.ACTIVE,"data-testid":"active-runs-menu-item",value:oe.gy.ACTIVE,onChange:()=>{x({lifecycleFilter:oe.gy.ACTIVE})},children:(0,y.Y)(ee.A,{id:"0pdAuV",defaultMessage:"Active"})},oe.gy.ACTIVE),(0,y.Y)(c.crD,{checked:k===oe.gy.DELETED,"data-testid":"deleted-runs-menu-item",value:oe.gy.DELETED,onChange:()=>{x({lifecycleFilter:oe.gy.DELETED})},children:(0,y.Y)(ee.A,{id:"yJpGwW",defaultMessage:"Deleted"})},oe.gy.DELETED)]})})]}),(0,y.Y)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_289",label:R.formatMessage({id:"IZ95jb",defaultMessage:"Datasets"}),value:S.map((e=>e.name)),multiSelect:!0,children:(0,y.FD)(c.paO,{title:!U&&(0,y.Y)(ee.A,{id:"v8UFxB",defaultMessage:"No datasets were recorded for this experiment's runs."}),children:[(0,y.Y)(c.gGe,{allowClear:!0,onClear:()=>x({datasetsFilter:[]}),"data-test-id":"datasets-select-dropdown",showTagAfterValueCount:1,disabled:!U}),U&&(0,y.Y)(c.dn6,{maxHeight:600,children:(0,y.Y)(c.HI_,{children:(0,y.Y)(c.dhl,{children:O.map((e=>(0,y.FD)(c.jTC,{checked:S.some((t=>(0,Ze.R)(t,e))),title:e.name,"data-test-id":`dataset-dropdown-${e.name}`,value:e.name,onChange:()=>(e=>{const t=S.some((t=>(0,Ze.R)(t,e)))?S.filter((t=>!(0,Ze.R)(t,e))):[...S,e];x({datasetsFilter:t})})(e),children:[e.name," (",e.digest,")"," ",e.context&&(0,y.Y)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_329",css:(0,a.AH)({textTransform:"capitalize",marginRight:L.spacing.xs},""),children:e.context})]},e.name+e.digest+e.context)))})})})]})}),g]}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,alignItems:"flex-start"},""),children:[(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_338",icon:(0,y.Y)(c.ssM,{}),"aria-label":R.formatMessage({id:"E5gbt5",defaultMessage:"More options"})})}),(0,y.FD)(c.rId.Content,{children:[(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_362",className:"csv-button",onClick:d,children:[(0,y.Y)(c.rId.IconWrapper,{children:(0,y.Y)(c.s3U,{})}),`Download ${i.runInfos.length} runs`]}),(0,y.Y)(c.rId.Separator,{}),(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_382",checked:_,onClick:()=>C((e=>({...e,hideEmptyCharts:!e.hideEmptyCharts}))),children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)(ee.A,{id:"lZPbxb",defaultMessage:"Hide charts with no data"})]}),(0,le.Hn)()&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Separator,{}),(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_402",checked:v,onClick:()=>C((e=>({...e,autoRefreshEnabled:!e.autoRefreshEnabled}))),children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)(ee.A,{id:"YamyaP",defaultMessage:"Auto-refresh"})]})]})]})]}),(0,y.Y)(tt,{isOpen:D,closeModal:()=>F(!1),experimentId:n}),N&&(0,y.Y)(c.paO,{title:R.formatMessage({id:"eZOxx1",defaultMessage:"Toggle the preview sidepane"}),useAsLabel:!0,children:(0,y.Y)(He.k,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_403",pressed:o.previewPaneVisible,icon:(0,y.Y)(c.CPw,{}),onClick:()=>l({previewPaneVisible:!o.previewPaneVisible})})}),!(0,le.Hn)()&&(0,y.Y)(Oe,{refreshRuns:h}),!I&&(0,y.FD)(c.rId.Root,{children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_415",type:w?void 0:"primary",icon:(0,y.Y)(c.c11,{}),children:(0,y.Y)(ee.A,{id:"PEYesH",defaultMessage:"New run"})})}),(0,y.FD)(c.rId.Content,{children:[(0,le.Ii)()&&(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_461",onSelect:()=>T(),children:[" ",(0,y.Y)(ee.A,{id:"UmwZQv",defaultMessage:"using Prompt Engineering"}),(0,y.Y)(nt.W,{})]}),(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_469",onSelect:()=>F(!0),children:[" ",(0,y.Y)(ee.A,{id:"vNRmQa",defaultMessage:"using Notebook"})]})]})]})]})]})}));var Jt=n(35286);const Zt="GROUP",Xt=(0,_e.GF)(Zt,oe.RO.ATTRIBUTES),en=(0,_e.GF)(Zt,oe.RO.PARAMS),tn=(0,_e.GF)(Zt,oe.RO.METRICS),nn=(0,_e.GF)(Zt,oe.RO.TAGS),an=(e,t)=>e.filter((e=>e.toLowerCase().includes(t.toLowerCase()))),rn=(e,t)=>{if(!t)return e;const n=e.toLowerCase().indexOf(t.toLowerCase()),a=e.substring(0,n),r=e.substring(n,n+t.length),s=e.substring(n+t.length);return n>-1?(0,y.FD)("span",{children:[a,(0,y.Y)("strong",{children:r}),s]}):e},sn=r.memo((e=>{let{runsData:t,columnSelectorVisible:n,onChangeColumnSelectorVisible:s,selectedColumns:i}=e;const o=(0,qt.e)(),l=(0,$.z)(),[d,u]=(0,r.useState)(""),{theme:g}=(0,p.u)(),h=(0,r.useRef)(null),f=(0,r.useRef)(null),v=(0,r.useRef)(null),_=(0,r.useMemo)((()=>(e=>{const t=[oe.qo.USER,oe.qo.SOURCE,oe.qo.VERSION,oe.qo.MODELS,oe.qo.DESCRIPTION];return e&&t.unshift(oe.qo.EXPERIMENT_NAME),t.unshift(oe.qo.DATASET),t})(l.length>1)),[l.length]),x=(0,r.useCallback)((e=>o((t=>{const n=e(t.selectedColumns),a=Array.from(new Set(n));return{...t,selectedColumns:a}}))),[o]),w=(0,r.useMemo)((()=>T.A.getVisibleTagKeyList(t.tagsList)),[t]),b=(0,r.useMemo)((()=>({[oe.RO.ATTRIBUTES]:_.map((e=>(0,_e.GF)(oe.RO.ATTRIBUTES,e))),[oe.RO.PARAMS]:t.paramKeyList.map((e=>(0,_e.GF)(oe.RO.PARAMS,e))),[oe.RO.METRICS]:t.metricKeyList.map((e=>(0,_e.GF)(oe.RO.METRICS,e))),[oe.RO.TAGS]:w.map((e=>(0,_e.GF)(oe.RO.TAGS,e)))})),[t,_,w]),Y=(0,r.useMemo)((()=>{const e=[],n=an(_,d),a=an(t.paramKeyList,d),r=an(t.metricKeyList,d),s=an(w,d);return n.length&&e.push({key:Xt,title:"Attributes",children:n.map((e=>({key:(0,_e.GF)(oe.RO.ATTRIBUTES,e),title:rn(e,d)})))}),r.length&&e.push({key:tn,title:`Metrics (${r.length})`,children:r.map((e=>{var t;const n=Jt.g[e];return{key:(0,_e.GF)(oe.RO.METRICS,e),title:rn(null!==(t=null===n||void 0===n?void 0:n.displayName)&&void 0!==t?t:e,d)}}))}),a.length&&e.push({key:en,title:`Parameters (${a.length})`,children:a.map((e=>({key:(0,_e.GF)(oe.RO.PARAMS,e),title:rn(e,d)})))}),s.length&&e.push({key:nn,title:`Tags (${s.length})`,children:s.map((e=>({key:(0,_e.GF)(oe.RO.TAGS,e),title:e})))}),e}),[_,d,t,w]),C=(0,r.useCallback)(((e,t)=>{x(e?e=>e.filter((e=>!t.includes(e))):e=>[...e,...t])}),[x]),I=(0,r.useCallback)(((e,t)=>{x(t?t=>t.filter((t=>t!==e)):t=>[...t,e])}),[x]);(0,r.useEffect)((()=>{n&&(u(""),requestAnimationFrame((()=>{var e,t;null===f||void 0===f||null===(e=f.current)||void 0===e||e.scrollTo(0,0),null===(t=h.current)||void 0===t||t.focus({preventScroll:!0}),v.current&&v.current.scrollIntoView({block:"nearest",behavior:"smooth"})})))}),[n]);const A=(0,r.useCallback)(((e,t)=>{let{node:{key:n,checked:a}}=t;if((0,_e.tG)(n.toString(),Zt)){const e=(0,_e.dz)(n.toString(),Zt),t=b[e];t&&C(a,an(t,d))}else I(n.toString(),a)}),[b,C,I,d]),k=(0,r.useCallback)((e=>{if("ArrowDown"===e.key){const e=(null===(t=f.current)||void 0===t?void 0:t.querySelector('[role="tree"] input'))||null;e&&e.focus()}var t}),[]),S=(0,y.FD)("div",{css:(0,a.AH)({backgroundColor:g.colors.backgroundPrimary,width:400,border:"1px solid",borderColor:g.colors.border,[g.responsive.mediaQueries.xs]:{width:"100vw"}},""),onKeyDown:e=>{var t;"Escape"===e.key&&(s(!1),null===(t=v.current)||void 0===t||t.focus())},children:[(0,y.Y)("div",{css:e=>({padding:e.spacing.md}),children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscolumnselector.tsx_300",value:d,prefix:(0,y.Y)(m.S,{}),placeholder:"Search columns",allowClear:!0,ref:h,onChange:e=>{u(e.target.value)},onKeyDown:k})}),(0,y.Y)("div",{ref:f,css:(0,a.AH)({maxHeight:480,overflowY:"scroll",overflowX:"hidden",paddingBottom:g.spacing.md,"span[title]":{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden"},[g.responsive.mediaQueries.xs]:{maxHeight:"calc(100vh - 100px)"}},""),children:(0,y.Y)(c.PH6,{"data-testid":"column-selector-tree",mode:"checkable",dangerouslySetAntdProps:{checkedKeys:i,onCheck:A},defaultExpandedKeys:[Xt,en,tn,nn],treeData:Y})})]});return(0,y.Y)(c.msM,{overlay:S,placement:"bottomLeft",trigger:["click"],visible:n,onVisibleChange:s,children:(0,y.FD)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscolumnselector.tsx_315",ref:v,style:{display:"flex",alignItems:"center"},"data-testid":"column-selection-dropdown",icon:(0,y.Y)(c.jng,{}),children:[(0,y.Y)(ee.A,{id:"ny+fBZ",defaultMessage:"Columns"})," ",(0,y.Y)(c.D3D,{})]})})})),on=e=>{let{type:t,expirationDate:n,className:r}=e;const{theme:s}=(0,p.u)();return(0,y.Y)(c.vwO,{componentId:"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14",className:r,css:(0,a.AH)({marginLeft:s.spacing.xs},""),color:"turquoise",children:(0,y.Y)(ee.A,{id:"8qJt7/",defaultMessage:"Experimental"})})};const ln="seenBefore";var dn={name:"b98nv4",styles:"position:absolute;inset:0"},cn={name:"1ww443i",styles:"max-width:200px"},un={name:"2qga7i",styles:"text-align:right"};const pn=e=>{let{multipleRunsSelected:t,isTableMode:n}=e;const a=Ke("compareRunsTooltip"),[s,i]=(0,r.useState)(t&&!a.getItem(ln));(0,r.useEffect)((()=>{const e=a.getItem(ln);i(!(!t||!n||e))}),[t,n,a]);const o=(0,r.useCallback)((e=>{i(e),a.setItem(ln,!0)}),[i,a]);return(0,y.Y)(y.FK,{children:(0,y.FD)(p.ax.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsmodeswitch.tsx_60",open:s,children:[(0,y.Y)(p.ax.Trigger,{asChild:!0,children:(0,y.Y)("div",{css:dn})}),(0,y.FD)(p.ax.Content,{align:"start",children:[(0,y.FD)("div",{css:cn,children:[(0,y.Y)(p.T.Paragraph,{children:(0,y.Y)(ee.A,{id:"1ECve7",defaultMessage:"You can now switch to the chart view to compare runs"})}),(0,y.Y)("div",{css:un,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsmodeswitch.tsx_65",onClick:()=>o(!1),type:"primary",children:(0,y.Y)(ee.A,{id:"khknxG",defaultMessage:"Got it"})})})]}),(0,y.Y)(p.ax.Arrow,{})]})]})})},mn=e=>{let{viewState:t,runsAreGrouped:n,hideBorder:a=!0}=e;const[,r]=(0,$t.sR)(),[s,i]=pe(),{classNamePrefix:o,theme:l}=(0,p.u)(),d=s||ce(),u=Wt(),m=u&&["TABLE","CHART"].includes(d)?"RUNS":d,g=1===r.length?r[0]:void 0;return(0,y.FD)(c.Y6f,{dangerouslyAppendEmotionCSS:{[`.${o}-tabs-nav`]:{marginBottom:0,"::before":{display:a?"none":"block"}}},activeKey:m,onChange:e=>{const t=e;if(m!==t)return"RUNS"===t?i("TABLE"):void i(t,g)},children:[g&&(0,le.Dz)()&&(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.FD)("span",{"data-testid":"experiment-runs-mode-switch-models",children:[(0,y.Y)(ee.A,{id:"b8rdDM",defaultMessage:"Models"}),(0,y.Y)(nt.W,{})]})},"MODELS"),u?(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-combined",children:(0,y.Y)(ee.A,{id:"qpFaad",defaultMessage:"Runs"})})},"RUNS"):(0,y.FD)(y.FK,{children:[(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-list",children:(0,y.Y)(ee.A,{id:"YOz/Dk",defaultMessage:"Table"})})},"TABLE"),(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.FD)(y.FK,{children:[(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-compare",children:(0,y.Y)(ee.A,{id:"KOQzJx",defaultMessage:"Chart"})}),(0,y.Y)(pn,{isTableMode:"TABLE"===s,multipleRunsSelected:!!t&&Object.keys(t.runsSelected).length>1})]})},"CHART")]}),(0,y.Y)(c.Y6f.TabPane,{disabled:n,tab:(0,y.Y)(c.paO,{title:n?(0,y.Y)(ee.A,{id:"YOp3/x",defaultMessage:"Unavailable when runs are grouped"}):void 0,children:(0,y.FD)("span",{"data-testid":"experiment-runs-mode-switch-evaluation",children:[(0,y.Y)(ee.A,{id:"SCqKp8",defaultMessage:"Evaluation"}),(0,y.Y)(nt.W,{})]})})},"ARTIFACT"),g&&(0,le.$Y)()&&(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.FD)("span",{"data-testid":"experiment-runs-mode-evaluation-results",children:[(0,y.Y)(ee.A,{id:"HV1iaK",defaultMessage:"Monitoring"}),(0,y.Y)(on,{type:"beta",expirationDate:oe.$u})]})},"EVAL_RESULTS"),(0,le.XK)()&&(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-traces",children:(0,y.Y)(ee.A,{id:"AGQOPV",defaultMessage:"Traces"})})},"TRACES")]})};var gn=n(62862),hn=n(12772);const fn=(0,pt.YK)({minimum:{id:"iT8ODo",defaultMessage:"Minimum"},maximum:{id:"au61Yy",defaultMessage:"Maximum"},average:{id:"/LfvdB",defaultMessage:"Average"},attributes:{id:"cy9EfG",defaultMessage:"Attributes"},tags:{id:"LEiN8m",defaultMessage:"Tags"},params:{id:"i9OADf",defaultMessage:"Params"},dataset:{id:"RtKhwd",defaultMessage:"Dataset"},noParams:{id:"Q73eXs",defaultMessage:"No params"},noTags:{id:"jHWRLw",defaultMessage:"No tags"},aggregationTooltip:{id:"fKx4kG",defaultMessage:"Aggregation: {value}"},noResults:{id:"aaVp/T",defaultMessage:"No results"}});var vn={name:"9q39xd",styles:"min-width:32px"},_n={name:"o5v4ro",styles:"max-height:400px;overflow-y:scroll"};const xn=e=>{let{runsData:t,onChange:n,groupBy:s,useGroupedValuesInCharts:i,onUseGroupedValuesInChartsChange:o}=e;const l=(0,Be.A)(),d=(0,r.useRef)(null),u=(0,r.useRef)(null),g=(0,r.useRef)(null),h=(0,r.useRef)(null),f=l.formatMessage(fn.minimum),v=l.formatMessage(fn.maximum),_=l.formatMessage(fn.average),x=l.formatMessage(fn.dataset),w=(0,r.useMemo)((()=>(0,j.uniq)((0,j.values)(t.tagsList).flatMap((e=>(0,j.keys)(e).filter((e=>!e.startsWith(Ie.nt))))))),[t.tagsList]),{aggregateFunction:b=hn.it.Average,groupByKeys:Y=[]}=s||{},C={min:f,max:v,average:_}[b],{theme:I}=(0,p.u)(),[A,k]=(0,r.useState)("");(0,r.useEffect)((()=>{requestAnimationFrame((()=>{h.current.focus()}))}),[]);const S=w.filter((e=>e.toLowerCase().includes(A.toLowerCase()))),E=t.paramKeyList.filter((e=>e.toLowerCase().includes(A.toLowerCase()))),M=(0,r.useMemo)((()=>!(0,j.isEmpty)((0,j.compact)(t.datasetsList))),[t.datasetsList])&&x.toLowerCase().includes(A.toLowerCase()),R=S.length>0||E.length>0||M,T=(0,r.useCallback)(((e,t,a)=>{if(a){const a=[...Y];a.some((n=>n.mode===e&&n.groupByData===t))||a.push({mode:e,groupByData:t}),n({aggregateFunction:b,groupByKeys:a})}else{const a=Y.filter((n=>!(n.mode===e&&n.groupByData===t)));if(!a.length)return void n(null);n({aggregateFunction:b,groupByKeys:a})}}),[b,Y,n]),D=(e,t)=>Y.some((n=>n.mode===e&&n.groupByData===t));return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:I.spacing.xs,padding:I.spacing.sm},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_191",value:A,onChange:e=>k(e.target.value),prefix:(0,y.Y)(m.S,{}),placeholder:"Search",autoFocus:!0,ref:h,onKeyDown:e=>{if("ArrowDown"!==e.key&&"Tab"!==e.key)"Escape"!==e.key&&e.stopPropagation();else{const e=d.current||u.current||g.current;null===e||void 0===e||e.focus()}}}),(0,y.FD)(c.rId.Root,{children:[(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(ee.A,{...fn.aggregationTooltip,values:{value:C||b}}),children:(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_168",icon:(0,y.Y)(c.L64,{}),css:vn,"aria-label":"Change aggregation function"})})}),(0,y.FD)(c.rId.Content,{align:"start",side:"right",children:[(0,le.Bh)()&&(0,y.FD)(y.FK,{children:[(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_233",disabled:!Y.length,checked:i,onCheckedChange:o,children:[(0,y.Y)(c.rId.ItemIndicator,{}),"Use grouping from the runs table in charts"]}),(0,y.Y)(c.rId.Separator,{})]}),(0,y.FD)(c.rId.RadioGroup,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_244",value:b,onValueChange:e=>{if((0,j.values)(hn.it).includes(e)){const t=e,a={...s,aggregateFunction:t};n(a)}},children:[(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:hn.it.Min,children:[(0,y.Y)(c.rId.ItemIndicator,{}),f]},hn.it.Min),(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:hn.it.Max,children:[(0,y.Y)(c.rId.ItemIndicator,{}),v]},hn.it.Max),(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:hn.it.Average,children:[(0,y.Y)(c.rId.ItemIndicator,{}),_]},hn.it.Average)]})]})]})]}),(0,y.FD)(c.rId.Group,{css:_n,children:[M&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(ee.A,{...fn.attributes})}),x.toLowerCase().includes(A.toLowerCase())&&(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_280",checked:D(hn.uq.Dataset,"dataset"),ref:d,onCheckedChange:e=>T(hn.uq.Dataset,"dataset",e),children:[(0,y.Y)(c.rId.ItemIndicator,{}),x]},(0,gn._p)(hn.uq.Dataset,"dataset",b)),(0,y.Y)(c.rId.Separator,{})]}),S.length>0&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(ee.A,{...fn.tags})}),S.map(((e,t)=>{const n=(0,gn._p)(hn.uq.Tag,e,b);return(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_302",checked:D(hn.uq.Tag,e),ref:0===t?u:void 0,onCheckedChange:t=>T(hn.uq.Tag,e,t),children:[(0,y.Y)(c.rId.ItemIndicator,{}),e]},n)})),!w.length&&(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_314",disabled:!0,children:[(0,y.Y)(c.rId.ItemIndicator,{})," ",(0,y.Y)(ee.A,{...fn.noTags})]}),(0,y.Y)(c.rId.Separator,{})]}),E.length>0&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(ee.A,{...fn.params})}),E.map(((e,t)=>{const n=(0,gn._p)(hn.uq.Param,e,b);return(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_330",checked:D(hn.uq.Param,e),ref:0===t?g:void 0,onCheckedChange:t=>T(hn.uq.Param,e,t),children:[(0,y.Y)(c.rId.ItemIndicator,{}),e]},n)})),!t.paramKeyList.length&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_342",disabled:!0,children:(0,y.Y)(ee.A,{...fn.noParams})})]}),!R&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_349",disabled:!0,children:(0,y.Y)(ee.A,{...fn.noResults})})]})]})};var yn={name:"taqmwb",styles:"margin-left:4px;margin-right:0"};const wn=r.memo((e=>{let{runsData:t,groupBy:n,isLoading:r,onChange:s,useGroupedValuesInCharts:i,onUseGroupedValuesInChartsChange:o}=e;const{theme:l}=(0,p.u)(),d=(0,gn.Zp)(n)||{aggregateFunction:hn.it.Average,groupByKeys:[]},u=d&&!(0,j.isEmpty)(d.groupByKeys);return(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.FD)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_306",icon:(0,y.Y)(c.aTS,{}),style:{display:"flex",alignItems:"center"},"data-testid":"column-selection-dropdown",endIcon:(0,y.Y)(c.D3D,{}),children:[u?(0,y.Y)(ee.A,{id:"AFI74W",defaultMessage:"Group by: {value}",values:{value:d.groupByKeys[0].groupByData}}):(0,y.Y)(ee.A,{id:"fjM/KK",defaultMessage:"Group by"}),d.groupByKeys.length>1&&(0,y.FD)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_426",css:yn,children:["+",d.groupByKeys.length-1]}),n&&(0,y.Y)(c.htq,{"aria-hidden":"false",css:(0,a.AH)({color:l.colors.textPlaceholder,fontSize:l.typography.fontSizeSm,marginLeft:l.spacing.sm,":hover":{color:l.colors.actionTertiaryTextHover}},""),role:"button",onClick:()=>{s(null)},onPointerDownCapture:e=>{e.stopPropagation()}})]})}),(0,y.Y)(c.rId.Content,{children:r?(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_436",children:(0,y.Y)(p.H,{})}):(0,y.Y)(xn,{groupBy:d,onChange:s,runsData:t,onUseGroupedValuesInChartsChange:o,useGroupedValuesInCharts:i})})]})}));var bn={name:"1kxd8xu",styles:"max-height:400px;overflow-y:auto"},Yn={name:"1j5vobt",styles:"display:flex;align-items:center;gap:4px"};const Cn=e=>{let{sortOptions:t,orderByKey:n,orderByAsc:s,onOptionSelected:i}=e;const{theme:o}=(0,p.u)(),l=(0,$t.Px)(),d=(0,qt.e)(),u=(0,r.useRef)(null),[g,h]=(0,r.useState)(""),f=(0,r.useRef)(null),v=(0,r.useMemo)((()=>t.filter((e=>e.label.toLowerCase().includes(g.toLowerCase())))),[t,g]),_=e=>{l({orderByAsc:e}),i()};return(0,r.useEffect)((()=>{requestAnimationFrame((()=>{var e;null===(e=u.current)||void 0===e||e.focus()}))}),[]),(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({padding:`${o.spacing.sm}px ${o.spacing.lg/2}px ${o.spacing.sm}px`,width:"100%",display:"flex",gap:o.spacing.xs},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_97",prefix:(0,y.Y)(m.S,{}),value:g,type:"search",onChange:e=>h(e.target.value),placeholder:"Search",autoFocus:!0,ref:u,onKeyDown:e=>{var t;"ArrowDown"!==e.key&&"Tab"!==e.key?e.stopPropagation():null===(t=f.current)||void 0===t||t.focus()}}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:o.spacing.xs},""),children:[(0,y.Y)(He.k,{pressed:!s,icon:(0,y.Y)(c.ZLN,{}),componentId:"mlflow.experiment_page.sort_select_v2.sort_desc",onClick:()=>_(!1),"aria-label":"Sort descending","data-testid":"sort-select-desc"}),(0,y.Y)(He.k,{pressed:s,icon:(0,y.Y)(c.Kpk,{}),componentId:"mlflow.experiment_page.sort_select_v2.sort_asc",onClick:()=>_(!0),"aria-label":"Sort ascending","data-testid":"sort-select-asc"})]})]}),(0,y.FD)(c.rId.Group,{css:bn,children:[v.map(((e,t)=>(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_137",onClick:()=>(e=>{l({orderByKey:e}),d((t=>t.selectedColumns.includes(e)?t:{...t,selectedColumns:[...t.selectedColumns,e]})),i()})(e.value),checked:e.value===n,"data-test-id":`sort-select-${e.label}`,ref:0===t?f:void 0,children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)("span",{css:Yn,children:(0,ie.fx)(e.label,50)})]},e.value))),!v.length&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_151",disabled:!0,children:(0,y.Y)(ee.A,{id:"EkUD0b",defaultMessage:"No results"})})]})]})};var In={name:"9q39xd",styles:"min-width:32px"};const An=r.memo((e=>{let{metricKeys:t,paramKeys:n,orderByAsc:a,orderByKey:s}=e;const i=(0,Be.A)(),[o,l]=(0,r.useState)(!1),{theme:d}=(0,p.u)(),u=(0,r.useMemo)((()=>Object.keys(oe.Eg).map((e=>({label:oe.Eg[e],value:oe.T8[e]})))),[]),m=(0,r.useMemo)((()=>t.map((e=>{var t,n;const a=(0,_e.GF)(oe.RO.METRICS,e);return{label:null!==(t=null===(n=Jt.g[e])||void 0===n?void 0:n.displayName)&&void 0!==t?t:e,value:a}}))),[t]),g=(0,r.useMemo)((()=>n.map((e=>({label:e,value:`${(0,_e.GF)(oe.RO.PARAMS,e)}`})))),[n]),h=(0,r.useMemo)((()=>[...u,...m,...g]),[u,m,g]),f=(0,r.useMemo)((()=>{const e=h.find((e=>e.value===s));let t=null===e||void 0===e?void 0:e.label;if(!t){const e=s.match(/^.+\.`(.+)`$/);e&&(t=e[1])}return`${i.formatMessage({id:"sKaamx",defaultMessage:"Sort"})}: ${t}`}),[h,i,s]);return(0,y.FD)(c.rId.Root,{open:o,onOpenChange:l,modal:!1,children:[(0,y.Y)(c.rId.Trigger,{"data-test-id":"sort-select-dropdown",asChild:!0,children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_page.sort_select_v2.toggle",icon:a?(0,y.Y)(c.GCP,{}):(0,y.Y)(c.MMv,{}),css:In,"aria-label":f,endIcon:(0,y.Y)(c.D3D,{}),children:f})}),(0,y.Y)(c.rId.Content,{minWidth:250,children:(0,y.Y)(Cn,{sortOptions:h,orderByKey:s,orderByAsc:a,onOptionSelected:()=>l(!1)})})]})})),kn=r.memo((e=>{var t;let{runsData:n,viewState:s,updateViewState:i,searchFacetsState:o,experimentId:l,requestError:d,expandRows:u,updateExpandRows:m,refreshRuns:g,uiState:h,isLoading:f}=e;const[v,_]=pe(),x=Wt(),{paramKeyList:w,metricKeyList:b,tagsList:Y}=n,{orderByAsc:C,orderByKey:I}=o,A=(0,qt.e)(),k="TABLE"!==v,S="ARTIFACT"===v,{theme:E}=(0,p.u)(),M=w,R=b,D=T.A.getVisibleTagKeyList(Y),F=(0,r.useCallback)((()=>(0,_e.D1)(n,D,M,R)),[R,M,D,n]),L=(((e,t)=>{(0,r.useMemo)((()=>{let n=[];const a=[oe.mh,oe.KU];return n=[...Object.keys(oe.Eg).reduce(((e,t)=>{const n=oe.Eg[t];return a.forEach((a=>{e.push({label:n,value:oe.T8[t]+oe.KE+a,order:a})})),e}),[]),...e.reduce(((e,t)=>(a.forEach((n=>{e.push({label:t,value:`${(0,_e.GF)(oe.RO.METRICS,t)}${oe.KE}${n}`,order:n})})),e)),[]),...t.reduce(((e,t)=>(a.forEach((n=>{e.push({label:t,value:`${(0,_e.GF)(oe.RO.PARAMS,t)}${oe.KE}${n}`,order:n})})),e)),[])],n}),[e,t])})(R,M),Object.values(s.runsSelected).filter(Boolean).length),B=L>1||1===L||L>0,H=!S,P=(0,r.useCallback)((e=>i({columnSelectorVisible:e})),[i]),N=(0,r.useCallback)((()=>m(!u)),[u,m]),O=(0,r.useMemo)((()=>n.datasetsList.some((e=>(null===e||void 0===e?void 0:e.length)>1))),[n]);return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:E.spacing.sm,flexDirection:"column",marginTop:h.viewMaximized?void 0:E.spacing.sm,marginBottom:x?E.spacing.sm:0},""),children:[x&&(0,y.Y)(mn,{hideBorder:!1,viewState:s,runsAreGrouped:Boolean(h.groupBy)}),B&&(0,y.Y)(Fe,{runsData:n,searchFacetsState:o,viewState:s,refreshRuns:g}),!B&&(0,y.Y)(Qt,{onDownloadCsv:F,searchFacetsState:o,experimentId:l,viewState:s,updateViewState:i,runsData:n,requestError:d,refreshRuns:g,viewMaximized:h.viewMaximized,autoRefreshEnabled:h.autoRefreshEnabled,hideEmptyCharts:h.hideEmptyCharts,additionalControls:(0,y.FD)(y.FK,{children:[(0,y.Y)(An,{orderByAsc:C,orderByKey:I,metricKeys:R,paramKeys:M}),!k&&(0,y.Y)(sn,{columnSelectorVisible:s.columnSelectorVisible,onChangeColumnSelectorVisible:P,runsData:n,selectedColumns:h.selectedColumns}),!k&&O&&(0,y.Y)(c.ffE,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrols.tsx_175",onClick:N,children:(0,y.Y)(ee.A,{id:"mRc7MY",defaultMessage:"Expand rows"})}),H&&(0,y.Y)(wn,{groupBy:h.groupBy,onChange:e=>{A((t=>({...t,groupBy:e})))},runsData:n,isLoading:f,useGroupedValuesInCharts:null===(t=h.useGroupedValuesInCharts)||void 0===t||t,onUseGroupedValuesInChartsChange:e=>{A((t=>({...t,useGroupedValuesInCharts:e})))}})]})}),!x&&(0,y.Y)(mn,{viewState:s,runsAreGrouped:Boolean(h.groupBy)})]})}));var Sn=n(28940);class En{constructor(){this.runsSelected={},this.hiddenChildRunsSelected={},this.columnSelectorVisible=!1,this.previewPaneVisible=!1,this.artifactViewState={selectedTables:[],groupByCols:[],outputColumn:"",intersectingOnly:!1}}}var Mn=n(57368);const Rn="FETCHED_RUN_NOTIFICATION_KEY",Tn=e=>{const{formatMessage:t}=(0,Be.A)(),n=(0,r.useCallback)(((e,n)=>e===n?t({id:"KJbYrw",defaultMessage:"Loaded {childRuns} child {childRuns, plural, =1 {run} other {runs}}"},{childRuns:n}):t({id:"ziIhFQ",defaultMessage:"Loaded {allRuns} {allRuns, plural, =1 {run} other {runs}}, including {childRuns} child {childRuns, plural, =1 {run} other {runs}}"},{allRuns:e,childRuns:n})),[t]);return(0,r.useCallback)(((t,a)=>{if(Array.isArray(t)){const{allRuns:r,childRuns:s}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const n=e.filter((e=>!t.some((t=>t.runUuid===e.info.runUuid)))),a=n.filter((e=>{var t;const n=null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.tags;return Array.isArray(n)&&n.some((e=>e.key===_e.Ol&&Boolean(e.value)))}));return{allRuns:n.length,childRuns:a.length}}(t,a);if(s<1)return;e.close(Rn),e.info({message:n(r,s),duration:3,placement:"bottomRight",key:Rn})}}),[e,n])};var Dn=n(85343),Fn=n(51293);const Ln=r.createContext({getMissingParams:()=>[],pendingDataLoading:{},getEvaluableRowCount:()=>0,evaluateCell:()=>{},evaluateAllClick:()=>{},runColumnsBeingEvaluated:[],canEvaluateInRunColumn:()=>!1,toggleExpandedHeader:()=>{},isHeaderExpanded:!1}),Bn=e=>{let{tableData:t,outputColumn:n,children:a}=e;const i=(0,Be.A)(),[o,l]=(0,r.useState)(!1),d=(0,r.useCallback)((()=>l((e=>!e))),[]),c=(0,r.useCallback)(((e,n)=>{if(!(0,ut.o1)(e))return null;const a=t.find((e=>e.key===n));if(!a)return null;const{promptTemplate:r}=(0,ut.vC)(e);if(!r)return null;return(0,ut.rl)(r).filter((e=>!a.groupByCellValues[e]))}),[t]),u=(0,s.wA)(),{startEvaluatingRunColumn:p,stopEvaluatingRunColumn:m,runColumnsBeingEvaluated:g}=((e,t)=>{const n=(0,r.useRef)(e),a=(0,r.useRef)([]),i=(0,Be.A)();(0,r.useEffect)((()=>{n.current=e}),[e]);const[o,l]=(0,r.useState)([]);(0,r.useEffect)((()=>{a.current=o}),[o]);const d=(0,s.wA)(),c=(0,r.useCallback)((e=>{const r=n.current,{parameters:s,promptTemplate:o,routeName:u,routeType:p}=(0,ut.vC)(e);if(!o)return;const m=(0,ut.rl)(o),g=r.find((t=>!t.cellValues[e.runUuid]&&((e,t)=>0===t.filter((t=>!e.groupByCellValues[t])).length)(t,m)));if(!g)return void l((t=>t.filter((t=>t!==e.runUuid))));const h=g.key,f=g.groupByCellValues;if(!o)return;const v=(0,ut.TG)(o,f);u&&d(Dt({routeName:u,routeType:p,compiledPrompt:v,inputValues:f,outputColumn:t,rowKey:h,parameters:s,run:e})).then((()=>{a.current.includes(e.runUuid)&&c(e)})).catch((t=>{const n=Lt(t),a=i.formatMessage({id:"jI22Qu",defaultMessage:'Gateway returned the following error: "{errorMessage}"'},{errorMessage:n});T.A.logErrorAndNotifyUser(a),l((t=>t.filter((t=>t!==e.runUuid))))}))}),[d,t,i]),u=(0,r.useCallback)((e=>{l((t=>[...t,e.runUuid])),c(e)}),[c]),p=(0,r.useCallback)((e=>{l((t=>t.filter((t=>t!==e.runUuid))))}),[]);return{runColumnsBeingEvaluated:o,startEvaluatingRunColumn:u,stopEvaluatingRunColumn:p}})(t,n),h=(0,s.d4)((e=>{let{evaluationData:t}=e;return t.evaluationPendingDataLoadingByRunUuid})),f=(0,r.useCallback)((e=>n===ut.hR&&(0,ut.o1)(e)),[n]),v=(0,r.useCallback)((e=>t.filter((t=>{if(t.cellValues[e.runUuid])return!1;const n=c(e,t.key);return 0===(null===n||void 0===n?void 0:n.length)})).length),[t,c]),_=(0,r.useCallback)((e=>{g.includes(e.runUuid)?m(e):p(e)}),[g,p,m]),x=(0,r.useCallback)(((e,a)=>{const r=t.find((e=>{let{key:t}=e;return t===a}));if(!r)return;const s=r.groupByCellValues,{parameters:o,promptTemplate:l,routeName:d,routeType:c}=(0,ut.vC)(e);if(!l)return;const p=(0,ut.TG)(l,s);if(d){u((()=>Dt({routeName:d,routeType:c,compiledPrompt:p,inputValues:s,outputColumn:n,rowKey:a,parameters:o,run:e}))()).catch((e=>{const t=Lt(e),n=i.formatMessage({id:"nyHcAz",defaultMessage:'MLflow deployment returned the following error: "{errorMessage}"'},{errorMessage:t});T.A.logErrorAndNotifyUser(n)}))}}),[t,u,n,i]),w=(0,r.useMemo)((()=>({getMissingParams:c,getEvaluableRowCount:v,evaluateCell:x,evaluateAllClick:_,pendingDataLoading:h,canEvaluateInRunColumn:f,runColumnsBeingEvaluated:g,isHeaderExpanded:o,toggleExpandedHeader:d})),[c,v,_,x,h,f,g,o,d]);return(0,y.Y)(Ln.Provider,{value:w,children:a})},Hn=()=>(0,r.useContext)(Ln);var Pn={name:"1h52dri",styles:"overflow:hidden;text-overflow:ellipsis;white-space:nowrap"},Nn={name:"lyel5l",styles:"font-size:0"},On={name:"1hetg88",styles:"max-width:300px"};const Un=e=>{let{run:t}=e;const{theme:n}=(0,p.u)(),{isHeaderExpanded:r}=Hn(),i=(0,ut.vC)(t),o=(0,s.d4)((e=>{let{modelGateway:t}=e;const n=`${i.routeType}:${i.routeName}`;return i.routeName?t.modelGatewayRoutes[n]:null}));if(!(0,ut.o1)(t)||!i)return null;const{parameters:l,promptTemplate:d,routeName:c}=i,{stop:u=[]}=l;return(0,y.FD)("div",{css:(0,a.AH)({marginTop:n.spacing.xs,flex:1,display:"flex",flexDirection:"column",gap:n.spacing.sm,overflowX:"hidden",width:"100%"},""),children:[o&&"mlflowDeployment"in o&&o.mlflowDeployment&&(0,y.Y)(p.T.Hint,{children:o.mlflowDeployment.name}),r&&(0,y.FD)(y.FK,{children:[(0,y.Y)(p.T.Hint,{children:(0,y.Y)(ee.A,{id:"HGBit9",defaultMessage:"Temperature: {temperature}",values:l})}),(0,y.Y)(p.T.Hint,{children:(0,y.Y)(ee.A,{id:"6K04VZ",defaultMessage:"Max. tokens: {max_tokens}",values:l})}),u.length?(0,y.Y)(p.T.Hint,{css:Pn,children:(0,y.Y)(ee.A,{id:"iC2Owx",defaultMessage:"Stop sequences: {stopSequences}",values:{stopSequences:null===u||void 0===u?void 0:u.join(", ")}})}):null,(0,y.Y)("div",{css:Nn,children:(0,y.FD)(p.ax.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadermodelindicator.tsx_107",children:[(0,y.Y)(p.ax.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadermodelindicator.tsx_115",type:"link",size:"small",css:(0,a.AH)({fontSize:n.typography.fontSizeSm},""),children:(0,y.Y)(ee.A,{id:"XX8+x1",defaultMessage:"View prompt template"})})}),(0,y.FD)(p.ax.Content,{css:On,children:[(0,y.Y)(p.ax.Arrow,{}),d]})]})})]})]})};var Kn=n(38243);var Vn={name:"1iwgvlm",styles:"flex-shrink:1;flex-grow:1;overflow:hidden"},zn={name:"1kd7iwj",styles:"flex-shrink:0;flex-grow:1;display:flex;align-items:flex-end"};const Gn=e=>{var t;let{run:n,onDatasetSelected:s}=e;const{theme:i}=(0,p.u)(),o=(0,r.useCallback)((e=>s(e,n)),[s,n]);return(null===(t=n.datasets)||void 0===t?void 0:t.length)<1?null:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:i.spacing.xs,overflow:"hidden"},""),children:[(0,y.Y)("div",{css:Vn,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_37",type:"link",onClick:()=>o(n.datasets[0]),children:(0,y.Y)(Kn.E,{datasetWithTags:n.datasets[0],displayTextAsLink:!0,css:(0,a.AH)({marginTop:i.spacing.xs/2,marginBottom:i.spacing.xs/2},"")})})}),n.datasets.length>1&&(0,y.Y)("div",{css:zn,children:(0,y.FD)(p.ax.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_51",modal:!1,children:[(0,y.Y)(p.ax.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_49",size:"small",style:{borderRadius:"8px",width:"40px"},children:(0,y.FD)(p.T.Text,{color:"secondary",children:["+",n.datasets.length-1]})})}),(0,y.Y)(p.ax.Content,{align:"start",children:n.datasets.slice(1).filter(Boolean).map((e=>(0,y.Y)("div",{css:(0,a.AH)({height:i.general.heightSm,display:"flex",alignItems:"center"},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_66",type:"link",onClick:()=>o(e),children:(0,y.Y)(Kn.E,{datasetWithTags:e,displayTextAsLink:!0})})},`${e.dataset.name}-${e.dataset.digest}`)))})]})})]})};var $n={name:"1on1b16",styles:"width:100%;height:100%;display:flex;flex-direction:column"};const jn=e=>{let{children:t,className:n,groupHeaderContent:r=null}=e;const{theme:s}=(0,p.u)();return(0,y.FD)("div",{css:$n,children:[(0,y.Y)("div",{css:(0,a.AH)({width:"100%",flexBasis:40,display:"flex",alignItems:"center",padding:s.spacing.sm,borderBottom:`1px solid ${s.colors.borderDecorative}`},""),className:"header-group-cell",children:r}),(0,y.Y)("div",{css:(0,a.AH)({width:"100%",flex:1,display:"flex",justifyContent:"flex-start",alignItems:"flex-start",padding:s.spacing.xs,borderRight:`1px solid ${s.colors.borderDecorative}`},""),className:n,children:t})]})};var qn=n(70403),Wn=n(79432);var Qn={name:"8xhv84",styles:"width:100%;display:flex"},Jn={name:"ozd7xs",styles:"flex-shrink:0"},Zn={name:"82a6rk",styles:"flex:1"};const Xn=e=>{let{run:t,onHideRun:n,onDuplicateRun:s,onDatasetSelected:i,groupHeaderContent:l=null}=e;const{theme:d}=(0,p.u)(),{getEvaluableRowCount:u,evaluateAllClick:m,runColumnsBeingEvaluated:g,canEvaluateInRunColumn:f}=Hn(),v=(0,Be.A)(),_=u(t),x=(0,qn.LE)(),w=_>0,b=g.includes(t.runUuid),Y=(0,r.useMemo)((()=>w?w&&!b?v.formatMessage({id:"2gmOVq",defaultMessage:"Process {evaluableRowCount} rows without evaluation output"},{evaluableRowCount:_}):null:v.formatMessage({id:"7m2B5x",defaultMessage:"There are no evaluable rows within this column"})),[_,w,b,v]);return(0,y.FD)(jn,{css:(0,a.AH)({justifyContent:"flex-start",padding:d.spacing.sm,paddingBottom:0,paddingTop:d.spacing.sm,flexDirection:"column",gap:d.spacing.xs/2,overflow:"hidden"},""),groupHeaderContent:l,children:[(0,y.FD)("div",{css:Qn,children:[(0,y.FD)("span",{css:(0,a.AH)({display:"flex",gap:d.spacing.sm,alignItems:"center"},""),children:[(0,y.Y)(Wn.E,{color:x(t.runUuid)}),(0,y.Y)(o.N_,{to:h.h.getRunPageRoute(t.experimentId||"",t.runUuid),target:"_blank",children:t.runName})]}),(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm,flexShrink:0},"")}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_112",onClick:()=>n(t.runUuid),size:"small",icon:(0,y.Y)(c.kFX,{}),css:Jn}),(0,y.Y)("div",{css:Zn}),(0,le.Ii)()&&f(t)&&(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm},"")}),(0,y.Y)(c.paO,{title:Y,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_118",disabled:!w,size:"small",onClick:()=>m(t),icon:b?(0,y.Y)(c.wFz,{}):(0,y.Y)(c.udU,{}),children:b?(0,y.Y)(ee.A,{id:"plw4Kp",defaultMessage:"Stop evaluating"}):(0,y.Y)(ee.A,{id:"JnIbS3",defaultMessage:"Evaluate all"})})})]}),(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm},"")}),(0,le.Ii)()&&(0,ut.o1)(t)&&(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_143",size:"small",icon:(0,y.Y)(c.ssM,{})})}),(0,y.Y)(c.rId.Content,{children:(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_150",onClick:()=>s(t),children:(0,y.Y)(ee.A,{id:"1YGQOY",defaultMessage:"Duplicate run"})})})]})]}),(0,le.Ii)()&&(0,ut.o1)(t)?(0,y.Y)(Un,{run:t}):(0,y.Y)(Gn,{run:t,onDatasetSelected:i})]})},ea=e=>{let{disabled:t,isLoading:n,run:a,rowKey:r}=e;const s=(0,ut.o1)(a),{evaluateCell:i,getMissingParams:o}=Hn(),l=a&&o(a,r)||null;return l&&l.length>0?(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{id:"yAuDRt",defaultMessage:'Evaluation is not possible because values for the following inputs cannot be determined: {missingParamList}. Add input columns to the "group by" settings or use "Add row" button to define new parameter set.',values:{missingParamList:(0,y.Y)("code",{children:l.join(", ")})}}),children:(0,y.Y)(u.I,{})}):s?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcellevaluatebutton.tsx_59",loading:n,disabled:t,size:"small",onMouseDownCapture:e=>e.stopPropagation(),onClickCapture:e=>{e.stopPropagation(),i(a,r)},icon:(0,y.Y)(c.udU,{}),children:(0,y.Y)(y.FK,{children:"Evaluate"})}):(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{id:"MhtxHm",defaultMessage:"You cannot evaluate this cell, this run was not created using served LLM model route"}),children:(0,y.Y)(u.I,{})})};var ta=n(40555);const na=r.memo((e=>{let{text:t,highlight:n}=e;const{theme:s}=(0,p.u)();if(!n)return(0,y.Y)(y.FK,{children:t});const i=t.split(new RegExp(`(${n})`,"gi"));return(0,y.Y)(y.FK,{children:i.map(((e,t)=>(0,y.Y)(r.Fragment,{children:e.toLowerCase()===n.toLowerCase()?(0,y.Y)("span",{css:(0,a.AH)({backgroundColor:s.colors.yellow200},""),children:e}):e},t)))})}));var aa={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},ra={name:"52b6nu",styles:"display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:7"},sa={name:"1jwcxx3",styles:"font-style:italic"};const ia=e=>{var t,n;let{value:i,context:o,isGroupByColumn:l,run:d,data:u}=e;const{theme:m}=(0,p.u)(),{pendingDataLoading:g,canEvaluateInRunColumn:h}=Hn(),f=(0,s.d4)((e=>{let{modelGateway:{modelGatewayRoutesLoading:t,modelGatewayRoutesLoadingLegacy:n}}=e;return t.loading})),v=d&&(null===(t=g[d.runUuid])||void 0===t?void 0:t[null===u||void 0===u?void 0:u.key]),_=d&&(null===(n=u.outputMetadataByRunUuid)||void 0===n?void 0:n[d.runUuid])||null,x=null!==_&&void 0!==_&&_.isPending||u.isPendingInputRow?m.colors.backgroundSecondary:m.colors.backgroundPrimary,w=r.useMemo((()=>{try{return JSON.parse(i)}catch(e){return null}}),[i]);return(0,y.FD)("div",{css:(0,a.AH)({height:"100%",whiteSpace:"normal",padding:m.spacing.sm,overflow:"hidden",position:"relative",cursor:"pointer",backgroundColor:x,"&:hover":{backgroundColor:m.colors.actionDefaultBackgroundHover}},""),children:[v?(0,y.Y)(c.QvX,{lines:3}):(0,y.Y)(y.FK,{children:i?w?(0,y.Y)(ta.y,{json:JSON.stringify(w,null,2)}):(0,y.Y)("span",{css:ra,children:l&&o.highlightedText?(0,y.Y)(na,{text:i,highlight:o.highlightedText}):"string"===typeof i?i.substring(0,512):"object"!==typeof i&&i}):(0,y.Y)(p.T.Text,{color:"info",css:aa,children:(0,y.Y)(ee.A,{id:"aQxQIF",defaultMessage:"(empty)"})})}),(0,le.Ii)()&&d&&h(d)&&(0,y.FD)("div",{css:(0,a.AH)({position:"absolute",left:8,bottom:8,right:8,display:"flex",gap:m.spacing.sm,alignItems:"center",justifyContent:"space-between"},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:m.spacing.xs},""),children:[!i&&(0,y.Y)(ea,{disabled:v,isLoading:f,run:d,rowKey:u.key}),((null===_||void 0===_?void 0:_.isPending)||u.isPendingInputRow)&&(0,y.Y)(p.T.Hint,{size:"sm",css:sa,children:(0,y.Y)(ee.A,{id:"2fvQaF",defaultMessage:"Unsaved"})})]}),_&&!v&&(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:m.spacing.xs,alignItems:"center"},""),children:[_.evaluationTime&&(0,y.FD)(p.T.Hint,{size:"sm",children:[Math.round(_.evaluationTime)," ms",_.totalTokens?",":""]}),_.totalTokens&&(0,y.Y)(p.T.Hint,{size:"sm",children:(0,y.Y)(ee.A,{id:"/nPZbt",defaultMessage:"{totalTokens} total tokens",values:{totalTokens:_.totalTokens}})})]})]})]})},oa={initialWidthGroupBy:200,initialWidthOutput:360,maxWidth:500,minWidth:140};var la={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"};const da=e=>{let{displayName:t}=e;const{theme:n}=(0,p.u)();return(0,y.Y)(jn,{css:(0,a.AH)({justifyContent:"flex-start",padding:n.spacing.sm},""),children:(0,y.Y)(c.paO,{title:(0,j.truncate)(t,{length:250}),children:(0,y.Y)(p.T.Text,{bold:!0,css:la,children:t})})})};var ca={name:"l8l8b8",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis"};const ua={svg:{width:20,height:20}},pa=()=>{const{toggleExpandedHeader:e,isHeaderExpanded:t}=Hn();return(0,y.Y)(jn,{children:(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(ee.A,{id:"lDGQGa",defaultMessage:"Toggle detailed view"}),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationtableactionscolumnrenderer.tsx_22",icon:t?(0,y.Y)(c.D3D,{css:ua}):(0,y.Y)(p.q,{css:ua}),onClick:e})})})},ma=e=>{let{onAddNewInputs:t,displayAddNewInputsButton:n}=e;const{theme:r}=(0,p.u)();return n?(0,y.Y)("div",{css:(0,a.AH)({width:"100%",height:"100%",display:"flex",flexDirection:"column",padding:r.spacing.xs},""),children:(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(ee.A,{id:"PNfcez",defaultMessage:"Add row"}),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationtableactionscellrenderer.tsx_37",icon:(0,y.Y)(c.c11,{}),onClick:t})})}):null};var ga=n(54421);var ha={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},fa={name:"pdi3j6",styles:"display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:7;width:100%;height:100%"};const va=e=>{let{value:t}=e;const{theme:n}=(0,p.u)(),r=n.colors.backgroundPrimary;return(0,y.Y)("div",{css:(0,a.AH)({height:"100%",whiteSpace:"normal",padding:n.spacing.sm,overflow:"hidden",position:"relative",cursor:"pointer",backgroundColor:r,"&:hover":{backgroundColor:n.colors.actionDefaultBackgroundHover}},""),children:t&&t.url&&t.compressed_url?(0,y.Y)("span",{css:fa,children:(0,y.Y)(ga.TV,{imageUrl:t.url,compressedImageUrl:t.compressed_url})}):(0,y.Y)(p.T.Text,{color:"info",css:ha,children:(0,y.Y)(ee.A,{id:"aQxQIF",defaultMessage:"(empty)"})})})};var _a={name:"3ytxc3",styles:"height:100%;overflow:hidden"};const xa=e=>{let{resultList:t,visibleRuns:n,groupByColumns:i,onCellClick:o,onHideRun:l,onDatasetSelected:d,highlightedText:c="",isPreviewPaneVisible:g,outputColumnName:h,isImageColumn:f}=e;const[v,_]=(0,r.useState)([]),[x,w]=(0,r.useState)(null),b=(0,s.d4)((e=>{let{evaluationData:t}=e;return t.evaluationPendingDataByRunUuid})),Y=(0,r.useRef)(null),{isHeaderExpanded:C}=Hn(),{createNewRun:I}=Gt(),A=(0,r.useCallback)((e=>{null===x||void 0===x||x.refreshHeader(),l(e)}),[x,l]),k=(0,r.useCallback)((e=>{null===x||void 0===x||x.refreshHeader(),I(e)}),[I,x]);(0,r.useEffect)((()=>{x&&!g&&x.clearFocusedCell()}),[x,g]),(0,r.useEffect)((()=>{if(!x)return;const e=x.getRenderedNodes();x.refreshCells({force:!0,rowNodes:e})}),[x,b,c]);const{showAddNewInputsModal:S,AddNewInputsModal:E}=(()=>{const[e,t]=(0,r.useState)(!1),[n,s]=(0,r.useState)([]),[i,o]=(0,r.useState)({}),l=(0,r.useMemo)((()=>n.every((e=>{let{inputName:t}=e;return i[t]}))),[i,n]),[d,c]=(0,r.useState)((async()=>{})),g=(0,r.useCallback)(((e,t)=>{o((n=>({...n,[e]:t})))}),[]),h=(0,r.useCallback)(((e,n)=>{const a=e.filter(ut.o1).map((e=>({runName:e.runName,params:(0,ut.xP)(e)}))),r=(0,j.uniq)(a.map((e=>{let{params:t}=e;return t})).flat()).map((e=>({inputName:e,runNames:(0,j.compact)(a.filter((t=>t.params.includes(e))).map((e=>{let{runName:t}=e;return t})))})));t(!0),s(r),o({}),c((()=>n))}),[]),{theme:f}=(0,p.u)(),v=(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationaddnewinputsmodal.tsx_57",title:(0,y.Y)(ee.A,{id:"JnidmZ",defaultMessage:"Add row"}),okButtonProps:{disabled:!l},okText:(0,y.Y)(ee.A,{id:"eeLqSn",defaultMessage:"Submit"}),cancelText:(0,y.Y)(ee.A,{id:"jwALGI",defaultMessage:"Cancel"}),onOk:()=>{d(i),t(!1)},visible:e,onCancel:()=>t(!1),children:n.map((e=>{let{inputName:t,runNames:n}=e;return(0,y.FD)("div",{css:(0,a.AH)({marginBottom:f.spacing.md},""),children:[(0,y.Y)(p.T.Text,{bold:!0,children:t}),(0,y.Y)(p.T.Hint,{css:ca,children:(0,y.Y)(ee.A,{id:"s35HDG",defaultMessage:"Used by {runNames} {hasMore, select, true {and other runs} other {}}",values:{runNames:n.slice(0,5).join(", "),hasMore:n.length>5}})}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:f.spacing.sm},""),children:(0,y.Y)(m.I.TextArea,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationaddnewinputsmodal.tsx_99",value:i[t],onChange:e=>g(t,e.target.value)})})]},t)}))});return{showAddNewInputsModal:h,AddNewInputsModal:v}})(),M=(0,s.wA)(),R=(0,r.useCallback)((()=>{var e;const t=null===(e=Y.current)||void 0===e?void 0:e.querySelector(".ag-body-viewport");t?t.scrollTo({top:0,behavior:"smooth"}):null===x||void 0===x||x.ensureIndexVisible(0,"top")}),[x]),T=(0,r.useMemo)((()=>n.map(ut.xP).flat().length>0),[n]),D=(0,r.useCallback)((()=>{S(n,(e=>{M({type:"EVALUATE_ADD_INPUT_VALUES",payload:e,meta:{}}),R()}))}),[R,S,M,n]),{theme:F}=(0,p.u)(),L=(0,Be.A)(),B=(0,r.useCallback)((e=>{let{value:t,colDef:n,column:a}=e;const r=L.formatMessage({id:"aQxQIF",defaultMessage:"(empty)"});return null===o||void 0===o?void 0:o(t||r,n.headerName||a.getId())}),[L,o]),H=(0,r.useMemo)((()=>(0,y.Y)(p.T.Text,{bold:!0,children:h})),[h]);return(0,r.useEffect)((()=>{const e=[],{initialWidthGroupBy:t,initialWidthOutput:a,maxWidth:r,minWidth:s}=oa;(0,le.Ii)()&&n.some((e=>(0,ut.o1)(e)))&&e.push({resizable:!1,pinned:!0,width:40,headerComponent:"ActionsColumnRenderer",cellRendererSelector:e=>{let{rowIndex:t}=e;return 0===t?{component:"ActionsCellRenderer",params:{displayAddNewInputsButton:T,onAddNewInputs:D}}:void 0},cellClass:"leading-column-cell"}),i.forEach(((n,a)=>{const o=a===i.length-1;e.push({resizable:!0,initialWidth:t,minWidth:s,maxWidth:r,headerName:n,valueGetter:e=>{let{data:t}=e;return t.groupByCellValues[n]},suppressMovable:!0,cellRenderer:"TextRendererCellRenderer",headerClass:o?"last-group-by-header-cell":void 0,cellRendererParams:{isGroupByColumn:!0},headerComponent:"GroupHeaderCellRenderer",headerComponentParams:{displayAddNewInputsButton:T,onAddNewInputs:D},colId:n,onCellClicked:B})})),n.forEach(((t,n)=>{const i=0===n;e.push({resizable:!0,initialWidth:a,minWidth:s,maxWidth:r,headerName:t.runName,colId:t.runUuid,valueGetter:e=>{let{data:n}=e;return n.cellValues[t.runUuid]},suppressMovable:!0,cellRenderer:f?"ImageRendererCellRenderer":"TextRendererCellRenderer",cellRendererParams:{run:t},headerComponent:"RunHeaderCellRenderer",headerComponentParams:{run:t,onDuplicateRun:k,onHideRun:A,onDatasetSelected:d,groupHeaderContent:i?H:null},onCellClicked:B})})),_(e)}),[n,i,A,k,d,D,T,B,H,f]),(0,r.useEffect)((()=>{if(!x)return;const e=n.some((e=>{var t;return(0,ut.o1)(e)||(null===(t=e.datasets)||void 0===t?void 0:t.length)>0}));x.setHeaderHeight(function(){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?40+(arguments.length>0&&void 0!==arguments[0]&&arguments[0]?175:62):80}(C,e))}),[x,C,n]),(0,y.FD)("div",{css:_a,ref:Y,children:[(0,y.Y)(Fn.p,{css:ya(F),context:{highlightedText:c},rowHeight:190,onGridReady:e=>{let{api:t}=e;return w(t)},getRowId:e=>{let{data:t}=e;return t.key},suppressHorizontalScroll:!1,columnDefs:v,rowData:t,components:{TextRendererCellRenderer:ia,GroupHeaderCellRenderer:da,RunHeaderCellRenderer:Xn,ActionsColumnRenderer:pa,ActionsCellRenderer:ma,ImageRendererCellRenderer:va}}),E]})},ya=e=>({".ag-row:not(.ag-row-first), .ag-body-viewport":{borderTop:`1px solid ${e.colors.borderDecorative}`},".ag-row-last":{borderBottom:`1px solid ${e.colors.borderDecorative}`},".ag-cell, .last-group-by-header-cell .header-group-cell":{borderRight:`1px solid ${e.colors.borderDecorative}`},".ag-cell-focus:not(.leading-column-cell)::after":{content:'""',position:"absolute",inset:0,boxShadow:`inset 0 0 0px 2px ${e.colors.blue300}`,pointerEvents:"none"}});var wa=n(77484);const ba=(e,t)=>{const n=t.map((t=>{const n=e[t];return[t,(0,j.isString)(n)?n:JSON.stringify(n)]})),a=n.map((e=>{let[,t]=e;return String(t)})).join(".");return{key:a,groupByValues:(0,j.fromPairs)(n)}};var Ya=n(38566);var Ca=n(37752);const Ia=()=>{var e;return/mac/i.test(null===(e=window.navigator.userAgentData)||void 0===e?void 0:e.platform)||/mac/i.test(window.navigator.platform)},Aa=Ia()?"metaKey":"ctrlKey",ka=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const{altOrOptKey:a=!1,ctrlOrCmdKey:s=!1,shiftKey:i=!1}=t;return(0,r.useEffect)((()=>{const t=t=>{if((!s||t[Aa])&&(!a||t.altKey)&&(!i||t.shiftKey)&&t.key===e){n()&&t.preventDefault()}};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)}),[e,n,s,a,i]),{isMacKeyboard:Ia}},Sa=()=>{const{evaluationPendingDataByRunUuid:e,evaluationArtifactsBeingUploaded:t,evaluationDraftInputValues:n}=(0,s.d4)((e=>{let{evaluationData:t}=e;return t})),[o,l]=(0,r.useState)(!1),d=(0,s.wA)(),c=(0,r.useCallback)((()=>{d({type:"DISCARD_PENDING_EVALUATION_DATA"})}),[d]),u=Object.values(e).flat().length,m=n.length,g=Object.values(t).filter((e=>Object.values(e).some((e=>e)))).length;(0,r.useEffect)((()=>{0===u&&l(!1)}),[u]);const h=(0,r.useCallback)((()=>(0===u||o||(l(!0),d((async(e,t)=>{const{evaluationPendingDataByRunUuid:n,evaluationArtifactsByRunUuid:a}=t().evaluationData,r=Object.keys(n),s=(0,j.fromPairs)(Object.entries(a).filter((e=>{let[t,n]=e;return r.includes(t)&&n[oe.AH]})).map((e=>{let[t,n]=e;return[t,n[oe.AH]]}))),o=r.map((e=>{const t=s[e];if(!t)throw new Error(`Cannot find existing prompt engineering artifact for run ${e}`);const a=n[e].map((e=>{let{entryData:n,evaluationTime:a,totalTokens:r}=e;return t.columns.map((e=>e===ut.Ef?a.toString():e===ut.jC&&r?r.toString():n[e]||""))})),r=(0,j.cloneDeep)(s[e].rawArtifactFile);return null===r||void 0===r||r.data.unshift(...a),{runUuid:e,updatedArtifactFile:r}})),l=o.map((t=>{let{runUuid:n,updatedArtifactFile:a}=t;return e((0,i.Of)(n,oe.AH,a)).then((()=>{const e=(0,Tt.G4)(oe.AH,a);return{runUuid:n,newEvaluationTable:e}}))}));return e({type:"WRITE_BACK_EVALUATION_ARTIFACTS",payload:Promise.all(l),meta:{runUuidsToUpdate:r,artifactPath:oe.AH}})})).catch((e=>{T.A.logErrorAndNotifyUser(e)}))),!0)),[d,u,o]),{isMacKeyboard:f}=ka("s",{ctrlOrCmdKey:!0},h),{theme:v}=(0,p.u)(),_=m>0&&0===u,x=u>0||_?(0,y.FD)("div",{css:(0,a.AH)({backgroundColor:v.colors.backgroundPrimary,border:`1px solid ${v.colors.border}`,padding:v.spacing.md,marginBottom:v.spacing.sm,display:"flex",justifyContent:"space-between",alignItems:"center"},""),children:[_?(0,y.Y)(ee.A,{id:"EPcEi9",defaultMessage:"You have added rows with new input values, but you still need to evaluate the new data in order to save it."}):o?(0,y.Y)(p.T.Text,{children:(0,y.Y)(ee.A,{id:"NGsTf/",defaultMessage:"Synchronizing artifacts for {runsBeingSynchronizedCount} runs...",values:{runsBeingSynchronizedCount:(0,y.Y)("strong",{children:g})}})}):(0,y.Y)(p.T.Text,{children:(0,y.Y)(ee.A,{id:"BWpQZ7",defaultMessage:'You have <strong>{unsyncedDataEntriesCount}</strong> unsaved evaluated {unsyncedDataEntriesCount, plural, =1 {value} other {values}}. Click "Save" button or press {keyCombination} keys to synchronize the artifact data.',values:{strong:e=>(0,y.Y)("strong",{children:e}),unsyncedDataEntriesCount:u,keyCombination:f()?"\u2318CMD+S":"CTRL+S"}})}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:v.spacing.sm},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationartifactwriteback.tsx_102",disabled:o,onClick:c,children:(0,y.Y)(ee.A,{id:"kNTkr+",defaultMessage:"Discard"})})," ",u>0&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationartifactwriteback.tsx_110",loading:o,type:"primary",onClick:h,children:(0,y.Y)(ee.A,{id:"nhqO2Z",defaultMessage:"Save"})})]})]}):null;return{isSyncingArtifacts:o,EvaluationSyncStatusElement:x}},Ea=e=>{let{noEvalTablesLogged:t,userDeselectedAllColumns:n,areRunsSelected:a,areTablesSelected:r}=e;return!r||!a||n||t},Ma=e=>{let{noEvalTablesLogged:t,userDeselectedAllColumns:n,areRunsSelected:a}=e;const[r,s]=a?t?[(0,y.Y)(ee.A,{id:"sguNEF",defaultMessage:"No evaluation tables logged"}),(0,y.Y)(ee.A,{id:"j7cj5r",defaultMessage:"Please log at least one table artifact containing evaluation data. <link>Learn more</link>.",values:{link:e=>(0,y.Y)(p.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactviewemptystate.tsx_48",openInNewTab:!0,href:"https://mlflow.org/docs/latest/python_api/mlflow.html?highlight=log_table#mlflow.log_table",target:"_blank",rel:"noopener noreferrer",children:e})}})]:n?[(0,y.Y)(ee.A,{id:"owr9l2",defaultMessage:"No group by columns selected"}),(0,y.Y)(ee.A,{id:"lJQEW4",defaultMessage:'Using controls above, select at least one "group by" column.'})]:[(0,y.Y)(ee.A,{id:"ZvJTXB",defaultMessage:"No tables selected"}),(0,y.Y)(ee.A,{id:"18/UVG",defaultMessage:"Using controls above, select at least one artifact containing table."})]:[(0,y.Y)(ee.A,{id:"BFzsMn",defaultMessage:"No runs selected"}),(0,y.Y)(ee.A,{id:"nPWZsh",defaultMessage:"Make sure that at least one experiment run is visible and available to compare"})];return(0,y.Y)(c.SvL,{title:r,description:s})};var Ra=n(22853),Ta=n(58898);var Da={name:"1hetg88",styles:"max-width:300px"},Fa={name:"111arwn",styles:"width:300px;min-width:300px"},La={name:"1hetg88",styles:"max-width:300px"},Ba={name:"1hetg88",styles:"max-width:300px"},Ha={name:"1ichkjj",styles:"height:100%;display:flex;justify-content:center;align-items:center"};const Pa=e=>{let{comparedRuns:t,onDatasetSelected:n,viewState:o,updateViewState:l}=e;const d=(0,Be.A)(),{theme:g}=(0,p.u)(),h=((0,qt.e)(),(0,r.useMemo)((()=>t.filter((e=>{let{hidden:t}=e;return!t})).slice(0,10)),[t])),{selectedTables:f,groupByCols:v,outputColumn:_,setSelectedTables:x,setGroupByCols:w,setOutputColumn:b}=((e,t)=>{const{artifactViewState:n={}}=e,[a,s]=(0,r.useState)(n.selectedTables||[]),[i,o]=(0,r.useState)(n.groupByCols||[]),[l,d]=(0,r.useState)(n.outputColumn||"");return(0,r.useEffect)((()=>t({artifactViewState:{selectedTables:a,groupByCols:i,outputColumn:l}})),[t,a,i,l]),{selectedTables:a,groupByCols:i,outputColumn:l,setSelectedTables:s,setGroupByCols:o,setOutputColumn:d}})(o,l),[Y,C]=(0,r.useState)(!1),[I,A]=(0,r.useState)(""),[k,S]=(0,r.useState)(""),[E,M]=(0,r.useState)(!1),{isSyncingArtifacts:R,EvaluationSyncStatusElement:D}=Sa(),F=(0,s.wA)();(0,r.useEffect)((()=>{(0,le.Ii)()&&F(Ft()).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e)}))}),[F]);const L=(0,r.useCallback)((e=>x((t=>t.includes(e)?t.filter((t=>t!==e)):[...t,e]))),[x]),B=(0,r.useCallback)((e=>w((t=>{const n=t.includes(e)?t.filter((t=>t!==e)):[...t,e];return M(0===n.length),n}))),[w]),H=(0,r.useMemo)((()=>h.map((e=>{let{runUuid:t}=e;return t}))),[h]),{evaluationArtifactsByRunUuid:P,evaluationPendingDataByRunUuid:N,evaluationDraftInputValues:O}=(0,s.d4)((e=>{let{evaluationData:t}=e;return t})),{tables:U,tablesByRun:K,noEvalTablesLogged:V}=(z=h,(0,r.useMemo)((()=>{const e=(0,j.fromPairs)(z.map((e=>{const t=e.tags?(0,Ya.T)(e.tags):[];return[e.runUuid,t]})).filter((e=>{let[,t]=e;return t.length>0}))),t=Array.from(new Set(Object.values(e).flat())),n=t.filter((t=>z.every((n=>{var a;let{runUuid:r}=n;return null===(a=e[r])||void 0===a?void 0:a.includes(t)})))),a=0===t.length;return{tables:t,tablesByRun:e,tablesIntersection:n,noEvalTablesLogged:a}}),[z]));var z;(0,r.useEffect)((()=>{U.length>0&&0===f.length&&x([U[0]])}),[U,x,f.length]);const G=(0,s.d4)((e=>{let{evaluationData:t,modelGateway:n}=e;return n.modelGatewayRoutesLoading.loading||H.some((e=>f.some((n=>{var a;return null===(a=t.evaluationArtifactsLoadingByRunUuid[e])||void 0===a?void 0:a[n]}))))})),{columns:$,imageColumns:q}=(W=P,Q=H,J=f,(0,r.useMemo)((()=>{if(0===J.length||0===Q.length)return{columns:[],columnsIntersection:[],imageColumns:[]};const e=Q.map((e=>Object.values(W[e]||{}).filter((e=>{let{path:t}=e;return J.includes(t)})))).flat(),t=e.filter((e=>{let{path:t}=e;return J.includes(t)})).map((e=>{let{columns:t,entries:n}=e;return t.map((e=>{const t=String(e);if(n.length>0){const a=n[0][e];return"object"===typeof a&&"image"===(null===a||void 0===a?void 0:a.type)?{name:t,type:"image"}:{name:t,type:"text"}}return{name:t,type:"text"}}))})).flat(),n=Array.from(new Set(t.filter((e=>"text"===e.type)).map((e=>e.name)))),a=Array.from(new Set(t.filter((e=>"image"===e.type)).map((e=>e.name)))),r=n.filter((t=>e.every((e=>{let{columns:n}=e;return n.includes(t)}))));return{columns:n,columnsIntersection:r,imageColumns:a}}),[Q,W,J]));var W,Q,J;const Z=q.includes(_),X=((e,t,n,a,s,i,o)=>(0,r.useMemo)((()=>{const r=[],l={},d={},c=[];for(const e of n){const t=i.map((t=>[t,e[t]])),n=t.map((e=>{let[,t]=e;return t})).join(".");d[n]=(0,j.fromPairs)(t),c.push(n)}const u={},p=a.map((t=>{const n=Object.values(e[t]||{}).filter((e=>{let{path:t}=e;return s.includes(t)})).map((e=>{let{entries:t}=e;return t})).flat();return[t,n]}));for(const[e,t]of p)for(const n of t){const{key:t,groupByValues:a}=ba(n,i);if(Object.values(a).every((e=>!e)))continue;d[t]||(d[t]=a),(n[ut.Ef]||n[ut.jC])&&(u[t]||(u[t]={}),u[t][e]||(u[t][e]={isPending:!1,evaluationTime:parseFloat(n[ut.Ef]),totalTokens:n[ut.jC]?parseInt(n[ut.jC],10):void 0})),l[t]||(l[t]={});const r=l[t];r[e]=r[e]||n[o]}for(const[e,n]of Object.entries(t))for(const t of n){const{entryData:n,...a}=t,{key:r,groupByValues:s}=ba(n,i);if(Object.values(s).every((e=>!e)))continue;d[r]||(d[r]=s,c.push(r)),u[r]||(u[r]={}),u[r][e]=a,l[r]||(l[r]={});const p=l[r];p[e]=n[o]||p[e]}const m=(0,j.sortBy)(Object.entries(d),(e=>{let[t]=e;return!c.includes(t)}));for(const[e,t]of m){const n=r.find((t=>{let{key:n}=t;return e===n}));if(n&&l[e])n.cellValues=l[e],n.outputMetadataByRunUuid=u[e];else{const n=l[e];Object.keys(n||{}).forEach((e=>{if(null!==n[e]&&"object"===typeof n[e])try{const{type:t,filepath:a,compressed_filepath:r}=n[e];t===oe.Oe?n[e]={url:(0,wa.To)(a,e),compressed_url:(0,wa.To)(r,e)}:n[e]=JSON.stringify(n[e])}catch{n[e]=""}else(0,j.isNil)(n[e])||(0,j.isString)(n[e])||(n[e]=JSON.stringify(n[e]))})),r.push({key:e,groupByCellValues:t,cellValues:l[e]||{},outputMetadataByRunUuid:u[e],isPendingInputRow:c.includes(e)})}}return r}),[a,e,i,n,s,o,t]))(P,N,O,H,f,v,_),te=(0,r.useMemo)((()=>{const e=h.filter(ut.o1).map(ut.xP).flat();if(!e.length)return null;return Array.from(new Set(e)).filter((e=>$.includes(e)))}),[h,$]);(0,r.useEffect)((()=>{h.every(ut.o1)&&w([])}),[w,h]),(0,r.useEffect)((()=>{if(f.length)for(const e of h){if(!e)continue;const t=(K[e.runUuid]||[]).filter((e=>f.includes(e)));for(const n of t)F((0,i.sT)(e.runUuid,n,!1)).catch((e=>{e instanceof Tt.ap?T.A.displayGlobalErrorNotification(e.message):T.A.logErrorAndNotifyUser(e.message||e)}))}}),[h,F,f,K]);const ne=f.length>0,ae=h.length>0,re=!G&&ne&&ae,se=(0,r.useMemo)((()=>{if(!k.trim())return X;const e=new RegExp(k,"i");return X.filter((t=>{let{groupByCellValues:n}=t;return Object.values(n).some((t=>null===t||void 0===t?void 0:t.match(e)))}))}),[X,k]),ie=(0,Ra.v)(t),de=(0,r.useCallback)((e=>{ie(Ta.oy.CUSTOM,e)}),[ie]);(0,r.useEffect)((()=>{if(G||E)return;const e=v.length<1,t=v.some((e=>!$.includes(e))),n=$[0],a=te||(n?[n]:null);(e||t)&&a&&w(a)}),[G,E,v,_,$,w,te]);const ce=(0,r.useMemo)((()=>$.filter((e=>!e.startsWith("MLFLOW_")))),[$]),ue=(0,r.useMemo)((()=>[...$,...q].filter((e=>!v.includes(e)&&!e.startsWith("MLFLOW_")))),[$,q,v]);(0,r.useEffect)((()=>{if(v.includes(_)||!_){const e=ue.includes(ut.hR)?ut.hR:ue[0];b(e||"")}}),[v,_,ue,b]),(0,r.useEffect)((()=>{C(!0);const e=setTimeout((()=>S(I)),250);return()=>clearTimeout(e)}),[I]),(0,r.useEffect)((()=>{C(!1)}),[k]),(0,r.useEffect)((()=>{if(!ue.includes(_)){const e=ue.includes(ut.hR)?ut.hR:ue[0];b(e||"")}}),[_,ue,b]),(0,r.useEffect)((()=>{f.some((e=>!U.includes(e)))&&x([])}),[f,U,x]);const[pe,me]=(0,r.useState)(null),ge=(0,r.useCallback)(((e,t)=>{me({value:e,header:t}),l({previewPaneVisible:!0})}),[l]);return(0,y.FD)("div",{css:(0,a.AH)({flex:1,borderTop:`1px solid ${g.colors.border}`,borderLeft:`1px solid ${g.colors.border}`,marginLeft:-1,zIndex:1,height:"100%",display:"grid",gridTemplateColumns:o.previewPaneVisible?"1fr auto":"1fr",overflow:"hidden"},""),children:[(0,y.FD)("div",{css:(0,a.AH)({paddingLeft:g.spacing.sm,paddingTop:g.spacing.sm,height:"100%",display:"grid",gridTemplateRows:"auto auto 1fr",overflow:"hidden",rowGap:g.spacing.sm,backgroundColor:g.colors.backgroundSecondary},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:g.spacing.sm,overflow:"hidden",height:g.general.heightSm},""),children:[(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_358",label:(0,y.Y)(ee.A,{id:"syyEiR",defaultMessage:"Table"}),multiSelect:!0,value:f,children:[(0,y.Y)(c.gGe,{css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},""),"data-testid":"dropdown-tables",onClear:()=>x([]),disabled:R||!ae||V}),(0,y.Y)(c.dn6,{css:Da,children:(0,y.Y)(c.HI_,{children:U.map((e=>(0,y.Y)(c.jTC,{value:e,onChange:L,checked:f.includes(e),"data-testid":"dropdown-tables-option",children:e},e)))})})]}),(0,y.Y)(c.paO,{title:(0,y.Y)(ee.A,{id:"VZRc73",defaultMessage:"Using the list of logged table artifacts, select at least one to start comparing results."}),children:(0,y.Y)(u.I,{})})]}),G?(0,y.Y)(c.PLz,{}):(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",columnGap:g.spacing.sm,alignItems:"center",overflow:"hidden",height:g.general.heightSm},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_414",prefix:(0,y.Y)(m.S,{}),suffix:Y&&(0,y.Y)(p.H,{size:"small"}),css:Fa,onChange:e=>A(e.target.value),value:I,placeholder:d.formatMessage({id:"3D2Znf",defaultMessage:"Filter by {columnNames}"},{columnNames:v.join(", ")}),allowClear:!0,disabled:!re||R}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_433",value:v,multiSelect:!0,label:(0,y.Y)(ee.A,{id:"fWm1UC",defaultMessage:"Group by"}),children:[(0,y.Y)(c.gGe,{disabled:!re||R,allowClear:!1,showTagAfterValueCount:1,css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},""),"aria-label":'Select "group by" columns'}),(0,y.Y)(c.dn6,{css:La,children:(0,y.Y)(c.HI_,{children:ce.map((e=>(0,y.Y)(c.jTC,{value:e,onChange:B,checked:v.includes(e),children:e},e)))})})]}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_465",value:[_],label:(0,y.Y)(ee.A,{id:"ZqGzZd",defaultMessage:"Compare"}),children:[(0,y.Y)(c.gGe,{disabled:!re||R,allowClear:!1,css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},"")}),(0,y.Y)(c.dn6,{css:Ba,children:(0,y.Y)(c.HI_,{children:ue.map((e=>(0,y.Y)(c.crD,{value:e,onChange:()=>b(e),checked:_===e,children:e},e)))})})]})]}),Ea({areRunsSelected:ae,areTablesSelected:ne,noEvalTablesLogged:V,userDeselectedAllColumns:E})?(0,y.Y)("div",{css:Ha,children:(0,y.Y)(Ma,{areRunsSelected:ae,areTablesSelected:ne,noEvalTablesLogged:V,userDeselectedAllColumns:E})}):(0,y.Y)("div",{css:(0,a.AH)({position:"relative",zIndex:1,overflowY:"hidden",height:"100%",backgroundColor:g.colors.backgroundPrimary},""),children:(0,y.Y)(Bn,{tableData:X,outputColumn:_,children:(0,y.Y)(xa,{visibleRuns:h,groupByColumns:v,resultList:se,onCellClick:Z?void 0:ge,onHideRun:de,onDatasetSelected:n,highlightedText:k.trim(),isPreviewPaneVisible:o.previewPaneVisible,outputColumnName:_,isImageColumn:Z})})}),D]})]}),o.previewPaneVisible&&(0,y.Y)(Ca.V,{content:null!==pe&&void 0!==pe&&pe.value?(0,y.Y)(ta.f,{json:pe.value}):null,copyText:(null===pe||void 0===pe?void 0:pe.value)||"",headerText:null===pe||void 0===pe?void 0:pe.header,onClose:()=>l({previewPaneVisible:!1}),empty:(0,y.Y)(c.SvL,{description:(0,y.Y)(ee.A,{id:"lTngfr",defaultMessage:"Select a cell to display preview"})})})]})},Na=e=>{const{theme:t}=(0,p.u)();return e.disabled?(0,y.Y)("div",{css:(0,a.AH)({flex:1,backgroundColor:t.colors.backgroundSecondary,height:"100%",borderTop:`1px solid ${t.colors.border}`,borderLeft:`1px solid ${t.colors.border}`,paddingTop:t.spacing.lg,marginLeft:-1,zIndex:1,display:"flex",justifyContent:"center",alignItems:"center"},""),children:(0,y.Y)(c.SvL,{title:(0,y.Y)(ee.A,{id:"2gxV/O",defaultMessage:"Evaluation not available when grouping is enabled"}),description:(0,y.Y)(ee.A,{id:"kdTxC2",defaultMessage:"Disable run grouping in order to access the evaluation view"}),image:(0,y.Y)("div",{})})}):(0,y.Y)(Pa,{...e})};var Oa=n(30152),Ua=n(19415),Ka=n(36118),Va=n(73150),za=n(75627),Ga=n(39045),$a=n(62758),ja=n(63617),qa=n(55999),Wa=n(688),Qa=n(71932),Ja=n(40029),Za=n(21039);const Xa=(e,t,n,a,r,s)=>{var i,o;return{uuid:e.runUuid,displayName:(null===(i=e.runInfo)||void 0===i?void 0:i.runName)||e.runUuid,runInfo:e.runInfo,metrics:t[e.runUuid]||{},params:n[e.runUuid]||{},tags:a[e.runUuid]||{},images:r[e.runUuid]||{},color:s,pinned:e.pinned,pinnable:e.pinnable,metricsHistory:{},belongsToGroup:null===(o=e.runDateAndNestInfo)||void 0===o?void 0:o.belongsToGroup,hidden:e.hidden}},er=e=>{let{isLoading:t,comparedRuns:n,metricKeyList:i,paramKeyList:o,experimentTags:l,compareRunCharts:d,compareRunSections:c,groupBy:u,autoRefreshEnabled:m,hideEmptyCharts:g,globalLineChartConfig:h,chartsSearchFilter:f}=e;const v=(0,qt.e)(),_=(0,qn.LE)(),x=(0,$a.g_)(),{paramsByRunUuid:w,latestMetricsByRunUuid:b,tagsByRunUuid:Y,imagesByRunUuid:C}=(0,s.d4)((e=>({paramsByRunUuid:e.entities.paramsByRunUuid,latestMetricsByRunUuid:e.entities.latestMetricsByRunUuid,tagsByRunUuid:e.entities.tagsByRunUuid,imagesByRunUuid:e.entities.imagesByRunUuid}))),{theme:I}=(0,p.u)(),[A,k]=(0,r.useState)(!1),[S,E]=(0,r.useState)(null),[M,R]=(0,r.useState)(""),{formatMessage:T}=(0,Be.A)(),D=(0,r.useMemo)((()=>(0,gn.Zp)(u)),[u]),[F,L]=(0,r.useState)(void 0),B=(0,r.useCallback)((e=>{E(e)}),[]);(0,r.useEffect)((()=>{A||t||k(!0)}),[A,t]);const H=(0,r.useMemo)((()=>{const e=l[oe.S0],t=l[oe.Q4];return(null===e||void 0===e?void 0:e.value)||(null===t||void 0===t?void 0:t.value)||i[0]||""}),[l,i]),P=(0,r.useMemo)((()=>{if(!u)return n.filter((e=>e.runInfo)).map((e=>Xa(e,b,w,Y,C,_(e.runUuid))));const e=n.filter((e=>e.groupParentInfo&&!(0,gn.mC)(e.groupParentInfo))).map((e=>{var t;return((e,t)=>{var n,a,r;const s=null!==(n=e.groupParentInfo)&&void 0!==n&&n.aggregatedMetricData?(0,j.keyBy)((0,j.values)(null===(a=e.groupParentInfo)||void 0===a?void 0:a.aggregatedMetricData).map((e=>{let{key:t,value:n,maxStep:a}=e;return{key:t,value:n,step:a,timestamp:0}})),"key"):{};return{uuid:e.rowUuid,displayName:(0,gn.QD)(e.groupParentInfo),groupParentInfo:e.groupParentInfo,metrics:s,params:(null===(r=e.groupParentInfo)||void 0===r?void 0:r.aggregatedParamData)||{},tags:{},images:{},color:t,pinned:e.pinned,pinnable:e.pinnable,metricsHistory:{},hidden:e.hidden}})(e,_(null===(t=e.groupParentInfo)||void 0===t?void 0:t.groupId))}));return[...e,...n.filter((e=>{var t;return!e.groupParentInfo&&!(null!==(t=e.runDateAndNestInfo)&&void 0!==t&&t.belongsToGroup)})).map((e=>Xa(e,b,w,Y,C,_(e.runUuid))))]}),[u,n,b,w,Y,C,_]),N=P.filter((e=>!e.hidden&&e.tags[oe.Cr]));(0,qa.L)({runUuids:N.map((e=>e.uuid)),runUuidsIsActive:N.map((e=>{var t;return"RUNNING"===(null===(t=e.runInfo)||void 0===t?void 0:t.status)})),enabled:!0,autoRefreshEnabled:m}),(0,r.useEffect)((()=>{if((!c||!d)&&P.length>0){const{resultChartSet:e,resultSectionSet:t}=Oa.i$.getBaseChartAndSectionConfigs({primaryMetricKey:H,runsData:P,useParallelCoordinatesChart:!0});x((n=>({...n,compareRunCharts:e,compareRunSections:t})),!0)}}),[d,c,H,P,x]),(0,r.useEffect)((()=>{x((e=>{if(!e.compareRunCharts||!e.compareRunSections)return e;const{resultChartSet:t,resultSectionSet:n,isResultUpdated:a}=Oa.i$.updateChartAndSectionConfigs({compareRunCharts:e.compareRunCharts,compareRunSections:e.compareRunSections,runsData:P,isAccordionReordered:e.isAccordionReordered});return a?{...e,compareRunCharts:t,compareRunSections:n}:e}),!0)}),[P,x]);const O=(0,r.useCallback)((e=>{v((t=>({...t,runsPinned:t.runsPinned.includes(e)?t.runsPinned.filter((t=>t!==e)):[...t.runsPinned,e]})))}),[v]),U=(0,Ra.v)(n),K=(0,r.useCallback)((e=>U(Ta.oy.CUSTOM,e)),[U]),V=(0,$a.Ez)(),z=(0,$a.KP)(),G=(0,$a.iO)(),$=(0,$a.cA)(),q=(0,r.useMemo)((()=>({runs:P,onTogglePin:O,onHideRun:K})),[P,K,O]),W=(0,r.useMemo)((()=>g?null===d||void 0===d?void 0:d.filter((e=>!e.deleted&&!(0,Ka.Cs)(P,e))):null===d||void 0===d?void 0:d.filter((e=>!e.deleted))),[P,d,g]);return A?(0,y.FD)("div",{css:(0,a.AH)({flex:1,borderTop:`1px solid ${I.colors.border}`,borderLeft:`1px solid ${I.colors.border}`,marginLeft:-1,position:"relative",backgroundColor:I.colors.backgroundSecondary,paddingLeft:I.spacing.md,paddingRight:I.spacing.md,paddingBottom:I.spacing.md,zIndex:1,overflowY:"auto",minWidth:320},""),"data-testid":"experiment-view-compare-runs-chart-area",children:[(0,y.FD)("div",{css:[{paddingTop:I.spacing.sm,paddingBottom:I.spacing.sm,display:"flex",gap:I.spacing.xs,position:"sticky",top:0,zIndex:Za.K.SEARCH_BAR,backgroundColor:I.colors.backgroundSecondary,marginLeft:-I.spacing.md,marginRight:-I.spacing.md,paddingLeft:I.spacing.md,paddingRight:I.spacing.md},""],children:[(0,y.Y)(Ja.I,{chartsSearchFilter:f}),(0,y.Y)(Wa.f,{updateUIState:x,metricKeyList:i,globalLineChartConfig:h})]}),(0,y.Y)(za.W,{contextData:q,component:Va.X,children:(0,y.Y)(Qa.c_,{visibleChartCards:W,children:(0,y.Y)(Ga.J,{compareRunSections:c,compareRunCharts:W,reorderCharts:G,insertCharts:$,chartData:P,startEditChart:B,removeChart:z,addNewChartCard:e=>t=>{E(Oa.i$.getEmptyChartCardByType(t,!1,void 0,e))},search:null!==f&&void 0!==f?f:"",groupBy:D,setFullScreenChart:L,autoRefreshEnabled:m,hideEmptyCharts:g,globalLineChartConfig:h})})}),S&&(0,y.Y)(Ua.z,{chartRunData:P,metricKeyList:i,paramKeyList:o,config:S,onSubmit:e=>{V(e),E(null)},onCancel:()=>E(null),groupBy:D,globalLineChartConfig:h}),(0,y.Y)(ja._,{fullScreenChart:F,onCancel:()=>L(void 0),chartData:P,groupBy:D,tooltipContextValue:q,tooltipComponent:Va.X,autoRefreshEnabled:m,globalLineChartConfig:h})]}):(0,y.Y)(nr,{})},tr=e=>{const t=(0,qt.e)(),n=(0,r.useCallback)((e=>{t((t=>({...t,...e(t)})))}),[t]);return(0,y.Y)($a.oB,{updateChartsUIState:n,children:(0,y.Y)(er,{...e})})},nr=()=>{const{theme:e}=(0,p.u)();return(0,y.Y)("div",{css:(0,a.AH)({flex:1,display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gridTemplateRows:"200px",gap:e.spacing.md,borderTop:`1px solid ${e.colors.border}`,borderLeft:`1px solid ${e.colors.border}`,marginLeft:-1,backgroundColor:e.colors.backgroundSecondary,padding:e.spacing.md,zIndex:1},""),children:new Array(6).fill(null).map(((e,t)=>(0,y.Y)(c.QvX,{lines:5,seed:t.toString()},t)))})};var ar=n(4972),rr=n(42550),sr=n(25790);const ir=()=>{const e=new Date;return e.setMilliseconds(0),e};var or={name:"vm4aot",styles:"min-height:225px;height:100%;position:relative;display:flex"};const lr=r.memo((e=>{var t;const[n]=pe(),{experiments:a,runsData:i,uiState:o,searchFacetsState:l,isLoadingRuns:d,loadMoreRuns:u,moreRunsAvailable:p,requestError:m,refreshRuns:g}=e,[h,f]=(0,r.useState)(new En),{experimentId:v}=a[0],_=Ke(v),[x,w]=(0,r.useState)("true"===_.getItem("expandRows"));(0,r.useEffect)((()=>{_.setItem("expandRows",x)}),[x,_]);const{paramKeyList:b,metricKeyList:Y,tagsList:C,paramsList:I,metricsList:A,runInfos:k,runUuidsMatchingFilter:S,datasetsList:E}=i,M=(0,s.d4)((e=>{let{entities:t}=e;return t.modelVersionsByRunUuid})),R=(0,r.useMemo)((()=>k.map(((e,t)=>({runInfo:e,params:I[t],metrics:A[t],tags:C[t],datasets:E[t]})))),[E,A,I,k,C]),{orderByKey:D,searchFilter:F}=l,{runsPinned:L,runsExpanded:B,runsHidden:H,runListHidden:P}=o,N="TABLE"!==n,O=(0,r.useCallback)((e=>f((t=>({...t,...e})))),[]),U=(0,r.useCallback)((()=>{O({columnSelectorVisible:!0})}),[O]),K=(0,r.useMemo)((()=>!D&&!F||D===oe.T8.DATE),[D,F]),[V,z]=(0,r.useState)(ir);(0,r.useEffect)((()=>{z(ir)}),[k]);const G=(0,r.useMemo)((()=>T.A.getVisibleTagKeyList(C)),[C]),[$,j]=(0,r.useState)(!1),[q,W]=(0,r.useState)(),Q=((0,r.useMemo)((()=>a.map((e=>{let{experimentId:t}=e;return t}))),[a]),(0,Mn.ZU)({experiments:a,paramKeyList:b,metricKeyList:Y,modelVersionsByRunUuid:M,runsExpanded:B,tagKeyList:G,nestChildren:K,referenceTime:V,runData:R,runUuidsMatchingFilter:S,runsPinned:L,runsHidden:H,groupBy:o.groupBy,groupsExpanded:o.groupsExpanded,runsHiddenMode:o.runsHiddenMode,runsVisibilityMap:o.runsVisibilityMap,useGroupedValuesInCharts:o.useGroupedValuesInCharts,searchFacetsState:l})),[J,Z]=(0,c.oL1)(),X=Tn(J),[ee,te]=(0,r.useState)(295),ne=(0,r.useCallback)((()=>{p&&!d&&u().then((e=>{X(e,k)}))}),[p,d,u,k,X]),ae=(0,r.useCallback)(((e,t)=>{W({datasetWithTags:e,runData:t}),j(!0)}),[]),re=(0,ar.$)(),se=o.autoRefreshEnabled&&(0,le.Hn)()&&re,ie=null===(t=o.useGroupedValuesInCharts)||void 0===t||t,de=(0,y.Y)(Sn.P,{experiments:a,runsData:i,searchFacetsState:l,viewState:h,isLoading:d,updateViewState:O,onAddColumnClicked:U,rowsData:Q,loadMoreRunsFunc:ne,moreRunsAvailable:p,onDatasetSelected:ae,expandRows:x,uiState:o,compareRunsMode:n}),ce=(0,r.useMemo)((()=>a.map((e=>e.experimentId)).sort().join(",")),[a]);return(0,y.Y)(zt,{visibleRuns:Q,refreshRuns:g,children:(0,y.FD)(sr.Co,{children:[(0,y.Y)(kn,{viewState:h,updateViewState:O,runsData:i,searchFacetsState:l,experimentId:v,requestError:m,expandRows:x,updateExpandRows:w,refreshRuns:g,uiState:o,isLoading:d}),(0,y.FD)("div",{css:or,children:[N?(0,y.Y)(rr.t,{onResize:te,runListHidden:P,width:ee,children:de}):de,"CHART"===n&&(0,y.Y)(tr,{isLoading:d,comparedRuns:Q,metricKeyList:i.metricKeyList,paramKeyList:i.paramKeyList,experimentTags:i.experimentTags,compareRunCharts:o.compareRunCharts,compareRunSections:o.compareRunSections,groupBy:ie?o.groupBy:null,autoRefreshEnabled:se,hideEmptyCharts:o.hideEmptyCharts,globalLineChartConfig:o.globalLineChartConfig,chartsSearchFilter:o.chartsSearchFilter,storageKey:ce}),"ARTIFACT"===n&&(0,y.Y)(Na,{comparedRuns:Q,viewState:h,updateViewState:O,onDatasetSelected:ae,disabled:Boolean(o.groupBy)}),Z,q&&(0,y.Y)(Dn.O,{isOpen:$,setIsOpen:j,selectedDatasetWithRun:q,setSelectedDatasetWithRun:W})]})]})})}));function dr(e){try{return Ue.A.getStoreForComponent("ExperimentPage",e).loadComponentState()}catch{return T.A.logErrorAndNotifyUser(`Error: malformed persisted search state for experiment(s) ${e}`),{...(0,Ta.uY)(),...(0,jt.G)()}}}const cr=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=(0,$t.Px)(),i=(0,r.useMemo)((()=>n?JSON.stringify(n.sort()):null),[n]);(0,r.useEffect)((()=>{if(!a&&!t){const e=i?dr(i):null,t=(0,j.pick)({...(0,jt.G)(),...e},$t.sO);s(t,{replace:!0})}}),[t,i,a]),(0,r.useEffect)((()=>{var n,r;t&&i&&!a&&(n={...t,...e},r=i,Ue.A.getStoreForComponent("ExperimentPage",r).saveComponentState(n))}),[t,e,i,a])};var ur=n(32378),pr=n(85017);const mr=(e,t)=>{const{experiments:n}=t,a=t.experimentIds||n.map((e=>e.experimentId)),r=a.length>1,s=(e.entities.runInfoOrderByUuid||[]).map((t=>e.entities.runInfosByUuid[t])).filter((e=>{let{experimentId:t}=e;return a.includes(t)})).map((e=>{let{runUuid:t}=e;return t})),{modelVersionsByRunUuid:i,runUuidsMatchingFilter:o}=e.entities,l=((e,t,n)=>{let{lifecycleFilter:a=ur.gy.ACTIVE,modelVersionFilter:r=ur.EL.ALL_RUNS,datasetsFilter:s=[]}=n;const{modelVersionsByRunUuid:i}=t.entities;return e.map((e=>[(0,A.K4)(e,t),(0,A.jF)(e,t)])).filter((e=>{let[t,n]=e;return a===ur.gy.ACTIVE?"active"===t.lifecycleStage:"deleted"===t.lifecycleStage})).filter((e=>{let[t,n]=e;return r===ur.EL.ALL_RUNS||(r===ur.EL.WITH_MODEL_VERSIONS?t.runUuid in i:r===ur.EL.WTIHOUT_MODEL_VERSIONS?!(t.runUuid in i):(console.warn("Invalid input to model version filter - defaulting to showing all runs."),!0))})).filter((e=>{let[t,n]=e;return!s||0===s.length||!!n&&n.some((e=>{const t=e.dataset.name,n=e.dataset.digest;return s.some((e=>{let{name:a,digest:r}=e;return a===t&&r===n}))}))})).map((e=>{let[t,n]=e;return t}))})(s,e,t),d=new Set,c=new Set,u=l.map((t=>e.entities.runDatasetsByUuid[t.runUuid])),p=l.map((t=>{const n=(0,pr.d0)(t.runUuid,e),a=Object.values(n||{}).filter((e=>e.key.trim().length>0));return a.forEach((e=>{d.add(e.key)})),a})),m=l.map((t=>{const n=Object.values((0,A.tI)(t.runUuid,e)).filter((e=>e.key.trim().length>0));return n.forEach((e=>{c.add(e.key)})),n})),g=l.map((t=>(0,j.pickBy)((0,A.X3)(t.runUuid,e),(e=>e.key.trim().length>0)))),h=a[0];return{modelVersionsByRunUuid:i,experimentTags:r?{}:(0,A.xy)(h,e),runInfos:l,paramsList:m,tagsList:g,metricsList:p,runUuidsMatchingFilter:o,datasetsList:u,metricKeyList:Array.from(d.values()).sort(),paramKeyList:Array.from(c.values()).sort()}};var gr=n(69708);const hr=(e,t,n)=>{if(!e||!t.length)return null;return{...(0,je.TB)(t,{...e,runsPinned:n},Date.now()),requestedFacets:e}},fr=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const o=(0,s.wA)(),[l,d]=(0,r.useState)((()=>vr())),c=(0,r.useMemo)((()=>n?JSON.stringify(n.sort()):null),[n]),[u,p]=(0,r.useState)(!0),[m,g]=(0,r.useState)(!0),[h,f]=(0,r.useState)(null),[v,_]=(0,r.useState)(null),x=(0,r.useRef)([]),y=(0,r.useRef)(null),w=(0,r.useRef)(null),b=(0,r.useRef)(null);(0,r.useEffect)((()=>{a||(g(!0),d(vr()))}),[c,a]);const Y=(0,r.useCallback)(((e,t,n)=>{d(mr(e,{datasetsFilter:n.datasetsFilter,lifecycleFilter:n.lifecycleFilter,modelVersionFilter:n.modelVersionFilter,experiments:[],experimentIds:t}))}),[]);(0,r.useEffect)((()=>{x.current=e.runsPinned}),[e.runsPinned]);const C=(0,r.useCallback)((e=>{(0,je.iH)(e||[],gr.hY,o).catch((e=>{const t=e instanceof Ee.s?e.getMessageField():e.message;T.A.displayGlobalErrorNotification(`Failed to load model versions for runs: ${t}`)}))}),[o]),I=(0,r.useCallback)((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(((n,a)=>(t.isAutoRefreshing||(p(!0),w.current=e),n((e.pageToken?i.e$:i.bn)(e)).then((async n=>{var r;let{value:s}=n;return y.current=Date.now(),p(!1),g(!1),w.current&&null!==(r=t.discardResultsFn)&&void 0!==r&&r.call(t,w.current,s)||(b.current=e,f(s.next_page_token||null),Y(a(),e.experimentIds,e.requestedFacets),C(s.runs||[])),s})).catch((e=>{p(!1),g(!1),_(e),T.A.logErrorAndNotifyUser(e)})))))}),[o,Y,C]);(0,r.useEffect)((()=>{if(a)return;const e=hr(t,n,x.current);e&&I(e)}),[I,o,a,t,n]);const A=(0,r.useCallback)((()=>{b.current&&I({...b.current,pageToken:void 0})}),[I]);return(e=>{let{experimentIds:t,lastFetchedTime:n,fetchRuns:a,searchFacets:s,enabled:o,cachedPinnedRuns:l,runsData:d,isLoadingRuns:c}=e;const u=(0,r.useRef)(void 0),p=(0,r.useRef)(c),m=(0,r.useRef)(o),g=(0,r.useRef)(d.runInfos);g.current=d.runInfos,p.current=c,m.current=o,(0,r.useEffect)((()=>{if(window.clearTimeout(u.current),!o||c)return;const e=async()=>{const r=Boolean(n.current),o=n.current?Date.now()-n.current:0;if(s&&r&&o>=je.t4){const e=g.current.length,n=Math.max(1,Math.ceil(e/i.Aj))*i.Aj,r={...(0,je.TB)(t,{...s,runsPinned:l.current},Date.now()),requestedFacets:s,maxResults:n};let o,d=0,c=0;const u=(t,a)=>{var s,i;return!!(c+(null!==(s=null===a||void 0===a||null===(i=a.runs)||void 0===i?void 0:i.length)&&void 0!==s?s:0)<n&&null!==a&&void 0!==a&&a.next_page_token)||!m.current||e>n||!(0,j.isEqual)(t.requestedFacets,r.requestedFacets)};for(;(0===d||o)&&!(c>=n);){d++;const e=await a({...r,pageToken:o},{isAutoRefreshing:!0,discardResultsFn:u});c+=(0,j.isArray)(null===e||void 0===e?void 0:e.runs)?e.runs.length:0,o=null===e||void 0===e?void 0:e.next_page_token}}window.clearTimeout(u.current),m.current&&(u.current=window.setTimeout(e,je.t4))};return e(),()=>{clearTimeout(u.current)}}),[t,a,s,o,l,n,c])})({experimentIds:n,fetchRuns:I,searchFacets:t,enabled:e.autoRefreshEnabled&&(0,le.Hn)(),cachedPinnedRuns:x,runsData:l,isLoadingRuns:u,lastFetchedTime:y}),{isLoadingRuns:u,moreRunsAvailable:Boolean(h),refreshRuns:A,loadMoreRuns:async()=>{const e=hr(t,n,x.current);return h&&e?I({...e,pageToken:h}):[]},isInitialLoadingRuns:m,runsData:l,requestError:v}},vr=()=>({datasetsList:[],experimentTags:{},metricKeyList:[],metricsList:[],modelVersionsByRunUuid:{},paramKeyList:[],paramsList:[],runInfos:[],runUuidsMatchingFilter:[],tagsList:[]}),_r=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const[a]=(0,o.ok)(),s=(0,Be.A)(),i=a.get(oe.ex),l=Boolean(i),d=(0,$t.Px)(),[c,u]=(0,r.useState)(null),[p,m]=(0,r.useState)(null),[g,f]=(0,r.useState)(null),[v,_]=(0,r.useState)(null);(0,r.useEffect)((()=>{if(!i||!t)return;const e=t.tags.find((e=>{let{key:t}=e;return t===`${oe.o6}${i}`}));if(!e)return u(null),m(null),f(`Error loading shared view state: share key ${i} does not exist`),void _(s.formatMessage({id:"s2L+xL",defaultMessage:'Error loading shared view state: share key "{viewStateShareKey}" does not exist'},{viewStateShareKey:i}));(async e=>{try{const t=await(async e=>(0,ie.ib)(e)?JSON.parse(await(0,ie.U1)(e)):JSON.parse(e))(e.value),n=(0,j.pick)(t,$t.sO),a=(0,j.pick)(t,Ta.HC);u(n),m(a),f(null),_(null)}catch(t){u(null),m(null),f("Error loading shared view state: share key is invalid"),_(s.formatMessage({id:"Isig6r",defaultMessage:"Error loading shared view state: share key is invalid"}))}})(e)}),[t,i,s]),(0,r.useEffect)((()=>{c&&!n&&d(c,{replace:!0})}),[c,n]),(0,r.useEffect)((()=>{p&&!n&&e(p)}),[e,p,n]);const x=(0,o.Zp)();return(0,r.useEffect)((()=>{n||g&&t&&(T.A.logErrorAndNotifyUser(new Error(g)),T.A.displayGlobalErrorNotification(v,3),x(h.h.getExperimentPageRoute(t.experimentId),{replace:!0}))}),[g,v,t,x,n]),{isViewStateShared:l,sharedStateError:g}},xr=[(e,t,n)=>{const a=n.runInfos.filter(((e,t)=>{var a,r;return(null===(a=n.tagsList[t])||void 0===a||null===(r=a[oe.yU])||void 0===r?void 0:r.value)===oe.m4})).map((e=>{let{runUuid:t}=e;return t})),r=(0,j.compact)(n.runInfos.map(((e,t)=>{var r;let{runUuid:s}=e;return a.includes(s)&&(null===(r=n.tagsList[t])||void 0===r?void 0:r[_e.Ol].value)})));return r.length?{...t,runsExpanded:r.reduce(((e,t)=>({...e,[t]:!0})),t.runsExpanded)}:t}],yr=(0,Ta.uY)();var wr=n(56928),br=n(75703),Yr=n(11473);const Cr=[["header","bold","italic","strikethrough"],["link","code","image"],["unordered-list","ordered-list"]],Ir=(0,Yr.OT)(),Ar=e=>{if(e){const t=(0,Yr.NW)(Ir.makeHtml(e));return(0,Yr.Yc)(t)}return null},kr=e=>{let{experiment:t,editing:n,setEditing:o,setShowAddDescriptionButton:l,onNoteUpdated:d,defaultValue:m}=e;const g=(0,s.d4)((e=>{const n=(0,A.xy)(t.experimentId,e);return n?(e=>{var t;return(null===(t=Object.values(e).find((e=>e.key===wr.e)))||void 0===t?void 0:t.value)||void 0})(n):""}));l(!g);const[h,f]=(0,r.useState)(g),[v,_]=(0,r.useState)("write"),[x,w]=(0,r.useState)(!1),{theme:b}=(0,p.u)(),Y=16+2*b.spacing.sm,C=(0,s.wA)(),I=(0,r.useCallback)((e=>{o(!1),l(!e);const n=(0,i.EJ)(t.experimentId,wr.e,e);C(n).then(d)}),[t.experimentId,C,o,l,d]);return(0,y.FD)("div",{children:[(null!==h&&void 0!==h?h:m)&&(0,y.FD)("div",{style:{whiteSpace:x?"normal":"pre-wrap",lineHeight:b.typography.lineHeightLg,background:b.colors.backgroundSecondary,display:"flex",alignItems:"flex-start",padding:b.spacing.xs},children:[(0,y.Y)("div",{style:{flexGrow:1,marginRight:12,overflow:"hidden",overflowWrap:x?"break-word":void 0,padding:`${b.spacing.sm}px 12px`,maxHeight:x?"none":Y+"px",wordBreak:"break-word"},children:(0,y.Y)("div",{dangerouslySetInnerHTML:{__html:Ar(null!==h&&void 0!==h?h:m)}})}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_114",icon:(0,y.Y)(c.R2l,{}),onClick:()=>o(!0),style:{padding:`0px ${b.spacing.sm}px`}}),x?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_120",icon:(0,y.Y)(c.Mtm,{}),onClick:()=>w(!1),style:{padding:`0px ${b.spacing.sm}px`}}):(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_126",icon:(0,y.Y)(c.D3D,{}),onClick:()=>w(!0),style:{padding:`0px ${b.spacing.sm}px`}})]}),(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_141",title:(0,y.Y)(ee.A,{id:"Vkr4Bs",defaultMessage:"Add description"}),visible:n,okText:(0,y.Y)(ee.A,{id:"xUnYdk",defaultMessage:"Save"}),cancelText:(0,y.Y)(ee.A,{id:"757GVc",defaultMessage:"Cancel"}),onOk:()=>{I(h),o(!1)},onCancel:()=>{f(g),o(!1)},children:(0,y.Y)(r.Fragment,{children:(0,y.Y)(br.default,{value:h,minEditorHeight:200,maxEditorHeight:500,minPreviewHeight:20,toolbarCommands:Cr,onChange:e=>f(e),selectedTab:v,onTabChange:e=>_(e),generateMarkdownPreview:()=>Promise.resolve(Ar(h)),getIcon:e=>(0,y.Y)(c.paO,{title:e,children:(0,y.Y)("span",{css:(0,a.AH)({color:b.colors.textPrimary},""),children:(0,y.Y)(br.SvgIcon,{icon:e})})})})})})]})},Sr=e=>{let{experiment:t,size:n}=e;const a=(0,Be.A)();return(0,y.Y)(p.T.Text,{size:n,dangerouslySetAntdProps:{copyable:{text:t.name,icon:(0,y.Y)(c.TdU,{}),tooltips:[a.formatMessage({id:"A7R0ii",defaultMessage:"Copy path"}),a.formatMessage({id:"JCboZ7",defaultMessage:"Path copied"})]}}})},Er=e=>{let{artifactLocation:t}=e;return(0,y.Y)(y.FK,{children:t})},Mr=e=>{let{experiment:t}=e;const n=(0,Be.A)();return(0,y.Y)(p.T.Text,{size:"md",dangerouslySetAntdProps:{copyable:{text:t.experimentId,icon:(0,y.Y)(c.TdU,{}),tooltips:[n.formatMessage({id:"rytnce",defaultMessage:"Copy experiment id"}),n.formatMessage({id:"qH2cN+",defaultMessage:"Experiment id copied"})]}}})},Rr=e=>{let{experiment:t}=e;const n=(0,Be.A)();return(0,y.Y)(p.T.Text,{size:"md",dangerouslySetAntdProps:{copyable:{text:t.artifactLocation,icon:(0,y.Y)(c.TdU,{}),tooltips:[n.formatMessage({id:"cSSMIs",defaultMessage:"Copy artifact location"}),n.formatMessage({id:"5aZ7nE",defaultMessage:"Artifact location copied"})]}}})};const Tr=r.memo((e=>{let{experiment:t,searchFacetsState:n,uiState:s,showAddDescriptionButton:i,setEditing:o}=e;const l=(0,r.useMemo)((()=>t?[null===t||void 0===t?void 0:t.experimentId]:[]),[t]),{theme:d}=(0,p.u)(),u=(0,r.useMemo)((()=>t.name.split("/").pop()),[t.name]),m=oe.Cm;return(0,y.Y)(te.z,{title:(0,y.Y)("div",{css:(0,a.AH)({[d.responsive.mediaQueries.xs]:{display:"inline",wordBreak:"break-all"},[d.responsive.mediaQueries.sm]:{display:"inline-block",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"middle"}},""),title:u,children:u}),titleAddOns:[(0,y.Y)("div",{style:{display:"flex"},children:(0,y.Y)(c.UyZ,{iconTitle:"Info",children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",flexDirection:"column",gap:d.spacing.xs,flexWrap:"nowrap"},""),"data-testid":"experiment-view-header-info-tooltip-content",children:[(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(ee.A,{id:"F8MqzZ",defaultMessage:"Path"}),": ",t.name+" ",(0,y.Y)(Sr,{experiment:t,size:"md"})]}),(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(ee.A,{id:"PZGZHV",defaultMessage:"Experiment ID"}),": ",t.experimentId+" ",(0,y.Y)(Mr,{experiment:t})]}),(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(ee.A,{id:"8/7V3S",defaultMessage:"Artifact Location"}),": ",(0,y.Y)(Er,{artifactLocation:t.artifactLocation})," ",(0,y.Y)(Rr,{experiment:t})]})]})})}),(0,y.Y)(p.B,{href:m,target:"_blank",rel:"noreferrer",componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheaderv2.tsx_100",css:(0,a.AH)({marginLeft:d.spacing.sm},""),type:"link",size:"small",endIcon:(0,y.Y)(p.av,{}),children:(0,y.Y)(ee.A,{id:"d78wwA",defaultMessage:"Provide Feedback"})}),i&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheaderv2.tsx_271",size:"small",onClick:()=>{o(!0)},css:(0,a.AH)({marginLeft:d.spacing.sm,background:`${d.colors.backgroundSecondary} !important`,border:"none"},""),children:(0,y.Y)(p.T.Text,{size:"md",children:"Add Description"})})].filter(Boolean),breadcrumbs:[],spacerSize:"sm",dangerouslyAppendEmotionCSS:{[d.responsive.mediaQueries.sm]:{"& > div":{flexWrap:"nowrap"},h2:{display:"flex",overflow:"hidden"}}},children:(0,y.Y)("div",{css:(0,a.AH)({display:"flex",gap:d.spacing.sm},""),children:(0,y.Y)(fe,{experimentIds:l,searchFacetsState:n,uiState:s})})})}));var Dr={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};function Fr(){const{theme:e}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({height:2*e.general.heightSm},""),children:[(0,y.Y)("div",{css:(0,a.AH)({height:e.spacing.lg},""),children:(0,y.Y)(c.xUE,{css:(0,a.AH)({width:100,height:e.spacing.md},""),loading:!0})}),(0,y.FD)("div",{css:Dr,children:[(0,y.Y)("div",{children:(0,y.Y)(c.xUE,{css:(0,a.AH)({width:160,height:e.general.heightSm},""),loading:!0})}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:e.spacing.sm},""),children:[(0,y.Y)(c.xUE,{css:(0,a.AH)({width:100,height:e.general.heightSm},""),loading:!0}),(0,y.Y)(c.xUE,{css:(0,a.AH)({width:60,height:e.general.heightSm},""),loading:!0})]})]})]})}var Lr=n(91089);const Br=e=>{let{experimentIds:t}=e;const{theme:n}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({minHeight:225,marginTop:n.spacing.sm,display:"flex",flexDirection:"column",gap:n.spacing.sm,flex:1,overflow:"hidden"},""),children:[(0,y.Y)(mn,{hideBorder:!1}),(0,y.Y)(Lr.O,{experimentIds:t})]})};var Hr={name:"1h8i7is",styles:"overflow-y:hidden;flex-shrink:0;transition:max-height .12s"};const Pr=()=>{const e=(0,s.wA)(),[t,n,a]=(0,$t.sR)(),[o]=pe(),l=(d=n,(0,s.d4)((e=>d.map((t=>e.entities.experimentsById[t])).filter(Boolean)),((e,t)=>(0,j.isEqual)(e,t))));var d;const[u]=l,{fetchExperiments:p,isLoadingExperiment:m,requestError:g}=(()=>{const e=(0,r.useContext)(q);if(!e)throw new Error("Trying to use GetExperimentsContext actions outside of the context!");return e})(),{elementHeight:h,observeHeight:f}=(e=>{const[t,n]=(0,r.useState)(null),[a,s]=(0,r.useState)(void 0);return(0,r.useEffect)((()=>{if(!t||!window.ResizeObserver)return;const n=new ResizeObserver((t=>{let[n]=t;null===e||void 0===e||e(n),n.target.scrollHeight&&s(n.target.scrollHeight)}));return n.observe(t),()=>n.disconnect()}),[t,e]),{elementHeight:a,observeHeight:n}})(),[v,_]=(0,r.useState)(!1),[x,w]=(0,r.useState)(!0),[b,Y,C]=(e=>{const t=(0,r.useMemo)((()=>JSON.stringify(e.sort())),[e]),[{uiState:n,isFirstVisit:a},s]=((0,$t.Px)(),(0,r.useReducer)(((e,t)=>{if("UPDATE_UI_STATE"===t.type){const n="function"===typeof t.payload?t.payload(e.uiState):t.payload;return{...e,uiState:n}}return"INITIAL_UI_STATE_SEEDED"===t.type?e.isFirstVisit?{...e,isFirstVisit:!1}:e:"LOAD_NEW_EXPERIMENT"===t.type?{uiState:t.payload.uiState,isFirstVisit:t.payload.isFirstVisit,currentPersistKey:t.payload.newPersistKey}:e}),void 0,(()=>{const e=dr(t),n=(0,j.keys)(e||{}).length,a=n?(0,j.pick)(e,Ta.HC):{};return{uiState:{...yr,...a},isFirstVisit:!n,currentPersistKey:t}}))),i=(0,r.useCallback)((e=>{s({type:"UPDATE_UI_STATE",payload:e})}),[]),o=(0,r.useCallback)(((e,t)=>{a&&0!==e.length&&0!==t.runInfos.length&&(s({type:"INITIAL_UI_STATE_SEEDED"}),i((n=>xr.reduce(((n,a)=>a(e,n,t)),{...n}))))}),[a,i]);return(0,r.useEffect)((()=>{const e=dr(t),n=(0,j.pick)(e,Ta.HC),a=!(0,j.keys)(e||{}).length;s({type:"LOAD_NEW_EXPERIMENT",payload:{uiState:{...yr,...n},isFirstVisit:a,newPersistKey:t}})}),[t]),[n,i,o]})(n),{isViewStateShared:I}=_r(Y,(0,j.first)(l)),A=b.viewMaximized,{isLoadingRuns:k,loadMoreRuns:S,runsData:E,moreRunsAvailable:M,requestError:R,refreshRuns:D}=fr(b,t,n);(0,r.useEffect)((()=>{((0,le.Dz)()||(0,le.$Y)())&&n.every((e=>l.find((t=>t.experimentId===e))))||p(n)}),[p,n,l]),(0,r.useEffect)((()=>{C(l,E)}),[C,l,E]),(0,r.useEffect)((()=>{const t=(0,i.wT)(n);e(t).catch((e=>{T.A.logErrorAndNotifyUser(e)}))}),[e,n]);const F=n.length>1;cr(b,t,n,I||a);if(!Boolean(!m&&l[0]&&E&&t))return(0,y.Y)(c.PLz,{});if(g&&g.getErrorCode()===Q.tG.PERMISSION_DENIED)return(0,y.Y)(X,{errorMessage:g.getMessageField()});if(g&&g.getErrorCode()===Q.tG.RESOURCE_DOES_NOT_EXIST)return(0,y.Y)(J.A,{});rt()(t,"searchFacets should be initialized at this point");const L=m||!l[0];return(0,y.Y)(qt.i,{setUIState:Y,children:(0,y.FD)("div",{css:Nr.experimentViewWrapper,children:[L?(0,y.Y)(c.PLz,{title:!0,paragraph:!1,active:!0}):(0,y.Y)(y.FK,{children:F?(0,y.Y)(ve,{experiments:l}):(0,y.FD)(y.FK,{children:[(0,y.Y)(Tr,{experiment:u,searchFacetsState:t||void 0,uiState:b,showAddDescriptionButton:x,setEditing:_}),(0,y.Y)("div",{style:{maxHeight:A?0:h},css:Hr,children:(0,y.Y)("div",{ref:f,children:(0,y.Y)(kr,{experiment:u,setShowAddDescriptionButton:w,editing:v,setEditing:_})})})]})}),(0,le.XK)()&&"TRACES"===o?(0,y.Y)(Br,{experimentIds:n}):(0,y.Y)(lr,{isLoading:!1,experiments:l,isLoadingRuns:k,runsData:E,searchFacetsState:t,loadMoreRuns:S,moreRunsAvailable:M,requestError:R,refreshRuns:D,uiState:b})]})})},Nr={experimentViewWrapper:{height:"100%",display:"flex",flexDirection:"column"}};var Or=n.p+"static/media/no-experiments.0e4f4a114ef73e7d81c09474aba64b6c.svg";const Ur=function(){const{theme:e}=(0,p.u)();return(0,y.FD)("div",{className:"center",children:[(0,y.Y)("img",{alt:"No experiments found.",style:{height:"200px",marginTop:"80px"},src:Or}),(0,y.Y)("h1",{style:{paddingTop:"10px"},children:"No Experiments Exist"}),(0,y.FD)("h2",{css:(0,a.AH)({color:e.colors.textSecondary},""),children:["To create an experiment use the ",(0,y.Y)("a",{href:Q.d1,children:"mlflow experiments"})," CLI."]})]})};var Kr=n(39416);const Vr=e=>{let{experimentId:t="",activeTab:n}=e;const{theme:r}=(0,p.u)(),s=e=>[h.h.getExperimentPageRoute(t),[de,e].join("=")].join("?");return(0,y.Y)(c.KSe.Root,{children:(0,y.FD)(c.KSe.List,{css:(0,a.AH)({marginBottom:0,li:{lineHeight:r.typography.lineHeightBase,marginRight:r.spacing.lg,paddingTop:1.5*r.spacing.xs,paddingBottom:1.5*r.spacing.xs,"&>a":{padding:0},alignItems:"center"},"li+li":{marginLeft:.5*r.spacing.xs}},""),children:[(0,le.Dz)()&&(0,y.Y)(c.KSe.Item,{active:n===oe.fM.Models,children:(0,y.FD)(o.N_,{to:h.h.getExperimentPageTabRoute(t,oe.fM.Models),children:[(0,y.Y)(ee.A,{id:"b8rdDM",defaultMessage:"Models"}),(0,y.Y)(nt.W,{})]})},"MODELS"),(0,y.Y)(c.KSe.Item,{children:(0,y.Y)(o.N_,{to:s("TABLE"),children:(0,y.Y)(ee.A,{id:"qpFaad",defaultMessage:"Runs"})})},"RUNS"),(0,y.Y)(c.KSe.Item,{children:(0,y.FD)(o.N_,{to:s("ARTIFACT"),children:[(0,y.Y)(ee.A,{id:"SCqKp8",defaultMessage:"Evaluation"}),(0,y.Y)(nt.W,{})]})},"ARTIFACT"),(0,le.XK)()&&(0,y.Y)(c.KSe.Item,{children:(0,y.Y)(o.N_,{to:s("TRACES"),children:(0,y.Y)(ee.A,{id:"AGQOPV",defaultMessage:"Traces"})})},"TRACES")]})})};var zr=n(72314);var Gr=n(26626);const $r=e=>{var t,n;let{experiment:s,loading:i,onNoteUpdated:o,error:l}=e;const{theme:d}=(0,p.u)(),[u,m]=(0,r.useState)(!0),[g,h]=(0,r.useState)(!1),f=(0,r.useMemo)((()=>{const e=s;return e?{...e,creationTime:Number(null===e||void 0===e?void 0:e.creationTime),lastUpdateTime:Number(null===e||void 0===e?void 0:e.lastUpdateTime)}:null}),[s]),v=null===f||void 0===f||null===(t=f.tags)||void 0===t||null===(n=t.find((e=>e.key===wr.e)))||void 0===n?void 0:n.value,_=(0,Gr.b)(l);return i?(0,y.Y)(Fr,{}):_?(0,y.Y)("div",{css:(0,a.AH)({height:d.general.heightBase,marginTop:d.spacing.sm,marginBottom:d.spacing.md},""),children:(0,y.Y)(c.FcD,{componentId:"mlflow.logged_model.list.header.error",type:"error",message:(0,y.Y)(ee.A,{id:"E4Te7L",defaultMessage:"Experiment load error: {errorMessage}",values:{errorMessage:_}}),closable:!1})}):f?(0,y.FD)(y.FK,{children:[(0,y.Y)(Tr,{experiment:f,showAddDescriptionButton:u,setEditing:h}),(0,y.Y)(kr,{experiment:f,setShowAddDescriptionButton:m,editing:g,setEditing:h,onNoteUpdated:o,defaultValue:v})]}):null};var jr=n(26571);const qr=r.lazy((()=>Promise.all([n.e(643),n.e(1570),n.e(3122),n.e(7951),n.e(2708)]).then(n.bind(n,42708)))),Wr=()=>{const{experimentId:e,tabName:t}=(0,o.g)(),n=(0,jr.S)(oe.fM,t,oe.fM.Models);rt()(e,"Experiment ID must be defined"),rt()(t,"Tab name must be defined");const{data:a,loading:d,refetch:p,apiError:m,apolloError:g}=(0,zr.L)({experimentId:e}),h=null!==m&&void 0!==m?m:g;if((e=>{const t=(0,s.wA)();(0,r.useEffect)((()=>{const n=(0,j.get)(e,"experimentId");e&&n&&t(((t,a)=>{var r,s;Boolean(null===(r=a().entities)||void 0===r||null===(s=r.experimentsById)||void 0===s?void 0:s[n])||t({type:(0,l.ec)(i.gT),payload:{experiment:e}})}))}),[e,t])})(a),h instanceof Kr.ZR)throw h;return(0,y.FD)(y.FK,{children:[(0,y.Y)($r,{experiment:a,loading:d,onNoteUpdated:p,error:h}),(0,y.Y)(u.S,{size:"sm",shrinks:!1}),(0,y.Y)(Vr,{experimentId:e,activeTab:n}),(0,y.Y)(u.S,{size:"sm",shrinks:!1}),(0,y.Y)(r.Suspense,{fallback:(0,y.Y)(c.QvX,{lines:8}),children:n===oe.fM.Models&&(0,y.Y)(qr,{})})]})};var Qr=()=>{const{theme:e}=(0,p.u)();return(0,y.Y)("div",{css:(0,a.AH)({flex:1,overflow:"hidden",display:"flex",flexDirection:"column",padding:e.spacing.md,paddingTop:e.spacing.lg},""),children:(0,y.Y)(Wr,{})})};const Jr={setExperimentTagApi:i.EJ,getExperimentApi:i.yc,setCompareExperiments:i.I_};var Zr={name:"1ichkjj",styles:"height:100%;display:flex;justify-content:center;align-items:center"},Xr={name:"1s36c6h",styles:"display:flex;height:calc(100% - 60px)"},es={name:"tby1uk",styles:"height:100%;padding-top:24px;display:flex"};var ts=()=>{const e=(0,s.wA)(),{theme:t}=(0,p.u)(),n=(0,r.useRef)((0,l.yk)()),{tabName:c}=(0,o.g)(),u=(0,le.Dz)()&&Boolean(c),m=(0,$.z)(),g=(0,s.d4)((e=>(0,j.values)(e.entities.experimentsById))),f=g.length>0;if((0,r.useEffect)((()=>{e((0,i.vF)(n.current))}),[e]),!m.length){const e=(e=>[...e].sort(T.A.compareExperiments).find((e=>{let{lifecycleStage:t}=e;return"active"===t})))(g);if(e)return(0,y.Y)(o.C5,{to:h.h.getExperimentPageRoute(e.experimentId),replace:!0})}const v=(0,y.Y)("div",{css:Zr,children:(0,y.Y)(p.H,{size:"large"})});return(0,y.Y)(d.Ay,{requestIds:[n.current],customSpinner:v,children:(0,y.FD)("div",{css:Xr,children:[(0,y.Y)("div",{css:es,children:(0,y.Y)(G,{activeExperimentIds:m||[],experiments:g})}),u&&(0,y.Y)(Qr,{}),!u&&(0,y.Y)("div",{css:(0,a.AH)({height:"100%",flex:1,padding:t.spacing.md,paddingTop:t.spacing.lg,minWidth:0},""),children:(0,y.Y)(W,{actions:Jr,children:f?(0,y.Y)(Pr,{}):(0,y.Y)(Ur,{})})})]})})}}}]);
//# sourceMappingURL=experimentPage.1e041e70.chunk.js.map