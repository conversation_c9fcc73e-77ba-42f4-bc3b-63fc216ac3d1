{"version": 3, "file": "static/js/225.7bb7f04f.chunk.js", "mappings": "0OAgBA,IAAIA,EAA+B,KAE5B,MAAMC,EAAuBA,IAE9BD,IAGJA,EAAa,IAAIE,EAAAA,UACjBF,EAAWG,UAVmB,UAWvBH,GAQHI,EAAmB,CACvBC,YAAa,CACX,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,aACA,IACA,IACA,KACA,KACA,KACA,KACA,MACA,IACA,IACA,SACA,KACA,SACA,OACA,KACA,KACA,MACA,QACA,QACA,QACA,KACA,KACA,KACA,MACA,MACA,MACA,MACA,KACA,KACA,KACA,MACA,IACA,OACA,OACA,MACA,KACA,KACA,KACA,UACA,SACA,MACA,UACA,UAEFC,kBAAmB,CACjBC,EAAG,CAAC,OAAQ,OAAQ,UACpBC,IAAK,CAAC,MAAO,YACbC,IAAK,CAAC,YAAa,cAIVC,EAAyBC,GAC7BC,IAAaD,EAAWP,GAGpBS,EAAwBC,GAC5BA,EAAKC,QAAQ,IAAIC,OAAO,KAAM,KAAM,qB,2FC7EtC,MAAMC,EAOTC,GAGAC,IASA,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACXC,GAASC,EAAAA,EAAAA,KAEf,OACEC,EAAAA,EAAAA,GAACR,EACC,CACAM,OAAQA,EACRJ,SAAUA,EACVE,SAAUA,KACLH,GACL,C,6FCpCD,MAAMQ,UAA6BC,EAAAA,UAA8BC,WAAAA,GAAA,SAAAC,WAAA,KACtEC,MAAQ,CAAEC,MAAO,KAAO,CAExBC,iBAAAA,CAAkBD,EAAYE,GAC5BC,KAAKC,SAAS,CAAEJ,UAEhBK,QAAQL,MAAMA,EAAOE,EACvB,CAEAI,kBAAAA,CAAmBN,GACjB,OAAOG,KAAKhB,MAAMoB,iBAAkBC,EAAAA,EAAAA,IAAA,OAAAC,SAAA,CAAK,kBAAgBT,EAAMU,WAAiB,EAClF,CAEAC,MAAAA,GACE,MAAM,SAAEF,GAAaN,KAAKhB,OACpB,MAAEa,GAAUG,KAAKJ,MACvB,OAAIC,GAEAN,EAAAA,EAAAA,GAAA,OAAAe,UACED,EAAAA,EAAAA,IAAA,KAAAC,SAAA,EACEf,EAAAA,EAAAA,GAAA,KAAG,cAAY,YAAYkB,UAAU,uCAAuCC,IAAKC,EAAWC,WAC5FrB,EAAAA,EAAAA,GAAA,QAAAe,SAAM,+CACNf,EAAAA,EAAAA,GAAA,QAAAe,SAAM,qDAGNf,EAAAA,EAAAA,GAAA,KAAGsB,KAAMC,EAAAA,EAAMC,oBAAqBC,OAAO,SAAQV,SAAC,SAEhD,IACFN,KAAKG,mBAAmBN,QAM3BS,CACT,EAGF,MAAMK,EAAa,CACjBC,QAAS,CACPK,YAAa,G,6HCpDV,MAAMC,EAAeC,IAA4C,IAA3C,UAAEV,GAAmCU,EAChE,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAClB,OACE9B,EAAAA,EAAAA,GAAC+B,EAAAA,IAAG,CACFC,YAAY,oEACZd,UAAWA,EACXC,KAAGc,EAAAA,EAAAA,IAAE,CAAEP,WAAYG,EAAMK,QAAQC,IAAI,IACrCC,MAAM,YAAWrB,UAEjBf,EAAAA,EAAAA,GAACqC,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGb,C,6uQCsBH,MAAMC,UAA4BhD,EAAAA,UAA8DW,WAAAA,GAAA,SAAAC,WAAA,KAOrGC,MAAQ,CACNoC,cAAc,EACdC,mBAAmB,EACnB,CAEF,uBAAOC,CAAiBC,EAAeC,GACrC,OAAOD,EAASE,QAAQC,QACNC,IAAZD,EAAEzC,SAEFuC,GACAA,EAA2BI,SAASF,EAAET,KACtCS,EAAEzC,MAAM4C,iBAAmBC,EAAAA,GAAWC,0BAK9C,CAEA,+BAAOC,CAAyBC,GAC9B,MAAMb,IAAea,EAAUV,SAASW,QACpCD,EAAUV,SAASY,OAAOT,GAAWA,IAAkB,IAAbA,EAAEU,SAG1CC,EAAgBlB,EAAoBG,iBACxCW,EAAUV,SACVU,EAAUT,4BAGZ,MAAO,CACLJ,eACAC,kBAAmBgB,EAAcH,OAAS,EAC1CG,gBAEJ,CAEAC,kBAAAA,GACE,MAAM,SAAE5C,EAAQ,SAAE6B,EAAQ,cAAEgB,EAAa,qBAAEC,EAAoB,mBAAEC,EAAkB,4BAAEC,GACnFtD,KAAKhB,OAED,aAAEgD,EAAY,kBAAEC,EAAiB,cAAEgB,GAAkBjD,KAAKJ,MAC1D2D,EAAyBN,EAAcZ,QAAQmB,GAC5CA,EAAc3D,MAAM4C,iBAAmBC,EAAAA,GAAWe,oBAG3D,MAAwB,oBAAbnD,EACFA,GAAU0B,EAAcC,EAAmBE,EAAUc,GACnDjB,GAAgBC,GAAqBjC,KAAKhB,MAAM0E,2BACrDH,EAAuBT,OAAS,GAAKM,EAChCA,GAELnB,IAAsBoB,IACxBC,EAA8BA,EAA4BL,GAAiBU,EAAaV,IAGnF3C,GAGF6C,IAAiB5D,EAAAA,EAAAA,GAACqE,EAAAA,EAAO,GAClC,CAEApD,MAAAA,GACE,OAAOR,KAAKkD,oBACd,EArEWnB,EACJ8B,aAAe,CACpB1B,SAAU,GACVC,2BAA4B,GAC5BsB,4BAA4B,GAoEzB,MAAMC,EAAgBxB,IAI3B,MADAjC,QAAQL,MAAM,QAASsC,GACjB2B,MAAM,8BAA6B3B,EAAStC,QAAQ,EAO5D,OAAekE,EAAAA,EAAAA,KAJSC,CAACpE,EAAmBqE,KAAoD,CAC9F9B,UAAU+B,EAAAA,EAAAA,IAAQD,EAASE,WAAYvE,MAGzC,CAAwCmC,E,0KC1FjC,SAASqC,EAAYjD,GAA+B,IAA9B,KAAEkD,GAAyBlD,EACtD,MAAMmD,GACJ/E,EAAAA,EAAAA,GAACgF,EAAAA,IAAI,CAAAjE,SAEF+D,EAAKG,KAAIC,IAAA,IAAC,GAAE5C,EAAE,SAAE6C,EAAQ,QAAEC,EAAO,KAAE9D,KAAS+D,GAAYH,EAAA,OAEvDlF,EAAAA,EAAAA,GAACgF,EAAAA,IAAKM,KAAI,CAAUF,QAASA,EAAS9D,KAAMA,EAAM,eAAcgB,KAAQ+C,EAAUtE,SAC/EoE,GADa7C,EAEJ,MAMlB,OAAOwC,EAAKvB,OAAS,GACnBvD,EAAAA,EAAAA,GAACuF,EAAAA,IAAQ,CAACC,QAAST,EAAcU,QAAS,CAAC,SAAUC,UAAU,aAAaC,OAAK,EAAA5E,UAC/Ef,EAAAA,EAAAA,GAAC4F,EAAAA,EAAM,CACL5D,YAAY,kEACZ6D,MAAM7F,EAAAA,EAAAA,GAAC8F,EAAAA,IAAY,IACnB,eAAa,wBACb,aAAW,gCAGb,IACN,CAAC,IAAAC,EAAA,CAAAC,KAAA,UAAAC,OAAA,iBAsBM,SAASC,EAAWzG,GACzB,MAAM,MACJ0G,EAAK,YACLC,EAAc,GAAE,YAChBC,EAAc,GAAE,QAChBC,EAAO,SACPvF,EAAQ,WACRwF,EAAU,WACVC,GAAa,EAAK,4BAClBC,GACEhH,GACE,MAAEoC,IAAUC,EAAAA,EAAAA,MACL4E,EAAAA,EAAAA,KAEb,OACE5F,EAAAA,EAAAA,IAAA6F,EAAAA,GAAA,CAAA5F,SAAA,EACEf,EAAAA,EAAAA,GAAC4G,EAAAA,IAAM,CACLR,YACEA,EAAY7C,OAAS,IACnBvD,EAAAA,EAAAA,GAAC6G,EAAAA,IAAU,CAACC,sBAAoB,EAAA/F,SAC7BqF,EAAYnB,KAAI,CAAC8B,EAAGC,KACnBhH,EAAAA,EAAAA,GAAC6G,EAAAA,IAAWvB,KAAI,CAAAvE,SAAUgG,GAAJC,OAK9BC,QAASlG,EACToF,MAAOA,EAEPE,aACEvF,EAAAA,EAAAA,IAAA6F,EAAAA,GAAA,CAAA5F,SAAA,CACGuF,IAAWtG,EAAAA,EAAAA,GAAC2B,EAAAA,EAAY,CAACR,IAAG4E,IAC5BM,KAGLI,4BAA6BA,KAE/BzG,EAAAA,EAAAA,GAACkH,EAAAA,EACC,CACA/F,KAAGc,EAAAA,EAAAA,IAAE,CAEHkF,WAAY,KACRX,EAAa,CAAEY,QAAS,QAAW,CAAC,GACzC,IACDC,KAAMd,MAId,C,weC1HO,MAAMe,EAAS,CACpBC,KAAM,OACNC,QAAS,UACTC,WAAY,aACZC,SAAU,YAGCC,EAAgB,CAACL,EAAOE,QAASF,EAAOG,YAExCG,EAAc,CACzB,CAACN,EAAOC,MAAO,OACf,CAACD,EAAOE,SAAU,UAClB,CAACF,EAAOG,YAAa,aACrB,CAACH,EAAOI,UAAW,YAGRG,EAAqB,CAChC,CAACP,EAAOC,OACNvH,EAAAA,EAAAA,GAAC+B,EAAAA,IAAG,CAACC,YAAY,yDAAwDjB,SAAE6G,EAAYN,EAAOC,QAEhG,CAACD,EAAOE,UACNxH,EAAAA,EAAAA,GAAC+B,EAAAA,IAAG,CAACC,YAAY,yDAAyDI,MAAM,QAAOrB,SACpF6G,EAAYN,EAAOE,WAGxB,CAACF,EAAOG,aACNzH,EAAAA,EAAAA,GAAC+B,EAAAA,IAAG,CAACC,YAAY,yDAAyDI,MAAM,OAAMrB,SACnF6G,EAAYN,EAAOG,cAGxB,CAACH,EAAOI,WACN1H,EAAAA,EAAAA,GAAC+B,EAAAA,IAAG,CAACC,YAAY,yDAAyDI,MAAM,WAAUrB,SACvF6G,EAAYN,EAAOI,aAiBnB,IAAKI,EAAa,SAAbA,GAAa,OAAbA,EAAa,wCAAbA,EAAa,4CAAbA,EAAa,sCAAbA,EAAa,sCAAbA,EAAa,oCAAbA,EAAa,oCAAbA,EAAa,0BAAbA,CAAa,OAea9H,EAAAA,EAAAA,GAAA,OAAK+H,MAAO,CAAEC,WAAY,IAAKjH,SAAC,MAA/D,MAEMkH,EAAqB,CAChCC,MAAO,SAGIC,EAAoC,CAC/C,CAACF,EAAmBC,QAClBlI,EAAAA,EAAAA,GAACqC,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,YAIxB6F,EAAiC,CAC5C,CAACH,EAAmBC,QAClBlI,EAAAA,EAAAA,GAACqC,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAMR8F,EAA0B,CACrC,CAACJ,EAAmBC,QAAQlI,EAAAA,EAAAA,GAACsI,EAAAA,EAAS,KAG3BC,EAAqC,IAOrCC,EAAqC,GAErCC,EAA2C,GAE3CC,EAAsC,OAEtCC,EAA2C,YAE3CC,EAAqB,CAChCC,IAAK,SACLC,KAAM,WAGKC,EAAqCC,IAChDhJ,EAAAA,EAAAA,GAACqC,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sFAIf0G,OAAQ,CAAED,aAAcA,KAIfE,EACX,mF,6FCrHK,SAAS7E,EAAOzC,GAA8B,IAA7B,gBAAEuH,GAAwBvH,EAChD,OACE5B,EAAAA,EAAAA,GAAA,OAAKmB,IAAMU,GAAUoE,EAAOmD,QAAQvH,EAAOsH,GAAiBpI,UAC1Df,EAAAA,EAAAA,GAAA,OAAKqJ,IAAI,kBAAkBC,IAAKF,KAGtC,CAEA,MAAMnD,EAAS,CACbmD,QAASA,CAACvH,EAAc0H,KAAmB,CACzCC,MAAO,IACPxB,UAAW,IACXtG,WAAY,OACZ+H,YAAa,OAEb3K,IAAK,CACH4K,SAAU,WACVC,QAAS,EACTC,IAAK,MACLC,KAAM,MACNL,MAAkC,EAA3B3H,EAAMiI,QAAQC,WACrBC,OAAmC,EAA3BnI,EAAMiI,QAAQC,WACtB/B,WAAYnG,EAAMiI,QAAQC,WAC1BrI,YAAaG,EAAMiI,QAAQC,WAC3BE,UAAW,GAAGC,EAAAA,EAAS;;;;;;;;;iCAUvBC,eAAgBZ,EAAY,KAAO,U", "sources": ["common/utils/MarkdownUtils.ts", "common/utils/withRouterNext.tsx", "common/components/error-boundaries/SectionErrorBoundary.tsx", "shared/building_blocks/PreviewBadge.tsx", "common/components/RequestStateWrapper.tsx", "shared/building_blocks/PageHeader.tsx", "model-registry/constants.tsx", "common/components/Spinner.tsx"], "sourcesContent": ["/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport { useCallback } from 'react';\nimport sanitizeHtml from 'sanitize-html';\n// @ts-expect-error TS(7016): Could not find a declaration file for module 'show... Remove this comment to see the full error message\nimport { Converter } from 'showdown';\n\n// Use Github-like Markdown (i.e. support for tasklists, strikethrough,\n// simple line breaks, code blocks, emojis)\nconst DEFAULT_MARKDOWN_FLAVOR = 'github';\n\nlet _converter: Converter | null = null;\n\nexport const getMarkdownConverter = () => {\n  // Reuse the same converter instance if available\n  if (_converter) {\n    return _converter;\n  }\n  _converter = new Converter();\n  _converter.setFlavor(DEFAULT_MARKDOWN_FLAVOR);\n  return _converter;\n};\n\n// Options for HTML sanitizer.\n// See https://www.npmjs.com/package/sanitize-html#what-are-the-default-options for usage.\n// These options were chosen to be similar to Github's allowlist but simpler (i.e. we don't\n// do any transforms of the contained HTML and we disallow script entirely instead of\n// removing contents).\nconst sanitizerOptions = {\n  allowedTags: [\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'h7',\n    'h8',\n    'blockquote',\n    'p',\n    'a',\n    'ul',\n    'ol',\n    'nl',\n    'li',\n    'ins',\n    'b',\n    'i',\n    'strong',\n    'em',\n    'strike',\n    'code',\n    'hr',\n    'br',\n    'div',\n    'table',\n    'thead',\n    'tbody',\n    'tr',\n    'th',\n    'td',\n    'pre',\n    'del',\n    'sup',\n    'sub',\n    'dl',\n    'dt',\n    'dd',\n    'kbd',\n    'q',\n    'samp',\n    'samp',\n    'var',\n    'hr',\n    'rt',\n    'rp',\n    'summary',\n    'iframe',\n    'img',\n    'caption',\n    'figure',\n  ],\n  allowedAttributes: {\n    a: ['href', 'name', 'target'],\n    img: ['src', 'longdesc'],\n    div: ['itemscope', 'itemtype'],\n  },\n};\n\nexport const sanitizeConvertedHtml = (dirtyHtml: any) => {\n  return sanitizeHtml(dirtyHtml, sanitizerOptions);\n};\n\nexport const forceAnchorTagNewTab = (html: any) => {\n  return html.replace(new RegExp('<a', 'g'), '<a target=\"_blank\"');\n};\n", "import React from 'react';\n\nimport {\n  type Location,\n  type Params as RouterDOMParams,\n  type NavigateOptions,\n  type To,\n  useLocation,\n  useNavigate,\n  useParams,\n} from './RoutingUtils';\n\nexport interface WithRouterNextProps<Params extends RouterDOMParams = RouterDOMParams> {\n  navigate: ReturnType<typeof useNavigate>;\n  location: Location;\n  params: Params;\n}\n\n/**\n * This HoC serves as a retrofit for class components enabling them to use\n * react-router v6's location, navigate and params being injected via props.\n */\nexport const withRouterNext =\n  <\n    T,\n    Props extends JSX.IntrinsicAttributes &\n      JSX.LibraryManagedAttributes<React.ComponentType<T>, React.PropsWithChildren<T>>,\n    Params extends RouterDOMParams = RouterDOMParams,\n  >(\n    Component: React.ComponentType<T>,\n  ) =>\n  (\n    props: Omit<\n      Props,\n      | 'location'\n      | 'navigate'\n      | 'params'\n      | 'navigationType'\n      /* prettier-ignore*/\n    >,\n  ) => {\n    const location = useLocation();\n    const navigate = useNavigate();\n    const params = useParams<Params>();\n\n    return (\n      <Component\n        /* prettier-ignore */\n        params={params as Params}\n        location={location}\n        navigate={navigate}\n        {...(props as Props)}\n      />\n    );\n  };\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport Utils from '../../utils/Utils';\n\ntype Props = {\n  showServerError?: boolean;\n};\n\ntype State = any;\n\nexport class SectionErrorBoundary extends React.Component<Props, State> {\n  state = { error: null };\n\n  componentDidCatch(error: any, errorInfo: any) {\n    this.setState({ error });\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    console.error(error, errorInfo);\n  }\n\n  renderErrorMessage(error: any) {\n    return this.props.showServerError ? <div>Error message: {error.message}</div> : '';\n  }\n\n  render() {\n    const { children } = this.props;\n    const { error } = this.state;\n    if (error) {\n      return (\n        <div>\n          <p>\n            <i data-testid=\"icon-fail\" className=\"fa fa-exclamation-triangle icon-fail\" css={classNames.wrapper} />\n            <span> Something went wrong with this section. </span>\n            <span>If this error persists, please report an issue </span>\n            {/* Reported during ESLint upgrade */}\n            {/* eslint-disable-next-line react/jsx-no-target-blank */}\n            <a href={Utils.getSupportPageUrl()} target=\"_blank\">\n              here\n            </a>\n            .{this.renderErrorMessage(error)}\n          </p>\n        </div>\n      );\n    }\n\n    return children;\n  }\n}\n\nconst classNames = {\n  wrapper: {\n    marginLeft: -2, // to align the failure icon with the collapsable section caret toggle\n  },\n};\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport { Tag, useDesignSystemTheme } from '@databricks/design-system';\nexport const PreviewBadge = ({ className }: { className?: string }) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <Tag\n      componentId=\"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14\"\n      className={className}\n      css={{ marginLeft: theme.spacing.xs }}\n      color=\"turquoise\"\n    >\n      <FormattedMessage\n        defaultMessage=\"Experimental\"\n        description=\"Experimental badge shown for features which are experimental\"\n      />\n    </Tag>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport './RequestStateWrapper.css';\nimport { connect } from 'react-redux';\nimport { getApis } from '../../experiment-tracking/reducers/Reducers';\nimport { Spinner } from './Spinner';\nimport { ErrorCodes } from '../constants';\nimport { ErrorWrapper } from '../utils/ErrorWrapper';\nimport { ReduxState } from '../../redux-types';\n\nexport const DEFAULT_ERROR_MESSAGE = 'A request error occurred.';\n\ntype RequestStateWrapperProps = {\n  children?: React.ReactNode;\n  customSpinner?: React.ReactNode;\n  shouldOptimisticallyRender?: boolean;\n  requests: any[];\n  requestIds?: string[];\n  requestIdsWith404sToIgnore?: string[];\n  description?: any; // TODO: PropTypes.oneOf(Object.values(LoadingDescription))\n  permissionDeniedView?: React.ReactNode;\n  suppressErrorThrow?: boolean;\n  customRequestErrorHandlerFn?: (\n    failedRequests: {\n      id: string;\n      active?: boolean;\n      error: Error | ErrorWrapper;\n    }[],\n  ) => void;\n};\n\ntype RequestStateWrapperState = any;\n\nexport class RequestStateWrapper extends Component<RequestStateWrapperProps, RequestStateWrapperState> {\n  static defaultProps = {\n    requests: [],\n    requestIdsWith404sToIgnore: [],\n    shouldOptimisticallyRender: false,\n  };\n\n  state = {\n    shouldRender: false,\n    shouldRenderError: false,\n  };\n\n  static getErrorRequests(requests: any, requestIdsWith404sToIgnore: any) {\n    return requests.filter((r: any) => {\n      if (r.error !== undefined) {\n        return !(\n          requestIdsWith404sToIgnore &&\n          requestIdsWith404sToIgnore.includes(r.id) &&\n          r.error.getErrorCode() === ErrorCodes.RESOURCE_DOES_NOT_EXIST\n        );\n      }\n      return false;\n    });\n  }\n\n  static getDerivedStateFromProps(nextProps: any) {\n    const shouldRender = nextProps.requests.length\n      ? nextProps.requests.every((r: any) => r && r.active === false)\n      : false;\n\n    const requestErrors = RequestStateWrapper.getErrorRequests(\n      nextProps.requests,\n      nextProps.requestIdsWith404sToIgnore,\n    );\n\n    return {\n      shouldRender,\n      shouldRenderError: requestErrors.length > 0,\n      requestErrors,\n    };\n  }\n\n  getRenderedContent() {\n    const { children, requests, customSpinner, permissionDeniedView, suppressErrorThrow, customRequestErrorHandlerFn } =\n      this.props;\n    // @ts-expect-error TS(2339): Property 'requestErrors' does not exist on type '{... Remove this comment to see the full error message\n    const { shouldRender, shouldRenderError, requestErrors } = this.state;\n    const permissionDeniedErrors = requestErrors.filter((failedRequest: any) => {\n      return failedRequest.error.getErrorCode() === ErrorCodes.PERMISSION_DENIED;\n    });\n\n    if (typeof children === 'function') {\n      return children(!shouldRender, shouldRenderError, requests, requestErrors);\n    } else if (shouldRender || shouldRenderError || this.props.shouldOptimisticallyRender) {\n      if (permissionDeniedErrors.length > 0 && permissionDeniedView) {\n        return permissionDeniedView;\n      }\n      if (shouldRenderError && !suppressErrorThrow) {\n        customRequestErrorHandlerFn ? customRequestErrorHandlerFn(requestErrors) : triggerError(requestErrors);\n      }\n\n      return children;\n    }\n\n    return customSpinner || <Spinner />;\n  }\n\n  render() {\n    return this.getRenderedContent();\n  }\n}\n\nexport const triggerError = (requests: any) => {\n  // This triggers the OOPS error boundary.\n  // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n  console.error('ERROR', requests);\n  throw Error(`${DEFAULT_ERROR_MESSAGE}: ${requests.error}`);\n};\n\nconst mapStateToProps = (state: ReduxState, ownProps: Omit<RequestStateWrapperProps, 'requests'>) => ({\n  requests: getApis(ownProps.requestIds, state),\n});\n\nexport default connect(mapStateToProps)(RequestStateWrapper);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>b,\n  Button,\n  Spacer,\n  Dropdown,\n  Menu,\n  Header,\n  OverflowIcon,\n  useDesignSystemTheme,\n  type HeaderProps,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { PreviewBadge } from './PreviewBadge';\n\ntype OverflowMenuProps = {\n  menu?: {\n    id: string;\n    itemName: React.ReactNode;\n    onClick?: (...args: any[]) => any;\n    href?: string;\n  }[];\n};\n\nexport function OverflowMenu({ menu }: OverflowMenuProps) {\n  const overflowMenu = (\n    <Menu>\n      {/* @ts-expect-error TS(2532): Object is possibly 'undefined'. */}\n      {menu.map(({ id, itemName, onClick, href, ...otherProps }) => (\n        // @ts-expect-error TS(2769): No overload matches this call.\n        <Menu.Item key={id} onClick={onClick} href={href} data-test-id={id} {...otherProps}>\n          {itemName}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n  return menu.length > 0 ? (\n    <Dropdown overlay={overflowMenu} trigger={['click']} placement=\"bottomLeft\" arrow>\n      <Button\n        componentId=\"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54\"\n        icon={<OverflowIcon />}\n        data-test-id=\"overflow-menu-trigger\"\n        aria-label=\"Open header dropdown menu\"\n      />\n    </Dropdown>\n  ) : null;\n}\n\ntype PageHeaderProps = Pick<HeaderProps, 'dangerouslyAppendEmotionCSS'> & {\n  title: React.ReactNode;\n  breadcrumbs?: React.ReactNode[];\n  preview?: boolean;\n  feedbackOrigin?: string;\n  infoPopover?: React.ReactNode;\n  children?: React.ReactNode;\n  spacerSize?: 'xs' | 'sm' | 'md' | 'lg';\n  hideSpacer?: boolean;\n  titleAddOns?: React.ReactNode | React.ReactNode[];\n};\n\n/**\n * A page header that includes:\n *   - title,\n *   - optional breadcrumb content,\n *   - optional preview mark,\n *   - optional feedback origin: shows the \"Send feedback\" button when not empty, and\n *   - optional info popover, safe to have link inside.\n */\nexport function PageHeader(props: PageHeaderProps) {\n  const {\n    title, // required\n    breadcrumbs = [],\n    titleAddOns = [],\n    preview,\n    children,\n    spacerSize,\n    hideSpacer = false,\n    dangerouslyAppendEmotionCSS,\n  } = props;\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  return (\n    <>\n      <Header\n        breadcrumbs={\n          breadcrumbs.length > 0 && (\n            <Breadcrumb includeTrailingCaret>\n              {breadcrumbs.map((b, i) => (\n                <Breadcrumb.Item key={i}>{b}</Breadcrumb.Item>\n              ))}\n            </Breadcrumb>\n          )\n        }\n        buttons={children}\n        title={title}\n        // prettier-ignore\n        titleAddOns={\n          <>\n            {preview && <PreviewBadge css={{ marginLeft: 0 }} />}\n            {titleAddOns}\n          </>\n        }\n        dangerouslyAppendEmotionCSS={dangerouslyAppendEmotionCSS}\n      />\n      <Spacer\n        // @ts-expect-error TS(2322): Type '{ css: { flexShrink: number; }; }' is not as... Remove this comment to see the full error message\n        css={{\n          // Ensure spacer's fixed height\n          flexShrink: 0,\n          ...(hideSpacer ? { display: 'none' } : {}),\n        }}\n        size={spacerSize}\n      />\n    </>\n  );\n}\n", "import { Tag } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { ReadyIcon } from './utils';\n\nexport const Stages = {\n  NONE: 'None',\n  STAGING: 'Staging',\n  PRODUCTION: 'Production',\n  ARCHIVED: 'Archived',\n};\n\nexport const ACTIVE_STAGES = [Stages.STAGING, Stages.PRODUCTION];\n\nexport const StageLabels = {\n  [Stages.NONE]: 'None',\n  [Stages.STAGING]: 'Staging',\n  [Stages.PRODUCTION]: 'Production',\n  [Stages.ARCHIVED]: 'Archived',\n};\n\nexport const StageTagComponents = {\n  [Stages.NONE]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_37\">{StageLabels[Stages.NONE]}</Tag>\n  ),\n  [Stages.STAGING]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_38\" color=\"lemon\">\n      {StageLabels[Stages.STAGING]}\n    </Tag>\n  ),\n  [Stages.PRODUCTION]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_39\" color=\"lime\">\n      {StageLabels[Stages.PRODUCTION]}\n    </Tag>\n  ),\n  [Stages.ARCHIVED]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_40\" color=\"charcoal\">\n      {StageLabels[Stages.ARCHIVED]}\n    </Tag>\n  ),\n};\n\nexport interface ModelVersionActivity {\n  creation_timestamp?: number;\n  user_id?: string;\n  activity_type: ActivityTypes;\n  comment?: string;\n  last_updated_timestamp?: number;\n  from_stage?: string;\n  to_stage?: string;\n  system_comment?: string;\n  id?: string;\n}\n\nexport enum ActivityTypes {\n  APPLIED_TRANSITION = 'APPLIED_TRANSITION',\n  REQUESTED_TRANSITION = 'REQUESTED_TRANSITION',\n  SYSTEM_TRANSITION = 'SYSTEM_TRANSITION',\n  CANCELLED_REQUEST = 'CANCELLED_REQUEST',\n  APPROVED_REQUEST = 'APPROVED_REQUEST',\n  REJECTED_REQUEST = 'REJECTED_REQUEST',\n  NEW_COMMENT = 'NEW_COMMENT',\n}\n\nexport interface PendingModelVersionActivity {\n  type: ActivityTypes;\n  to_stage: string;\n}\n\nexport const EMPTY_CELL_PLACEHOLDER = <div style={{ marginTop: -12 }}>_</div>;\n\nexport const ModelVersionStatus = {\n  READY: 'READY',\n};\n\nexport const DefaultModelVersionStatusMessages = {\n  [ModelVersionStatus.READY]: (\n    <FormattedMessage defaultMessage=\"Ready.\" description=\"Default status message for model versions that are ready\" />\n  ),\n};\n\nexport const modelVersionStatusIconTooltips = {\n  [ModelVersionStatus.READY]: (\n    <FormattedMessage\n      defaultMessage=\"Ready\"\n      description=\"Tooltip text for ready model version status icon in model view page\"\n    />\n  ),\n};\n\nexport const ModelVersionStatusIcons = {\n  [ModelVersionStatus.READY]: <ReadyIcon />,\n};\n\nexport const MODEL_VERSION_STATUS_POLL_INTERVAL = 10000;\n\n// Number of registered models initially shown on the model registry list page\nconst REGISTERED_MODELS_PER_PAGE = 10;\n\n// Variant for compact tables (unified list pattern), this is\n// going to become a default soon\nexport const REGISTERED_MODELS_PER_PAGE_COMPACT = 25;\n\nexport const MAX_RUNS_IN_SEARCH_MODEL_VERSIONS_FILTER = 75; // request size has a limit of 4KB\n\nexport const REGISTERED_MODELS_SEARCH_NAME_FIELD = 'name';\n\nexport const REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD = 'timestamp';\n\nexport const AntdTableSortOrder = {\n  ASC: 'ascend',\n  DESC: 'descend',\n};\n\nexport const archiveExistingVersionToolTipText = (currentStage: string) => (\n  <FormattedMessage\n    defaultMessage=\"Model versions in the `{currentStage}` stage will be moved to the\n     `Archived` stage.\"\n    description=\"Tooltip text for transitioning existing model versions in stage to archived\n     in the model versions page\"\n    values={{ currentStage: currentStage }}\n  />\n);\n\nexport const mlflowAliasesLearnMoreLink =\n  'https://mlflow.org/docs/latest/model-registry.html#using-registered-model-aliases';\n", "import spinner from '../static/mlflow-spinner.png';\nimport { Interpolation, keyframes, Theme } from '@emotion/react';\n\ntype Props = {\n  showImmediately?: boolean;\n};\n\nexport function Spinner({ showImmediately }: Props) {\n  return (\n    <div css={(theme) => styles.spinner(theme, showImmediately)}>\n      <img alt=\"Page loading...\" src={spinner} />\n    </div>\n  );\n}\n\nconst styles = {\n  spinner: (theme: Theme, immediate?: boolean): Interpolation<Theme> => ({\n    width: 100,\n    marginTop: 100,\n    marginLeft: 'auto',\n    marginRight: 'auto',\n\n    img: {\n      position: 'absolute',\n      opacity: 0,\n      top: '50%',\n      left: '50%',\n      width: theme.general.heightBase * 2,\n      height: theme.general.heightBase * 2,\n      marginTop: -theme.general.heightBase,\n      marginLeft: -theme.general.heightBase,\n      animation: `${keyframes`\n          0% {\n            opacity: 1;\n          }\n          100% {\n            opacity: 1;\n            -webkit-transform: rotate(360deg);\n                transform: rotate(360deg);\n            }\n          `} 3s linear infinite`,\n      animationDelay: immediate ? '0s' : '0.5s',\n    },\n  }),\n};\n"], "names": ["_converter", "getMarkdownConverter", "Converter", "set<PERSON><PERSON>or", "sanitizerOptions", "allowedTags", "allowedAttributes", "a", "img", "div", "sanitizeConvertedHtml", "dirtyHtml", "sanitizeHtml", "forceAnchorTagNewTab", "html", "replace", "RegExp", "withRouterNext", "Component", "props", "location", "useLocation", "navigate", "useNavigate", "params", "useParams", "_jsx", "SectionErrorBoundary", "React", "constructor", "arguments", "state", "error", "componentDidCatch", "errorInfo", "this", "setState", "console", "renderErrorMessage", "showServerError", "_jsxs", "children", "message", "render", "className", "css", "classNames", "wrapper", "href", "Utils", "getSupportPageUrl", "target", "marginLeft", "PreviewBadge", "_ref", "theme", "useDesignSystemTheme", "Tag", "componentId", "_css", "spacing", "xs", "color", "FormattedMessage", "id", "defaultMessage", "RequestStateWrapper", "shouldRender", "shouldRenderError", "getErrorRequests", "requests", "requestIdsWith404sToIgnore", "filter", "r", "undefined", "includes", "getErrorCode", "ErrorCodes", "RESOURCE_DOES_NOT_EXIST", "getDerivedStateFromProps", "nextProps", "length", "every", "active", "requestErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customSpinner", "permissionDeniedView", "suppressErrorThrow", "customRequestErrorHandlerFn", "permissionDeniedErrors", "failedRequest", "PERMISSION_DENIED", "shouldOptimisticallyRender", "triggerError", "Spinner", "defaultProps", "Error", "connect", "mapStateToProps", "ownProps", "get<PERSON><PERSON>", "requestIds", "OverflowMenu", "menu", "overflowMenu", "<PERSON><PERSON>", "map", "_ref2", "itemName", "onClick", "otherProps", "<PERSON><PERSON>", "Dropdown", "overlay", "trigger", "placement", "arrow", "<PERSON><PERSON>", "icon", "OverflowIcon", "_ref3", "name", "styles", "<PERSON><PERSON><PERSON><PERSON>", "title", "breadcrumbs", "titleAddOns", "preview", "spacerSize", "hideSpacer", "dangerouslyAppendEmotionCSS", "useIntl", "_Fragment", "Header", "Breadcrumb", "includeTrailingCaret", "b", "i", "buttons", "Spacer", "flexShrink", "display", "size", "Stages", "NONE", "STAGING", "PRODUCTION", "ARCHIVED", "ACTIVE_STAGES", "StageLabels", "StageTagComponents", "ActivityTypes", "style", "marginTop", "ModelVersionStatus", "READY", "DefaultModelVersionStatusMessages", "modelVersionStatusIconTooltips", "ModelVersionStatusIcons", "ReadyIcon", "MODEL_VERSION_STATUS_POLL_INTERVAL", "REGISTERED_MODELS_PER_PAGE_COMPACT", "MAX_RUNS_IN_SEARCH_MODEL_VERSIONS_FILTER", "REGISTERED_MODELS_SEARCH_NAME_FIELD", "REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD", "AntdTableSortOrder", "ASC", "DESC", "archiveExistingVersionToolTipText", "currentStage", "values", "mlflowAliasesLearnMoreLink", "showImmediately", "spinner", "alt", "src", "immediate", "width", "marginRight", "position", "opacity", "top", "left", "general", "heightBase", "height", "animation", "keyframes", "animationDelay"], "sourceRoot": ""}