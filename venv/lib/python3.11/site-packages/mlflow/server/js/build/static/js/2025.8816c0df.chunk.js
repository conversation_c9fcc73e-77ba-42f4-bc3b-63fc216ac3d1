/*! For license information please see 2025.8816c0df.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[2025],{18930:function(e,t,r){e.exports=r(91231)},20109:function(e,t,r){r.d(t,{X:function(){return c}});r(31014);var a=r(28486),n=r(3293),o=r(77520),s=r(88443),l=r(50111);function i(){return(0,l.Y)(n.SvL,{"data-testid":"fallback",title:(0,l.Y)(s.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,l.Y)(s.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(o.j,{})})}function u(e){let{children:t,customFallbackComponent:r}=e;function n(e,t){console.error("Caught Unexpected Error: ",e,t.componentStack)}return r?(0,l.Y)(a.tH,{onError:n,FallbackComponent:r,children:t}):(0,l.Y)(a.tH,{onError:n,fallback:(0,l.Y)(i,{}),children:t})}function c(e,t,r,a){return function(e){return(0,l.Y)(u,{customFallbackComponent:a,children:(0,l.Y)(t,{...e})})}}},28486:function(e,t,r){r.d(t,{tH:function(){return l}});var a=r(31014);function n(e,t,r,a){Object.defineProperty(e,t,{get:r,set:a,enumerable:!0,configurable:!0})}n({},"ErrorBoundary",(()=>l));n({},"ErrorBoundaryContext",(()=>o));const o=(0,a.createContext)(null),s={didCatch:!1,error:null};class l extends a.Component{state=(()=>s)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];e.props.onReset?.({args:a,reason:"imperative-api"}),e.setState(s)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:r}=this.state,{resetKeys:a}=this.props;r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,r)=>!Object.is(e,t[r])))}(e.resetKeys,a)&&(this.props.onReset?.({next:a,prev:e.resetKeys,reason:"keys"}),this.setState(s))}render(){const{children:e,fallbackRender:t,FallbackComponent:r,fallback:n}=this.props,{didCatch:s,error:l}=this.state;let i=e;if(s){const e={error:l,resetErrorBoundary:this.resetErrorBoundary};if((0,a.isValidElement)(n))i=n;else if("function"===typeof t)i=t(e);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");i=(0,a.createElement)(r,e)}}return(0,a.createElement)(o.Provider,{value:{didCatch:s,error:l,resetErrorBoundary:this.resetErrorBoundary}},i)}}function i(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function u(){const e=(0,a.useContext)(o);i(e);const[t,r]=(0,a.useState)({error:null,hasError:!1}),n=(0,a.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:e=>r({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return n}n({},"useErrorBoundary",(()=>u));function c(e,t){const r=r=>(0,a.createElement)(l,t,(0,a.createElement)(e,r)),n=e.displayName||e.name||"Unknown";return r.displayName=`withErrorBoundary(${n})`,r}n({},"withErrorBoundary",(()=>c))},30922:function(e,t,r){r.d(t,{q:function(){return s}});var a=r(77520),n=r(88443),o=r(50111);const s=e=>{let{row:{original:t},getValue:r}=e;const s=r();return s?(0,o.Y)(a.T.Text,{children:(0,o.Y)(n.A,{id:"sM0MBJ",defaultMessage:"Version {version}",values:{version:s}})}):null}},41261:function(e,t,r){r.d(t,{I:function(){return s}});var a=r(28999),n=r(45586),o=r(44200);function s(e,t,r){const s=(0,a.vh)(e,t,r);return(0,o.t)(s,n.$)}},43683:function(e,t,r){r.d(t,{Q:function(){return x}});var a=r(89555),n=r(9133),o=r(31014),s=r(77520),l=r(42848),i=r(3293),u=r(88464),c=r(88443),d=r(13369),m=r(50111);var p={name:"4zleql",styles:"display:block"};function f(e){return t=>function(e,t){const r=(0,u.A)(),{theme:l}=(0,s.u)(),c=e.props.searchValue.toLowerCase();return(0,o.useMemo)((()=>{if(!c)return e;if((0,n.sortedIndexOf)(t,c)>=0)return e;const s=/^[^,.:/=\-\s]+$/.test(c);return o.cloneElement(e,{flattenOptions:[{data:{value:c,disabled:!s,style:{color:s?l.colors.actionTertiaryTextDefault:l.colors.actionDisabledText},children:(0,m.Y)(i.paO,{title:s?void 0:r.formatMessage({id:"fWEvZL",defaultMessage:", . : / - = and blank spaces are not allowed"}),placement:"right",children:(0,m.FD)("span",{css:p,children:[(0,m.Y)(i.c11,{css:(0,a.AH)({marginRight:l.spacing.sm},"")}),r.formatMessage({id:"IJbauF",defaultMessage:'Add tag "{tagKey}"'},{tagKey:c})]})})},key:c,groupOption:!1},...e.props.flattenOptions]})}),[t,e,c,r,l])}(t,e)}var g={name:"1d3w5wq",styles:"width:100%"};function h(e){let{allAvailableTags:t,control:r,onKeyChangeCallback:a}=e;const n=(0,u.A)(),[s,l]=(0,o.useState)(!1),c=(0,o.useRef)(null),{field:p,fieldState:h}=(0,d.as)({control:r,name:"key",rules:{required:{message:n.formatMessage({id:"RlBbjb",defaultMessage:"A tag key is required"}),value:!0}}});return(0,m.Y)(i._vn,{allowClear:!0,ref:c,dangerouslySetAntdProps:{showSearch:!0,dropdownRender:f(t)},css:g,placeholder:n.formatMessage({id:"8ALhnh",defaultMessage:"Type a key"}),value:p.value,defaultValue:p.value,open:s,onDropdownVisibleChange:e=>{l(e)},filterOption:(e,t)=>null===t||void 0===t?void 0:t.value.toLowerCase().includes(e.toLowerCase()),onSelect:e=>{p.onChange(e),null===a||void 0===a||a(e)},onClear:()=>{p.onChange(void 0),null===a||void 0===a||a(void 0)},validationState:h.error?"error":void 0,children:t.map((e=>(0,m.Y)(i._vn.Option,{value:e,children:e},e)))})}var v=r(98597),y=r(52350);function w(e){return new Map(e.map((e=>[e.key,e])))}var M={name:"82a6rk",styles:"flex:1"},b={name:"82a6rk",styles:"flex:1"};const x=e=>{let{onSuccess:t,saveTagsHandler:r,allAvailableTags:p,valueRequired:f=!1,title:g}=e;const x=(0,o.useRef)(),[C,Y]=(0,o.useState)(""),{theme:k}=(0,s.u)(),[_,P]=(0,o.useState)(new Map),[E,A]=(0,o.useState)(new Map),[R,T]=(0,o.useState)(!1),F=(0,d.mN)({defaultValues:{key:void 0,value:""}}),V=()=>T(!1),D=(0,o.useCallback)((e=>{x.current=e,P(w(e.tags||[])),A(w(e.tags||[])),F.reset(),T(!0)}),[F]),I=async()=>{x.current&&(Y(""),B(!0),r(x.current,Array.from(_.values()),Array.from(E.values())).then((()=>{V(),null===t||void 0===t||t(),B(!1)})).catch((e=>{var t;B(!1),Y(e instanceof y.s?null===(t=e.getUserVisibleError())||void 0===t?void 0:t.message:e.message)})))},N=(0,u.A)(),O=F.watch(),[L,B]=(0,o.useState)(!1),K=(0,o.useMemo)((()=>!(0,n.isEqual)((0,n.sortBy)(Array.from(_.values()),"key"),(0,n.sortBy)(Array.from(E.values()),"key"))),[_,E]),j=O.key||O.value,U=K&&j;return{EditTagsModal:(0,m.FD)(l.d,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135",destroyOnClose:!0,visible:R,title:null!==g&&void 0!==g?g:(0,m.Y)(c.A,{id:"TBX+Gs",defaultMessage:"Add/Edit tags"}),onCancel:V,footer:(0,m.FD)(s.y,{children:[(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147",dangerouslyUseFocusPseudoClass:!0,onClick:V,css:(0,a.AH)({marginRight:K?0:k.spacing.sm},""),children:N.formatMessage({id:"2a/rR8",defaultMessage:"Cancel"})}),U?(0,m.Y)(S,{formValues:O,isLoading:L,onSaveTask:I}):(0,m.Y)(i.paO,{title:K?void 0:N.formatMessage({id:"16onEc",defaultMessage:"Please add or remove one or more tags before saving"}),children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174",dangerouslyUseFocusPseudoClass:!0,disabled:!K,loading:L,type:"primary",onClick:I,children:N.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})})]}),children:[(0,m.FD)("form",{onSubmit:F.handleSubmit((()=>{if(f&&!O.value.trim())return;const e=new Map(E);e.set(O.key,O),A(e),F.reset()})),css:(0,a.AH)({display:"flex",alignItems:"flex-end",gap:k.spacing.md},""),children:[(0,m.FD)("div",{css:(0,a.AH)({minWidth:0,display:"flex",gap:k.spacing.md,flex:1},""),children:[(0,m.FD)("div",{css:M,children:[(0,m.Y)(i.D$Q.Label,{htmlFor:"key",children:N.formatMessage({id:"crTWax",defaultMessage:"Key"})}),(0,m.Y)(h,{allAvailableTags:p||[],control:F.control,onKeyChangeCallback:e=>{var t;const r=e?E.get(e):void 0;F.setValue("value",null!==(t=null===r||void 0===r?void 0:r.value)&&void 0!==t?t:"")}})]}),(0,m.FD)("div",{css:b,children:[(0,m.Y)(i.D$Q.Label,{htmlFor:"value",children:f?N.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):N.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"})}),(0,m.Y)(i.tc_.Input,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223",name:"value",control:F.control,"aria-label":f?N.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):N.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"}),placeholder:N.formatMessage({id:"FFe+Ug",defaultMessage:"Type a value"})})]})]}),(0,m.Y)(i.paO,{title:N.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248",htmlType:"submit","aria-label":N.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,m.Y)(i.c11,{})})})]}),C&&(0,m.Y)(i.D$Q.Message,{type:"error",message:C}),(0,m.Y)("div",{css:(0,a.AH)({display:"flex",rowGap:k.spacing.xs,flexWrap:"wrap",marginTop:k.spacing.sm},""),children:Array.from(E.values()).map((e=>(0,m.Y)(v.t,{isClosable:!0,tag:e,onClose:()=>(e=>{let{key:t}=e;A((e=>(e.delete(t),new Map(e))))})(e)},e.key)))})]}),showEditTagsModal:D,isLoading:L}};var C={name:"1y0ex1",styles:"max-width:400px"};function S(e){let{isLoading:t,formValues:r,onSaveTask:o}=e;const l=(0,u.A)(),{theme:i}=(0,s.u)(),c=`${`${(0,n.truncate)(r.key,{length:20})||"_"}`}${r.value?`:${(0,n.truncate)(r.value,{length:20})}`:""}`,d=l.formatMessage({id:"wcSVYI",defaultMessage:'Are you sure you want to save and close without adding "{tag}"'},{tag:c});return(0,m.FD)(s.ax.Root,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309",children:[(0,m.Y)(s.ax.Trigger,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306",dangerouslyUseFocusPseudoClass:!0,loading:t,type:"primary",children:l.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})}),(0,m.FD)(s.ax.Content,{align:"end","aria-label":d,children:[(0,m.Y)(s.T.Paragraph,{css:C,children:d}),(0,m.Y)(s.ax.Close,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316",onClick:o,children:l.formatMessage({id:"mv6CY3",defaultMessage:"Yes, save and close"})})}),(0,m.Y)(s.ax.Close,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324",type:"primary",css:(0,a.AH)({marginLeft:i.spacing.sm},""),children:l.formatMessage({id:"geizp1",defaultMessage:"Cancel"})})}),(0,m.Y)(s.ax.Arrow,{})]})]})}},56412:function(e,t,r){r.d(t,{i:function(){return u}});var a=r(31014),n=r(88443),o=r(3293),s=r(77520),l=r(50111);var i={name:"1739oy8",styles:"z-index:1"};const u=e=>{let{copyText:t,showLabel:r=!0,componentId:u,...c}=e;const[d,m]=(0,a.useState)(!1);return(0,l.Y)(o.paO,{title:(0,l.Y)(n.A,{id:"X+boXI",defaultMessage:"Copied"}),dangerouslySetAntdProps:{visible:d},children:(0,l.Y)(s.B,{componentId:null!==u&&void 0!==u?u:"mlflow.shared.copy_button",type:"primary",onClick:()=>{navigator.clipboard.writeText(t),m(!0),setTimeout((()=>{m(!1)}),3e3)},onMouseLeave:()=>{m(!1)},css:i,children:r?(0,l.Y)(n.A,{id:"1Iq+NW",defaultMessage:"Copy"}):void 0,...c})})}},62448:function(e,t){class r{}r.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"},t.A=r},66742:function(e,t,r){r.r(t),r.d(t,{default:function(){return B}});var a=r(84069),n=r(41261),o=r(31014),s=r(82832);const l=e=>{let{queryKey:t}=e;const[,{searchFilter:r,pageToken:a}]=t;return s.M.listRegisteredPrompts(r,a)};var i=r(42848),u=r(3293),c=r(77520),d=r(88443),m=r(50111);const p=e=>{let{searchFilter:t,onSearchFilterChange:r}=e;return(0,m.Y)(u.R9P,{children:(0,m.Y)(u.z2z,{placeholder:"Search prompts by name",componentId:"mlflow.prompts.list.search",value:t,onChange:e=>r(e.target.value)})})};var f=r(89555),g=r(70618),h=r(9856),v=r(88464),y=r(98590),w=r(98597);var M={name:"zjik7",styles:"display:flex"},b={name:"tyk0n8",styles:"overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:flex"},x={name:"14cnl6e",styles:"flex-shrink:0;opacity:0;[role=row]:hover &{opacity:1;}[role=row]:focus-within &{opacity:1;}"};const C=e=>{var t;let{row:{original:r},table:{options:{meta:a}}}=e;const n=(0,v.A)(),{onEditTags:o}=a,s=(null===r||void 0===r||null===(t=r.tags)||void 0===t?void 0:t.filter((e=>(0,y.oD)(e.key))))||[],l=s.length>0;return(0,m.FD)("div",{css:M,children:[(0,m.Y)("div",{css:b,children:null===s||void 0===s?void 0:s.map((e=>(0,m.Y)(w.t,{tag:e},e.key)))}),(0,m.Y)(c.B,{componentId:"mlflow.prompts.list.tag.add",size:"small",icon:l?(0,m.Y)(u.R2l,{}):void 0,onClick:()=>null===o||void 0===o?void 0:o(r),"aria-label":n.formatMessage({id:"lA8QO2",defaultMessage:"Edit tags"}),children:l?void 0:(0,m.Y)(d.A,{id:"iDpAK8",defaultMessage:"Add tags"}),css:x,type:"tertiary"})]})};var S=r(93215),Y=r(58481);const k=e=>{let{row:{original:t},getValue:r}=e;const a=r();return t.name?(0,m.Y)(S.N_,{to:Y.h.getPromptDetailsPageRoute(encodeURIComponent(t.name)),children:a}):a};var _=r(76010),P=r(30922),E=r(9133);var A={name:"1h3rtzg",styles:"align-items:center"};const R=e=>{let{prompts:t,hasNextPage:r,hasPreviousPage:a,isLoading:n,isFiltered:s,onNextPage:l,onPreviousPage:i,onEditTags:p}=e;const{theme:y}=(0,c.u)(),w=(()=>{const e=(0,v.A)();return(0,o.useMemo)((()=>{const t=[{header:e.formatMessage({id:"tzA/LZ",defaultMessage:"Name"}),accessorKey:"name",id:"name",cell:k},{header:e.formatMessage({id:"bmd4rb",defaultMessage:"Latest version"}),cell:P.q,accessorFn:e=>{var t;let{latest_versions:r}=e;return null===(t=(0,E.first)(r))||void 0===t?void 0:t.version},id:"latestVersion"},{header:e.formatMessage({id:"FiKsFK",defaultMessage:"Last modified"}),id:"lastModified",accessorFn:t=>{let{last_updated_timestamp:r}=t;return _.A.formatTimestamp(r,e)}},{header:e.formatMessage({id:"KMVqUP",defaultMessage:"Tags"}),accessorKey:"tags",id:"tags",cell:C}];return t}),[e])})(),M=(0,g.N4)({data:null!==t&&void 0!==t?t:[],columns:w,getCoreRowModel:(0,h.HT)(),getRowId:(e,t)=>{var r;return null!==(r=e.name)&&void 0!==r?r:t.toString()},meta:{onEditTags:p}});return(0,m.FD)(u.XIK,{scrollable:!0,pagination:(0,m.Y)(u.vIA,{hasNextPage:r,hasPreviousPage:a,onNextPage:l,onPreviousPage:i,componentId:"mlflow.prompts.list.pagination"}),empty:(()=>{const e=!n&&(0,E.isEmpty)(t);return e&&s?(0,m.Y)(u.SvL,{image:(0,m.Y)(u.xfv,{}),title:(0,m.Y)(d.A,{id:"hlpNRa",defaultMessage:"No prompts found"}),description:null}):e?(0,m.Y)(u.SvL,{title:(0,m.Y)(d.A,{id:"/fwKFW",defaultMessage:"No prompts created"}),description:(0,m.Y)(d.A,{id:"WM5IeI",defaultMessage:'Use "Create prompt" button in order to create a new prompt'})}):null})(),children:[(0,m.Y)(u.Hjg,{isHeader:!0,children:M.getLeafHeaders().map((e=>(0,m.Y)(u.A0N,{componentId:"mlflow.prompts.list.table.header",children:(0,g.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),n?(0,m.Y)(u.BAM,{table:M}):M.getRowModel().rows.map((e=>(0,m.Y)(u.Hjg,{css:(0,f.AH)({height:y.general.buttonHeight},""),children:e.getAllCells().map((e=>(0,m.Y)(u.nA6,{css:A,children:(0,g.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})};var T=r(83090),F=r(70724),V=r(20109),D=r(62448),I=r(72282),N=r(76137);var O={name:"1m5f2z8",styles:"overflow:hidden;display:flex;flex-direction:column"},L={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"};var B=(0,V.X)(D.A.mlflowServices.EXPERIMENTS,(()=>{const[e,t]=(0,o.useState)(""),r=(0,S.Zp)(),[s]=(0,N.d7)(e,500),{data:f,error:g,refetch:h,hasNextPage:v,hasPreviousPage:y,isLoading:w,onNextPage:M,onPreviousPage:b}=function(){var e,t,r,a;let{searchFilter:s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=(0,o.useRef)([]),[u,c]=(0,o.useState)(void 0),d=(0,n.I)(["prompts_list",{searchFilter:s,pageToken:u}],{queryFn:l,retry:!1}),m=(0,o.useCallback)((()=>{var e;i.current.push(u),c(null===(e=d.data)||void 0===e?void 0:e.next_page_token)}),[null===(e=d.data)||void 0===e?void 0:e.next_page_token,u]),p=(0,o.useCallback)((()=>{const e=i.current.pop();c(e)}),[]);return{data:null===(t=d.data)||void 0===t?void 0:t.registered_models,error:null!==(r=d.error)&&void 0!==r?r:void 0,isLoading:d.isLoading,hasNextPage:void 0!==(null===(a=d.data)||void 0===a?void 0:a.next_page_token),hasPreviousPage:Boolean(u),onNextPage:m,onPreviousPage:p,refetch:d.refetch}}({searchFilter:s}),{EditTagsModal:x,showEditPromptTagsModal:C}=(0,T.i)({onSuccess:h}),{CreatePromptModal:k,openModal:_}=(0,F.z)({mode:F.v.CreatePrompt,onSuccess:e=>{let{promptName:t}=e;return r(Y.h.getPromptDetailsPageRoute(t))}});return(0,m.FD)(a.m,{css:O,children:[(0,m.Y)(i.S,{shrinks:!1}),(0,m.Y)(u.Y9Y,{title:(0,m.Y)(d.A,{id:"aLRRam",defaultMessage:"Prompts"}),buttons:(0,m.Y)(c.B,{componentId:"mlflow.prompts.list.create",type:"primary",onClick:_,children:(0,m.Y)(d.A,{id:"7x6xu8",defaultMessage:"Create prompt"})})}),(0,m.Y)(i.S,{shrinks:!1}),(0,m.FD)("div",{css:L,children:[(0,m.Y)(p,{searchFilter:e,onSearchFilterChange:t}),(null===g||void 0===g?void 0:g.message)&&(0,m.FD)(m.FK,{children:[(0,m.Y)(u.FcD,{type:"error",message:g.message,componentId:"mlflow.prompts.list.error",closable:!1}),(0,m.Y)(i.S,{})]}),(0,m.Y)(R,{prompts:f,error:g,hasNextPage:v,hasPreviousPage:y,isLoading:w,isFiltered:Boolean(e),onNextPage:M,onPreviousPage:b,onEditTags:C})]}),x,k]})}),void 0,I.u)},70724:function(e,t,r){r.d(t,{v:function(){return p},z:function(){return f}});var a=r(42848),n=r(3293),o=r(31014),s=r(13369),l=r(88464),i=r(88443),u=r(77020),c=r(82832),d=r(84565);var m=r(50111);let p=function(e){return e.CreatePrompt="CreatePrompt",e.CreatePromptVersion="CreatePromptVersion",e}({});const f=e=>{let{mode:t=p.CreatePromptVersion,registeredPrompt:r,latestVersion:f,onSuccess:g}=e;const[h,v]=(0,o.useState)(!1),y=(0,l.A)(),w=(0,s.mN)({defaultValues:{draftName:"",draftValue:"",commitMessage:"",tags:[]}}),M=t===p.CreatePrompt,b=t===p.CreatePromptVersion,{mutate:x,error:C,reset:S,isLoading:Y}=(0,u.n)({mutationFn:async e=>{var t;let{promptName:r,createPromptEntity:a,content:n,commitMessage:o,tags:s}=e;a&&await c.M.createRegisteredPrompt(r);const l=await c.M.createRegisteredPromptVersion(r,[{key:d.Dh,value:n},...s],o),i=null===l||void 0===l||null===(t=l.model_version)||void 0===t?void 0:t.version;if(!i)throw new Error("Failed to create a new prompt version");return{version:i}}});return{CreatePromptModal:(0,m.FD)(a.d,{componentId:"mlflow.prompts.create.modal",visible:h,onCancel:()=>v(!1),title:b?(0,m.Y)(i.A,{id:"XLSmZx",defaultMessage:"Create prompt version"}):(0,m.Y)(i.A,{id:"wh+Eos",defaultMessage:"Create prompt"}),okText:(0,m.Y)(i.A,{id:"TSXmaB",defaultMessage:"Create"}),okButtonProps:{loading:Y},onOk:w.handleSubmit((async e=>{const t=b&&null!==r&&void 0!==r&&r.name?null===r||void 0===r?void 0:r.name:e.draftName;x({createPromptEntity:M,content:e.draftValue,commitMessage:e.commitMessage,promptName:t,tags:e.tags},{onSuccess:e=>{const r=null===e||void 0===e?void 0:e.version;null===g||void 0===g||g({promptName:t,promptVersion:r}),v(!1)}})})),cancelText:(0,m.Y)(i.A,{id:"WP50re",defaultMessage:"Cancel"}),size:"wide",children:[(null===C||void 0===C?void 0:C.message)&&(0,m.FD)(m.FK,{children:[(0,m.Y)(n.FcD,{componentId:"mlflow.prompts.create.error",closable:!1,message:C.message,type:"error"}),(0,m.Y)(a.S,{})]}),M&&(0,m.FD)(m.FK,{children:[(0,m.Y)(n.D$Q.Label,{htmlFor:"mlflow.prompts.create.name",children:"Name:"}),(0,m.Y)(n.tc_.Input,{control:w.control,id:"mlflow.prompts.create.name",componentId:"mlflow.prompts.create.name",name:"draftName",rules:{required:{value:!0,message:y.formatMessage({id:"Rlwm5V",defaultMessage:"Name is required"})},pattern:{value:/^[a-zA-Z0-9_\-.]+$/,message:y.formatMessage({id:"HZdpLU",defaultMessage:"Only alphanumeric characters, underscores, hyphens, and dots are allowed"})}},placeholder:y.formatMessage({id:"zAuZ2j",defaultMessage:"Provide an unique prompt name"}),validationState:w.formState.errors.draftName?"error":void 0}),w.formState.errors.draftName&&(0,m.Y)(n.D$Q.Message,{type:"error",message:w.formState.errors.draftName.message}),(0,m.Y)(a.S,{})]}),(0,m.Y)(n.D$Q.Label,{htmlFor:"mlflow.prompts.create.content",children:"Prompt:"}),(0,m.Y)(n.tc_.TextArea,{control:w.control,id:"mlflow.prompts.create.content",componentId:"mlflow.prompts.create.content",name:"draftValue",autoSize:{minRows:3,maxRows:10},rules:{required:{value:!0,message:y.formatMessage({id:"wq+WyH",defaultMessage:"Prompt content is required"})}},placeholder:y.formatMessage({id:"2JfWh3",defaultMessage:"Type prompt content here. Wrap variables with double curly brace e.g. '{{' name '}}'."}),validationState:w.formState.errors.draftValue?"error":void 0}),w.formState.errors.draftValue&&(0,m.Y)(n.D$Q.Message,{type:"error",message:w.formState.errors.draftValue.message}),(0,m.Y)(a.S,{}),(0,m.Y)(n.D$Q.Label,{htmlFor:"mlflow.prompts.create.commit_message",children:"Commit message (optional):"}),(0,m.Y)(n.tc_.Input,{control:w.control,id:"mlflow.prompts.create.commit_message",componentId:"mlflow.prompts.create.commit_message",name:"commitMessage"})]}),openModal:()=>{var e;(S(),t===p.CreatePromptVersion&&f)&&w.reset({commitMessage:"",draftName:"",draftValue:null!==(e=(0,d.dv)(f))&&void 0!==e?e:"",tags:[]});v(!0)}}}},72282:function(e,t,r){r.d(t,{u:function(){return u}});var a=r(3293),n=r(77520),o=r(88443),s=r(84069),l=r(50111);var i={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const u=e=>{var t;let{error:r}=e;return(0,l.Y)(s.m,{css:i,children:(0,l.Y)(a.SvL,{"data-testid":"fallback",title:(0,l.Y)(o.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===r||void 0===r?void 0:r.message)&&void 0!==t?t:(0,l.Y)(o.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(n.j,{})})})}},76137:function(e,t,r){r.d(t,{YQ:function(){return n},d7:function(){return s}});var a=r(31014);function n(e,t,r){var n=this,o=(0,a.useRef)(null),s=(0,a.useRef)(0),l=(0,a.useRef)(null),i=(0,a.useRef)([]),u=(0,a.useRef)(),c=(0,a.useRef)(),d=(0,a.useRef)(e),m=(0,a.useRef)(!0);d.current=e;var p="undefined"!=typeof window,f=!t&&0!==t&&p;if("function"!=typeof e)throw new TypeError("Expected a function");t=+t||0;var g=!!(r=r||{}).leading,h=!("trailing"in r)||!!r.trailing,v="maxWait"in r,y="debounceOnServer"in r&&!!r.debounceOnServer,w=v?Math.max(+r.maxWait||0,t):null;(0,a.useEffect)((function(){return m.current=!0,function(){m.current=!1}}),[]);var M=(0,a.useMemo)((function(){var e=function(e){var t=i.current,r=u.current;return i.current=u.current=null,s.current=e,c.current=d.current.apply(r,t)},r=function(e,t){f&&cancelAnimationFrame(l.current),l.current=f?requestAnimationFrame(e):setTimeout(e,t)},a=function(e){if(!m.current)return!1;var r=e-o.current;return!o.current||r>=t||r<0||v&&e-s.current>=w},M=function(t){return l.current=null,h&&i.current?e(t):(i.current=u.current=null,c.current)},b=function e(){var n=Date.now();if(a(n))return M(n);if(m.current){var l=t-(n-o.current),i=v?Math.min(l,w-(n-s.current)):l;r(e,i)}},x=function(){if(p||y){var d=Date.now(),f=a(d);if(i.current=[].slice.call(arguments),u.current=n,o.current=d,f){if(!l.current&&m.current)return s.current=o.current,r(b,t),g?e(o.current):c.current;if(v)return r(b,t),e(o.current)}return l.current||r(b,t),c.current}};return x.cancel=function(){l.current&&(f?cancelAnimationFrame(l.current):clearTimeout(l.current)),s.current=0,i.current=o.current=u.current=l.current=null},x.isPending=function(){return!!l.current},x.flush=function(){return l.current?M(Date.now()):c.current},x}),[g,v,t,w,h,f,p,y]);return M}function o(e,t){return e===t}function s(e,t,r){var s=r&&r.equalityFn||o,l=(0,a.useRef)(e),i=(0,a.useState)({})[1],u=n((0,a.useCallback)((function(e){l.current=e,i({})}),[i]),t,r),c=(0,a.useRef)(e);return s(c.current,e)||(u(e),c.current=e),[l.current,u]}},77020:function(e,t,r){r.d(t,{n:function(){return m}});var a=r(31014),n=r(61226),o=r(28999),s=r(84865),l=r(95904),i=r(21363);class u extends i.Q{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;const r=this.options;this.options=this.client.defaultMutationOptions(e),(0,o.f8)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.currentMutation)||e.removeObserver(this))}onMutationUpdate(e){this.updateResult();const t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const e=this.currentMutation?this.currentMutation.state:(0,s.$)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){l.j.batch((()=>{var t,r,a,n;if(this.mutateOptions&&this.hasListeners())if(e.onSuccess)null==(t=(r=this.mutateOptions).onSuccess)||t.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(a=(n=this.mutateOptions).onSettled)||a.call(n,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(e.onError){var o,s,l,i;null==(o=(s=this.mutateOptions).onError)||o.call(s,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(l=(i=this.mutateOptions).onSettled)||l.call(i,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}e.listeners&&this.listeners.forEach((e=>{let{listener:t}=e;t(this.currentResult)}))}))}}var c=r(27288),d=r(71233);function m(e,t,r){const s=(0,o.GR)(e,t,r),i=(0,c.jE)({context:s.context}),[m]=a.useState((()=>new u(i,s)));a.useEffect((()=>{m.setOptions(s)}),[m,s]);const f=(0,n.r)(a.useCallback((e=>m.subscribe(l.j.batchCalls(e))),[m]),(()=>m.getCurrentResult()),(()=>m.getCurrentResult())),g=a.useCallback(((e,t)=>{m.mutate(e,t).catch(p)}),[m]);if(f.error&&(0,d.G)(m.options.useErrorBoundary,[f.error]))throw f.error;return{...f,mutate:g,mutateAsync:f.mutate}}function p(){}},82832:function(e,t,r){r.d(t,{M:function(){return l}});var a=r(39416),n=r(53962),o=r(84565);const s=async e=>{let{reject:t,response:r,err:n}=e;const o=(0,a.a$)(r),s=o instanceof a.Bk?n:o;if(r)try{var l;const e=null===(l=await r.json())||void 0===l?void 0:l.message;e&&(s.message=e)}catch{}t(s)},l={listRegisteredPrompts:(e,t)=>{const r=new URLSearchParams;let a=`tags.\`${o.PS}\` = '${o.pY}'`;e&&(a=`${a} AND name ILIKE '%${e}%'`),t&&r.append("page_token",t),r.append("filter",a);const l=["ajax-api/2.0/mlflow/registered-models/search",r.toString()].join("?");return(0,n.AC)({relativeUrl:l,error:s})},setRegisteredPromptTag:(e,t,r)=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/set-tag",method:"POST",body:JSON.stringify({key:t,value:r,name:e}),error:s}),deleteRegisteredPromptTag:(e,t)=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete-tag",method:"DELETE",body:JSON.stringify({key:t,name:e}),error:s}),createRegisteredPrompt:e=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/create",method:"POST",body:JSON.stringify({name:e,tags:[{key:o.PS,value:o.pY}]}),error:s}),createRegisteredPromptVersion:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/create",method:"POST",body:JSON.stringify({name:e,description:r,source:"dummy-source",tags:[{key:o.PS,value:o.pY},...t]}),error:s})},setRegisteredPromptVersionTag:(e,t,r,a)=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/set-tag",method:"POST",body:JSON.stringify({key:r,value:a,name:e,version:t}),error:s}),deleteRegisteredPromptVersionTag:(e,t,r)=>{(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete-tag",method:"DELETE",body:JSON.stringify({key:r,name:e,version:t}),error:s})},getPromptDetails:e=>{const t=new URLSearchParams;t.append("name",e);const r=["ajax-api/2.0/mlflow/registered-models/get",t.toString()].join("?");return(0,n.AC)({relativeUrl:r,error:s})},getPromptVersions:e=>{const t=new URLSearchParams;t.append("filter",`name='${e}' AND tags.\`${o.PS}\` = '${o.pY}'`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,n.AC)({relativeUrl:r,error:s})},getPromptVersionsForRun:e=>{const t=new URLSearchParams;t.append("filter",`tags.\`${o.PS}\` = '${o.pY}' AND tags.\`${o.xd}\` ILIKE "%${e}%"`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,n.AC)({relativeUrl:r,error:s})},deleteRegisteredPrompt:e=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete",method:"DELETE",body:JSON.stringify({name:e}),error:s}),deleteRegisteredPromptVersion:(e,t)=>(0,n.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete",method:"DELETE",body:JSON.stringify({name:e,version:t}),error:s})}},83090:function(e,t,r){r.d(t,{i:function(){return i}});var a=r(77020),n=r(43683),o=r(82832),s=r(31014),l=r(98590);const i=e=>{let{onSuccess:t}=e;const r=(0,a.n)({mutationFn:async e=>{let{toAdd:t,toDelete:r,promptId:a}=e;return Promise.all([...t.map((e=>{let{key:t,value:r}=e;return o.M.setRegisteredPromptTag(a,t,r)})),...r.map((e=>{let{key:t}=e;return o.M.deleteRegisteredPromptTag(a,t)}))])}}),{EditTagsModal:i,showEditTagsModal:u,isLoading:c}=(0,n.Q)({valueRequired:!0,saveTagsHandler:(e,a,n)=>{const{addedOrModifiedTags:o,deletedTags:s}=(0,l.Rf)(a,n);return new Promise(((a,n)=>{if(!e.name)return n();r.mutate({promptId:e.name,toAdd:o,toDelete:s},{onSuccess:()=>{a(),null===t||void 0===t||t()},onError:n})}))}});return{EditTagsModal:i,showEditPromptTagsModal:(0,s.useCallback)((e=>u({name:e.name,tags:e.tags.filter((e=>(0,l.oD)(e.key)))})),[u]),isLoading:c}}},84069:function(e,t,r){r.d(t,{m:function(){return s}});var a=r(3293),n=r(50111);var o={name:"gmowil",styles:"height:calc(100% - 60px)"};const s=e=>{let{children:t,className:r}=e;return(0,n.Y)(a.ffj,{css:o,className:r,children:t})}},84565:function(e,t,r){r.d(t,{Dh:function(){return a},Dp:function(){return l},PS:function(){return o},dv:function(){return i},pY:function(){return s},xd:function(){return n}});const a="mlflow.prompt.text",n="mlflow.prompt.run_ids",o="mlflow.prompt.is_prompt",s="true";let l=function(e){return e.TABLE="table",e.PREVIEW="preview",e.COMPARE="compare",e}({});const i=e=>{var t,r;return null===e||void 0===e||null===(t=e.tags)||void 0===t||null===(r=t.find((e=>e.key===a)))||void 0===r?void 0:r.value}},91231:function(e,t,r){var a=r(31014);var n="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=a.useState,s=a.useEffect,l=a.useLayoutEffect,i=a.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(a){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),a=o({inst:{value:r,getSnapshot:t}}),n=a[0].inst,c=a[1];return l((function(){n.value=r,n.getSnapshot=t,u(n)&&c({inst:n})}),[e,r,t]),s((function(){return u(n)&&c({inst:n}),e((function(){u(n)&&c({inst:n})}))}),[e]),i(r),r};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:c},98597:function(e,t,r){r.d(t,{t:function(){return v}});var a=r(89555),n=r(3293),o=r(77520),s=r(31014),l=r(88464),i=r(42848),u=r(56412),c=r(50111);const{Paragraph:d}=o.T;var m={name:"zjik7",styles:"display:flex"},p={name:"1ff36h2",styles:"flex-grow:1"};const f=s.memo((e=>{const{theme:t}=(0,o.u)();return(0,c.Y)(i.d,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17",title:"Tag: "+e.tagKey,visible:e.isKeyValueTagFullViewModalVisible,onCancel:()=>e.setIsKeyValueTagFullViewModalVisible(!1),children:(0,c.FD)("div",{css:m,children:[(0,c.Y)(d,{css:p,children:(0,c.Y)("pre",{css:(0,a.AH)({backgroundColor:t.colors.backgroundPrimary,marginTop:t.spacing.sm,whiteSpace:"pre-wrap",wordBreak:"break-all"},""),children:e.tagValue})}),(0,c.Y)("div",{css:(0,a.AH)({marginTop:t.spacing.sm},""),children:(0,c.Y)(u.i,{copyText:e.tagValue,showLabel:!1,icon:(0,c.Y)(n.TdU,{}),"aria-label":"Copy"})})]})})})),g=30;function h(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?{overflow:"hidden",textOverflow:"ellipsis",textWrap:"nowrap",whiteSpace:"nowrap"}:{whiteSpace:"nowrap"}}const v=e=>{let{isClosable:t=!1,onClose:r,tag:i,enableFullViewModal:u=!1,charLimit:d=g,maxWidth:m=300,className:p}=e;const v=(0,l.A)(),[y,w]=(0,s.useState)(!1),{shouldTruncateKey:M,shouldTruncateValue:b}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:g;const{key:r,value:a}=e,n=r.length+a.length,o=r.length>a.length,s=o?a.length:r.length;return n<=t?{shouldTruncateKey:!1,shouldTruncateValue:!1}:s>t/2?{shouldTruncateKey:!0,shouldTruncateValue:!0}:{shouldTruncateKey:o,shouldTruncateValue:!o}}(i,d),x=u&&(M||b),C=v.formatMessage({id:"ZXUtU8",defaultMessage:"Click to see more"});return(0,c.FD)("div",{children:[(0,c.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60",closable:t,onClose:r,title:i.key,className:p,children:(0,c.Y)(n.paO,{title:x?C:"",children:(0,c.FD)("span",{css:(0,a.AH)({maxWidth:m,display:"inline-flex"},""),onClick:()=>x?w(!0):void 0,children:[(0,c.Y)(o.T.Text,{bold:!0,title:i.key,css:h(M),children:i.key}),i.value&&(0,c.FD)(o.T.Text,{title:i.value,css:h(b),children:[": ",i.value]})]})})}),(0,c.Y)("div",{children:y&&(0,c.Y)(f,{tagKey:i.key,tagValue:i.value,isKeyValueTagFullViewModalVisible:y,setIsKeyValueTagFullViewModalVisible:w})})]})}}}]);
//# sourceMappingURL=2025.8816c0df.chunk.js.map