{"version": 3, "file": "static/js/252.c6c53062.chunk.js", "mappings": "gPAiBA,MAAM,KAAEA,GAASC,EAAAA,EAajB,SAASC,EAAkBC,EAAwBC,GACjD,MAAM,KAAEC,GAASF,EAEXG,EAAc,IAAIC,OAfC,EAeMH,GAC/B,GAAa,WAATC,EAAmB,CAUrB,MAAO,GAAGC,OATYE,OAAOC,KAAKN,EAAWO,YAAYC,KAAKC,IAC5D,MAAMC,EAAWV,EAAWO,WAAWE,GACjCE,EAAeD,EAASE,SAAW,GAAK,cACxCC,EAAed,EAAkBW,EAAUT,EAAmB,GAC9Da,EArBe,GAqBCb,EAAmB,GAEzC,MAAO,GAAG,IAAIG,OAAOU,KAAgBL,MAAiBI,EAAaE,MAAMD,GAAgBH,GAAc,IAGhEK,KAAK,WAAWb,IAC3D,CAEA,GAAa,UAATD,EAAkB,CACpB,MAAMY,EA9BiB,EA8BFb,EAErB,MAAO,GAAGE,UADYJ,EAAkBC,EAAWiB,MAAOhB,GAAkBc,MAAMD,KAEpF,CAEA,MAAO,GAAGX,IAAcD,GAC1B,CAAC,IAAAgB,EAAA,CAAAC,KAAA,SAAAC,OAAA,oBAED,SAASC,EAAUC,GAAmE,IAAlE,KAAEC,GAAyCD,EACzDV,GAAW,OACOY,IAAlBD,EAAKX,WACJA,YAAaW,QACWC,IAAlBD,EAAKE,UAA0BF,EAAKE,WAC7Cb,GAAW,GAEb,MAAMc,EAAcd,GAAWe,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAAAC,SAAC,gBAAoBF,EAAAA,EAAAA,GAAC9B,EAAI,CAACiC,MAAM,YAAWD,SAAC,eAEhFV,EAAO,SAAUI,EAAOA,EAAKJ,KAAO,IAE1C,OACEY,EAAAA,EAAAA,IAAClC,EAAI,CAACmC,IAAGd,EAAqBW,SAAA,CAC3BV,EAAK,IAAEO,IAGd,CAEA,SAASO,EAAYC,GAAmE,IAAlE,KAAEX,GAAyCW,EAC/D,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KACZC,EAAqB,WAAdd,EAAKrB,KAlDX,mBADkBoC,EAmD+Bf,GAlDpB,eAAegB,kBAAkBD,EAAW,eAAeE,UAkD/BzC,EAAkBwB,EAAM,GAnD1F,IAA2Be,EAqDzB,OACEX,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACHC,WAAY,WACZC,QAASR,EAAMS,QAAQC,GACvBC,UAAWX,EAAMS,QAAQC,GACzBE,aAAcZ,EAAMS,QAAQC,IAC7B,IAAChB,SAEDQ,GAGP,CAAC,IAAAW,EAAA,CAAA7B,KAAA,UAAAC,OAAA,6BAAA6B,EAAA,CAAA9B,KAAA,SAAAC,OAAA,6BAED,MAAM8B,EAAiBC,IAAmE,IAAlE,WAAEC,GAA0DD,EAClF,OAAIE,EAAAA,EAAAA,SAAQD,IAERzB,EAAAA,EAAAA,GAAC2B,EAAAA,IAAQ,CAAAzB,UACPF,EAAAA,EAAAA,GAAC4B,EAAAA,IAAS,CAAA1B,UACRF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sGAIfC,OAAQ,CACNC,KAAOC,IACLlC,EAAAA,EAAAA,GAAA,KAAGmC,KAAMC,EAAAA,GAA0BC,OAAO,SAASC,IAAI,aAAYpC,SAChEgC,YAUflC,EAAAA,EAAAA,GAAAuC,EAAAA,GAAA,CAAArC,SACa,OAAVuB,QAAU,IAAVA,OAAU,EAAVA,EAAY5C,KAAI,CAAC2D,EAAWC,KAC3BrC,EAAAA,EAAAA,IAACuB,EAAAA,IAAQ,CAAAzB,SAAA,EACPF,EAAAA,EAAAA,GAAC4B,EAAAA,IAAS,CAACvB,IAAGgB,EAAoCnB,UAChDF,EAAAA,EAAAA,GAACN,EAAU,CAACE,KAAM4C,OAEpBxC,EAAAA,EAAAA,GAAC4B,EAAAA,IAAS,CAACvB,IAAGiB,EAAoCpB,UAChDF,EAAAA,EAAAA,GAACM,EAAY,CAACV,KAAM4C,QALTC,MAShB,EAEL,IAAAC,EAAA,CAAAlD,KAAA,SAAAC,OAAA,mBAAAkD,EAAA,CAAAnD,KAAA,UAAAC,OAAA,UAAAmD,EAAA,CAAApD,KAAA,UAAAC,OAAA,UAAAoD,EAAA,CAAArD,KAAA,SAAAC,OAAA,kBAAAqD,EAAA,CAAAtD,KAAA,SAAAC,OAAA,kBAEK,MAAMsD,EAAcC,IAA8C,IAA7C,OAAEC,EAAM,qBAAEC,GAA6BF,EACjE,MAAM,MAAExC,IAAUC,EAAAA,EAAAA,MACX0C,EAAgBC,IAAqBC,EAAAA,EAAAA,UAASH,IAC9CI,EAAiBC,IAAsBF,EAAAA,EAAAA,UAASH,GAEvD,OACE9C,EAAAA,EAAAA,IAACoD,EAAAA,IAAK,CAACnD,IAAGqC,EAAoBxC,SAAA,EAC5BE,EAAAA,EAAAA,IAACuB,EAAAA,IAAQ,CAAC8B,UAAQ,EAAAvD,SAAA,EAChBF,EAAAA,EAAAA,GAAC0D,EAAAA,IAAW,CAACC,YAAY,kCAAkCtD,IAAGsC,EAAczC,UAC1EF,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAACI,KAAGS,EAAAA,EAAAA,IAAE,CAAE8C,YAAapD,EAAMS,QAAQ4C,GAAKrD,EAAMS,QAAQ6C,IAAI,IAAC5D,UACnEF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAKrB/B,EAAAA,EAAAA,GAAC0D,EAAAA,IAAW,CAACC,YAAY,kCAAkCtD,IAAGuC,EAAc1C,UAC1EF,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAAAC,UACRF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAMvB3B,EAAAA,EAAAA,IAAAmC,EAAAA,GAAA,CAAArC,SAAA,EACEF,EAAAA,EAAAA,GAAC2B,EAAAA,IAAQ,CAACoC,QAASA,IAAMX,GAAmBD,GAAiB9C,IAAGwC,EAAwB3C,UACtFF,EAAAA,EAAAA,GAAC4B,EAAAA,IAAS,CAAA1B,UACRE,EAAAA,EAAAA,IAAA,OAAKC,KAAGS,EAAAA,EAAAA,IAAE,CAAEkD,QAAS,OAAQC,WAAY,SAAUC,IAAK1D,EAAMS,QAAQ6C,IAAI,IAAC5D,SAAA,EACzEF,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACHqD,MAAO3D,EAAMS,QAAQ4C,GACrBO,OAAQ5D,EAAMS,QAAQ4C,GACtBG,QAAS,OACTC,WAAY,SACZI,eAAgB,SAChBC,IAAK,CACHnE,MAAOK,EAAM+D,OAAOC,gBAEvB,IAACtE,SAEDiD,GAAiBnD,EAAAA,EAAAA,GAACyE,EAAAA,IAAe,KAAMzE,EAAAA,EAAAA,GAAC0E,EAAAA,IAAc,OAEzD1E,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAEfC,OAAQ,CACN2C,UAAW1B,EAAO2B,OAAOC,iBAMlC1B,IAAkBnD,EAAAA,EAAAA,GAACuB,EAAc,CAACE,WAAYwB,EAAO2B,UACtD5E,EAAAA,EAAAA,GAAC2B,EAAAA,IAAQ,CAACoC,QAASA,IAAMR,GAAoBD,GAAkBjD,IAAGyC,EAAwB5C,UACxFF,EAAAA,EAAAA,GAAC4B,EAAAA,IAAS,CAAA1B,UACRE,EAAAA,EAAAA,IAAA,OAAKC,KAAGS,EAAAA,EAAAA,IAAE,CAAEkD,QAAS,OAAQC,WAAY,SAAUC,IAAK1D,EAAMS,QAAQ6C,IAAI,IAAC5D,SAAA,EACzEF,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACHqD,MAAO3D,EAAMS,QAAQ4C,GACrBO,OAAQ5D,EAAMS,QAAQ4C,GACtBG,QAAS,OACTC,WAAY,SACZI,eAAgB,SAChBC,IAAK,CACHnE,MAAOK,EAAM+D,OAAOC,gBAEvB,IAACtE,SAEDoD,GAAkBtD,EAAAA,EAAAA,GAACyE,EAAAA,IAAe,KAAMzE,EAAAA,EAAAA,GAAC0E,EAAAA,IAAc,OAE1D1E,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAEfC,OAAQ,CACN8C,WAAY7B,EAAO8B,QAAQF,iBAMpCvB,IAAmBtD,EAAAA,EAAAA,GAACuB,EAAc,CAACE,WAAYwB,EAAO8B,eAEnD,C,8VChM2B,IAAApF,EAAA,CAAAH,KAAA,UAAAC,OAAA,oBAMhC,MAAMuF,EAAsBC,IACjC,MAAMC,GAAOC,EAAAA,EAAAA,KACPC,GAAWC,EAAAA,EAAAA,MAEXC,GAAiCC,EAAAA,EAAAA,SAAOC,EAAAA,EAAAA,OACxCC,GAA8BF,EAAAA,EAAAA,SAAOC,EAAAA,EAAAA,QAErC,aAAEE,GAAiBT,GAClBU,EAASC,IAAcvC,EAAAA,EAAAA,WAAS,IAChCwC,EAAgBC,IAAqBzC,EAAAA,EAAAA,WAAS,GAC/C0C,GAAWC,EAAAA,EAAAA,MAEXC,GAAcC,EAAAA,EAAAA,KAAaC,GAAsBA,EAAMC,SAASH,cAEhEI,GAAOd,EAAAA,EAAAA,UASPe,EAAyBA,KAC7BV,GAAW,GACXE,GAAkB,EAAM,EAGpBS,EAA6BC,IACjCV,GAAkB,GAClBW,EAAAA,EAAMC,sBAAsBF,EAAE,EAG1BG,GAA+BC,EAAAA,EAAAA,cAClCC,IACCd,GAASe,EAAAA,EAAAA,KAA0BC,EAAAA,EAAAA,IAAmBF,GAzCvB,GAyC6D,GAE9F,CAACd,IAGGiB,GAAwCC,EAAAA,EAAAA,UAC5C,KAAMC,EAAAA,EAAAA,UAASP,EAA8B,MAC7C,CAACA,KAgDHQ,EAAAA,EAAAA,YAAU,KACRpB,GAASe,EAAAA,EAAAA,MAA4B,GACpC,CAACf,KAEJoB,EAAAA,EAAAA,YAAU,KACJxB,GACFI,GAASe,EAAAA,EAAAA,MACX,GACC,CAACf,EAAUJ,IAsCd,OACEvF,EAAAA,EAAAA,IAAA,OAAKgH,UAAU,4BAA2BlH,SAAA,EACxCF,EAAAA,EAAAA,GAACqH,EAAAA,EAAM,CACL1D,YAAY,8EACZyD,UAAU,oBACV7I,KAAK,UACLwF,QA/HoBuD,KACxB1B,GAAW,EAAK,EA8He1F,UAE3BF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAInB/B,EAAAA,EAAAA,GAACuH,EAAAA,EAAK,CACJC,OACExH,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yDAEfC,OAAQ,CAAEyF,gBAAiB/B,EAAalG,KAAMkI,mBAAoBhC,EAAaiC,WAInFxD,MAAO,IACPwB,QAASA,EACTiC,KAnHkBC,KACtBxB,EAAKyB,QAAQC,iBAAiBC,MAAMhG,IAClC8D,GAAkB,GAClB,MAAMmC,EAAoBjG,EAAOkG,EAAAA,IAC3BC,EAAa,WAAazC,EAAalG,KAAO,IAAMkG,EAAaiC,QACvE,GAAIM,IAAsBG,EAAAA,GAA+B,CACvD,MAAMC,EAAerG,EAAOsG,EAAAA,IAC5BvC,GAASwC,EAAAA,EAAAA,IAAyBF,EAAc/C,EAA+BwC,UAC5EE,MAAK,IACJjC,GACEyC,EAAAA,EAAAA,IACEH,EACAF,EACAzC,EAAa+C,OACb/C,EAAagD,KACbjD,EAA4BqC,YAIjCE,MAAMW,IACLrC,IACA,MAAM,QAAEqB,GAAYgB,EAASC,MAAqB,cAClDxD,EAASyD,EAAAA,EAAoBC,yBAAyBT,EAAcV,GAAS,IAE9EoB,MAAMxC,EACX,MACER,GACEyC,EAAAA,EAAAA,IACEP,EACAE,EACAzC,EAAa+C,OACb/C,EAAagD,KACbjD,EAA4BqC,UAG7BE,MAAMW,IACLrC,IACA,MAAM,QAAEqB,GAAYgB,EAASC,MAAqB,cAClDxD,EAASyD,EAAAA,EAAoBC,yBAAyBb,EAAmBN,GAAS,IAEnFoB,MAAMxC,EACX,GACA,EA0EEyC,OAAQ9D,EAAK+D,cAAc,CAAAnH,GAAA,SACzBC,eAAe,YAGjBmH,WAAYhE,EAAK+D,cAAc,CAAAnH,GAAA,SAC7BC,eAAe,WAGjB8D,eAAgBA,EAChBsD,SAvJoBC,KACxBxD,GAAW,EAAM,EAuJbyD,UAAQ,EAAAnJ,UArEVE,EAAAA,EAAAA,IAAAmC,EAAAA,GAAA,CAAArC,SAAA,EACEF,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWmL,UAAS,CAACjJ,IAAGV,EAAyBO,UAChDF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gRAKfC,OAAQ,CACNC,KAAOC,IACLlC,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWoL,KAAI,CACd5F,YAAY,8EACZxB,KACE,mGAGFqH,cAAY,EAAAtJ,SAEXgC,UAMXlC,EAAAA,EAAAA,GAACyJ,EAAAA,GAAiB,CAChBxD,YAAaA,EACbyD,SAAUrD,EACVsD,yBAA0B3C,EAC1B4C,QAAM,WA6CN,E,0DCvLH,IAAKC,EAAiC,SAAjCA,GAAiC,OAAjCA,EAAAA,EAAiC,qCAAjCA,EAAAA,EAAiC,qBAAjCA,EAAAA,EAAiC,mBAAjCA,EAAAA,EAAiC,mBAAjCA,CAAiC,MAOtC,MAAMC,EAAgCnK,IAcK,IAdJ,QAC5CgG,EAAO,SACPwD,EAAQ,QACRY,EAAO,+BACPC,EAA8B,sBAC9BC,EAAqB,UACrBC,EAAS,KACTC,EAAON,EAAkCO,iBAOCzK,EAC1C,MAAM,MAAEa,IAAUC,EAAAA,EAAAA,KACZ4F,GAAOgE,EAAAA,EAAAA,IAA6C,CACxDC,cAAe,CACbC,QAAS,GACTC,yBAAyB,KA4C7B,OANArD,EAAAA,EAAAA,YAAU,KACJxB,GACFU,EAAKoE,OACP,GACC,CAACpE,EAAMV,KAGRvF,EAAAA,EAAAA,IAACmH,EAAAA,EAAK,CACJC,MAzCE2C,IAASN,EAAkCa,SAE3C1K,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAKjBoI,IAASN,EAAkCc,QAE3C3K,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2BAKjBoI,IAASN,EAAkCe,QAE3C5K,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAMnB/B,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAgBjB4B,YAAY,kDACZgC,QAASA,EACTwD,SAAUA,EACVH,QACEhJ,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,OAInBmH,YACElJ,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInB6F,KAAMsC,GAAa7D,EAAKwE,aAAaX,GAAWhK,SAAA,CAE/C+J,GACDjK,EAAAA,EAAAA,GAAC8K,EAAAA,EAAM,CAACC,KAAK,QACb/K,EAAAA,EAAAA,GAACgL,EAAAA,IAAOC,MAAK,CAACC,QAAQ,0DAAyDhL,SAAC,aAChFF,EAAAA,EAAAA,GAACmL,EAAAA,IAAwBC,SAAQ,CAC/B5L,KAAK,UACLsC,GAAG,0DACH6B,YAAY,0DACZ0H,QAAShF,EAAKgF,QACdC,KAAM,KAERtL,EAAAA,EAAAA,GAAC8K,EAAAA,EAAM,CAACC,KAAK,OAEZf,GAAkCD,IACjC/J,EAAAA,EAAAA,GAACmL,EAAAA,IAAwBI,SAAQ,CAC/B/L,KAAK,0BACLmE,YAAY,4EACZ0H,QAAShF,EAAKgF,QAAQnL,UAEtBF,EAAAA,EAAAA,GAACwL,EAAAA,EAAO,CACN7H,YAAY,oFACZ8H,SAASC,EAAAA,EAAAA,IAAkC3B,GAAS7J,UAEpDF,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAE,gBAAiB,CAAE6K,YAAanL,EAAMS,QAAQ6C,KAAM,IAAC5D,UAChEF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sEAGfC,OAAQ,CACN4J,cAAc5L,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAE+K,WAAYrL,EAAMS,QAAQ6C,IAAI,IAAC5D,SAAE4L,EAAAA,GAAmB/B,KAC/EgC,eACE/L,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAE+K,WAAYrL,EAAMS,QAAQ6C,IAAI,IAAC5D,SAAE4L,EAAAA,GAAmBE,EAAAA,GAAOC,uBAQhF,EC1HL,MAAMC,UAAqCC,EAAAA,UAGhDC,WAAAA,GAAA,SAAAC,WAAA,KAKAlG,MAA2C,CACzCmG,qBAAqB,EACrBC,mBAAoB,KACpBC,mBAAe3M,GACf,KAEF4M,oBAAuBC,IACrB,MAAM,SAAEC,GAAaC,KAAK3H,MAC1B2H,KAAKC,SAAS,CACZP,qBAAqB,EACrBC,mBAAoBG,EACpBF,cACEG,GAAQ,CACN3K,IAGA,GAFA4K,KAAKC,SAAS,CAAEP,qBAAqB,IAEjCtK,EAAJ,CACE,MAAM,wBAAEwI,GAA0B,GAAUxI,EAE5C2K,EAASD,EAAUlC,EAErB,MACD,IACH,EACF,KAEFsC,yBAA2B,KACzBF,KAAKC,SAAS,CAAEP,qBAAqB,GAAQ,EAC7C,KAEFS,qBAAwBnB,IACtB,MAAMoB,EAAStO,OAAOsD,OAAOgK,EAAAA,IAE7B,OADAiB,IAAAA,OAASD,GAASE,GAAMA,IAAMtB,IACvBoB,CAAM,CACb,CAEFG,OAAAA,GACE,MAAM,aAAEvB,GAAiBgB,KAAK3H,MACxBmI,EAAmBR,KAAKG,qBAAqBnB,GACnD,OACE5L,EAAAA,EAAAA,GAACqN,EAAAA,IAAI,CAAAnN,SACFkN,EAAiBvO,KAAKyO,IACrBlN,EAAAA,EAAAA,IAACiN,EAAAA,IAAKE,KAAI,CAERxJ,QAASA,IACP6I,KAAKH,oBAAoB,CACvBlO,KAAMiP,EAAAA,GAAcC,mBACpBC,SAAUJ,IAEbpN,SAAA,EAEDF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGf,gBAEF/B,EAAAA,EAAAA,GAAC2N,EAAAA,IAAc,IAAG,eAEjB7B,EAAAA,GAAmBwB,KAhBf,iBAAiBA,QAqBhC,CAEAM,kBAAAA,GACE,MAAM,oBAAEtB,EAAmB,mBAAEC,EAAkB,cAAEC,GAAkBI,KAAKzG,MAExE,IAAKoG,EACH,OAAO,KAGT,MAAMvC,EACJuC,EAAmBhO,OAASiP,EAAAA,GAAcC,oBAC1CI,EAAAA,GAAcC,SAASvB,EAAmBmB,UAE5C,OACE1N,EAAAA,EAAAA,GAAC8J,EAA6B,CAC5BnE,QAAS2G,EACTvC,QAASwC,EAAmBmB,SAC5BxD,UAAWsC,EACXrD,SAAUyD,KAAKE,yBACf7C,sBAAuB8D,EAA0BxB,GACjDvC,+BAAgCA,GAGtC,CAEAgE,MAAAA,GACE,MAAM,aAAEpC,GAAiBgB,KAAK3H,MAC9B,OACE7E,EAAAA,EAAAA,IAAA,QAAAF,SAAA,EACEF,EAAAA,EAAAA,GAACiO,EAAAA,IAAQ,CAACC,QAAStB,KAAKO,UAAWgB,QAAS,CAAC,SAAU/G,UAAU,4BAA2BlH,UAC1FE,EAAAA,EAAAA,IAAA,QAAAF,SAAA,CACG4L,EAAAA,GAA+B,OAAZF,QAAY,IAAZA,EAAAA,EAAgBI,EAAAA,GAAOoC,OAC3CpO,EAAAA,EAAAA,GAACqO,EAAAA,IAAe,CAAChO,KAAGS,EAAAA,EAAAA,IAAE,CAAEwN,OAAQ,UAAWzC,YAAa,GAAG,WAG9De,KAAKgB,uBAGZ,EA9GW1B,EAIJqC,aAAe,CACpB3C,aAAcI,EAAAA,GAAOoC,MA4GzB,MAAML,EAA6BrB,GAC7BA,GAEAtM,EAAAA,EAAAA,IAAA,OAAAF,SAAA,EACEF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGf,gBAEF/B,EAAAA,EAAAA,GAAC2N,EAAAA,IAAc,IAAG,eAEjB7B,EAAAA,GAAmBY,EAASgB,aAI5B,K,uJCtJ2B,IAAAnO,EAAA,CAAAC,KAAA,SAAAC,OAAA,kDAE7B,MAAM+O,EAA8B7O,IAUpC,IAVqC,QAC1C8O,EAAU,GAAE,YACZC,EAAW,QACX/G,EAAO,kBACPgH,GAMDhP,EACC,MAAM,iBAAEiP,EAAgB,qBAAEC,IAAyBC,EAAAA,EAAAA,GAAmC,CACpFC,MAAOL,GAAe,KACtBM,UAAWL,IAEPM,GAAYrI,EAAAA,EAAAA,cAAY,KAC5BiI,EAAqBlH,EAAQ,GAC5B,CAACkH,EAAsBlH,IAC1B,OACEvH,EAAAA,EAAAA,IAAAmC,EAAAA,GAAA,CAAArC,SAAA,CACG0O,EACAH,EAAQ5J,OAAS,GAChB7E,EAAAA,EAAAA,GAACqH,EAAAA,EAAM,CACL1D,YAAY,8FACZoH,KAAK,QACLxM,KAAK,OACLwF,QAASkL,EACTzH,MAAM,cAAatH,SACpB,SAIDE,EAAAA,EAAAA,IAAA,OAAKC,IAAGd,EAA8DW,SAAA,CACnEuO,EAAQ5P,KAAKqQ,IACZlP,EAAAA,EAAAA,GAACmP,EAAAA,EAAoB,CAACC,SAAO,EAACxG,MAAOsG,GAAYA,MAEnDlP,EAAAA,EAAAA,GAACqH,EAAAA,EAAM,CACL1D,YAAY,8FACZoH,KAAK,QACLsE,MAAMrP,EAAAA,EAAAA,GAACsP,EAAAA,IAAU,IACjBvL,QAASkL,EACTzH,MAAM,sBAIX,E,eCTwD,IAAAnG,EAAA,CAAA7B,KAAA,SAAAC,OAAA,mCAAA6B,EAAA,CAAA9B,KAAA,SAAAC,OAAA,oBAAAuD,EAAA,CAAAxD,KAAA,SAAAC,OAAA,wDAgCxD,MAAM8P,UAA6BpD,EAAAA,UAAsEC,WAAAA,GAAA,SAAAC,WAAA,KAC9GlG,MAAQ,CACNqJ,sBAAsB,EACtBC,6BAA6B,EAC7BC,uBAAuB,EACvBC,sBAAsB,GACtB,KAEFC,QAAUzD,EAAAA,YAAkB,KAO5B0D,oBAAsB,KACpB,MAAM,UAAEC,EAAY,GAAE,aAAEpK,EAAY,SAAEN,GAAawH,KAAK3H,OAClD,QAAE0C,GAAYjC,EACpBkH,KAAKmD,qBACLnD,KAAK3H,MACF+K,sBAAsBF,EAAWnI,GACjCK,MAAK,KACJ5C,EAASyD,EAAAA,EAAoBoH,kBAAkBH,GAAW,IAE3D/G,OAAOvC,IACNoG,KAAKsD,qBACLzJ,EAAAA,EAAMC,sBAAsBF,EAAE,GAC9B,EACJ,KAEF2J,gBAAkB,KAChBvD,KAAKC,SAAS,CAAE2C,sBAAsB,GAAO,EAC7C,KAEFY,gBAAkB,KAChBxD,KAAKC,SAAS,CAAE2C,sBAAsB,GAAQ,EAC9C,KAEFO,mBAAqB,KACnBnD,KAAKC,SAAS,CAAE4C,6BAA6B,GAAO,EACpD,KAEFS,mBAAqB,KACnBtD,KAAKC,SAAS,CAAE4C,6BAA6B,GAAQ,EACrD,KAEFY,4BAA8B,KAC5BzD,KAAKC,SAAS,CAAE6C,uBAAuB,GAAQ,EAC/C,KAEFY,4BAA+BC,GACtB3D,KAAK3H,MAAMuL,sBAAsBD,GAAavI,MAAK,KACxD4E,KAAKC,SAAS,CAAE6C,uBAAuB,GAAQ,IAEjD,KAEFe,wBAA2BjK,IACzBA,EAAEkK,kBACF9D,KAAKC,SAAS,CAAE6C,uBAAuB,GAAO,EAC9C,KAEFiB,aAAgB3O,IACd,MAAMqE,EAAOuG,KAAKgD,QAAQ9H,SACpB,UAAEgI,GAAclD,KAAK3H,OACrB,QAAE0C,GAAYiF,KAAK3H,MAAMS,aAC/BkH,KAAKC,SAAS,CAAE8C,sBAAsB,IACtC/C,KAAK3H,MACF2L,sBAAsBd,EAAWnI,EAAS3F,EAAOxC,KAAMwC,EAAO4G,OAC9DZ,MAAK,KACJ4E,KAAKC,SAAS,CAAE8C,sBAAsB,IACrCtJ,EAAawK,aAAa,IAE5B9H,OAAO+H,IACNlE,KAAKC,SAAS,CAAE8C,sBAAsB,IAEtCoB,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhF3K,EAAAA,EAAM4K,+BACJzE,KAAK3H,MAAMC,KAAK+D,cACd,CAAAnH,GAAA,SACEC,eAAe,gDAGjB,CACEkP,qBAGL,GACD,EACJ,KAEFK,eAAiB3R,IAA2B,IAA1B,KAAEH,EAAI,MAAEoJ,GAAYjJ,EACpC,MAAM,UAAEmQ,GAAclD,KAAK3H,OACrB,QAAE0C,GAAYiF,KAAK3H,MAAMS,aAC/B,OAAOkH,KAAK3H,MAAM2L,sBAAsBd,EAAWnI,EAASnI,EAAMoJ,GAAOG,OAAO+H,IAE9EC,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhF3K,EAAAA,EAAM4K,+BACJzE,KAAK3H,MAAMC,KAAK+D,cACd,CAAAnH,GAAA,SACEC,eAAe,gDAGjB,CACEkP,qBAGL,GACD,EACF,KAEFM,gBAAkBhS,IAAoB,IAAnB,KAAEC,GAAWD,EAC9B,MAAM,UAAEuQ,GAAclD,KAAK3H,OACrB,QAAE0C,GAAYiF,KAAK3H,MAAMS,aAC/B,OAAOkH,KAAK3H,MAAMuM,yBAAyB1B,EAAWnI,EAASnI,GAAMuJ,OAAO+H,IAE1EC,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhF3K,EAAAA,EAAM4K,+BACJzE,KAAK3H,MAAMC,KAAK+D,cACd,CAAAnH,GAAA,SACEC,eAAe,mDAGjB,CACEkP,qBAGL,GACD,EACF,KAwKFQ,kBAAoB,KAAO,IAADC,EAAAC,EAExB,MAAMC,EAAiBhF,KAAK3H,MAAMS,aAAaiC,QACzCkK,GACkB,QAAtBH,EAAA9E,KAAK3H,MAAMyJ,mBAAW,IAAAgD,GAAS,QAATC,EAAtBD,EAAwBjD,eAAO,IAAAkD,OAAT,EAAtBA,EAAiCG,QAAOvR,IAAA,IAAC,QAAEoH,GAASpH,EAAA,OAAKoH,IAAYiK,CAAc,IAAE/S,KAAI2C,IAAA,IAAC,MAAE0N,GAAO1N,EAAA,OAAK0N,CAAK,MAC7G,GACF,OACElP,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,YAEd7B,UAEHF,EAAAA,EAAAA,GAACwO,EAA2B,CAC1BC,QAASoD,EACTlK,QAASiF,KAAK3H,MAAMS,aAAaiC,QACjC+G,YAAa9B,KAAK3H,MAAMyJ,YACxBC,kBAAmB/B,KAAK3H,MAAM0J,qBAV5B,0BAYc,CAEtB,CA7TFsD,iBAAAA,GACE,MAAMC,EAAY,GAAGtF,KAAK3H,MAAM6K,cAAclD,KAAK3H,MAAMS,aAAaiC,yBACtElB,EAAAA,EAAM0L,gBAAgBD,EACxB,CA8HAE,sBAAAA,GACE,OAAO,CACT,CAEAC,mBAAAA,CAAoB3M,GAClB,MAAM,oCAAE4M,GAAwC1F,KAAK3H,MACrD,OACEjF,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,UAEd7B,SAEFwF,EAAa6M,SAAWC,EAAAA,GAAmBC,OAC1CzS,EAAAA,EAAAA,GAACkM,EAA4B,CAC3BN,aAAclG,EAAagN,cAC3BC,gBAAiBjN,EAAakN,iBAC9BjG,SAAU2F,IAGZxG,EAAAA,GAAmBpG,EAAagN,gBAb9B,wBAiBV,CAEAG,mBAAAA,CAAoBnN,GAClB,MAAMoN,GACJ9S,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2GAGfC,OAAQ,CACNC,KAAOC,IACLlC,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWoL,KAAI,CACd5F,YAAY,4EACZxB,KAAM4Q,EAAAA,GACNvJ,cAAY,EAAAtJ,SAEXgC,OAMX,OACElC,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,uBAEd7B,UAEHE,EAAAA,EAAAA,IAAA,OAAKC,IAAGgB,EAA4CnB,SAAA,CACjD8S,EAAAA,GAAYtN,EAAagN,gBAC1B1S,EAAAA,EAAAA,GAACiT,EAAAA,IAAa,CAACzL,MAAOsL,EAAgBI,UAAU,SAAQhT,UACtDF,EAAAA,EAAAA,GAACmT,EAAAA,EAAQ,CAAC9S,IAAGiB,UATb,iCAcV,CAEA8R,oCAAAA,CAAqCC,GACnC,OACErT,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,kBAEd7B,SAEFuG,EAAAA,EAAM6M,gBAAgBD,EAAoBzG,KAAK3H,MAAMC,OANlD,2BASV,CAEAqO,wBAAAA,CAAyBC,GACvB,OACEA,IACExT,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,YAEd7B,SAEFsT,GANG,0BAUZ,CAEAC,6BAAAA,CAA8BC,GAC5B,OACE1T,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,kBAEd7B,SAEFuG,EAAAA,EAAM6M,gBAAgBI,EAAwB9G,KAAK3H,MAAMC,OANtD,2BASV,CAEAyO,0BAAAA,GAA8B,IAADC,EAE3B,OAA4B,QAAxBA,EAAChH,KAAK3H,MAAMS,oBAAY,IAAAkO,GAAvBA,EAAyBnL,QAI5BzI,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,eAIjBqF,UAAU,aAAYlH,SAErB0M,KAAKiH,kBARF,8BAJC,IAeX,CAEAC,oBAAAA,GACE,MAAM,OAAEC,GAAWnH,KAAK3H,MAAMS,aAE9B,IAAKqO,IADiB,0BACQC,KAAKD,GACjC,OAAO,KAET,MAAME,EAAcF,EAAOG,MAAM,KAC3BzM,EAAkBwM,EAAY,GAC9BvM,EAAqBuM,EAAY,GACjChS,GACJ7B,EAAAA,EAAAA,IAAAmC,EAAAA,GAAA,CAAArC,SAAA,EACEF,EAAAA,EAAAA,GAACuJ,EAAAA,GAAI,CACH,eAAa,mBACb4K,GAAItL,EAAAA,EAAoBC,yBAAyBrB,EAAiBC,GAAoBxH,SAErFuH,IACI,QAEPzH,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iCAEfC,OAAQ,CAAE0F,2BAIhB,OACE1H,EAAAA,EAAAA,GAAC+R,EAAAA,EAAaxE,KAAI,CAEhByE,MAAOpF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,gBAEd7B,SAEF+B,GANG,8BASV,CA0BAmS,eAAAA,CAAgB1O,GACd,MAAM,kBAAE2O,GAAsBzH,KAAK3H,MAWnC,MATqB,CACnB2H,KAAKwG,qCAAqC1N,EAAa2N,oBACvDzG,KAAK2G,yBAAyB7N,EAAa8N,SAC3C5G,KAAK6G,8BAA8B/N,EAAagO,wBAChD9G,KAAK+G,6BACL/G,KAAKkH,uBACLO,EAAoBzH,KAAK6E,oBAAsB7E,KAAKyF,oBAAoB3M,GACxE2O,EAAoBzH,KAAKiG,oBAAoBnN,GAAgB,MAE3CoM,QAAQwC,GAAkB,OAATA,GACvC,CAEAC,cAAAA,CAAe7O,GACb,OAEE1F,EAAAA,EAAAA,GAAC+R,EAAAA,EAAY,CAAC3K,UAAU,gBAAelH,SAAE0M,KAAKwH,gBAAgB1O,IAElE,CAEA8O,iBAAAA,GACE,MAAM,OAAEjC,EAAM,eAAEkC,GAAmB7H,KAAK3H,MAAMS,aAC9C,GAAI6M,IAAWC,EAAAA,GAAmBC,MAAO,CACvC,MAAM1Q,EAAiB2S,EAAAA,GAAkCnC,GAGnDhU,EAAOgU,IAAWC,EAAAA,GAAmBmC,oBAAsB,QAAU,OAC3E,OACE3U,EAAAA,EAAAA,GAAC4U,EAAAA,IAAK,CACJrW,KAAMA,EACN6I,UAAW,6BAA6B7I,IACxC6S,QAASqD,GAAkB1S,EAE3BsN,KAAMwF,EAAAA,GAAwBtC,GAC9BuC,QAAM,GAGZ,CACA,OAAO,IACT,CAEAC,yBAAAA,GACE,OACE3U,EAAAA,EAAAA,IAACiH,EAAAA,EAAM,CACL1D,YAAY,4EACZ,eAAa,wBACbpF,KAAK,OACLwF,QAAS6I,KAAK6D,wBAAwBvQ,SAAA,EAEtCF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,SAGd,MAGT,CAEA8R,cAAAA,GACE,MAAM,aAAEnO,EAAY,QAAEsP,GAAYpI,KAAK3H,MACvC,GAAIS,EAAauP,SACf,OAGEjV,EAAAA,EAAAA,GAAA,KAAGqC,OAAO,SAASF,KAAMuD,EAAauP,SAAS/U,SAC5C0M,KAAKsI,mBAGL,GAAIF,EAAS,CAAC,IAADG,EAClB,IAAIC,EAAe,KACnB,MAAMC,EAAqC,QAA1BF,EAAGvI,KAAK3H,MAAMS,oBAAY,IAAAyP,OAAA,EAAvBA,EAAyBpB,OAI7C,OAHIsB,IACFD,EC1dD,SAA4CC,EAAqBC,GAAgB,IAADC,EACrF,OAAgE,QAAhEA,EAAOF,EAAYG,MAAM,IAAIC,OAAO,IAAIH,4BAAwB,IAAAC,OAAA,EAAzDA,EAA4D,EACrE,CDwduBG,CAAmCL,EAAaL,EAAQW,WAGvE3V,EAAAA,EAAAA,GAACuJ,EAAAA,GAAI,CAAC4K,GAAIyB,EAAAA,EAAQC,gBAAgBb,EAAQc,aAAcd,EAAQW,QAASP,GAAclV,SACpF0M,KAAKsI,kBAGZ,CACA,OAAO,IACT,CAEAA,cAAAA,GACE,MAAM,aAAExP,EAAY,QAAEsP,EAAO,eAAEe,GAAmBnJ,KAAK3H,MACvD,OAAIS,EAAauP,SAGRvP,EAAauP,SAASe,OAAO,EAAG,IAAM,MACpChB,EACFe,GAAkBf,EAAQW,QAE1B,IAEX,CAEAM,uBAAAA,GACE,MAAM,aAAEvQ,EAAY,kBAAE2O,EAAiB,SAAEjP,GAAawH,KAAK3H,MAC3D,OAAOoP,GAAoBrU,EAAAA,EAAAA,GAACgF,EAAkB,CAACU,aAAcA,IAAmB,IAClF,CAEAwQ,aAAAA,CAAc1O,EAAY2O,GACxB,MAAMC,EAAO,CACX,CACEtU,GAAI,SACJuU,UACErW,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInBgC,QAAS6I,KAAKuD,gBACdmG,SAAUzI,EAAAA,GAAcC,SAASlB,KAAK3H,MAAMS,aAAagN,iBAG7D,OACEtS,EAAAA,EAAAA,IAACmW,EAAAA,EAAU,CAAC/O,MAAOA,EAAO2O,YAAaA,EAAYjW,SAAA,EAC/C0M,KAAKwF,2BAA4BpS,EAAAA,EAAAA,GAACwW,EAAAA,EAAY,CAACJ,KAAMA,IACtDxJ,KAAKqJ,4BAGZ,CAEAjI,MAAAA,GACE,MAAM,UAAE8B,EAAY,GAAE,aAAEpK,EAAY,KAAEgD,EAAI,OAAEzF,GAAW2J,KAAK3H,OACtD,YAAEsL,GAAgB7K,GAClB,qBAAE8J,EAAoB,4BAAEC,EAA2B,sBAAEC,EAAqB,qBAAEC,GAChF/C,KAAKzG,MACDqB,GACJxH,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAEfC,OAAQ,CAAEyU,WAAY/Q,EAAaiC,WAGjCwO,EAAc,EAClBnW,EAAAA,EAAAA,GAACuJ,EAAAA,GAAI,CAAC4K,GAAItL,EAAAA,EAAoB6N,mBAAmBxW,UAC/CF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAKnB/B,EAAAA,EAAAA,GAACuJ,EAAAA,GAAI,CAAC,eAAa,4BAA4B4K,GAAItL,EAAAA,EAAoBoH,kBAAkBH,GAAW5P,SACjG4P,KAGL,OACE1P,EAAAA,EAAAA,IAAA,OAAAF,SAAA,CACG0M,KAAKsJ,cAAc1O,EAAO2O,GAC1BvJ,KAAK4H,oBAGL5H,KAAK2H,eAAe7O,IAGpBiR,EAAAA,EAAAA,QACC3W,EAAAA,EAAAA,GAAA,OAAKK,IAAG2C,EAAgE9C,UACtEF,EAAAA,EAAAA,GAAC4W,EAAAA,EAAwB,OAK7B5W,EAAAA,EAAAA,GAAC6W,EAAAA,EAAkB,CACjBrP,OACEpH,EAAAA,EAAAA,IAAA,QAAAF,SAAA,EACEF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAEd,IACD2N,EAA2D,KAAnC9C,KAAKmI,+BAGnC+B,UAAWpH,EACXqH,kBAAmBxG,EACnB,eAAa,oCAAmCrQ,UAEhDF,EAAAA,EAAAA,GAACgX,EAAAA,EAAY,CACXC,gBAAiB1G,EACjB2G,SAAUtK,KAAK0D,4BACfnH,SAAUyD,KAAKyD,4BACf8G,WAAYzH,OAGhB1P,EAAAA,EAAAA,GAAA,OAAK,eAAa,eAAcE,UAC9BF,EAAAA,EAAAA,GAAC6W,EAAAA,EAAkB,CACjBrP,OACExH,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,SAInBgV,iBAA6D,IAA3CtQ,EAAAA,EAAM2Q,oBAAoB1O,GAAM7D,OAClD,eAAa,6BAA4B3E,UAEzCF,EAAAA,EAAAA,GAACqX,EAAAA,EACC,CACA3N,SAAUkD,KAAKgD,QACfe,aAAc/D,KAAK+D,aACnBY,gBAAiB3E,KAAK2E,gBACtBD,eAAgB1E,KAAK0E,eACrB5I,KAAMA,EACN4O,iBAAkB3H,SAIxB3P,EAAAA,EAAAA,GAAC6W,EAAAA,EAAkB,CACjBrP,OACExH,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInB,eAAa,+BAA8B7B,UAE3CF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAW,CAACE,OAAQA,OAEvBjD,EAAAA,EAAAA,GAACuH,EAAAA,EAAK,CACJC,MAAOoF,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACnCC,eAAe,yBAGjB4D,QAAS6J,EACT3J,eAAgB4J,EAChB7H,KAAMgF,KAAKiD,oBACX7G,OAAQ4D,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACpCC,eAAe,WAIjBwV,OAAO,SACPpO,SAAUyD,KAAKwD,gBACflH,WAAY0D,KAAK3H,MAAMC,KAAK+D,cAAc,CAAAnH,GAAA,SACxCC,eAAe,WAEd7B,UAEHF,EAAAA,EAAAA,GAAA,QAAAE,UACEF,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qFAIfC,OAAQ,CAAEyU,WAAY/Q,EAAaiC,iBAM/C,EAGF,MAMM6P,EAAqB,CAAE5G,sBAAqB,KAAEY,yBAAyB,MAEhEiG,GAAmBC,EAAAA,EAAAA,KARRC,CAACxR,EAAYyR,KACnC,MAAM,UAAE9H,GAAc8H,GAChB,QAAEjQ,GAAYiQ,EAASlS,aAE7B,MAAO,CAAEgD,MADImP,EAAAA,EAAAA,IAAoB/H,EAAWnI,EAASxB,GACtC,GAMfqR,EAF8BE,EAG9BI,EAAAA,EAAAA,IAAwBC,EAAAA,EAAAA,IAA8CxI,K,gHExlBjE,MAAMyI,WAA6B7L,EAAAA,UAAsEC,WAAAA,GAAA,SAAAC,WAAA,KAC9G4L,6BAAuB,OACvBC,oBAAc,OAEdC,qCAAsC3S,EAAAA,EAAAA,MAAU,KAChD4S,iBAAkB5S,EAAAA,EAAAA,MAAU,KAC5B6S,6BAA8B7S,EAAAA,EAAAA,MAAU,KACxC8S,sCAAuC9S,EAAAA,EAAAA,MAAU,KACjD+S,iCAAkC/S,EAAAA,EAAAA,MAAU,KAC5CgT,6BAA8BhT,EAAAA,EAAAA,MAAU,KACxCW,MAAQ,CACNsS,0BAA2B,CAAC7L,KAAKuL,oCAAqCvL,KAAK4L,8BAC3E,KAEFE,yBAA2B,CAAC9L,KAAK2L,gCAAiC3L,KAAKwL,iBAAiB,KAExFO,yBAA2B,IACzB/L,KAAK8L,yBAAyBE,OAAOC,IACnC,MAAMC,EAAUlM,KAAK3H,MAAM8T,KAAKF,GAChC,OAAOG,QAAQF,GAAWA,EAAQG,OAAO,IACxC,KAELC,SAAYC,IACV,MAAMC,EAAW,CAACxM,KAAKyM,gCAAgCF,IACvD,OAAOG,QAAQC,IAAIH,EAAS,EAC5B,KAEFI,SAAW,KACT,MAAM,UAAE1J,EAAS,QAAEnI,EAAO,SAAEvC,GAAawH,KAAK3H,MAC9C,OAAK2H,KAAK+L,4BAA8BlS,EAAAA,EAAMgT,sBAErC7M,KAAKsM,WAAWnQ,OAAOvC,IACH,4BAArBA,EAAEkT,gBACJjT,EAAAA,EAAMC,sBAAsBF,GAC5BoG,KAAK3H,MAAM+K,sBAAsBF,EAAWnI,OAAS9H,GAAW,GAChEuF,EAASyD,EAAAA,EAAoBoH,kBAAkBH,KAG/CiB,QAAQC,MAAMxK,EAChB,IAGG8S,QAAQK,SAAS,EAwC1B,KACArH,oCAAsC,CACpC5F,EACAlC,KAEA,MAAM,UAAEsF,EAAS,QAAEnI,GAAYiF,KAAK3H,MAC9B8E,EAAU2C,EAASgB,SACrBhB,EAASnO,OAASiP,EAAAA,GAAcC,oBAClCb,KAAK3H,MACF2U,+BACC9J,EACAnI,EAAQkS,WACR9P,EACAS,EACAoC,KAAK0L,sCAENtQ,KAAK4E,KAAKsM,UACVnQ,MAAMtC,EAAAA,EAAMC,sBACjB,EACA,KAEF8J,sBAAyBD,IACvB,MAAM,UAAET,EAAS,QAAEnI,GAAYiF,KAAK3H,MACpC,OACE2H,KAAK3H,MACF6U,sBAAsBhK,EAAWnI,EAAS4I,EAAa3D,KAAKyL,6BAC5DrQ,KAAK4E,KAAKsM,UAEVnQ,MAAMgI,QAAQC,MAAM,EAEzB,KAUF+I,yBAA2B,KACzBnN,KAAK3H,MAAM+U,sBAAsBpN,KAAK3H,MAAM6K,UAAU,CACtD,CA5EFuJ,+BAAAA,CAAgCF,GAC9B,MAAM,UAAErJ,EAAS,QAAEnI,GAAYiF,KAAK3H,MACpC,OAAO2H,KAAK3H,MACTgV,mBACCnK,EACAnI,GACqB,IAArBwR,EAA4BvM,KAAKuL,oCAAsCvL,KAAK2L,iCAE7EvQ,MAAKrI,IAAqB,IAADua,EAAA,IAAnB,MAAEtR,GAAYjJ,EAEfiJ,IAAUA,GAAMuR,EAAAA,GAAAA,GAAc,kBAAkBlF,UAAiD,QAAzCiF,EAAItR,GAAMuR,EAAAA,GAAAA,GAAc,yBAAiB,IAAAD,GAArCA,EAAuCzR,QACrGmE,KAAK3H,MAAMmV,UAAUxR,GAAMuR,EAAAA,GAAAA,GAAc,kBAAkB1R,OAAQmE,KAAKwL,gBAC1E,GAEN,CAGAiC,0BAAAA,GACE,MAAM,UAAEvK,EAAS,QAAEnI,GAAYiF,KAAK3H,MACpC2H,KAAK3H,MACFqV,2BAA2BxK,EAAWnI,GACtCK,MAAMyD,GACLmB,KAAK3H,MAAMsV,iBAAiBzK,EAAWnI,EAAS8D,EAAQ7C,MAAOgE,KAAK4L,+BAErEzP,OAAM,KAIL6D,KAAKC,UAAU2N,IAAc,CAC3B/B,0BAA2BxL,IAAAA,QAAUuN,EAAU/B,0BAA2B7L,KAAK4L,gCAC9E,GAET,CAkCAvG,iBAAAA,GAEErF,KAAKsM,UAAS,GAAMnQ,MAAMgI,QAAQC,OAClCpE,KAAKmN,2BACLnN,KAAKsL,eAAiBuC,YAAY7N,KAAK4M,SAAUkB,EAAAA,IACjD9N,KAAKyN,4BACP,CAOAM,kBAAAA,CAAmBC,GACbhO,KAAK3H,MAAM0C,UAAYiT,EAAUjT,SAAWiF,KAAK3H,MAAM6K,YAAc8K,EAAU9K,YAEjFlD,KAAKsM,UAAS,GAAMnQ,MAAMgI,QAAQC,OAClCpE,KAAKyN,6BAET,CAEAQ,oBAAAA,GACEC,cAAclO,KAAKsL,eACrB,CAEAlK,MAAAA,GACE,MAAM,UAAE8B,EAAS,QAAEnI,EAAO,aAAEjC,EAAY,QAAEsP,EAAO,eAAEe,EAAc,SAAE3Q,EAAQ,OAAEnC,EAAM,YAAEyL,GAAgB9B,KAAK3H,MAE1G,OACEjF,EAAAA,EAAAA,GAAC+a,GAAAA,EAAa,CAAA7a,UACZF,EAAAA,EAAAA,GAACgb,GAAAA,GAAmB,CAClBC,WAAYrO,KAAKzG,MAAMsS,0BACvBvY,SAECA,CAACgb,EAAcC,EAAeC,KAC7B,GAAID,EAAU,CACZL,cAAclO,KAAKsL,gBACnB,MAAMmD,EAAwB5U,EAAAA,EAAM6U,yBAClCF,EACAxO,KAAKzG,MAAMsS,2BAEb,GAAI4C,EACF,OACErb,EAAAA,EAAAA,GAACub,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAYJ,EAAsBrK,MAAMG,kBACxCuK,2BAA4B7S,EAAAA,EAAoB6N,qBAItD,GAAIjQ,EAAAA,EAAMkV,gBAAgBP,EAAUxO,KAAKzG,MAAMsS,2BAC7C,OACEzY,EAAAA,EAAAA,GAACub,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAY,SAAS3L,MAAcnI,mBACnC+T,2BAA4B7S,EAAAA,EAAoB6N,qBAKtD,MAAMkF,EAAyBR,EAAStJ,QAAQgH,IAAkB,IAAD+C,EAC/D,OACEjP,KAAKzG,MAAMsS,0BAA0B3K,SAASgL,EAAQhX,MACzC,QAAb+Z,EAAA/C,EAAQ9H,aAAK,IAAA6K,OAAA,EAAbA,EAAenC,kBAAmBoC,EAAAA,GAAWC,iBAAiB,IAGR,IAADC,EAAzD,GAAIJ,GAA0BA,EAAuB,GACnD,OACE5b,EAAAA,EAAAA,GAACub,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAY7O,KAAK3H,MAAMC,KAAK+D,cAC1B,CAAAnH,GAAA,SACEC,eAAe,4EAGjB,CACE+N,UAAWA,EACXnI,QAASA,EACTsU,SAAyC,QAAjCD,EAAEJ,EAAuB,GAAG5K,aAAK,IAAAgL,OAAA,EAA/BA,EAAiC7K,oBAG/CuK,2BAA4B7S,EAAAA,EAAoB6N,sBAItDwF,EAAAA,GAAAA,IAAad,EACf,KAAO,IAAIF,EACT,OAAOlb,EAAAA,EAAAA,GAACmc,GAAAA,EAAO,IACV,GAAIzW,EAET,OACE1F,EAAAA,EAAAA,GAACyX,EAAgB,CACf3H,UAAWA,EACXpK,aAAcA,EACdgJ,YAAaA,EACbsG,QAASA,EACTe,eAAgBA,EAChBvF,sBAAuB5D,KAAK4D,sBAC5BR,sBAAuBpD,KAAK3H,MAAM+K,sBAClC5K,SAAUA,EACVkN,oCAAqC1F,KAAK0F,oCAC1CrP,OAAQA,EACR0L,kBAAmB/B,KAAKmN,0BAG9B,CACA,OAAO,IAAI,KAKrB,EAGF,MAyBMvC,GAAqB,CACzByC,mBAAkB,KAClBD,sBAAqB,KACrBF,sBAAqB,KACrBF,+BAA8B,KAC9BU,2BAA0B,KAC1BC,iBAAgB,KAChBvK,sBAAqB,KACrBoK,UACF,MAEMgC,IAA6BC,EAAAA,GAAAA,IAEjC3E,EAAAA,EAAAA,KAtCsBC,CAACxR,EAAmByR,KAC1C,MAAM9H,EAAYwM,mBAAmB1E,EAAS2E,OAAOzM,YAC/C,QAAEnI,GAAYiQ,EAAS2E,OACvB7W,GAAe8W,EAAAA,EAAAA,IAAgBrW,EAAO2J,EAAWnI,GACjD1E,GAASwZ,EAAAA,EAAAA,IAAuBtW,EAAO2J,EAAWnI,GACxD,IAAIqN,EAAgC,KAChCtP,IAAiBA,EAAauP,WAChCD,GAAU0H,EAAAA,GAAAA,IAAWhX,GAAgBA,EAAa+C,OAAQtC,IAE5D,MACM4P,EADOf,IAAW2H,EAAAA,GAAAA,IAAW3H,EAAQW,QAASxP,IACrB6O,GAAWvO,EAAAA,EAAMmW,kBAAkB5H,EAASA,EAAQW,SAC7EjH,EAAcvI,EAAMC,SAASH,YAAY6J,IACzC,KAAEiJ,GAAS5S,EACjB,MAAO,CACL2J,YACAnI,UACAjC,eACAzC,SACA+R,UACAe,iBACAgD,OACArK,cACD,GAgBwB8I,GAAzBE,EAA6CK,EAAAA,EAAAA,IAAWC,MAG7C6E,IAAmBC,EAAAA,GAAAA,GAAkBC,GAAAA,EAAWC,eAAeC,eAAgBb,IAE5F,S,mIChVO,SAASrV,EAAmBmW,GACjC,OAAIA,EACK,GAAGC,EAAAA,aAA6CC,EAAAA,EAAAA,IAAmBF,GAAO,KAE1E,EAEX,CAEO,SAASG,IAIP,IAJ+B,MACtCH,EAAQ,IAGT7Q,UAAAxH,OAAA,QAAAhF,IAAAwM,UAAA,GAAAA,UAAA,GAAG,CAAC,EACH,MAAMiR,EAAU,GACVC,EAAgBL,EAAMpP,SAAS,SAAWoP,EAAQnW,EAAmBmW,GAE3E,OADIK,GAAeD,EAAQE,KAAKD,GACzBD,EAAQje,KAAK,QACtB,CAEO,SAASoe,EAAiCC,GAC/C,MAAI,gBAAiBA,EACZA,EAAsB,YAE3B,oBAAqBA,GAAY,mBAAoBA,EAChD3W,EAAmB2W,EAA0B,iBAAK,QAAUA,EAAyB,eAE1F,mBAAoBA,EACfA,EAAyB,eAE9B,oBAAqBA,EAChBA,EAA0B,gBAE5B,EACT,C,2LCvBA,MAAM,OAAEC,EAAM,SAAEC,GAAaC,EAAAA,IAEvBC,EAAyB,mBAGlB1V,EAAgC,QAAQ0V,SACxC5V,EAAuB,gBACvBI,EAAmB,YAYzB,MAAMmB,UAA0B0C,EAAAA,UAA8BC,WAAAA,GAAA,SAAAC,WAAA,KACnElG,MAAQ,CACN4X,cAAe,MACf,KAEFC,wBAA2BD,IACzBnR,KAAKC,SAAS,CAAEkR,iBAAgB,EAChC,KAEFE,mBAAqB,CAACC,EAAWtV,EAAYuV,KAC3C,MAAM,YAAElY,GAAgB2G,KAAK3H,MAC7BkZ,EAASlY,EAAY2C,GAAS,UAAUA,0BAA2B/I,EAAU,EAC7E,KAEFue,mBAAqB,CAACvX,EAAYwX,KAE6B,KAD9CA,GAAUA,EAAOzV,OAAU,IAC7B0V,cAAcC,QAAQ1X,EAAMyX,cACzC,CAEFE,qBAAAA,GACE,MAAM,OAAE5U,GAAWgD,KAAK3H,OAClB,cAAE8Y,GAAkBnR,KAAKzG,MAG/B,IAAK4X,GAFoBA,IAAkB3V,EAGzC,OAAO,KAGT,MAAMqW,EAAc7U,GAClB5J,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,wEAEfC,OAAQ,CAAE+b,cAAeA,MAG3B/d,EAAAA,EAAAA,GAAC6B,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,oEAEfC,OAAQ,CAAE+b,cAAeA,KAI7B,OAAO/d,EAAAA,EAAAA,GAAA,KAAGoH,UAAU,yBAAwBlH,SAAEue,GAChD,CAEAC,WAAAA,CAAY3P,GACV,OACE/O,EAAAA,EAAAA,GAAC2d,EAAM,CAAC/U,MAAOmG,EAAMvP,KAAKU,SACvB6O,EAAMvP,MADuBuP,EAAMvP,KAI1C,CACAwO,MAAAA,GACE,MAAM,YAAE/H,EAAW,SAAEyD,EAAQ,OAAEE,GAAWgD,KAAK3H,OACzC,cAAE8Y,GAAkBnR,KAAKzG,MACzBwY,EAAmBZ,IAAkB3V,EAC3C,OAEEhI,EAAAA,EAAAA,IAACwe,EAAAA,IAAU,CAACC,IAAKnV,EAAUoV,OAAO,WAAW1X,UAAU,sBAAqBlH,SAAA,EAE1EF,EAAAA,EAAAA,GAAC4e,EAAAA,IAAWrR,KAAI,CACdyE,MAAOpI,GAAS5J,EAAAA,EAAAA,GAAA,KAAAE,SAAG,kBAAoB,QACvCV,KAAM0I,EACN6W,MAAO,CAAC,CAAE9f,UAAU,EAAMmS,QAAS,+CAAgDlR,UAEnFE,EAAAA,EAAAA,IAACyd,EAAAA,IAAY,CACXmB,kBAAkB,wBAClBC,SAAUrS,KAAKoR,wBACfkB,YAAY,iBACZC,aAAcvS,KAAKwR,mBACnBgB,SAAUxS,KAAK3H,MAAM0E,yBAErB0V,YAAU,EAAAnf,SAAA,EAEVE,EAAAA,EAAAA,IAACud,EAAM,CAAC/U,MAAOR,EAA+BhB,UAAU,0BAAyBlH,SAAA,EAC/EF,EAAAA,EAAAA,GAAA,KAAGoH,UAAU,mBAAmBkY,MAAO,CAAEC,SAAU,MAAQ,IAAEzB,MAE/D9d,EAAAA,EAAAA,GAAC4d,EAAQ,CAAC5L,MAAM,SAAQ9R,SAAExB,OAAOsD,OAAOiE,GAAapH,KAAKkQ,GAAUnC,KAAK8R,YAAY3P,YAKxF4P,GACC3e,EAAAA,EAAAA,GAAC4e,EAAAA,IAAWrR,KAAI,CACdyE,MAAM,aACNxS,KAAM8I,EACNyW,MAAO,CACL,CAAE9f,UAAU,EAAMmS,QAAS,0CAC3B,CAAEoO,UAAW5S,KAAKqR,qBAClB/d,UAEFF,EAAAA,EAAAA,GAACyf,EAAAA,EAAK,CACJ9b,YAAY,6EACZub,YAAY,yBAGd,KAGHtS,KAAK4R,0BAGZ,E", "sources": ["model-registry/components/SchemaTable.tsx", "model-registry/components/PromoteModelButton.tsx", "model-registry/components/ModelStageTransitionFormModal.tsx", "model-registry/components/ModelStageTransitionDropdown.tsx", "model-registry/components/aliases/ModelVersionViewAliasEditor.tsx", "model-registry/components/ModelVersionView.tsx", "model-registry/utils/VersionUtils.ts", "model-registry/components/ModelVersionPage.tsx", "model-registry/utils/SearchUtils.ts", "model-registry/components/RegisterModelForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Table,\n  Table<PERSON>ell,\n  TableHeader,\n  TableRow,\n  Typography,\n  useDesignSystemTheme,\n  MinusSquareIcon,\n  PlusSquareIcon,\n} from '@databricks/design-system';\nimport { LogModelWithSignatureUrl } from '../../common/constants';\nimport { ColumnSpec, TensorSpec, ColumnType } from '../types/model-schema';\nimport { FormattedMessage } from 'react-intl';\nimport { Interpolation, Theme } from '@emotion/react';\nimport { isEmpty } from 'lodash';\n\nconst { Text } = Typography;\nconst INDENTATION_SPACES = 2;\n\ntype Props = {\n  schema?: any;\n  defaultExpandAllRows?: boolean;\n};\n\nfunction getTensorTypeRepr(tensorType: TensorSpec): string {\n  return `Tensor (dtype: ${tensorType['tensor-spec'].dtype}, shape: [${tensorType['tensor-spec'].shape}])`;\n}\n\n// return a formatted string representation of the column type\nfunction getColumnTypeRepr(columnType: ColumnType, indentationLevel: number): string {\n  const { type } = columnType;\n\n  const indentation = ' '.repeat(indentationLevel * INDENTATION_SPACES);\n  if (type === 'object') {\n    const propertyReprs = Object.keys(columnType.properties).map((propertyName) => {\n      const property = columnType.properties[propertyName];\n      const requiredRepr = property.required ? '' : ' (optional)';\n      const propertyRepr = getColumnTypeRepr(property, indentationLevel + 1);\n      const indentOffset = (indentationLevel + 1) * INDENTATION_SPACES;\n\n      return `${' '.repeat(indentOffset)}${propertyName}: ${propertyRepr.slice(indentOffset) + requiredRepr}`;\n    });\n\n    return `${indentation}{\\n${propertyReprs.join(',\\n')}\\n${indentation}}`;\n  }\n\n  if (type === 'array') {\n    const indentOffset = indentationLevel * INDENTATION_SPACES;\n    const itemsTypeRepr = getColumnTypeRepr(columnType.items, indentationLevel).slice(indentOffset);\n    return `${indentation}Array(${itemsTypeRepr})`;\n  }\n\n  return `${indentation}${type}`;\n}\n\nfunction ColumnName({ spec }: { spec: ColumnSpec | TensorSpec }): React.ReactElement {\n  let required = true;\n  if (spec.required !== undefined) {\n    ({ required } = spec);\n  } else if (spec.optional !== undefined && spec.optional) {\n    required = false;\n  }\n  const requiredTag = required ? <Text bold>(required)</Text> : <Text color=\"secondary\">(optional)</Text>;\n\n  const name = 'name' in spec ? spec.name : '-';\n\n  return (\n    <Text css={{ marginLeft: 32 }}>\n      {name} {requiredTag}\n    </Text>\n  );\n}\n\nfunction ColumnSchema({ spec }: { spec: ColumnSpec | TensorSpec }): React.ReactElement {\n  const { theme } = useDesignSystemTheme();\n  const repr = spec.type === 'tensor' ? getTensorTypeRepr(spec) : getColumnTypeRepr(spec, 0);\n\n  return (\n    <pre\n      css={{\n        whiteSpace: 'pre-wrap',\n        padding: theme.spacing.sm,\n        marginTop: theme.spacing.sm,\n        marginBottom: theme.spacing.sm,\n      }}\n    >\n      {repr}\n    </pre>\n  );\n}\n\nconst SchemaTableRow = ({ schemaData }: { schemaData?: (ColumnSpec | TensorSpec)[] }) => {\n  if (isEmpty(schemaData)) {\n    return (\n      <TableRow>\n        <TableCell>\n          <FormattedMessage\n            defaultMessage=\"No schema. See <link>MLflow docs</link> for how to include\n                     input and output schema with your model.\"\n            description=\"Text for schema table when no schema exists in the model version\n                     page\"\n            values={{\n              link: (chunks: any) => (\n                <a href={LogModelWithSignatureUrl} target=\"_blank\" rel=\"noreferrer\">\n                  {chunks}\n                </a>\n              ),\n            }}\n          />\n        </TableCell>\n      </TableRow>\n    );\n  }\n  return (\n    <>\n      {schemaData?.map((schemaRow, index) => (\n        <TableRow key={index}>\n          <TableCell css={{ flex: 2, alignItems: 'center' }}>\n            <ColumnName spec={schemaRow} />\n          </TableCell>\n          <TableCell css={{ flex: 3, alignItems: 'center' }}>\n            <ColumnSchema spec={schemaRow} />\n          </TableCell>\n        </TableRow>\n      ))}\n    </>\n  );\n};\n\nexport const SchemaTable = ({ schema, defaultExpandAllRows }: Props) => {\n  const { theme } = useDesignSystemTheme();\n  const [inputsExpanded, setInputsExpanded] = useState(defaultExpandAllRows);\n  const [outputsExpanded, setOutputsExpanded] = useState(defaultExpandAllRows);\n\n  return (\n    <Table css={{ maxWidth: 800 }}>\n      <TableRow isHeader>\n        <TableHeader componentId=\"mlflow.schema_table.header.name\" css={{ flex: 2 }}>\n          <Text bold css={{ paddingLeft: theme.spacing.lg + theme.spacing.xs }}>\n            <FormattedMessage\n              defaultMessage=\"Name\"\n              description=\"Text for name column in schema table in model version page\"\n            />\n          </Text>\n        </TableHeader>\n        <TableHeader componentId=\"mlflow.schema_table.header.type\" css={{ flex: 3 }}>\n          <Text bold>\n            <FormattedMessage\n              defaultMessage=\"Type\"\n              description=\"Text for type column in schema table in model version page\"\n            />\n          </Text>\n        </TableHeader>\n      </TableRow>\n      <>\n        <TableRow onClick={() => setInputsExpanded(!inputsExpanded)} css={{ cursor: 'pointer' }}>\n          <TableCell>\n            <div css={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>\n              <div\n                css={{\n                  width: theme.spacing.lg,\n                  height: theme.spacing.lg,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  svg: {\n                    color: theme.colors.textSecondary,\n                  },\n                }}\n              >\n                {inputsExpanded ? <MinusSquareIcon /> : <PlusSquareIcon />}\n              </div>\n              <FormattedMessage\n                defaultMessage=\"Inputs ({numInputs})\"\n                description=\"Input section header for schema table in model version page\"\n                values={{\n                  numInputs: schema.inputs.length,\n                }}\n              />\n            </div>\n          </TableCell>\n        </TableRow>\n        {inputsExpanded && <SchemaTableRow schemaData={schema.inputs} />}\n        <TableRow onClick={() => setOutputsExpanded(!outputsExpanded)} css={{ cursor: 'pointer' }}>\n          <TableCell>\n            <div css={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>\n              <div\n                css={{\n                  width: theme.spacing.lg,\n                  height: theme.spacing.lg,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  svg: {\n                    color: theme.colors.textSecondary,\n                  },\n                }}\n              >\n                {outputsExpanded ? <MinusSquareIcon /> : <PlusSquareIcon />}\n              </div>\n              <FormattedMessage\n                defaultMessage=\"Outputs ({numOutputs})\"\n                description=\"Input section header for schema table in model version page\"\n                values={{\n                  numOutputs: schema.outputs.length,\n                }}\n              />\n            </div>\n          </TableCell>\n        </TableRow>\n        {outputsExpanded && <SchemaTableRow schemaData={schema.outputs} />}\n      </>\n    </Table>\n  );\n};\n", "import { Button, Modal, Typography } from '@databricks/design-system';\nimport { debounce } from 'lodash';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\nimport { useDispatch, useSelector } from 'react-redux';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { useNavigate } from '../../common/utils/RoutingUtils';\nimport Utils from '../../common/utils/Utils';\nimport { getModelNameFilter } from '../utils/SearchUtils';\nimport { ReduxState, ThunkDispatch } from '../../redux-types';\nimport { createModelVersionApi, createRegisteredModelApi, searchRegisteredModelsApi } from '../actions';\nimport { ModelRegistryRoutes } from '../routes';\nimport {\n  CREATE_NEW_MODEL_OPTION_VALUE,\n  MODEL_NAME_FIELD,\n  RegisterModelForm,\n  SELECTED_MODEL_FIELD,\n} from './RegisterModelForm';\nimport { ModelVersionInfoEntity } from '../../experiment-tracking/types';\n\nconst MAX_SEARCH_REGISTERED_MODELS = 5;\n\ntype PromoteModelButtonImplProps = {\n  modelVersion: ModelVersionInfoEntity;\n};\n\nexport const PromoteModelButton = (props: PromoteModelButtonImplProps) => {\n  const intl = useIntl();\n  const navigate = useNavigate();\n\n  const createRegisteredModelRequestId = useRef(getUUID());\n  const createModelVersionRequestId = useRef(getUUID());\n\n  const { modelVersion } = props;\n  const [visible, setVisible] = useState(false);\n  const [confirmLoading, setConfirmLoading] = useState(false);\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  const modelByName = useSelector((state: ReduxState) => state.entities.modelByName);\n\n  const form = useRef<any>();\n  const showRegisterModal = () => {\n    setVisible(true);\n  };\n\n  const hideRegisterModal = () => {\n    setVisible(false);\n  };\n\n  const resetAndClearModalForm = () => {\n    setVisible(false);\n    setConfirmLoading(false);\n  };\n\n  const handleRegistrationFailure = (e: any) => {\n    setConfirmLoading(false);\n    Utils.logErrorAndNotifyUser(e);\n  };\n\n  const handleSearchRegisteredModels = useCallback(\n    (input: any) => {\n      dispatch(searchRegisteredModelsApi(getModelNameFilter(input), MAX_SEARCH_REGISTERED_MODELS));\n    },\n    [dispatch],\n  );\n\n  const debouncedHandleSearchRegisteredModels = useMemo(\n    () => debounce(handleSearchRegisteredModels, 300),\n    [handleSearchRegisteredModels],\n  );\n\n  const handleCopyModel = () => {\n    form.current.validateFields().then((values: any) => {\n      setConfirmLoading(true);\n      const selectedModelName = values[SELECTED_MODEL_FIELD];\n      const copySource = 'models:/' + modelVersion.name + '/' + modelVersion.version;\n      if (selectedModelName === CREATE_NEW_MODEL_OPTION_VALUE) {\n        const newModelName = values[MODEL_NAME_FIELD];\n        dispatch(createRegisteredModelApi(newModelName, createRegisteredModelRequestId.current))\n          .then(() =>\n            dispatch(\n              createModelVersionApi(\n                newModelName,\n                copySource,\n                modelVersion.run_id,\n                modelVersion.tags,\n                createModelVersionRequestId.current,\n              ),\n            ),\n          )\n          .then((mvResult: any) => {\n            resetAndClearModalForm();\n            const { version } = mvResult.value['model_version'];\n            navigate(ModelRegistryRoutes.getModelVersionPageRoute(newModelName, version));\n          })\n          .catch(handleRegistrationFailure);\n      } else {\n        dispatch(\n          createModelVersionApi(\n            selectedModelName,\n            copySource,\n            modelVersion.run_id,\n            modelVersion.tags,\n            createModelVersionRequestId.current,\n          ),\n        )\n          .then((mvResult: any) => {\n            resetAndClearModalForm();\n            const { version } = mvResult.value['model_version'];\n            navigate(ModelRegistryRoutes.getModelVersionPageRoute(selectedModelName, version));\n          })\n          .catch(handleRegistrationFailure);\n      }\n    });\n  };\n\n  useEffect(() => {\n    dispatch(searchRegisteredModelsApi());\n  }, [dispatch]);\n\n  useEffect(() => {\n    if (visible) {\n      dispatch(searchRegisteredModelsApi());\n    }\n  }, [dispatch, visible]);\n\n  const renderRegisterModelForm = () => {\n    return (\n      <>\n        <Typography.Paragraph css={{ marginTop: '-12px' }}>\n          <FormattedMessage\n            defaultMessage=\"Copy your MLflow models to another registered model for\n            simple model promotion across environments. For more mature production-grade setups, we\n            recommend setting up automated model training workflows to produce models in controlled\n            environments. <link>Learn more</link>\"\n            description=\"Model registry > OSS Promote model modal > description paragraph body\"\n            values={{\n              link: (chunks) => (\n                <Typography.Link\n                  componentId=\"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_140\"\n                  href={\n                    'https://mlflow.org/docs/latest/model-registry.html' +\n                    '#promoting-an-mlflow-model-across-environments'\n                  }\n                  openInNewTab\n                >\n                  {chunks}\n                </Typography.Link>\n              ),\n            }}\n          />\n        </Typography.Paragraph>\n        <RegisterModelForm\n          modelByName={modelByName}\n          innerRef={form}\n          onSearchRegisteredModels={debouncedHandleSearchRegisteredModels}\n          isCopy\n        />\n      </>\n    );\n  };\n\n  return (\n    <div className=\"promote-model-btn-wrapper\">\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_165\"\n        className=\"promote-model-btn\"\n        type=\"primary\"\n        onClick={showRegisterModal}\n      >\n        <FormattedMessage\n          defaultMessage=\"Promote model\"\n          description=\"Button text to pomote the model to a different registered model\"\n        />\n      </Button>\n      <Modal\n        title={\n          <FormattedMessage\n            defaultMessage=\"Promote {sourceModelName} version {sourceModelVersion}\"\n            description=\"Modal title to pomote the model to a different registered model\"\n            values={{ sourceModelName: modelVersion.name, sourceModelVersion: modelVersion.version }}\n          />\n        }\n        // @ts-expect-error TS(2322): Type '{ children: Element; title: any; width: numb... Remove this comment to see the full error message\n        width={640}\n        visible={visible}\n        onOk={handleCopyModel}\n        okText={intl.formatMessage({\n          defaultMessage: 'Promote',\n          description: 'Confirmation text to promote the model',\n        })}\n        cancelText={intl.formatMessage({\n          defaultMessage: 'Cancel',\n          description: 'Cancel text to cancel the flow to copy the model',\n        })}\n        confirmLoading={confirmLoading}\n        onCancel={hideRegisterModal}\n        centered\n      >\n        {renderRegisterModelForm()}\n      </Modal>\n    </div>\n  );\n};\n", "import {\n  FormUI,\n  Modal,\n  ModalProps,\n  RHFControlledComponents,\n  Spacer,\n  Tooltip,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { archiveExistingVersionToolTipText, Stages, StageTagComponents } from '../constants';\nimport { useForm } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\nimport { useEffect } from 'react';\n\nexport interface ModelStageTransitionFormModalValues {\n  comment: string;\n  archiveExistingVersions: boolean;\n}\n\nexport enum ModelStageTransitionFormModalMode {\n  RequestOrDirect,\n  Approve,\n  Reject,\n  Cancel,\n}\n\nexport const ModelStageTransitionFormModal = ({\n  visible,\n  onCancel,\n  toStage,\n  allowArchivingExistingVersions,\n  transitionDescription,\n  onConfirm,\n  mode = ModelStageTransitionFormModalMode.RequestOrDirect,\n}: {\n  toStage?: string;\n  transitionDescription: React.ReactNode;\n  allowArchivingExistingVersions?: boolean;\n  onConfirm?: (values: ModelStageTransitionFormModalValues) => void;\n  mode?: ModelStageTransitionFormModalMode;\n} & Pick<ModalProps, 'visible' | 'onCancel'>) => {\n  const { theme } = useDesignSystemTheme();\n  const form = useForm<ModelStageTransitionFormModalValues>({\n    defaultValues: {\n      comment: '',\n      archiveExistingVersions: false,\n    },\n  });\n\n  const getModalTitle = () => {\n    if (mode === ModelStageTransitionFormModalMode.Approve) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Approve pending request\"\n          description=\"Title for a model version stage transition modal when approving a pending request\"\n        />\n      );\n    }\n    if (mode === ModelStageTransitionFormModalMode.Reject) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Reject pending request\"\n          description=\"Title for a model version stage transition modal when rejecting a pending request\"\n        />\n      );\n    }\n    if (mode === ModelStageTransitionFormModalMode.Cancel) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Cancel pending request\"\n          description=\"Title for a model version stage transition modal when cancelling a pending request\"\n        />\n      );\n    }\n    return (\n      <FormattedMessage\n        defaultMessage=\"Stage transition\"\n        description=\"Title for a model version stage transition modal\"\n      />\n    );\n  };\n\n  // Reset form values when modal is reopened\n  useEffect(() => {\n    if (visible) {\n      form.reset();\n    }\n  }, [form, visible]);\n\n  return (\n    <Modal\n      title={getModalTitle()}\n      componentId=\"mlflow.model_registry.stage_transition_modal_v2\"\n      visible={visible}\n      onCancel={onCancel}\n      okText={\n        <FormattedMessage\n          defaultMessage=\"OK\"\n          description=\"Confirmation button text on the model version stage transition request/approval modal\"\n        />\n      }\n      cancelText={\n        <FormattedMessage\n          defaultMessage=\"Cancel\"\n          description=\"Cancellation button text on the model version stage transition request/approval modal\"\n        />\n      }\n      onOk={onConfirm && form.handleSubmit(onConfirm)}\n    >\n      {transitionDescription}\n      <Spacer size=\"sm\" />\n      <FormUI.Label htmlFor=\"mlflow.model_registry.stage_transition_modal_v2.comment\">Comment</FormUI.Label>\n      <RHFControlledComponents.TextArea\n        name=\"comment\"\n        id=\"mlflow.model_registry.stage_transition_modal_v2.comment\"\n        componentId=\"mlflow.model_registry.stage_transition_modal_v2.comment\"\n        control={form.control}\n        rows={4}\n      />\n      <Spacer size=\"sm\" />\n\n      {allowArchivingExistingVersions && toStage && (\n        <RHFControlledComponents.Checkbox\n          name=\"archiveExistingVersions\"\n          componentId=\"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions\"\n          control={form.control}\n        >\n          <Tooltip\n            componentId=\"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions.tooltip\"\n            content={archiveExistingVersionToolTipText(toStage)}\n          >\n            <span css={{ '[role=status]': { marginRight: theme.spacing.xs } }}>\n              <FormattedMessage\n                defaultMessage=\"Transition existing {currentStage} model version to {archivedStage}\"\n                description=\"Description text for checkbox for archiving existing model versions\n                  in the toStage for model version stage transition request\"\n                values={{\n                  currentStage: <span css={{ marginLeft: theme.spacing.xs }}>{StageTagComponents[toStage]}</span>,\n                  archivedStage: (\n                    <span css={{ marginLeft: theme.spacing.xs }}>{StageTagComponents[Stages.ARCHIVED]}</span>\n                  ),\n                }}\n              />\n            </span>\n          </Tooltip>\n        </RHFControlledComponents.Checkbox>\n      )}\n    </Modal>\n  );\n};\n", "import React from 'react';\nimport { Dropdown, Menu, ChevronDownIcon, ArrowRightIcon } from '@databricks/design-system';\nimport {\n  Stages,\n  StageTagComponents,\n  ActivityTypes,\n  type PendingModelVersionActivity,\n  ACTIVE_STAGES,\n} from '../constants';\nimport _ from 'lodash';\nimport { FormattedMessage } from 'react-intl';\nimport { ModelStageTransitionFormModal, ModelStageTransitionFormModalValues } from './ModelStageTransitionFormModal';\n\ntype ModelStageTransitionDropdownProps = {\n  currentStage?: string;\n  permissionLevel?: string;\n  onSelect?: (activity: PendingModelVersionActivity, comment?: string, archiveExistingVersions?: boolean) => void;\n};\n\ntype ModelStageTransitionDropdownState = {\n  confirmModalVisible: boolean;\n  confirmingActivity: PendingModelVersionActivity | null;\n  handleConfirm: ((values: ModelStageTransitionFormModalValues) => void) | undefined;\n};\n\nexport class ModelStageTransitionDropdown extends React.Component<\n  ModelStageTransitionDropdownProps,\n  ModelStageTransitionDropdownState\n> {\n  static defaultProps = {\n    currentStage: Stages.NONE,\n  };\n\n  state: ModelStageTransitionDropdownState = {\n    confirmModalVisible: false,\n    confirmingActivity: null,\n    handleConfirm: undefined,\n  };\n\n  handleMenuItemClick = (activity: PendingModelVersionActivity) => {\n    const { onSelect } = this.props;\n    this.setState({\n      confirmModalVisible: true,\n      confirmingActivity: activity,\n      handleConfirm:\n        onSelect &&\n        ((values: ModelStageTransitionFormModalValues) => {\n          this.setState({ confirmModalVisible: false });\n\n          if (values) {\n            const { archiveExistingVersions = false } = values;\n            // @ts-expect-error TS(2722): Cannot invoke an object which is possibly 'undefin... Remove this comment to see the full error message\n            onSelect(activity, archiveExistingVersions);\n            return;\n          }\n        }),\n    });\n  };\n\n  handleConfirmModalCancel = () => {\n    this.setState({ confirmModalVisible: false });\n  };\n\n  getNoneCurrentStages = (currentStage?: string) => {\n    const stages = Object.values(Stages);\n    _.remove(stages, (s) => s === currentStage);\n    return stages;\n  };\n\n  getMenu() {\n    const { currentStage } = this.props;\n    const nonCurrentStages = this.getNoneCurrentStages(currentStage);\n    return (\n      <Menu>\n        {nonCurrentStages.map((stage) => (\n          <Menu.Item\n            key={`transition-to-${stage}`}\n            onClick={() =>\n              this.handleMenuItemClick({\n                type: ActivityTypes.APPLIED_TRANSITION,\n                to_stage: stage,\n              })\n            }\n          >\n            <FormattedMessage\n              defaultMessage=\"Transition to\"\n              description=\"Text for transitioning a model version to a different stage under\n                 dropdown menu in model version page\"\n            />\n            &nbsp;&nbsp;&nbsp;\n            <ArrowRightIcon />\n            &nbsp;&nbsp;&nbsp;\n            {StageTagComponents[stage]}\n          </Menu.Item>\n        ))}\n      </Menu>\n    );\n  }\n\n  renderConfirmModal() {\n    const { confirmModalVisible, confirmingActivity, handleConfirm } = this.state;\n\n    if (!confirmingActivity) {\n      return null;\n    }\n\n    const allowArchivingExistingVersions =\n      confirmingActivity.type === ActivityTypes.APPLIED_TRANSITION &&\n      ACTIVE_STAGES.includes(confirmingActivity.to_stage);\n\n    return (\n      <ModelStageTransitionFormModal\n        visible={confirmModalVisible}\n        toStage={confirmingActivity.to_stage}\n        onConfirm={handleConfirm}\n        onCancel={this.handleConfirmModalCancel}\n        transitionDescription={renderActivityDescription(confirmingActivity)}\n        allowArchivingExistingVersions={allowArchivingExistingVersions}\n      />\n    );\n  }\n\n  render() {\n    const { currentStage } = this.props;\n    return (\n      <span>\n        <Dropdown overlay={this.getMenu()} trigger={['click']} className=\"stage-transition-dropdown\">\n          <span>\n            {StageTagComponents[currentStage ?? Stages.NONE]}\n            <ChevronDownIcon css={{ cursor: 'pointer', marginLeft: -4 }} />\n          </span>\n        </Dropdown>\n        {this.renderConfirmModal()}\n      </span>\n    );\n  }\n}\n\nconst renderActivityDescription = (activity: PendingModelVersionActivity) => {\n  if (activity) {\n    return (\n      <div>\n        <FormattedMessage\n          defaultMessage=\"Transition to\"\n          description=\"Text for activity description under confirmation modal for model\n             version stage transition\"\n        />\n        &nbsp;&nbsp;&nbsp;\n        <ArrowRightIcon />\n        &nbsp;&nbsp;&nbsp;\n        {StageTagComponents[activity.to_stage]}\n      </div>\n    );\n  }\n  return null;\n};\n", "import { Button, PencilIcon } from '@databricks/design-system';\nimport type { ModelEntity } from '../../../experiment-tracking/types';\nimport { useEditRegisteredModelAliasesModal } from '../../hooks/useEditRegisteredModelAliasesModal';\nimport { ModelVersionAliasTag } from './ModelVersionAliasTag';\nimport { useCallback } from 'react';\n\nexport const ModelVersionViewAliasEditor = ({\n  aliases = [],\n  modelEntity,\n  version,\n  onAliasesModified,\n}: {\n  modelEntity?: ModelEntity;\n  aliases?: string[];\n  version: string;\n  onAliasesModified?: () => void;\n}) => {\n  const { EditAliasesModal, showEditAliasesModal } = useEditRegisteredModelAliasesModal({\n    model: modelEntity || null,\n    onSuccess: onAliasesModified,\n  });\n  const onAddEdit = useCallback(() => {\n    showEditAliasesModal(version);\n  }, [showEditAliasesModal, version]);\n  return (\n    <>\n      {EditAliasesModal}\n      {aliases.length < 1 ? (\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_29\"\n          size=\"small\"\n          type=\"link\"\n          onClick={onAddEdit}\n          title=\"Add aliases\"\n        >\n          Add\n        </Button>\n      ) : (\n        <div css={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>\n          {aliases.map((alias) => (\n            <ModelVersionAliasTag compact value={alias} key={alias} />\n          ))}\n          <Button\n            componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_37\"\n            size=\"small\"\n            icon={<PencilIcon />}\n            onClick={onAddEdit}\n            title=\"Edit aliases\"\n          />\n        </div>\n      )}\n    </>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { Link, NavigateFunction } from '../../common/utils/RoutingUtils';\nimport { ModelRegistryRoutes } from '../routes';\nimport { PromoteModelButton } from './PromoteModelButton';\nimport { SchemaTable } from './SchemaTable';\nimport Utils from '../../common/utils/Utils';\nimport { ModelStageTransitionDropdown } from './ModelStageTransitionDropdown';\nimport { Descriptions } from '../../common/components/Descriptions';\nimport { modelStagesMigrationGuideLink } from '../../common/constants';\nimport { Alert, Modal, Button, InfoIcon, LegacyTooltip, Typography } from '@databricks/design-system';\nimport {\n  ModelVersionStatus,\n  StageLabels,\n  StageTagComponents,\n  ModelVersionStatusIcons,\n  DefaultModelVersionStatusMessages,\n  ACTIVE_STAGES,\n  type ModelVersionActivity,\n  type PendingModelVersionActivity,\n} from '../constants';\nimport Routers from '../../experiment-tracking/routes';\nimport { CollapsibleSection } from '../../common/components/CollapsibleSection';\nimport { EditableNote } from '../../common/components/EditableNote';\nimport { EditableTagsTableView } from '../../common/components/EditableTagsTableView';\nimport { getModelVersionTags } from '../reducers';\nimport { setModelVersionTagApi, deleteModelVersionTagApi } from '../actions';\nimport { connect } from 'react-redux';\nimport { OverflowMenu, PageHeader } from '../../shared/building_blocks/PageHeader';\nimport { FormattedMessage, type IntlShape, injectIntl } from 'react-intl';\nimport { extractArtifactPathFromModelSource } from '../utils/VersionUtils';\nimport { withNextModelsUIContext } from '../hooks/useNextModelsUI';\nimport { ModelsNextUIToggleSwitch } from './ModelsNextUIToggleSwitch';\nimport { shouldShowModelsNextUI } from '../../common/utils/FeatureUtils';\nimport { ModelVersionViewAliasEditor } from './aliases/ModelVersionViewAliasEditor';\nimport type { ModelEntity, RunInfoEntity } from '../../experiment-tracking/types';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\n\ntype ModelVersionViewImplProps = {\n  modelName?: string;\n  modelVersion?: any;\n  modelEntity?: ModelEntity;\n  schema?: any;\n  activities?: ModelVersionActivity[];\n  transitionRequests?: Record<string, unknown>[];\n  onCreateComment: (...args: any[]) => any;\n  onEditComment: (...args: any[]) => any;\n  onDeleteComment: (...args: any[]) => any;\n  runInfo?: RunInfoEntity;\n  runDisplayName?: string;\n  handleStageTransitionDropdownSelect: (\n    activity: PendingModelVersionActivity,\n    comment?: string,\n    archiveExistingVersions?: boolean,\n  ) => void;\n  deleteModelVersionApi: (...args: any[]) => any;\n  handleEditDescription: (...args: any[]) => any;\n  onAliasesModified: () => void;\n  navigate: NavigateFunction;\n  tags: any;\n  setModelVersionTagApi: (...args: any[]) => any;\n  deleteModelVersionTagApi: (...args: any[]) => any;\n  intl: IntlShape;\n  usingNextModelsUI: boolean;\n};\n\ntype ModelVersionViewImplState = any;\n\nexport class ModelVersionViewImpl extends React.Component<ModelVersionViewImplProps, ModelVersionViewImplState> {\n  state = {\n    isDeleteModalVisible: false,\n    isDeleteModalConfirmLoading: false,\n    showDescriptionEditor: false,\n    isTagsRequestPending: false,\n  };\n\n  formRef = React.createRef();\n\n  componentDidMount() {\n    const pageTitle = `${this.props.modelName} v${this.props.modelVersion.version} - MLflow Model`;\n    Utils.updatePageTitle(pageTitle);\n  }\n\n  handleDeleteConfirm = () => {\n    const { modelName = '', modelVersion, navigate } = this.props;\n    const { version } = modelVersion;\n    this.showConfirmLoading();\n    this.props\n      .deleteModelVersionApi(modelName, version)\n      .then(() => {\n        navigate(ModelRegistryRoutes.getModelPageRoute(modelName));\n      })\n      .catch((e: any) => {\n        this.hideConfirmLoading();\n        Utils.logErrorAndNotifyUser(e);\n      });\n  };\n\n  showDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: true });\n  };\n\n  hideDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: false });\n  };\n\n  showConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: true });\n  };\n\n  hideConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: false });\n  };\n\n  handleCancelEditDescription = () => {\n    this.setState({ showDescriptionEditor: false });\n  };\n\n  handleSubmitEditDescription = (description: any) => {\n    return this.props.handleEditDescription(description).then(() => {\n      this.setState({ showDescriptionEditor: false });\n    });\n  };\n\n  startEditingDescription = (e: any) => {\n    e.stopPropagation();\n    this.setState({ showDescriptionEditor: true });\n  };\n\n  handleAddTag = (values: any) => {\n    const form = this.formRef.current;\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    this.setState({ isTagsRequestPending: true });\n    this.props\n      .setModelVersionTagApi(modelName, version, values.name, values.value)\n      .then(() => {\n        this.setState({ isTagsRequestPending: false });\n        (form as any).resetFields();\n      })\n      .catch((ex: ErrorWrapper | Error) => {\n        this.setState({ isTagsRequestPending: false });\n        // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n        console.error(ex);\n\n        const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n        Utils.displayGlobalErrorNotification(\n          this.props.intl.formatMessage(\n            {\n              defaultMessage: 'Failed to add tag. Error: {userVisibleError}',\n              description: 'Text for user visible error when adding tag in model version view',\n            },\n            {\n              userVisibleError,\n            },\n          ),\n        );\n      });\n  };\n\n  handleSaveEdit = ({ name, value }: any) => {\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    return this.props.setModelVersionTagApi(modelName, version, name, value).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n\n      const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n      Utils.displayGlobalErrorNotification(\n        this.props.intl.formatMessage(\n          {\n            defaultMessage: 'Failed to set tag. Error: {userVisibleError}',\n            description: 'Text for user visible error when setting tag in model version view',\n          },\n          {\n            userVisibleError,\n          },\n        ),\n      );\n    });\n  };\n\n  handleDeleteTag = ({ name }: any) => {\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    return this.props.deleteModelVersionTagApi(modelName, version, name).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n\n      const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n      Utils.displayGlobalErrorNotification(\n        this.props.intl.formatMessage(\n          {\n            defaultMessage: 'Failed to delete tag. Error: {userVisibleError}',\n            description: 'Text for user visible error when deleting tag in model version view',\n          },\n          {\n            userVisibleError,\n          },\n        ),\n      );\n    });\n  };\n\n  shouldHideDeleteOption() {\n    return false;\n  }\n\n  renderStageDropdown(modelVersion: any) {\n    const { handleStageTransitionDropdownSelect } = this.props;\n    return (\n      <Descriptions.Item\n        key=\"description-key-stage\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Stage',\n          description: 'Label name for stage metadata in model version page',\n        })}\n      >\n        {modelVersion.status === ModelVersionStatus.READY ? (\n          <ModelStageTransitionDropdown\n            currentStage={modelVersion.current_stage}\n            permissionLevel={modelVersion.permission_level}\n            onSelect={handleStageTransitionDropdownSelect}\n          />\n        ) : (\n          StageTagComponents[modelVersion.current_stage]\n        )}\n      </Descriptions.Item>\n    );\n  }\n\n  renderDisabledStage(modelVersion: any) {\n    const tooltipContent = (\n      <FormattedMessage\n        defaultMessage=\"Stages have been deprecated in the new Model Registry UI. Learn how to\n      migrate models <link>here</link>.\"\n        description=\"Tooltip content for the disabled stage metadata in model version page\"\n        values={{\n          link: (chunks: any) => (\n            <Typography.Link\n              componentId=\"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_301\"\n              href={modelStagesMigrationGuideLink}\n              openInNewTab\n            >\n              {chunks}\n            </Typography.Link>\n          ),\n        }}\n      />\n    );\n    return (\n      <Descriptions.Item\n        key=\"description-key-stage-disabled\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Stage (deprecated)',\n          description: 'Label name for the deprecated stage metadata in model version page',\n        })}\n      >\n        <div css={{ display: 'flex', alignItems: 'center' }}>\n          {StageLabels[modelVersion.current_stage]}\n          <LegacyTooltip title={tooltipContent} placement=\"bottom\">\n            <InfoIcon css={{ paddingLeft: '4px' }} />\n          </LegacyTooltip>\n        </div>\n      </Descriptions.Item>\n    );\n  }\n\n  renderRegisteredTimestampDescription(creation_timestamp: any) {\n    return (\n      <Descriptions.Item\n        key=\"description-key-register\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Registered At',\n          description: 'Label name for registered timestamp metadata in model version page',\n        })}\n      >\n        {Utils.formatTimestamp(creation_timestamp, this.props.intl)}\n      </Descriptions.Item>\n    );\n  }\n\n  renderCreatorDescription(user_id: any) {\n    return (\n      user_id && (\n        <Descriptions.Item\n          key=\"description-key-creator\"\n          label={this.props.intl.formatMessage({\n            defaultMessage: 'Creator',\n            description: 'Label name for creator metadata in model version page',\n          })}\n        >\n          {user_id}\n        </Descriptions.Item>\n      )\n    );\n  }\n\n  renderLastModifiedDescription(last_updated_timestamp: any) {\n    return (\n      <Descriptions.Item\n        key=\"description-key-modified\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Last Modified',\n          description: 'Label name for last modified timestamp metadata in model version page',\n        })}\n      >\n        {Utils.formatTimestamp(last_updated_timestamp, this.props.intl)}\n      </Descriptions.Item>\n    );\n  }\n\n  renderSourceRunDescription() {\n    // We don't show the source run link if the model version is not created from a run\n    if (!this.props.modelVersion?.run_id) {\n      return null;\n    }\n    return (\n      <Descriptions.Item\n        key=\"description-key-source-run\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Source Run',\n          description: 'Label name for source run metadata in model version page',\n        })}\n        // @ts-expect-error TS(2322): Type '{ children: Element | null; key: string; lab... Remove this comment to see the full error message\n        className=\"linked-run\"\n      >\n        {this.resolveRunLink()}\n      </Descriptions.Item>\n    );\n  }\n\n  renderCopiedFromLink() {\n    const { source } = this.props.modelVersion;\n    const modelUriRegex = /^models:\\/[^/]+\\/[^/]+$/;\n    if (!source || !modelUriRegex.test(source)) {\n      return null;\n    }\n    const sourceParts = source.split('/');\n    const sourceModelName = sourceParts[1];\n    const sourceModelVersion = sourceParts[2];\n    const link = (\n      <>\n        <Link\n          data-test-id=\"copied-from-link\"\n          to={ModelRegistryRoutes.getModelVersionPageRoute(sourceModelName, sourceModelVersion)}\n        >\n          {sourceModelName}\n        </Link>\n        &nbsp;\n        <FormattedMessage\n          defaultMessage=\"(Version {sourceModelVersion})\"\n          description=\"Version number of the source model version\"\n          values={{ sourceModelVersion }}\n        />\n      </>\n    );\n    return (\n      <Descriptions.Item\n        key=\"description-key-copied-from\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Copied from',\n          description: 'Label name for source model version metadata in model version page',\n        })}\n      >\n        {link}\n      </Descriptions.Item>\n    );\n  }\n\n  renderAliasEditor = () => {\n    // Extract aliases for the currently displayed model version from the model entity object\n    const currentVersion = this.props.modelVersion.version;\n    const currentVersionAliases =\n      this.props.modelEntity?.aliases?.filter(({ version }) => version === currentVersion).map(({ alias }) => alias) ||\n      [];\n    return (\n      <Descriptions.Item\n        key=\"description-key-aliases\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Aliases',\n          description: 'Aliases section in the metadata on model version page',\n        })}\n      >\n        <ModelVersionViewAliasEditor\n          aliases={currentVersionAliases}\n          version={this.props.modelVersion.version}\n          modelEntity={this.props.modelEntity}\n          onAliasesModified={this.props.onAliasesModified}\n        />\n      </Descriptions.Item>\n    );\n  };\n\n  getDescriptions(modelVersion: any) {\n    const { usingNextModelsUI } = this.props;\n\n    const defaultOrder = [\n      this.renderRegisteredTimestampDescription(modelVersion.creation_timestamp),\n      this.renderCreatorDescription(modelVersion.user_id),\n      this.renderLastModifiedDescription(modelVersion.last_updated_timestamp),\n      this.renderSourceRunDescription(),\n      this.renderCopiedFromLink(),\n      usingNextModelsUI ? this.renderAliasEditor() : this.renderStageDropdown(modelVersion),\n      usingNextModelsUI ? this.renderDisabledStage(modelVersion) : null,\n    ];\n    return defaultOrder.filter((item) => item !== null);\n  }\n\n  renderMetadata(modelVersion: any) {\n    return (\n      // @ts-expect-error TS(2322): Type '{ children: any[]; className: string; }' is ... Remove this comment to see the full error message\n      <Descriptions className=\"metadata-list\">{this.getDescriptions(modelVersion)}</Descriptions>\n    );\n  }\n\n  renderStatusAlert() {\n    const { status, status_message } = this.props.modelVersion;\n    if (status !== ModelVersionStatus.READY) {\n      const defaultMessage = DefaultModelVersionStatusMessages[status];\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore - OSS specific ignore\n      const type = status === ModelVersionStatus.FAILED_REGISTRATION ? 'error' : 'info';\n      return (\n        <Alert\n          type={type}\n          className={`status-alert status-alert-${type}`}\n          message={status_message || defaultMessage}\n          // @ts-expect-error TS(2322): Type '{ type: \"error\" | \"info\"; className: string;... Remove this comment to see the full error message\n          icon={ModelVersionStatusIcons[status]}\n          banner\n        />\n      );\n    }\n    return null;\n  }\n\n  renderDescriptionEditIcon() {\n    return (\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_516\"\n        data-test-id=\"descriptionEditButton\"\n        type=\"link\"\n        onClick={this.startEditingDescription}\n      >\n        <FormattedMessage\n          defaultMessage=\"Edit\"\n          description=\"Text for the edit button next to the description section title on\n             the model version view page\"\n        />{' '}\n      </Button>\n    );\n  }\n\n  resolveRunLink() {\n    const { modelVersion, runInfo } = this.props;\n    if (modelVersion.run_link) {\n      return (\n        // Reported during ESLint upgrade\n        // eslint-disable-next-line react/jsx-no-target-blank\n        <a target=\"_blank\" href={modelVersion.run_link}>\n          {this.resolveRunName()}\n        </a>\n      );\n    } else if (runInfo) {\n      let artifactPath = null;\n      const modelSource = this.props.modelVersion?.source;\n      if (modelSource) {\n        artifactPath = extractArtifactPathFromModelSource(modelSource, runInfo.runUuid);\n      }\n      return (\n        <Link to={Routers.getRunPageRoute(runInfo.experimentId, runInfo.runUuid, artifactPath)}>\n          {this.resolveRunName()}\n        </Link>\n      );\n    }\n    return null;\n  }\n\n  resolveRunName() {\n    const { modelVersion, runInfo, runDisplayName } = this.props;\n    if (modelVersion.run_link) {\n      // We use the first 37 chars to stay consistent with runDisplayName, which is typically:\n      // Run: [ID]\n      return modelVersion.run_link.substr(0, 37) + '...';\n    } else if (runInfo) {\n      return runDisplayName || runInfo.runUuid;\n    } else {\n      return null;\n    }\n  }\n\n  renderPomoteModelButton() {\n    const { modelVersion, usingNextModelsUI, navigate } = this.props;\n    return usingNextModelsUI ? <PromoteModelButton modelVersion={modelVersion} /> : null;\n  }\n\n  getPageHeader(title: any, breadcrumbs: any) {\n    const menu = [\n      {\n        id: 'delete',\n        itemName: (\n          <FormattedMessage\n            defaultMessage=\"Delete\"\n            description=\"Text for delete button on model version view page header\"\n          />\n        ),\n        onClick: this.showDeleteModal,\n        disabled: ACTIVE_STAGES.includes(this.props.modelVersion.current_stage),\n      },\n    ];\n    return (\n      <PageHeader title={title} breadcrumbs={breadcrumbs}>\n        {!this.shouldHideDeleteOption() && <OverflowMenu menu={menu} />}\n        {this.renderPomoteModelButton()}\n      </PageHeader>\n    );\n  }\n\n  render() {\n    const { modelName = '', modelVersion, tags, schema } = this.props;\n    const { description } = modelVersion;\n    const { isDeleteModalVisible, isDeleteModalConfirmLoading, showDescriptionEditor, isTagsRequestPending } =\n      this.state;\n    const title = (\n      <FormattedMessage\n        defaultMessage=\"Version {versionNum}\"\n        description=\"Title text for model version page\"\n        values={{ versionNum: modelVersion.version }}\n      />\n    );\n    const breadcrumbs = [\n      <Link to={ModelRegistryRoutes.modelListPageRoute}>\n        <FormattedMessage\n          defaultMessage=\"Registered Models\"\n          description=\"Text for link back to models page under the header on the model version\n             view page\"\n        />\n      </Link>,\n      <Link data-test-id=\"breadcrumbRegisteredModel\" to={ModelRegistryRoutes.getModelPageRoute(modelName)}>\n        {modelName}\n      </Link>,\n    ];\n    return (\n      <div>\n        {this.getPageHeader(title, breadcrumbs)}\n        {this.renderStatusAlert()}\n\n        {/* Metadata List */}\n        {this.renderMetadata(modelVersion)}\n\n        {/* New models UI switch */}\n        {shouldShowModelsNextUI() && (\n          <div css={{ marginTop: 8, display: 'flex', justifyContent: 'flex-end' }}>\n            <ModelsNextUIToggleSwitch />\n          </div>\n        )}\n\n        {/* Page Sections */}\n        <CollapsibleSection\n          title={\n            <span>\n              <FormattedMessage\n                defaultMessage=\"Description\"\n                description=\"Title text for the description section on the model version view page\"\n              />{' '}\n              {!showDescriptionEditor ? this.renderDescriptionEditIcon() : null}\n            </span>\n          }\n          forceOpen={showDescriptionEditor}\n          defaultCollapsed={!description}\n          data-test-id=\"model-version-description-section\"\n        >\n          <EditableNote\n            defaultMarkdown={description}\n            onSubmit={this.handleSubmitEditDescription}\n            onCancel={this.handleCancelEditDescription}\n            showEditor={showDescriptionEditor}\n          />\n        </CollapsibleSection>\n        <div data-test-id=\"tags-section\">\n          <CollapsibleSection\n            title={\n              <FormattedMessage\n                defaultMessage=\"Tags\"\n                description=\"Title text for the tags section on the model versions view page\"\n              />\n            }\n            defaultCollapsed={Utils.getVisibleTagValues(tags).length === 0}\n            data-test-id=\"model-version-tags-section\"\n          >\n            <EditableTagsTableView\n              // @ts-expect-error TS(2322): Type '{ innerRef: RefObject<unknown>; handleAddTag... Remove this comment to see the full error message\n              innerRef={this.formRef}\n              handleAddTag={this.handleAddTag}\n              handleDeleteTag={this.handleDeleteTag}\n              handleSaveEdit={this.handleSaveEdit}\n              tags={tags}\n              isRequestPending={isTagsRequestPending}\n            />\n          </CollapsibleSection>\n        </div>\n        <CollapsibleSection\n          title={\n            <FormattedMessage\n              defaultMessage=\"Schema\"\n              description=\"Title text for the schema section on the model versions view page\"\n            />\n          }\n          data-test-id=\"model-version-schema-section\"\n        >\n          <SchemaTable schema={schema} />\n        </CollapsibleSection>\n        <Modal\n          title={this.props.intl.formatMessage({\n            defaultMessage: 'Delete Model Version',\n            description: 'Title text for model version deletion modal in model versions view page',\n          })}\n          visible={isDeleteModalVisible}\n          confirmLoading={isDeleteModalConfirmLoading}\n          onOk={this.handleDeleteConfirm}\n          okText={this.props.intl.formatMessage({\n            defaultMessage: 'Delete',\n            description: 'OK button text for model version deletion modal in model versions view page',\n          })}\n          // @ts-expect-error TS(2322): Type '{ children: Element; title: any; visible: bo... Remove this comment to see the full error message\n          okType=\"danger\"\n          onCancel={this.hideDeleteModal}\n          cancelText={this.props.intl.formatMessage({\n            defaultMessage: 'Cancel',\n            description: 'Cancel button text for model version deletion modal in model versions view page',\n          })}\n        >\n          <span>\n            <FormattedMessage\n              defaultMessage=\"Are you sure you want to delete model version {versionNum}? This\n                 cannot be undone.\"\n              description=\"Comment text for model version deletion modal in model versions view\n                 page\"\n              values={{ versionNum: modelVersion.version }}\n            />\n          </span>\n        </Modal>\n      </div>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { modelName } = ownProps;\n  const { version } = ownProps.modelVersion;\n  const tags = getModelVersionTags(modelName, version, state);\n  return { tags };\n};\nconst mapDispatchToProps = { setModelVersionTagApi, deleteModelVersionTagApi };\n\nexport const ModelVersionView = connect(\n  mapStateToProps,\n  mapDispatchToProps,\n)(withNextModelsUIContext(injectIntl<'intl', ModelVersionViewImplProps>(ModelVersionViewImpl)));\n", "/**\n * Extract artifact path from provided `modelSource` string\n */\nexport function extractArtifactPathFromModelSource(modelSource: string, runId: string) {\n  return modelSource.match(new RegExp(`/${runId}/artifacts/(.+)`))?.[1];\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport {\n  getModelVersionApi,\n  getRegisteredModelApi,\n  updateModelVersionApi,\n  deleteModelVersionApi,\n  transitionModelVersionStageApi,\n  getModelVersionArtifactApi,\n  parseMlModelFile,\n} from '../actions';\nimport { getRunApi } from '../../experiment-tracking/actions';\nimport { getModelVersion, getModelVersionSchemas } from '../reducers';\nimport { ModelVersionView } from './ModelVersionView';\nimport {\n  ActivityTypes,\n  PendingModelVersionActivity,\n  MODEL_VERSION_STATUS_POLL_INTERVAL as POLL_INTERVAL,\n} from '../constants';\nimport Utils from '../../common/utils/Utils';\nimport { getRunInfo, getRunTags } from '../../experiment-tracking/reducers/Reducers';\nimport RequestStateWrapper, { triggerError } from '../../common/components/RequestStateWrapper';\nimport { ErrorView } from '../../common/components/ErrorView';\nimport { Spinner } from '../../common/components/Spinner';\nimport { ModelRegistryRoutes } from '../routes';\nimport { getProtoField } from '../utils';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport _ from 'lodash';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\nimport type { ModelEntity, RunInfoEntity } from '../../experiment-tracking/types';\nimport { ReduxState } from '../../redux-types';\nimport { ErrorCodes } from '../../common/constants';\nimport { injectIntl } from 'react-intl';\n\ntype ModelVersionPageImplProps = WithRouterNextProps & {\n  modelName: string;\n  version: string;\n  modelVersion?: any;\n  runInfo?: any;\n  runDisplayName?: string;\n  modelEntity?: ModelEntity;\n  getModelVersionApi: (...args: any[]) => any;\n  getRegisteredModelApi: typeof getRegisteredModelApi;\n  updateModelVersionApi: (...args: any[]) => any;\n  transitionModelVersionStageApi: (...args: any[]) => any;\n  deleteModelVersionApi: (...args: any[]) => any;\n  getRunApi: (...args: any[]) => any;\n  apis: any;\n  getModelVersionArtifactApi: (...args: any[]) => any;\n  parseMlModelFile: (...args: any[]) => any;\n  schema?: any;\n  activities?: Record<string, unknown>[];\n  intl?: any;\n};\n\ntype ModelVersionPageImplState = any;\n\nexport class ModelVersionPageImpl extends React.Component<ModelVersionPageImplProps, ModelVersionPageImplState> {\n  listTransitionRequestId: any;\n  pollIntervalId: any;\n\n  initGetModelVersionDetailsRequestId = getUUID();\n  getRunRequestId = getUUID();\n  updateModelVersionRequestId = getUUID();\n  transitionModelVersionStageRequestId = getUUID();\n  getModelVersionDetailsRequestId = getUUID();\n  initGetMlModelFileRequestId = getUUID();\n  state = {\n    criticalInitialRequestIds: [this.initGetModelVersionDetailsRequestId, this.initGetMlModelFileRequestId],\n  };\n\n  pollingRelatedRequestIds = [this.getModelVersionDetailsRequestId, this.getRunRequestId];\n\n  hasPendingPollingRequest = () =>\n    this.pollingRelatedRequestIds.every((requestId) => {\n      const request = this.props.apis[requestId];\n      return Boolean(request && request.active);\n    });\n\n  loadData = (isInitialLoading: any) => {\n    const promises = [this.getModelVersionDetailAndRunInfo(isInitialLoading)];\n    return Promise.all(promises);\n  };\n\n  pollData = () => {\n    const { modelName, version, navigate } = this.props;\n    if (!this.hasPendingPollingRequest() && Utils.isBrowserTabVisible()) {\n      // @ts-expect-error TS(2554): Expected 1 arguments, but got 0.\n      return this.loadData().catch((e) => {\n        if (e.getErrorCode() === 'RESOURCE_DOES_NOT_EXIST') {\n          Utils.logErrorAndNotifyUser(e);\n          this.props.deleteModelVersionApi(modelName, version, undefined, true);\n          navigate(ModelRegistryRoutes.getModelPageRoute(modelName));\n        } else {\n          // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n          console.error(e);\n        }\n      });\n    }\n    return Promise.resolve();\n  };\n\n  // We need to do this because currently the ModelVersionDetailed we got does not contain\n  // experimentId. We need experimentId to construct a link to the source run. This workaround can\n  // be removed after the availability of experimentId.\n  getModelVersionDetailAndRunInfo(isInitialLoading: any) {\n    const { modelName, version } = this.props;\n    return this.props\n      .getModelVersionApi(\n        modelName,\n        version,\n        isInitialLoading === true ? this.initGetModelVersionDetailsRequestId : this.getModelVersionDetailsRequestId,\n      )\n      .then(({ value }: any) => {\n        // Do not fetch run info if there is no run_id (e.g. model version created directly from a logged model)\n        if (value && !value[getProtoField('model_version')].run_link && value[getProtoField('model_version')]?.run_id) {\n          this.props.getRunApi(value[getProtoField('model_version')].run_id, this.getRunRequestId);\n        }\n      });\n  }\n  // We need this for getting mlModel artifact file,\n  // this will be replaced with a single backend call in the future when supported\n  getModelVersionMlModelFile() {\n    const { modelName, version } = this.props;\n    this.props\n      .getModelVersionArtifactApi(modelName, version)\n      .then((content: any) =>\n        this.props.parseMlModelFile(modelName, version, content.value, this.initGetMlModelFileRequestId),\n      )\n      .catch(() => {\n        // Failure of this call chain should not block the page. Here we remove\n        // `initGetMlModelFileRequestId` from `criticalInitialRequestIds`\n        // to unblock RequestStateWrapper from rendering its content\n        this.setState((prevState: any) => ({\n          criticalInitialRequestIds: _.without(prevState.criticalInitialRequestIds, this.initGetMlModelFileRequestId),\n        }));\n      });\n  }\n\n  // prettier-ignore\n  handleStageTransitionDropdownSelect = (\n    activity: PendingModelVersionActivity,\n    archiveExistingVersions?: boolean,\n  ) => {\n    const { modelName, version } = this.props;\n    const toStage = activity.to_stage;\n    if (activity.type === ActivityTypes.APPLIED_TRANSITION) {\n      this.props\n        .transitionModelVersionStageApi(\n          modelName,\n          version.toString(),\n          toStage,\n          archiveExistingVersions,\n          this.transitionModelVersionStageRequestId,\n        )\n        .then(this.loadData)\n        .catch(Utils.logErrorAndNotifyUser);\n    }\n  };\n\n  handleEditDescription = (description: any) => {\n    const { modelName, version } = this.props;\n    return (\n      this.props\n        .updateModelVersionApi(modelName, version, description, this.updateModelVersionRequestId)\n        .then(this.loadData)\n        // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n        .catch(console.error)\n    );\n  };\n\n  componentDidMount() {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    this.loadData(true).catch(console.error);\n    this.loadModelDataWithAliases();\n    this.pollIntervalId = setInterval(this.pollData, POLL_INTERVAL);\n    this.getModelVersionMlModelFile();\n  }\n\n  loadModelDataWithAliases = () => {\n    this.props.getRegisteredModelApi(this.props.modelName);\n  };\n\n  // Make a new initial load if model version or name has changed\n  componentDidUpdate(prevProps: ModelVersionPageImplProps) {\n    if (this.props.version !== prevProps.version || this.props.modelName !== prevProps.modelName) {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      this.loadData(true).catch(console.error);\n      this.getModelVersionMlModelFile();\n    }\n  }\n\n  componentWillUnmount() {\n    clearInterval(this.pollIntervalId);\n  }\n\n  render() {\n    const { modelName, version, modelVersion, runInfo, runDisplayName, navigate, schema, modelEntity } = this.props;\n\n    return (\n      <PageContainer>\n        <RequestStateWrapper\n          requestIds={this.state.criticalInitialRequestIds}\n          // eslint-disable-next-line no-trailing-spaces\n        >\n          {(loading: any, hasError: any, requests: any) => {\n            if (hasError) {\n              clearInterval(this.pollIntervalId);\n              const resourceConflictError = Utils.getResourceConflictError(\n                requests,\n                this.state.criticalInitialRequestIds,\n              );\n              if (resourceConflictError) {\n                return (\n                  <ErrorView\n                    statusCode={409}\n                    subMessage={resourceConflictError.error.getMessageField()}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              if (Utils.shouldRender404(requests, this.state.criticalInitialRequestIds)) {\n                return (\n                  <ErrorView\n                    statusCode={404}\n                    subMessage={`Model ${modelName} v${version} does not exist`}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              // TODO(Zangr) Have a more generic boundary to handle all errors, not just 404.\n              const permissionDeniedErrors = requests.filter((request: any) => {\n                return (\n                  this.state.criticalInitialRequestIds.includes(request.id) &&\n                  request.error?.getErrorCode() === ErrorCodes.PERMISSION_DENIED\n                );\n              });\n              if (permissionDeniedErrors && permissionDeniedErrors[0]) {\n                return (\n                  <ErrorView\n                    statusCode={403}\n                    subMessage={this.props.intl.formatMessage(\n                      {\n                        defaultMessage: 'Permission denied for {modelName} version {version}. Error: \"{errorMsg}\"',\n                        description: 'Permission denied error message on model version detail page',\n                      },\n                      {\n                        modelName: modelName,\n                        version: version,\n                        errorMsg: permissionDeniedErrors[0].error?.getMessageField(),\n                      },\n                    )}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              triggerError(requests);\n            } else if (loading) {\n              return <Spinner />;\n            } else if (modelVersion) {\n              // Null check to prevent NPE after delete operation\n              return (\n                <ModelVersionView\n                  modelName={modelName}\n                  modelVersion={modelVersion}\n                  modelEntity={modelEntity}\n                  runInfo={runInfo}\n                  runDisplayName={runDisplayName}\n                  handleEditDescription={this.handleEditDescription}\n                  deleteModelVersionApi={this.props.deleteModelVersionApi}\n                  navigate={navigate}\n                  handleStageTransitionDropdownSelect={this.handleStageTransitionDropdownSelect}\n                  schema={schema}\n                  onAliasesModified={this.loadModelDataWithAliases}\n                />\n              );\n            }\n            return null;\n          }}\n        </RequestStateWrapper>\n      </PageContainer>\n    );\n  }\n}\n\nconst mapStateToProps = (state: ReduxState, ownProps: WithRouterNextProps<{ modelName: string; version: string }>) => {\n  const modelName = decodeURIComponent(ownProps.params.modelName);\n  const { version } = ownProps.params;\n  const modelVersion = getModelVersion(state, modelName, version);\n  const schema = getModelVersionSchemas(state, modelName, version);\n  let runInfo: RunInfoEntity | null = null;\n  if (modelVersion && !modelVersion.run_link) {\n    runInfo = getRunInfo(modelVersion && modelVersion.run_id, state);\n  }\n  const tags = runInfo && getRunTags(runInfo.runUuid, state);\n  const runDisplayName = tags && runInfo && Utils.getRunDisplayName(runInfo, runInfo.runUuid);\n  const modelEntity = state.entities.modelByName[modelName];\n  const { apis } = state;\n  return {\n    modelName,\n    version,\n    modelVersion,\n    schema,\n    runInfo,\n    runDisplayName,\n    apis,\n    modelEntity,\n  };\n};\n\nconst mapDispatchToProps = {\n  getModelVersionApi,\n  getRegisteredModelApi,\n  updateModelVersionApi,\n  transitionModelVersionStageApi,\n  getModelVersionArtifactApi,\n  parseMlModelFile,\n  deleteModelVersionApi,\n  getRunApi,\n};\n\nconst ModelVersionPageWithRouter = withRouterNext(\n  // @ts-expect-error TS(2769): No overload matches this call.\n  connect(mapStateToProps, mapDispatchToProps)(injectIntl(ModelVersionPageImpl)),\n);\n\nexport const ModelVersionPage = withErrorBoundary(ErrorUtils.mlflowServices.MODEL_REGISTRY, ModelVersionPageWithRouter);\n\nexport default ModelVersionPage;\n", "import { REGISTERED_MODELS_SEARCH_NAME_FIELD } from '../constants';\nimport { resolveFilterValue } from '../actions';\n\nexport function getModelNameFilter(query: string): string {\n  if (query) {\n    return `${REGISTERED_MODELS_SEARCH_NAME_FIELD} ilike ${resolveFilterValue(query, true)}`;\n  } else {\n    return '';\n  }\n}\n\nexport function getCombinedSearchFilter({\n  query = '',\n}: {\n  query?: string;\n} = {}) {\n  const filters = [];\n  const initialFilter = query.includes('tags.') ? query : getModelNameFilter(query);\n  if (initialFilter) filters.push(initialFilter);\n  return filters.join(' AND ');\n}\n\nexport function constructSearchInputFromURLState(urlState: Record<string, string>): string {\n  if ('searchInput' in urlState) {\n    return urlState['searchInput'];\n  }\n  if ('nameSearchInput' in urlState && 'tagSearchInput' in urlState) {\n    return getModelNameFilter(urlState['nameSearchInput']) + ` AND ` + urlState['tagSearchInput'];\n  }\n  if ('tagSearchInput' in urlState) {\n    return urlState['tagSearchInput'];\n  }\n  if ('nameSearchInput' in urlState) {\n    return urlState['nameSearchInput'];\n  }\n  return '';\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { LegacyForm, Input, LegacySelect } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nimport './RegisterModelForm.css';\n\nconst { Option, OptGroup } = LegacySelect;\n\nconst CREATE_NEW_MODEL_LABEL = 'Create New Model';\n// Include 'CREATE_NEW_MODEL_LABEL' as part of the value for filtering to work properly. Also added\n// prefix and postfix to avoid value conflict with actual model names.\nexport const CREATE_NEW_MODEL_OPTION_VALUE = `$$$__${CREATE_NEW_MODEL_LABEL}__$$$`;\nexport const SELECTED_MODEL_FIELD = 'selectedModel';\nexport const MODEL_NAME_FIELD = 'modelName';\nconst DESCRIPTION_FIELD = 'description';\n\ntype Props = {\n  modelByName?: any;\n  isCopy?: boolean;\n  onSearchRegisteredModels: (...args: any[]) => any;\n  innerRef: any;\n};\n\ntype State = any;\n\nexport class RegisterModelForm extends React.Component<Props, State> {\n  state = {\n    selectedModel: null,\n  };\n\n  handleModelSelectChange = (selectedModel: any) => {\n    this.setState({ selectedModel });\n  };\n\n  modelNameValidator = (rule: any, value: any, callback: any) => {\n    const { modelByName } = this.props;\n    callback(modelByName[value] ? `Model \"${value}\" already exists.` : undefined);\n  };\n\n  handleFilterOption = (input: any, option: any) => {\n    const value = (option && option.value) || '';\n    return value.toLowerCase().indexOf(input.toLowerCase()) !== -1;\n  };\n\n  renderExplanatoryText() {\n    const { isCopy } = this.props;\n    const { selectedModel } = this.state;\n    const creatingNewModel = selectedModel === CREATE_NEW_MODEL_OPTION_VALUE;\n\n    if (!selectedModel || creatingNewModel) {\n      return null;\n    }\n\n    const explanation = isCopy ? (\n      <FormattedMessage\n        defaultMessage=\"The model version will be copied to {selectedModel} as a new version.\"\n        description=\"Model registry > OSS Promote model modal > copy explanatory text\"\n        values={{ selectedModel: selectedModel }}\n      />\n    ) : (\n      <FormattedMessage\n        defaultMessage=\"The model will be registered as a new version of {selectedModel}.\"\n        description=\"Explantory text for registering a model\"\n        values={{ selectedModel: selectedModel }}\n      />\n    );\n\n    return <p className=\"modal-explanatory-text\">{explanation}</p>;\n  }\n\n  renderModel(model: any) {\n    return (\n      <Option value={model.name} key={model.name}>\n        {model.name}\n      </Option>\n    );\n  }\n  render() {\n    const { modelByName, innerRef, isCopy } = this.props;\n    const { selectedModel } = this.state;\n    const creatingNewModel = selectedModel === CREATE_NEW_MODEL_OPTION_VALUE;\n    return (\n      // @ts-expect-error TS(2322): Type '{ children: (Element | null)[]; ref: any; la... Remove this comment to see the full error message\n      <LegacyForm ref={innerRef} layout=\"vertical\" className=\"register-model-form\">\n        {/* \"+ Create new model\" OR \"Select existing model\" */}\n        <LegacyForm.Item\n          label={isCopy ? <b>Copy to model</b> : 'Model'}\n          name={SELECTED_MODEL_FIELD}\n          rules={[{ required: true, message: 'Please select a model or create a new one.' }]}\n        >\n          <LegacySelect\n            dropdownClassName=\"model-select-dropdown\"\n            onChange={this.handleModelSelectChange}\n            placeholder=\"Select a model\"\n            filterOption={this.handleFilterOption}\n            onSearch={this.props.onSearchRegisteredModels}\n            // @ts-expect-error TS(2769): No overload matches this call.\n            showSearch\n          >\n            <Option value={CREATE_NEW_MODEL_OPTION_VALUE} className=\"create-new-model-option\">\n              <i className=\"fa fa-plus fa-fw\" style={{ fontSize: 13 }} /> {CREATE_NEW_MODEL_LABEL}\n            </Option>\n            <OptGroup label=\"Models\">{Object.values(modelByName).map((model) => this.renderModel(model))}</OptGroup>\n          </LegacySelect>\n        </LegacyForm.Item>\n\n        {/* Name the new model when \"+ Create new model\" is selected */}\n        {creatingNewModel ? (\n          <LegacyForm.Item\n            label=\"Model Name\"\n            name={MODEL_NAME_FIELD}\n            rules={[\n              { required: true, message: 'Please input a name for the new model.' },\n              { validator: this.modelNameValidator },\n            ]}\n          >\n            <Input\n              componentId=\"codegen_mlflow_app_src_model-registry_components_registermodelform.tsx_132\"\n              placeholder=\"Input a model name\"\n            />\n          </LegacyForm.Item>\n        ) : null}\n\n        {/* Explanatory text shown when existing model is selected */}\n        {this.renderExplanatoryText()}\n      </LegacyForm>\n    );\n  }\n}\n"], "names": ["Text", "Typography", "getColumnTypeRepr", "columnType", "indentationLevel", "type", "indentation", "repeat", "Object", "keys", "properties", "map", "propertyName", "property", "requiredRepr", "required", "propertyRepr", "indentOffset", "slice", "join", "items", "_ref2", "name", "styles", "ColumnName", "_ref", "spec", "undefined", "optional", "requiredTag", "_jsx", "bold", "children", "color", "_jsxs", "css", "ColumnSchema", "_ref3", "theme", "useDesignSystemTheme", "repr", "tensorType", "dtype", "shape", "_css", "whiteSpace", "padding", "spacing", "sm", "marginTop", "marginBottom", "_ref5", "_ref6", "SchemaTableRow", "_ref4", "schemaData", "isEmpty", "TableRow", "TableCell", "FormattedMessage", "id", "defaultMessage", "values", "link", "chunks", "href", "LogModelWithSignatureUrl", "target", "rel", "_Fragment", "schemaRow", "index", "_ref8", "_ref9", "_ref10", "_ref11", "_ref12", "SchemaTable", "_ref7", "schema", "defaultExpandAllRows", "inputsExpanded", "setInputsExpanded", "useState", "outputsExpanded", "setOutputsExpanded", "Table", "<PERSON><PERSON><PERSON><PERSON>", "TableHeader", "componentId", "paddingLeft", "lg", "xs", "onClick", "display", "alignItems", "gap", "width", "height", "justifyContent", "svg", "colors", "textSecondary", "MinusSquareIcon", "PlusSquareIcon", "numInputs", "inputs", "length", "numOutputs", "outputs", "PromoteModelButton", "props", "intl", "useIntl", "navigate", "useNavigate", "createRegisteredModelRequestId", "useRef", "getUUID", "createModelVersionRequestId", "modelVersion", "visible", "setVisible", "confirmLoading", "setConfirmLoading", "dispatch", "useDispatch", "modelByName", "useSelector", "state", "entities", "form", "resetAndClearModalForm", "handleRegistrationFailure", "e", "Utils", "logErrorAndNotifyUser", "handleSearchRegisteredModels", "useCallback", "input", "searchRegisteredModelsApi", "getModelNameFilter", "debouncedHandleSearchRegisteredModels", "useMemo", "debounce", "useEffect", "className", "<PERSON><PERSON>", "showRegisterModal", "Modal", "title", "sourceModelName", "sourceModelVersion", "version", "onOk", "handleCopyModel", "current", "validateFields", "then", "selectedModelName", "SELECTED_MODEL_FIELD", "copySource", "CREATE_NEW_MODEL_OPTION_VALUE", "newModelName", "MODEL_NAME_FIELD", "createRegisteredModelApi", "createModelVersionApi", "run_id", "tags", "mvResult", "value", "ModelRegistryRoutes", "getModelVersionPageRoute", "catch", "okText", "formatMessage", "cancelText", "onCancel", "hideRegisterModal", "centered", "Paragraph", "Link", "openInNewTab", "RegisterModelForm", "innerRef", "onSearchRegisteredModels", "isCopy", "ModelStageTransitionFormModalMode", "ModelStageTransitionFormModal", "toStage", "allowArchivingExistingVersions", "transitionDescription", "onConfirm", "mode", "RequestOrDirect", "useForm", "defaultValues", "comment", "archiveExistingVersions", "reset", "Approve", "Reject", "Cancel", "handleSubmit", "Spacer", "size", "FormUI", "Label", "htmlFor", "RHFControlledComponents", "TextArea", "control", "rows", "Checkbox", "<PERSON><PERSON><PERSON>", "content", "archiveExistingVersionToolTipText", "marginRight", "currentStage", "marginLeft", "StageTagComponents", "archivedStage", "Stages", "ARCHIVED", "ModelStageTransitionDropdown", "React", "constructor", "arguments", "confirmModalVisible", "confirmingActivity", "handleConfirm", "handleMenuItemClick", "activity", "onSelect", "this", "setState", "handleConfirmModalCancel", "getNoneCurrentStages", "stages", "_", "s", "getMenu", "nonCurrentStages", "<PERSON><PERSON>", "stage", "<PERSON><PERSON>", "ActivityTypes", "APPLIED_TRANSITION", "to_stage", "ArrowRightIcon", "renderConfirmModal", "ACTIVE_STAGES", "includes", "renderActivityDescription", "render", "Dropdown", "overlay", "trigger", "NONE", "ChevronDownIcon", "cursor", "defaultProps", "ModelVersionViewAliasEditor", "aliases", "modelEntity", "onAliasesModified", "EditAliasesModal", "showEditAliasesModal", "useEditRegisteredModelAliasesModal", "model", "onSuccess", "onAddEdit", "alias", "ModelVersionAliasTag", "compact", "icon", "PencilIcon", "ModelVersionViewImpl", "isDeleteModalVisible", "isDeleteModalConfirmLoading", "showDescriptionEditor", "isTagsRequestPending", "formRef", "handleDeleteConfirm", "modelName", "showConfirmLoading", "deleteModelVersionApi", "getModelPageRoute", "hideConfirmLoading", "showDeleteModal", "hideDeleteModal", "handleCancelEditDescription", "handleSubmitEditDescription", "description", "handleEditDescription", "startEditingDescription", "stopPropagation", "handleAddTag", "setModelVersionTagApi", "resetFields", "ex", "console", "error", "userVisibleError", "ErrorWrapper", "getMessageField", "message", "displayGlobalErrorNotification", "handleSaveEdit", "handleDeleteTag", "deleteModelVersionTagApi", "renderAliasEditor", "_this$props$modelEnti", "_this$props$modelEnti2", "currentVersion", "currentVersionAliases", "filter", "Descriptions", "label", "componentDidMount", "pageTitle", "updatePageTitle", "shouldHideDeleteOption", "renderStageDropdown", "handleStageTransitionDropdownSelect", "status", "ModelVersionStatus", "READY", "current_stage", "permissionLevel", "permission_level", "renderDisabledStage", "tooltipContent", "modelStagesMigrationGuideLink", "StageLabels", "LegacyTooltip", "placement", "InfoIcon", "renderRegisteredTimestampDescription", "creation_timestamp", "formatTimestamp", "renderCreatorDescription", "user_id", "renderLastModifiedDescription", "last_updated_timestamp", "renderSourceRunDescription", "_this$props$modelVers", "resolveRunLink", "renderCopiedFromLink", "source", "test", "sourceParts", "split", "to", "getDescriptions", "usingNextModelsUI", "item", "renderMetadata", "renderStatusAlert", "status_message", "DefaultModelVersionStatusMessages", "FAILED_REGISTRATION", "<PERSON><PERSON>", "ModelVersionStatusIcons", "banner", "renderDescriptionEditIcon", "runInfo", "run_link", "resolveRunName", "_this$props$modelVers2", "artifactPath", "modelSource", "runId", "_modelSource$match", "match", "RegExp", "extractArtifactPathFromModelSource", "runUuid", "Routers", "getRunPageRoute", "experimentId", "runDisplayName", "substr", "renderPomoteModelButton", "getPageHeader", "breadcrumbs", "menu", "itemName", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "OverflowMenu", "versionNum", "modelListPageRoute", "shouldShowModelsNextUI", "ModelsNextUIToggleSwitch", "CollapsibleSection", "forceOpen", "defaultCollapsed", "EditableNote", "defaultMarkdown", "onSubmit", "showEditor", "getVisibleTagValues", "EditableTagsTableView", "isRequestPending", "okType", "mapDispatchToProps", "ModelVersionView", "connect", "mapStateToProps", "ownProps", "getModelVersionTags", "withNextModelsUIContext", "injectIntl", "ModelVersionPageImpl", "listTransitionRequestId", "pollIntervalId", "initGetModelVersionDetailsRequestId", "getRunRequestId", "updateModelVersionRequestId", "transitionModelVersionStageRequestId", "getModelVersionDetailsRequestId", "initGetMlModelFileRequestId", "criticalInitialRequestIds", "pollingRelatedRequestIds", "hasPendingPollingRequest", "every", "requestId", "request", "apis", "Boolean", "active", "loadData", "isInitialLoading", "promises", "getModelVersionDetailAndRunInfo", "Promise", "all", "pollData", "isBrowserTabVisible", "getErrorCode", "resolve", "transitionModelVersionStageApi", "toString", "updateModelVersionApi", "loadModelDataWithAliases", "getRegisteredModelApi", "getModelVersionApi", "_value$getProtoField", "getProtoField", "getRunApi", "getModelVersionMlModelFile", "getModelVersionArtifactApi", "parseMlModelFile", "prevState", "setInterval", "POLL_INTERVAL", "componentDidUpdate", "prevProps", "componentWillUnmount", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "RequestStateWrapper", "requestIds", "loading", "<PERSON><PERSON><PERSON><PERSON>", "requests", "resourceConflictError", "getResourceConflictError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "subMessage", "fallbackHomePageReactRoute", "shouldRender404", "permissionDeniedErrors", "_request$error", "ErrorCodes", "PERMISSION_DENIED", "_permissionDeniedErro", "errorMsg", "triggerError", "Spinner", "ModelVersionPageWithRouter", "withRouterNext", "decodeURIComponent", "params", "getModelVersion", "getModelVersionSchemas", "getRunInfo", "getRunTags", "getRunDisplayName", "ModelVersionPage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "query", "REGISTERED_MODELS_SEARCH_NAME_FIELD", "resolveFilterValue", "getCombinedSearchFilter", "filters", "initialFilter", "push", "constructSearchInputFromURLState", "urlState", "Option", "OptGroup", "LegacySelect", "CREATE_NEW_MODEL_LABEL", "selected<PERSON><PERSON>l", "handleModelSelectChange", "modelNameValidator", "rule", "callback", "handleFilterOption", "option", "toLowerCase", "indexOf", "renderExplanatoryText", "explanation", "renderModel", "creatingNewModel", "LegacyForm", "ref", "layout", "rules", "dropdownClassName", "onChange", "placeholder", "filterOption", "onSearch", "showSearch", "style", "fontSize", "validator", "Input"], "sourceRoot": ""}