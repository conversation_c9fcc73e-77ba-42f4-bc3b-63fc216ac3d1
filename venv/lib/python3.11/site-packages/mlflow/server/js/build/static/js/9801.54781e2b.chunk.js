"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9801],{79716:function(t,e,i){i.r(e),i.d(e,{default:function(){return m}});var r=i(31014),s=i(77484),o=(i(14230),i(30359)),a=i.n(o),p=i(37024),n=i(18249),l=i(91071),c=i(8220),h=i(57596),d=i(50111);function f(t,e){if(t.properties&&t.properties.popupContent){const{popupContent:i}=t.properties;e.bindPopup(i)}}class u extends r.Component{constructor(t){super(t),this.leafletMap=void 0,this.mapDivId=void 0,this.mapRef=null,this.state={loading:!0,error:void 0,features:void 0},this.fetchArtifacts=this.fetchArtifacts.bind(this),this.leafletMap=void 0,this.mapDivId="map"}componentDidMount(){this.fetchArtifacts()}componentDidUpdate(t){if(this.props.path===t.path&&this.props.runUuid===t.runUuid||this.fetchArtifacts(),void 0!==this.leafletMap&&this.leafletMap.hasOwnProperty("_layers")){this.leafletMap.off(),this.leafletMap.remove();const t="<div id='"+this.mapDivId+"'></div>";document.getElementsByClassName("map-container")[0].innerHTML=t,this.leafletMap=void 0}if(void 0!==this.state.features&&this.mapRef){const t=a().map(this.mapRef),e="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",i='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors';a().tileLayer(e,{attribution:i}).addTo(t);const r=a().geoJSON(this.state.features,{style(t){return t.properties&&t.properties.style},pointToLayer(t,e){return t.properties&&t.properties.style?a().circleMarker(e,t.properties&&t.properties.style):t.properties&&t.properties.icon?a().marker(e,{icon:a().icon(t.properties&&t.properties.icon)}):a().marker(e,{icon:a().icon({iconRetinaUrl:n,iconUrl:p,shadowUrl:l,iconSize:[24,36],iconAnchor:[12,36]})})},onEachFeature:f}).addTo(t);t.fitBounds(r.getBounds()),this.leafletMap=t}}render(){return this.state.loading?(0,d.Y)(c.$,{className:"artifact-map-view-loading"}):this.state.error?(0,d.Y)(h.F,{className:"artifact-map-view-error"}):(0,d.Y)("div",{className:"map-container",children:(0,d.Y)("div",{id:this.mapDivId,ref:t=>{this.mapRef=t}})})}fetchArtifacts(){var t,e;const{path:i,runUuid:r,isLoggedModelsMode:o,loggedModelId:a}=this.props,p=o&&a?(0,s.qk)(i,a):(0,s.To)(i,r);null===(t=(e=this.props).getArtifact)||void 0===t||t.call(e,p).then((t=>{const e=JSON.parse(t);this.setState({features:e,loading:!1})})).catch((t=>{this.setState({error:t,loading:!1,features:void 0})}))}}u.defaultProps={getArtifact:s.Y0};var m=u}}]);
//# sourceMappingURL=9801.54781e2b.chunk.js.map