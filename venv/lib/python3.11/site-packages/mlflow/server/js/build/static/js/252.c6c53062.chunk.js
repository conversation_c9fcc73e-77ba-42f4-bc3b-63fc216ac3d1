"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[252],{16407:function(e,t,s){s.d(t,{i:function(){return y}});var i=s(89555),n=s(31014),o=s(77520),r=s(3293),l=s(47664),a=s(88443),d=s(9133),c=s(50111);const{Text:p}=o.T;function u(e,t){const{type:s}=e,i=" ".repeat(2*t);if("object"===s){return`${i}{\n${Object.keys(e.properties).map((s=>{const i=e.properties[s],n=i.required?"":" (optional)",o=u(i,t+1),r=2*(t+1);return`${" ".repeat(r)}${s}: ${o.slice(r)+n}`})).join(",\n")}\n${i}}`}if("array"===s){const s=2*t;return`${i}Array(${u(e.items,t).slice(s)})`}return`${i}${s}`}var m={name:"hhyoc3",styles:"margin-left:32px"};function h(e){let{spec:t}=e,s=!0;void 0!==t.required?({required:s}=t):void 0!==t.optional&&t.optional&&(s=!1);const i=s?(0,c.Y)(p,{bold:!0,children:"(required)"}):(0,c.Y)(p,{color:"secondary",children:"(optional)"}),n="name"in t?t.name:"-";return(0,c.FD)(p,{css:m,children:[n," ",i]})}function g(e){let{spec:t}=e;const{theme:s}=(0,o.u)(),n="tensor"===t.type?`Tensor (dtype: ${(r=t)["tensor-spec"].dtype}, shape: [${r["tensor-spec"].shape}])`:u(t,0);var r;return(0,c.Y)("pre",{css:(0,i.AH)({whiteSpace:"pre-wrap",padding:s.spacing.sm,marginTop:s.spacing.sm,marginBottom:s.spacing.sm},""),children:n})}var f={name:"1q1lx84",styles:"flex:2;align-items:center"},M={name:"qejc9x",styles:"flex:3;align-items:center"};const v=e=>{let{schemaData:t}=e;return(0,d.isEmpty)(t)?(0,c.Y)(r.Hjg,{children:(0,c.Y)(r.nA6,{children:(0,c.Y)(a.A,{id:"Wd8Tga",defaultMessage:"No schema. See <link>MLflow docs</link> for how to include input and output schema with your model.",values:{link:e=>(0,c.Y)("a",{href:l.zR,target:"_blank",rel:"noreferrer",children:e})}})})}):(0,c.Y)(c.FK,{children:null===t||void 0===t?void 0:t.map(((e,t)=>(0,c.FD)(r.Hjg,{children:[(0,c.Y)(r.nA6,{css:f,children:(0,c.Y)(h,{spec:e})}),(0,c.Y)(r.nA6,{css:M,children:(0,c.Y)(g,{spec:e})})]},t)))})};var A={name:"ddxhyk",styles:"max-width:800px"},D={name:"1189xqs",styles:"flex:2"},I={name:"15hibo9",styles:"flex:3"},_={name:"e0dnmk",styles:"cursor:pointer"},Y={name:"e0dnmk",styles:"cursor:pointer"};const y=e=>{let{schema:t,defaultExpandAllRows:s}=e;const{theme:l}=(0,o.u)(),[d,u]=(0,n.useState)(s),[m,h]=(0,n.useState)(s);return(0,c.FD)(r.XIK,{css:A,children:[(0,c.FD)(r.Hjg,{isHeader:!0,children:[(0,c.Y)(r.A0N,{componentId:"mlflow.schema_table.header.name",css:D,children:(0,c.Y)(p,{bold:!0,css:(0,i.AH)({paddingLeft:l.spacing.lg+l.spacing.xs},""),children:(0,c.Y)(a.A,{id:"Nj6Ez5",defaultMessage:"Name"})})}),(0,c.Y)(r.A0N,{componentId:"mlflow.schema_table.header.type",css:I,children:(0,c.Y)(p,{bold:!0,children:(0,c.Y)(a.A,{id:"2f2qeb",defaultMessage:"Type"})})})]}),(0,c.FD)(c.FK,{children:[(0,c.Y)(r.Hjg,{onClick:()=>u(!d),css:_,children:(0,c.Y)(r.nA6,{children:(0,c.FD)("div",{css:(0,i.AH)({display:"flex",alignItems:"center",gap:l.spacing.xs},""),children:[(0,c.Y)("div",{css:(0,i.AH)({width:l.spacing.lg,height:l.spacing.lg,display:"flex",alignItems:"center",justifyContent:"center",svg:{color:l.colors.textSecondary}},""),children:d?(0,c.Y)(r.NEo,{}):(0,c.Y)(r.Xeq,{})}),(0,c.Y)(a.A,{id:"sxlGRc",defaultMessage:"Inputs ({numInputs})",values:{numInputs:t.inputs.length}})]})})}),d&&(0,c.Y)(v,{schemaData:t.inputs}),(0,c.Y)(r.Hjg,{onClick:()=>h(!m),css:Y,children:(0,c.Y)(r.nA6,{children:(0,c.FD)("div",{css:(0,i.AH)({display:"flex",alignItems:"center",gap:l.spacing.xs},""),children:[(0,c.Y)("div",{css:(0,i.AH)({width:l.spacing.lg,height:l.spacing.lg,display:"flex",alignItems:"center",justifyContent:"center",svg:{color:l.colors.textSecondary}},""),children:m?(0,c.Y)(r.NEo,{}):(0,c.Y)(r.Xeq,{})}),(0,c.Y)(a.A,{id:"llVQ2r",defaultMessage:"Outputs ({numOutputs})",values:{numOutputs:t.outputs.length}})]})})}),m&&(0,c.Y)(v,{schemaData:t.outputs})]})]})}},17765:function(e,t,s){s.r(t),s.d(t,{ModelVersionPage:function(){return ue},ModelVersionPageImpl:function(){return de},default:function(){return me}});var i=s(31014),n=s(10811),o=s(69708),r=s(26809),l=s(65871),a=s(93215),d=s(69869),c=s(77520),p=s(42848),u=s(9133),m=s.n(u),h=s(88464),g=s(88443),f=s(7204),M=s(76010),v=s(59508),A=s(87665),D=s(50111);var I={name:"1yh8glp",styles:"margin-top:-12px"};const _=e=>{const t=(0,h.A)(),s=(0,a.Zp)(),r=(0,i.useRef)((0,f.yk)()),l=(0,i.useRef)((0,f.yk)()),{modelVersion:m}=e,[_,Y]=(0,i.useState)(!1),[y,R]=(0,i.useState)(!1),S=(0,n.wA)(),N=(0,n.d4)((e=>e.entities.modelByName)),E=(0,i.useRef)(),V=()=>{Y(!1),R(!1)},C=e=>{R(!1),M.A.logErrorAndNotifyUser(e)},b=(0,i.useCallback)((e=>{S((0,o.JK)((0,v.pc)(e),5))}),[S]),w=(0,i.useMemo)((()=>(0,u.debounce)(b,300)),[b]);(0,i.useEffect)((()=>{S((0,o.JK)())}),[S]),(0,i.useEffect)((()=>{_&&S((0,o.JK)())}),[S,_]);return(0,D.FD)("div",{className:"promote-model-btn-wrapper",children:[(0,D.Y)(c.B,{componentId:"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_165",className:"promote-model-btn",type:"primary",onClick:()=>{Y(!0)},children:(0,D.Y)(g.A,{id:"+RYBrq",defaultMessage:"Promote model"})}),(0,D.Y)(p.d,{title:(0,D.Y)(g.A,{id:"oBjwod",defaultMessage:"Promote {sourceModelName} version {sourceModelVersion}",values:{sourceModelName:m.name,sourceModelVersion:m.version}}),width:640,visible:_,onOk:()=>{E.current.validateFields().then((e=>{R(!0);const t=e[A.BE],i="models:/"+m.name+"/"+m.version;if(t===A.QN){const t=e[A.F3];S((0,o.eF)(t,r.current)).then((()=>S((0,o.Ey)(t,i,m.run_id,m.tags,l.current)))).then((e=>{V();const{version:i}=e.value.model_version;s(d.f.getModelVersionPageRoute(t,i))})).catch(C)}else S((0,o.Ey)(t,i,m.run_id,m.tags,l.current)).then((e=>{V();const{version:i}=e.value.model_version;s(d.f.getModelVersionPageRoute(t,i))})).catch(C)}))},okText:t.formatMessage({id:"DCkSC3",defaultMessage:"Promote"}),cancelText:t.formatMessage({id:"0tU5gv",defaultMessage:"Cancel"}),confirmLoading:y,onCancel:()=>{Y(!1)},centered:!0,children:(0,D.FD)(D.FK,{children:[(0,D.Y)(c.T.Paragraph,{css:I,children:(0,D.Y)(g.A,{id:"V5cjvM",defaultMessage:"Copy your MLflow models to another registered model for simple model promotion across environments. For more mature production-grade setups, we recommend setting up automated model training workflows to produce models in controlled environments. <link>Learn more</link>",values:{link:e=>(0,D.Y)(c.T.Link,{componentId:"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_140",href:"https://mlflow.org/docs/latest/model-registry.html#promoting-an-mlflow-model-across-environments",openInNewTab:!0,children:e})}})}),(0,D.Y)(A.xH,{modelByName:N,innerRef:E,onSearchRegisteredModels:w,isCopy:!0})]})})]})};var Y=s(16407),y=s(89555),R=s(3293),S=s(81866),N=s(13369);let E=function(e){return e[e.RequestOrDirect=0]="RequestOrDirect",e[e.Approve=1]="Approve",e[e.Reject=2]="Reject",e[e.Cancel=3]="Cancel",e}({});const V=e=>{let{visible:t,onCancel:s,toStage:n,allowArchivingExistingVersions:o,transitionDescription:r,onConfirm:l,mode:a=E.RequestOrDirect}=e;const{theme:d}=(0,c.u)(),u=(0,N.mN)({defaultValues:{comment:"",archiveExistingVersions:!1}});return(0,i.useEffect)((()=>{t&&u.reset()}),[u,t]),(0,D.FD)(p.d,{title:a===E.Approve?(0,D.Y)(g.A,{id:"CjBv5h",defaultMessage:"Approve pending request"}):a===E.Reject?(0,D.Y)(g.A,{id:"dzoxyA",defaultMessage:"Reject pending request"}):a===E.Cancel?(0,D.Y)(g.A,{id:"bS/iHC",defaultMessage:"Cancel pending request"}):(0,D.Y)(g.A,{id:"rxMHgr",defaultMessage:"Stage transition"}),componentId:"mlflow.model_registry.stage_transition_modal_v2",visible:t,onCancel:s,okText:(0,D.Y)(g.A,{id:"7PCvDy",defaultMessage:"OK"}),cancelText:(0,D.Y)(g.A,{id:"25VTYi",defaultMessage:"Cancel"}),onOk:l&&u.handleSubmit(l),children:[r,(0,D.Y)(p.S,{size:"sm"}),(0,D.Y)(R.D$Q.Label,{htmlFor:"mlflow.model_registry.stage_transition_modal_v2.comment",children:"Comment"}),(0,D.Y)(R.tc_.TextArea,{name:"comment",id:"mlflow.model_registry.stage_transition_modal_v2.comment",componentId:"mlflow.model_registry.stage_transition_modal_v2.comment",control:u.control,rows:4}),(0,D.Y)(p.S,{size:"sm"}),o&&n&&(0,D.Y)(R.tc_.Checkbox,{name:"archiveExistingVersions",componentId:"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions",control:u.control,children:(0,D.Y)(p.T,{componentId:"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions.tooltip",content:(0,S.gL)(n),children:(0,D.Y)("span",{css:(0,y.AH)({"[role=status]":{marginRight:d.spacing.xs}},""),children:(0,D.Y)(g.A,{id:"rAJDzz",defaultMessage:"Transition existing {currentStage} model version to {archivedStage}",values:{currentStage:(0,D.Y)("span",{css:(0,y.AH)({marginLeft:d.spacing.xs},""),children:S.$0[n]}),archivedStage:(0,D.Y)("span",{css:(0,y.AH)({marginLeft:d.spacing.xs},""),children:S.$0[S.e3.ARCHIVED]})}})})})})]})};class C extends i.Component{constructor(){super(...arguments),this.state={confirmModalVisible:!1,confirmingActivity:null,handleConfirm:void 0},this.handleMenuItemClick=e=>{const{onSelect:t}=this.props;this.setState({confirmModalVisible:!0,confirmingActivity:e,handleConfirm:t&&(s=>{if(this.setState({confirmModalVisible:!1}),s){const{archiveExistingVersions:i=!1}=s;t(e,i)}else;})})},this.handleConfirmModalCancel=()=>{this.setState({confirmModalVisible:!1})},this.getNoneCurrentStages=e=>{const t=Object.values(S.e3);return m().remove(t,(t=>t===e)),t}}getMenu(){const{currentStage:e}=this.props,t=this.getNoneCurrentStages(e);return(0,D.Y)(R.W1t,{children:t.map((e=>(0,D.FD)(R.W1t.Item,{onClick:()=>this.handleMenuItemClick({type:S.$p.APPLIED_TRANSITION,to_stage:e}),children:[(0,D.Y)(g.A,{id:"UhdPmo",defaultMessage:"Transition to"}),"\xa0\xa0\xa0",(0,D.Y)(R.flY,{}),"\xa0\xa0\xa0",S.$0[e]]},`transition-to-${e}`)))})}renderConfirmModal(){const{confirmModalVisible:e,confirmingActivity:t,handleConfirm:s}=this.state;if(!t)return null;const i=t.type===S.$p.APPLIED_TRANSITION&&S.jI.includes(t.to_stage);return(0,D.Y)(V,{visible:e,toStage:t.to_stage,onConfirm:s,onCancel:this.handleConfirmModalCancel,transitionDescription:b(t),allowArchivingExistingVersions:i})}render(){const{currentStage:e}=this.props;return(0,D.FD)("span",{children:[(0,D.Y)(R.msM,{overlay:this.getMenu(),trigger:["click"],className:"stage-transition-dropdown",children:(0,D.FD)("span",{children:[S.$0[null!==e&&void 0!==e?e:S.e3.NONE],(0,D.Y)(R.D3D,{css:(0,y.AH)({cursor:"pointer",marginLeft:-4},"")})]})}),this.renderConfirmModal()]})}}C.defaultProps={currentStage:S.e3.NONE};const b=e=>e?(0,D.FD)("div",{children:[(0,D.Y)(g.A,{id:"ZYBoeF",defaultMessage:"Transition to"}),"\xa0\xa0\xa0",(0,D.Y)(R.flY,{}),"\xa0\xa0\xa0",S.$0[e.to_stage]]}):null;var w=s(90925),x=s(47664),k=s(58481),T=s(10405),F=s(96034),q=s(41287),P=s(79085),L=s(64912);var $=s(73414),O=s(84963),H=s(91144),j=s(30311),U=s(64756);var K={name:"lkh5er",styles:"display:flex;flex-wrap:wrap;align-items:center"};const z=e=>{let{aliases:t=[],modelEntity:s,version:n,onAliasesModified:o}=e;const{EditAliasesModal:r,showEditAliasesModal:l}=(0,j.o)({model:s||null,onSuccess:o}),a=(0,i.useCallback)((()=>{l(n)}),[l,n]);return(0,D.FD)(D.FK,{children:[r,t.length<1?(0,D.Y)(c.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_29",size:"small",type:"link",onClick:a,title:"Add aliases",children:"Add"}):(0,D.FD)("div",{css:K,children:[t.map((e=>(0,D.Y)(U.m,{compact:!0,value:e},e))),(0,D.Y)(c.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_37",size:"small",icon:(0,D.Y)(R.R2l,{}),onClick:a,title:"Edit aliases"})]})]})};var B=s(52350);var G={name:"s5xdrg",styles:"display:flex;align-items:center"},Q={name:"gc6sdx",styles:"padding-left:4px"},W={name:"vw6wts",styles:"margin-top:8px;display:flex;justify-content:flex-end"};class X extends i.Component{constructor(){super(...arguments),this.state={isDeleteModalVisible:!1,isDeleteModalConfirmLoading:!1,showDescriptionEditor:!1,isTagsRequestPending:!1},this.formRef=i.createRef(),this.handleDeleteConfirm=()=>{const{modelName:e="",modelVersion:t,navigate:s}=this.props,{version:i}=t;this.showConfirmLoading(),this.props.deleteModelVersionApi(e,i).then((()=>{s(d.f.getModelPageRoute(e))})).catch((e=>{this.hideConfirmLoading(),M.A.logErrorAndNotifyUser(e)}))},this.showDeleteModal=()=>{this.setState({isDeleteModalVisible:!0})},this.hideDeleteModal=()=>{this.setState({isDeleteModalVisible:!1})},this.showConfirmLoading=()=>{this.setState({isDeleteModalConfirmLoading:!0})},this.hideConfirmLoading=()=>{this.setState({isDeleteModalConfirmLoading:!1})},this.handleCancelEditDescription=()=>{this.setState({showDescriptionEditor:!1})},this.handleSubmitEditDescription=e=>this.props.handleEditDescription(e).then((()=>{this.setState({showDescriptionEditor:!1})})),this.startEditingDescription=e=>{e.stopPropagation(),this.setState({showDescriptionEditor:!0})},this.handleAddTag=e=>{const t=this.formRef.current,{modelName:s}=this.props,{version:i}=this.props.modelVersion;this.setState({isTagsRequestPending:!0}),this.props.setModelVersionTagApi(s,i,e.name,e.value).then((()=>{this.setState({isTagsRequestPending:!1}),t.resetFields()})).catch((e=>{this.setState({isTagsRequestPending:!1}),console.error(e);const t=e instanceof B.s?e.getMessageField():e.message;M.A.displayGlobalErrorNotification(this.props.intl.formatMessage({id:"IWuVOI",defaultMessage:"Failed to add tag. Error: {userVisibleError}"},{userVisibleError:t}))}))},this.handleSaveEdit=e=>{let{name:t,value:s}=e;const{modelName:i}=this.props,{version:n}=this.props.modelVersion;return this.props.setModelVersionTagApi(i,n,t,s).catch((e=>{console.error(e);const t=e instanceof B.s?e.getMessageField():e.message;M.A.displayGlobalErrorNotification(this.props.intl.formatMessage({id:"HNLDYl",defaultMessage:"Failed to set tag. Error: {userVisibleError}"},{userVisibleError:t}))}))},this.handleDeleteTag=e=>{let{name:t}=e;const{modelName:s}=this.props,{version:i}=this.props.modelVersion;return this.props.deleteModelVersionTagApi(s,i,t).catch((e=>{console.error(e);const t=e instanceof B.s?e.getMessageField():e.message;M.A.displayGlobalErrorNotification(this.props.intl.formatMessage({id:"Ay+ZP/",defaultMessage:"Failed to delete tag. Error: {userVisibleError}"},{userVisibleError:t}))}))},this.renderAliasEditor=()=>{var e,t;const s=this.props.modelVersion.version,i=(null===(e=this.props.modelEntity)||void 0===e||null===(t=e.aliases)||void 0===t?void 0:t.filter((e=>{let{version:t}=e;return t===s})).map((e=>{let{alias:t}=e;return t})))||[];return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"HzGYm5",defaultMessage:"Aliases"}),children:(0,D.Y)(z,{aliases:i,version:this.props.modelVersion.version,modelEntity:this.props.modelEntity,onAliasesModified:this.props.onAliasesModified})},"description-key-aliases")}}componentDidMount(){const e=`${this.props.modelName} v${this.props.modelVersion.version} - MLflow Model`;M.A.updatePageTitle(e)}shouldHideDeleteOption(){return!1}renderStageDropdown(e){const{handleStageTransitionDropdownSelect:t}=this.props;return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"IHyeUr",defaultMessage:"Stage"}),children:e.status===S.IP.READY?(0,D.Y)(C,{currentStage:e.current_stage,permissionLevel:e.permission_level,onSelect:t}):S.$0[e.current_stage]},"description-key-stage")}renderDisabledStage(e){const t=(0,D.Y)(g.A,{id:"gIc8Lp",defaultMessage:"Stages have been deprecated in the new Model Registry UI. Learn how to migrate models <link>here</link>.",values:{link:e=>(0,D.Y)(c.T.Link,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_301",href:x.pG,openInNewTab:!0,children:e})}});return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"paQ2Wc",defaultMessage:"Stage (deprecated)"}),children:(0,D.FD)("div",{css:G,children:[S.QQ[e.current_stage],(0,D.Y)(R.paO,{title:t,placement:"bottom",children:(0,D.Y)(p.I,{css:Q})})]})},"description-key-stage-disabled")}renderRegisteredTimestampDescription(e){return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"+1i81u",defaultMessage:"Registered At"}),children:M.A.formatTimestamp(e,this.props.intl)},"description-key-register")}renderCreatorDescription(e){return e&&(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"5Mzn2b",defaultMessage:"Creator"}),children:e},"description-key-creator")}renderLastModifiedDescription(e){return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"o7dzKo",defaultMessage:"Last Modified"}),children:M.A.formatTimestamp(e,this.props.intl)},"description-key-modified")}renderSourceRunDescription(){var e;return null!==(e=this.props.modelVersion)&&void 0!==e&&e.run_id?(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"+LLlvi",defaultMessage:"Source Run"}),className:"linked-run",children:this.resolveRunLink()},"description-key-source-run"):null}renderCopiedFromLink(){const{source:e}=this.props.modelVersion;if(!e||!/^models:\/[^/]+\/[^/]+$/.test(e))return null;const t=e.split("/"),s=t[1],i=t[2],n=(0,D.FD)(D.FK,{children:[(0,D.Y)(a.N_,{"data-test-id":"copied-from-link",to:d.f.getModelVersionPageRoute(s,i),children:s}),"\xa0",(0,D.Y)(g.A,{id:"1RxxTj",defaultMessage:"(Version {sourceModelVersion})",values:{sourceModelVersion:i}})]});return(0,D.Y)(w.K.Item,{label:this.props.intl.formatMessage({id:"oZReP2",defaultMessage:"Copied from"}),children:n},"description-key-copied-from")}getDescriptions(e){const{usingNextModelsUI:t}=this.props;return[this.renderRegisteredTimestampDescription(e.creation_timestamp),this.renderCreatorDescription(e.user_id),this.renderLastModifiedDescription(e.last_updated_timestamp),this.renderSourceRunDescription(),this.renderCopiedFromLink(),t?this.renderAliasEditor():this.renderStageDropdown(e),t?this.renderDisabledStage(e):null].filter((e=>null!==e))}renderMetadata(e){return(0,D.Y)(w.K,{className:"metadata-list",children:this.getDescriptions(e)})}renderStatusAlert(){const{status:e,status_message:t}=this.props.modelVersion;if(e!==S.IP.READY){const s=S.zA[e],i=e===S.IP.FAILED_REGISTRATION?"error":"info";return(0,D.Y)(R.FcD,{type:i,className:`status-alert status-alert-${i}`,message:t||s,icon:S.UA[e],banner:!0})}return null}renderDescriptionEditIcon(){return(0,D.FD)(c.B,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_516","data-test-id":"descriptionEditButton",type:"link",onClick:this.startEditingDescription,children:[(0,D.Y)(g.A,{id:"Ik96R6",defaultMessage:"Edit"})," "]})}resolveRunLink(){const{modelVersion:e,runInfo:t}=this.props;if(e.run_link)return(0,D.Y)("a",{target:"_blank",href:e.run_link,children:this.resolveRunName()});if(t){var s;let e=null;const i=null===(s=this.props.modelVersion)||void 0===s?void 0:s.source;return i&&(e=function(e,t){var s;return null===(s=e.match(new RegExp(`/${t}/artifacts/(.+)`)))||void 0===s?void 0:s[1]}(i,t.runUuid)),(0,D.Y)(a.N_,{to:k.h.getRunPageRoute(t.experimentId,t.runUuid,e),children:this.resolveRunName()})}return null}resolveRunName(){const{modelVersion:e,runInfo:t,runDisplayName:s}=this.props;return e.run_link?e.run_link.substr(0,37)+"...":t?s||t.runUuid:null}renderPomoteModelButton(){const{modelVersion:e,usingNextModelsUI:t,navigate:s}=this.props;return t?(0,D.Y)(_,{modelVersion:e}):null}getPageHeader(e,t){const s=[{id:"delete",itemName:(0,D.Y)(g.A,{id:"4+RPaz",defaultMessage:"Delete"}),onClick:this.showDeleteModal,disabled:S.jI.includes(this.props.modelVersion.current_stage)}];return(0,D.FD)(P.z,{title:e,breadcrumbs:t,children:[!this.shouldHideDeleteOption()&&(0,D.Y)(P.o,{menu:s}),this.renderPomoteModelButton()]})}render(){const{modelName:e="",modelVersion:t,tags:s,schema:i}=this.props,{description:n}=t,{isDeleteModalVisible:o,isDeleteModalConfirmLoading:r,showDescriptionEditor:l,isTagsRequestPending:c}=this.state,u=(0,D.Y)(g.A,{id:"F16DNA",defaultMessage:"Version {versionNum}",values:{versionNum:t.version}}),m=[(0,D.Y)(a.N_,{to:d.f.modelListPageRoute,children:(0,D.Y)(g.A,{id:"pl1bdY",defaultMessage:"Registered Models"})}),(0,D.Y)(a.N_,{"data-test-id":"breadcrumbRegisteredModel",to:d.f.getModelPageRoute(e),children:e})];return(0,D.FD)("div",{children:[this.getPageHeader(u,m),this.renderStatusAlert(),this.renderMetadata(t),(0,H.WX)()&&(0,D.Y)("div",{css:W,children:(0,D.Y)(O.C,{})}),(0,D.Y)(T.i,{title:(0,D.FD)("span",{children:[(0,D.Y)(g.A,{id:"z9UqPZ",defaultMessage:"Description"})," ",l?null:this.renderDescriptionEditIcon()]}),forceOpen:l,defaultCollapsed:!n,"data-test-id":"model-version-description-section",children:(0,D.Y)(F.V,{defaultMarkdown:n,onSubmit:this.handleSubmitEditDescription,onCancel:this.handleCancelEditDescription,showEditor:l})}),(0,D.Y)("div",{"data-test-id":"tags-section",children:(0,D.Y)(T.i,{title:(0,D.Y)(g.A,{id:"TlFUsN",defaultMessage:"Tags"}),defaultCollapsed:0===M.A.getVisibleTagValues(s).length,"data-test-id":"model-version-tags-section",children:(0,D.Y)(q.h,{innerRef:this.formRef,handleAddTag:this.handleAddTag,handleDeleteTag:this.handleDeleteTag,handleSaveEdit:this.handleSaveEdit,tags:s,isRequestPending:c})})}),(0,D.Y)(T.i,{title:(0,D.Y)(g.A,{id:"gUdmaJ",defaultMessage:"Schema"}),"data-test-id":"model-version-schema-section",children:(0,D.Y)(Y.i,{schema:i})}),(0,D.Y)(p.d,{title:this.props.intl.formatMessage({id:"98Ub01",defaultMessage:"Delete Model Version"}),visible:o,confirmLoading:r,onOk:this.handleDeleteConfirm,okText:this.props.intl.formatMessage({id:"ICtiKS",defaultMessage:"Delete"}),okType:"danger",onCancel:this.hideDeleteModal,cancelText:this.props.intl.formatMessage({id:"zMhOr3",defaultMessage:"Cancel"}),children:(0,D.Y)("span",{children:(0,D.Y)(g.A,{id:"88l+j9",defaultMessage:"Are you sure you want to delete model version {versionNum}? This cannot be undone.",values:{versionNum:t.version}})})})]})}}const J={setModelVersionTagApi:o.Id,deleteModelVersionTagApi:o.nZ},Z=(0,n.Ng)(((e,t)=>{const{modelName:s}=t,{version:i}=t.modelVersion;return{tags:(0,l.hs)(s,i,e)}}),J)((0,$.p)((0,L.Ay)(X)));var ee=s(72877),te=s(53140),se=s(82214),ie=s(96277),ne=s(24478),oe=s(48588),re=s(25869),le=s(20109),ae=s(62448);class de extends i.Component{constructor(){super(...arguments),this.listTransitionRequestId=void 0,this.pollIntervalId=void 0,this.initGetModelVersionDetailsRequestId=(0,f.yk)(),this.getRunRequestId=(0,f.yk)(),this.updateModelVersionRequestId=(0,f.yk)(),this.transitionModelVersionStageRequestId=(0,f.yk)(),this.getModelVersionDetailsRequestId=(0,f.yk)(),this.initGetMlModelFileRequestId=(0,f.yk)(),this.state={criticalInitialRequestIds:[this.initGetModelVersionDetailsRequestId,this.initGetMlModelFileRequestId]},this.pollingRelatedRequestIds=[this.getModelVersionDetailsRequestId,this.getRunRequestId],this.hasPendingPollingRequest=()=>this.pollingRelatedRequestIds.every((e=>{const t=this.props.apis[e];return Boolean(t&&t.active)})),this.loadData=e=>{const t=[this.getModelVersionDetailAndRunInfo(e)];return Promise.all(t)},this.pollData=()=>{const{modelName:e,version:t,navigate:s}=this.props;return!this.hasPendingPollingRequest()&&M.A.isBrowserTabVisible()?this.loadData().catch((i=>{"RESOURCE_DOES_NOT_EXIST"===i.getErrorCode()?(M.A.logErrorAndNotifyUser(i),this.props.deleteModelVersionApi(e,t,void 0,!0),s(d.f.getModelPageRoute(e))):console.error(i)})):Promise.resolve()},this.handleStageTransitionDropdownSelect=(e,t)=>{const{modelName:s,version:i}=this.props,n=e.to_stage;e.type===S.$p.APPLIED_TRANSITION&&this.props.transitionModelVersionStageApi(s,i.toString(),n,t,this.transitionModelVersionStageRequestId).then(this.loadData).catch(M.A.logErrorAndNotifyUser)},this.handleEditDescription=e=>{const{modelName:t,version:s}=this.props;return this.props.updateModelVersionApi(t,s,e,this.updateModelVersionRequestId).then(this.loadData).catch(console.error)},this.loadModelDataWithAliases=()=>{this.props.getRegisteredModelApi(this.props.modelName)}}getModelVersionDetailAndRunInfo(e){const{modelName:t,version:s}=this.props;return this.props.getModelVersionApi(t,s,!0===e?this.initGetModelVersionDetailsRequestId:this.getModelVersionDetailsRequestId).then((e=>{var t;let{value:s}=e;s&&!s[(0,ne.z)("model_version")].run_link&&null!==(t=s[(0,ne.z)("model_version")])&&void 0!==t&&t.run_id&&this.props.getRunApi(s[(0,ne.z)("model_version")].run_id,this.getRunRequestId)}))}getModelVersionMlModelFile(){const{modelName:e,version:t}=this.props;this.props.getModelVersionArtifactApi(e,t).then((s=>this.props.parseMlModelFile(e,t,s.value,this.initGetMlModelFileRequestId))).catch((()=>{this.setState((e=>({criticalInitialRequestIds:m().without(e.criticalInitialRequestIds,this.initGetMlModelFileRequestId)})))}))}componentDidMount(){this.loadData(!0).catch(console.error),this.loadModelDataWithAliases(),this.pollIntervalId=setInterval(this.pollData,S.Gs),this.getModelVersionMlModelFile()}componentDidUpdate(e){this.props.version===e.version&&this.props.modelName===e.modelName||(this.loadData(!0).catch(console.error),this.getModelVersionMlModelFile())}componentWillUnmount(){clearInterval(this.pollIntervalId)}render(){const{modelName:e,version:t,modelVersion:s,runInfo:i,runDisplayName:n,navigate:o,schema:r,modelEntity:l}=this.props;return(0,D.Y)(oe.L,{children:(0,D.Y)(te.Ay,{requestIds:this.state.criticalInitialRequestIds,children:(a,c,p)=>{if(c){clearInterval(this.pollIntervalId);const s=M.A.getResourceConflictError(p,this.state.criticalInitialRequestIds);if(s)return(0,D.Y)(se.E,{statusCode:409,subMessage:s.error.getMessageField(),fallbackHomePageReactRoute:d.f.modelListPageRoute});if(M.A.shouldRender404(p,this.state.criticalInitialRequestIds))return(0,D.Y)(se.E,{statusCode:404,subMessage:`Model ${e} v${t} does not exist`,fallbackHomePageReactRoute:d.f.modelListPageRoute});const i=p.filter((e=>{var t;return this.state.criticalInitialRequestIds.includes(e.id)&&(null===(t=e.error)||void 0===t?void 0:t.getErrorCode())===x.tG.PERMISSION_DENIED}));var u;if(i&&i[0])return(0,D.Y)(se.E,{statusCode:403,subMessage:this.props.intl.formatMessage({id:"bQSJKh",defaultMessage:'Permission denied for {modelName} version {version}. Error: "{errorMsg}"'},{modelName:e,version:t,errorMsg:null===(u=i[0].error)||void 0===u?void 0:u.getMessageField()}),fallbackHomePageReactRoute:d.f.modelListPageRoute});(0,te.dD)(p)}else{if(a)return(0,D.Y)(ie.y,{});if(s)return(0,D.Y)(Z,{modelName:e,modelVersion:s,modelEntity:l,runInfo:i,runDisplayName:n,handleEditDescription:this.handleEditDescription,deleteModelVersionApi:this.props.deleteModelVersionApi,navigate:o,handleStageTransitionDropdownSelect:this.handleStageTransitionDropdownSelect,schema:r,onAliasesModified:this.loadModelDataWithAliases})}return null}})})}}const ce={getModelVersionApi:o.s5,getRegisteredModelApi:o.SY,updateModelVersionApi:o.DL,transitionModelVersionStageApi:o.tP,getModelVersionArtifactApi:o.ax,parseMlModelFile:o.qI,deleteModelVersionApi:o.TQ,getRunApi:r.aO},pe=(0,re.h)((0,n.Ng)(((e,t)=>{const s=decodeURIComponent(t.params.modelName),{version:i}=t.params,n=(0,l.Af)(e,s,i),o=(0,l.SE)(e,s,i);let r=null;n&&!n.run_link&&(r=(0,ee.K4)(n&&n.run_id,e));const a=r&&(0,ee.X3)(r.runUuid,e)&&r&&M.A.getRunDisplayName(r,r.runUuid),d=e.entities.modelByName[s],{apis:c}=e;return{modelName:s,version:i,modelVersion:n,schema:o,runInfo:r,runDisplayName:a,apis:c,modelEntity:d}}),ce)((0,L.Ay)(de))),ue=(0,le.X)(ae.A.mlflowServices.MODEL_REGISTRY,pe);var me=ue},59508:function(e,t,s){s.d(t,{_C:function(){return l},pc:function(){return o},so:function(){return r}});var i=s(81866),n=s(69708);function o(e){return e?`${i.Qs} ilike ${(0,n.GP)(e,!0)}`:""}function r(){let{query:e=""}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[],s=e.includes("tags.")?e:o(e);return s&&t.push(s),t.join(" AND ")}function l(e){return"searchInput"in e?e.searchInput:"nameSearchInput"in e&&"tagSearchInput"in e?o(e.nameSearchInput)+" AND "+e.tagSearchInput:"tagSearchInput"in e?e.tagSearchInput:"nameSearchInput"in e?e.nameSearchInput:""}},87665:function(e,t,s){s.d(t,{QN:function(){return p},F3:function(){return m},xH:function(){return h},BE:function(){return u}});var i=s(31014),n=s(3293),o=s(65418),r=s(88443),l=s(50111);const{Option:a,OptGroup:d}=n._vn,c="Create New Model",p=`$$$__${c}__$$$`,u="selectedModel",m="modelName";class h extends i.Component{constructor(){super(...arguments),this.state={selectedModel:null},this.handleModelSelectChange=e=>{this.setState({selectedModel:e})},this.modelNameValidator=(e,t,s)=>{const{modelByName:i}=this.props;s(i[t]?`Model "${t}" already exists.`:void 0)},this.handleFilterOption=(e,t)=>-1!==(t&&t.value||"").toLowerCase().indexOf(e.toLowerCase())}renderExplanatoryText(){const{isCopy:e}=this.props,{selectedModel:t}=this.state;if(!t||t===p)return null;const s=e?(0,l.Y)(r.A,{id:"hxMEqi",defaultMessage:"The model version will be copied to {selectedModel} as a new version.",values:{selectedModel:t}}):(0,l.Y)(r.A,{id:"zjXq2X",defaultMessage:"The model will be registered as a new version of {selectedModel}.",values:{selectedModel:t}});return(0,l.Y)("p",{className:"modal-explanatory-text",children:s})}renderModel(e){return(0,l.Y)(a,{value:e.name,children:e.name},e.name)}render(){const{modelByName:e,innerRef:t,isCopy:s}=this.props,{selectedModel:i}=this.state,r=i===p;return(0,l.FD)(n.SQ4,{ref:t,layout:"vertical",className:"register-model-form",children:[(0,l.Y)(n.SQ4.Item,{label:s?(0,l.Y)("b",{children:"Copy to model"}):"Model",name:u,rules:[{required:!0,message:"Please select a model or create a new one."}],children:(0,l.FD)(n._vn,{dropdownClassName:"model-select-dropdown",onChange:this.handleModelSelectChange,placeholder:"Select a model",filterOption:this.handleFilterOption,onSearch:this.props.onSearchRegisteredModels,showSearch:!0,children:[(0,l.FD)(a,{value:p,className:"create-new-model-option",children:[(0,l.Y)("i",{className:"fa fa-plus fa-fw",style:{fontSize:13}})," ",c]}),(0,l.Y)(d,{label:"Models",children:Object.values(e).map((e=>this.renderModel(e)))})]})}),r?(0,l.Y)(n.SQ4.Item,{label:"Model Name",name:m,rules:[{required:!0,message:"Please input a name for the new model."},{validator:this.modelNameValidator}],children:(0,l.Y)(o.I,{componentId:"codegen_mlflow_app_src_model-registry_components_registermodelform.tsx_132",placeholder:"Input a model name"})}):null,this.renderExplanatoryText()]})}}}}]);
//# sourceMappingURL=252.c6c53062.chunk.js.map