"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[8618],{48588:function(e,t,r){r.d(t,{L:function(){return s}});r(31014);var n=r(3293),i=r(42848),a=r(50111);function s(e){const{usesFullHeight:t,...r}=e;return(0,a.FD)(n.ffj,{css:t?o.useFullHeightLayout:o.wrapper,children:[(0,a.Y)(i.S,{css:o.fixedSpacer}),t?e.children:(0,a.Y)("div",{...r,css:o.container})]})}s.defaultProps={usesFullHeight:!1};const o={useFullHeightLayout:{height:"calc(100% - 60px)",display:"flex",flexDirection:"column","&:last-child":{flexGrow:1}},wrapper:{flex:1},fixedSpacer:{flexShrink:0},container:{width:"100%",flexGrow:1,paddingBottom:24}}},76327:function(e,t,r){r.r(t),r.d(t,{RunPage:function(){return kt},default:function(){return Ft}});var n=r(89555),i=r(3293),a=r(77520),s=r(10811),o=r(4877),l=r.n(o),d=r(31014),u=r(48588),c=r(93215),m=r(76010),g=r(25866),p=r(53677),h=r(18006),v=r(53606),f=r(50111);const Y=e=>{let{runTags:t,runOutputs:r,artifactUri:i,runUuid:s}=e;const{theme:o}=(0,a.u)(),l=(0,v.U)(`(min-width: ${o.responsive.breakpoints.sm}px)`);return(0,f.Y)("div",{css:(0,n.AH)({flex:1,overflow:"hidden",display:"flex",paddingBottom:o.spacing.md,position:"relative"},""),children:(0,f.Y)(h.Ay,{runUuid:s,runTags:t,runOutputs:r,useAutoHeight:l,artifactRootUri:i})})};var x=r(88443),_=r(79085),y=r(58481),I=r(91144);const S=()=>{const{"*":e}=(0,c.g)();return"model-metrics"===e?g.N3.MODEL_METRIC_CHARTS:"system-metrics"===e?g.N3.SYSTEM_METRIC_CHARTS:(0,I.oX)()&&"traces"===e?g.N3.TRACES:null!==e&&void 0!==e&&e.match(/^(artifactPath|artifacts)/)?g.N3.ARTIFACTS:g.N3.OVERVIEW},w=[g.N3.ARTIFACTS,g.N3.EVALUATIONS],R=()=>{const{experimentId:e,runUuid:t}=(0,c.g)(),r=(0,c.Zp)(),n=S(),[a,s]=(0,d.useState)(w.includes(n));return(0,f.FD)(i.Y6f,{activeKey:n,onChange:i=>{e&&t&&n!==i&&(s(w.includes(i)),i!==g.N3.OVERVIEW?r(y.h.getRunPageTabRoute(e,t,i)):r(y.h.getRunPageRoute(e,t)))},tabBarStyle:{margin:a&&"0px"},children:[(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"kA+QJr",defaultMessage:"Overview"})},g.N3.OVERVIEW),(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"j25Ttg",defaultMessage:"Model metrics"})},g.N3.MODEL_METRIC_CHARTS),(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"hFlaPP",defaultMessage:"System metrics"})},g.N3.SYSTEM_METRIC_CHARTS),(0,I.oX)()&&(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"s8a93+",defaultMessage:"Traces"})},g.N3.TRACES),(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"vPqFJE",defaultMessage:"Artifacts"})},g.N3.ARTIFACTS)]})};var A=r(9133),C=r(85466),M=r(81866),b=(r(69869),r(21317));var D={name:"s5xdrg",styles:"display:flex;align-items:center"},T={name:"2ecpu6",styles:"margin-left:8px;margin-right:4px"};function E(e){let{models:t,onRegisterClick:r,experimentId:s,runUuid:o}=e;const{theme:l}=(0,a.u)(),d=(e,t)=>(0,f.FD)(i.rId.Group,{children:[(0,f.Y)(i.rId.Label,{children:e}),t.map((e=>{const t=(0,A.first)(e.registeredModelVersionSummaries);if(!t)return(0,f.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_50",onClick:()=>r(e),children:[(0,f.Y)("div",{css:(0,n.AH)({marginRight:l.spacing.md},""),children:(0,A.last)(e.path.split("/"))}),(0,f.Y)(i.rId.HintColumn,{children:(0,f.Y)(c.N_,{target:"_blank",to:y.h.getRunPageTabRoute(s,o,"artifacts/"+e.path),children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_58",type:"link",size:"small",onClick:e=>{e.stopPropagation()},endIcon:(0,f.Y)(a.av,{}),children:(0,f.Y)(x.A,{id:"NqEELO",defaultMessage:"View model"})})})})]},e.absolutePath);const{status:d,displayedName:u,version:m,link:g}=t;return(0,f.Y)(c.N_,{target:"_blank",to:g,children:(0,f.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_80",children:[(0,f.Y)(i.rId.IconWrapper,{css:D,children:"READY"===d?(0,f.Y)(b.h,{}):d?M.UA[d]:null}),(0,f.FD)("span",{css:(0,n.AH)({marginRight:l.spacing.md},""),children:[u,(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_90",css:T,children:["v",m]})]}),(0,f.Y)(i.rId.HintColumn,{children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_89",type:"link",size:"small",onClick:e=>{e.stopPropagation()},endIcon:(0,f.Y)(a.av,{}),children:(0,f.Y)(x.A,{id:"T1sd79",defaultMessage:"Go to model"})})})]})},e.absolutePath)}))]}),u=t.filter((e=>e.registeredModelVersionSummaries.length>0)),m=t.filter((e=>!e.registeredModelVersionSummaries.length));return(0,f.FD)(f.FK,{children:[m.length?d("Unregistered models",m):null,m.length&&u.length?(0,f.Y)(i.rId.Separator,{}):null,u.length?d("Registered models",u):null]})}const U=e=>{let{runUuid:t,experimentId:r,runTags:s,artifactRootUri:o,registeredModelVersionSummaries:l}=e;const{theme:u}=(0,a.u)(),g=(0,d.useMemo)((()=>s?m.A.getLoggedModelsFromTags(s).map((e=>{let{artifactPath:t}=e;return t})):[]),[s]),p=(0,d.useMemo)((()=>(0,A.orderBy)(g.map((e=>({path:e,absolutePath:`${o}/${e}`,registeredModelVersionSummaries:(null===l||void 0===l?void 0:l.filter((t=>{let{source:r}=t;return r===`${o}/${e}`})))||[]}))),(e=>{var t;return parseInt((null===(t=e.registeredModelVersionSummaries[0])||void 0===t?void 0:t.version)||"0",10)}),"desc")),[g,l,o]),[h,v]=(0,d.useState)(null);if(p.length>1){const e=p.filter((e=>e.registeredModelVersionSummaries.length>0));return(0,f.FD)(f.FK,{children:[h&&(0,f.Y)(C.v,{runUuid:t,modelPath:h.absolutePath,modelRelativePath:h.path,disabled:!1,showButton:!1,modalVisible:!0,onCloseModal:()=>v(null)}),(0,f.FD)(i.rId.Root,{modal:!1,children:[(0,f.Y)(i.paO,{placement:"bottom",title:(0,f.Y)(x.A,{id:"qE/ZB7",defaultMessage:"{registeredCount}/{loggedCount} logged models are registered",values:{registeredCount:e.length,loggedCount:p.length}}),children:(0,f.Y)(i.rId.Trigger,{asChild:!0,children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_195",type:"primary",endIcon:(0,f.Y)(i.D3D,{}),children:(0,f.Y)(x.A,{id:"WGN2f3",defaultMessage:"Register model"})})})}),(0,f.Y)(i.rId.Content,{align:"end",children:(0,f.Y)(E,{models:p,onRegisterClick:v,experimentId:r,runUuid:t})})]})]})}const Y=(0,A.first)(p);if(!Y)return null;const _=(0,A.first)(Y.registeredModelVersionSummaries);return _?(0,f.Y)(c.N_,{to:_.link,target:"_blank",css:(0,n.AH)({marginLeft:u.spacing.sm},""),children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_231",endIcon:(0,f.Y)(a.av,{}),type:"link",children:"Model registered"})}):(0,f.Y)(C.v,{disabled:!1,runUuid:t,modelPath:Y.absolutePath,modelRelativePath:Y.path,showButton:!0,buttonType:"primary"})};var k={name:"ozd7xs",styles:"flex-shrink:0"};const F=e=>{let{hasComparedExperimentsBefore:t,comparedExperimentIds:r=[],experiment:n,runDisplayName:i,runTags:a,runParams:s,runUuid:o,handleRenameRunClick:l,handleDeleteRunClick:d,artifactRootUri:u,registeredModelVersionSummaries:m,isLoading:g}=e;const p=[function(){var e;return t&&r?(0,f.Y)(c.N_,{to:y.h.getCompareExperimentsPageRoute(r),children:(0,f.Y)(x.A,{id:"LLm5Bo",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:r.length}})}):(0,f.Y)(c.N_,{to:y.h.getExperimentPageRoute(null!==(e=null===n||void 0===n?void 0:n.experimentId)&&void 0!==e?e:""),"data-test-id":"experiment-runs-link",children:n.name})}()];return(0,f.FD)("div",{css:k,children:[(0,f.FD)(_.z,{title:(0,f.Y)("span",{"data-test-id":"runs-header",children:i}),breadcrumbs:p,children:[(0,f.Y)(_.o,{menu:[{id:"overflow-rename-button",onClick:l,itemName:(0,f.Y)(x.A,{id:"fv7vQf",defaultMessage:"Rename"})},...d?[{id:"overflow-delete-button",onClick:d,itemName:(0,f.Y)(x.A,{id:"7WkP1e",defaultMessage:"Delete"})}]:[]]}),(()=>{var e;return(0,f.Y)(U,{runUuid:o,experimentId:null!==(e=null===n||void 0===n?void 0:n.experimentId)&&void 0!==e?e:"",runTags:a,artifactRootUri:u,registeredModelVersionSummaries:m})})()]}),(0,f.Y)(R,{})]})};var L=r(88464),N=r(42848),P=r(21616),O=r(76758);const H=e=>{let{status:t}=e;const{theme:r}=(0,a.u)();return(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewstatusbox.tsx_81",css:(0,n.AH)({backgroundColor:"FINISHED"===t?r.isDarkMode?r.colors.green800:r.colors.green100:"KILLED"===t||"FAILED"===t?r.isDarkMode?r.colors.red800:r.colors.red100:"SCHEDULED"===t||"RUNNING"===t?r.isDarkMode?r.colors.blue800:r.colors.blue100:void 0},""),children:[t&&(0,f.Y)(O.F,{status:t})," ",(0,f.Y)(a.T.Text,{css:(0,n.AH)({marginLeft:r.spacing.sm},""),children:"FINISHED"===t?(0,f.Y)(a.T.Text,{color:"success",children:(0,f.Y)(x.A,{id:"ujm55z",defaultMessage:"Finished"})}):"KILLED"===t?(0,f.Y)(a.T.Text,{color:"error",children:(0,f.Y)(x.A,{id:"AupQl+",defaultMessage:"Killed"})}):"FAILED"===t?(0,f.Y)(a.T.Text,{color:"error",children:(0,f.Y)(x.A,{id:"AM8DuO",defaultMessage:"Failed"})}):"RUNNING"===t?(0,f.Y)(a.T.Text,{color:"info",children:(0,f.Y)(x.A,{id:"On3g1B",defaultMessage:"Running"})}):"SCHEDULED"===t?(0,f.Y)(a.T.Text,{color:"info",children:(0,f.Y)(x.A,{id:"X8OaXU",defaultMessage:"Scheduled"})}):t})]})},B=e=>{var t;let{runInfo:r,tags:n}=e;const i=m.A.getUser(r,n);return(0,f.Y)(c.N_,{to:y.h.searchRunsByUser(null!==(t=null===r||void 0===r?void 0:r.experimentId)&&void 0!==t?t:"",i),children:i})};var z=r(68109),V=r(65418),j=r(69526),K=r(32039),$=r(70618),W=r(9856);const{systemMetricsLabel:Z,modelMetricsLabel:G}=(0,j.YK)({systemMetricsLabel:{id:"aFMLIO",defaultMessage:"System metrics"},modelMetricsLabel:{id:"NiSPrL",defaultMessage:"Model metrics"}}),J=e=>t=>{let{key:r}=t;return r.toLowerCase().includes(e.toLowerCase())};var q={name:"1ff36h2",styles:"flex-grow:1"};const X=e=>{let{metricsList:t,runInfo:r,header:s,table:o}=e;const{theme:l}=(0,a.u)(),[{column:d}]=o.getLeafHeaders();return t.length?(0,f.FD)(f.FK,{children:[s&&(0,f.Y)(i.Hjg,{children:(0,f.Y)(i.nA6,{css:(0,n.AH)({flex:1,backgroundColor:l.colors.backgroundSecondary},""),children:(0,f.FD)(a.T.Text,{bold:!0,children:[s," (",t.length,")"]})})}),t.map((e=>{var t,n;let{key:a,value:s}=e;return(0,f.FD)(i.Hjg,{children:[(0,f.Y)(i.nA6,{style:{flexGrow:0,flexBasis:d.getSize()},children:(0,f.Y)(c.N_,{to:y.h.getMetricPageRoute([null!==(t=r.runUuid)&&void 0!==t?t:""],a,[null!==(n=r.experimentId)&&void 0!==n?n:""]),children:a})}),(0,f.Y)(i.nA6,{css:q,children:s.toString()})]},a)}))]}):null};var Q={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"},ee={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},te={name:"ozd7xs",styles:"flex-shrink:0"};const re=e=>{let{latestMetrics:t,runInfo:r}=e;const{theme:s}=(0,a.u)(),o=(0,L.A)(),[l,u]=(0,d.useState)(""),c=(0,d.useMemo)((()=>(0,A.values)(t)),[t]),m=(0,d.useMemo)((()=>[{id:"key",accessorKey:"key",header:()=>(0,f.Y)(x.A,{id:"pjlcSc",defaultMessage:"Metric"}),enableResizing:!0,size:240},{id:"value",header:()=>(0,f.Y)(x.A,{id:"t0Qkuc",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!1}]),[]),g=(0,d.useMemo)((()=>{const e=c.filter((e=>{let{key:t}=e;return(0,K.bw)(t)})),t=c.filter((e=>{let{key:t}=e;return!(0,K.bw)(t)}));return e.length>0&&t.length>0?[{header:o.formatMessage(Z),metrics:e.filter(J(l))},{header:o.formatMessage(G),metrics:t.filter(J(l))}]:[{header:void 0,metrics:c.filter(J(l))}]}),[l,c,o]),p=(0,$.N4)({data:c,getCoreRowModel:(0,W.HT)(),getRowId:e=>e.key,enableColumnResizing:!0,columnResizeMode:"onChange",columns:m});return(0,f.FD)("div",{css:ee,children:[(0,f.Y)(a.T.Title,{level:4,css:te,children:(0,f.Y)(x.A,{id:"m4159e",defaultMessage:"Metrics ({length})",values:{length:c.filter(J(l)).length}})}),(0,f.Y)("div",{css:(0,n.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,display:"flex",flexDirection:"column",flex:1,overflow:"hidden"},""),children:(()=>{if(!c.length)return(0,f.Y)("div",{css:Q,children:(0,f.Y)(i.SvL,{description:(0,f.Y)(x.A,{id:"9vj5Ap",defaultMessage:"No metrics recorded"})})});const e=(0,A.sum)(g.map((e=>{let{metrics:t}=e;return t.length})))<1;return(0,f.FD)(f.FK,{children:[(0,f.Y)("div",{css:(0,n.AH)({marginBottom:s.spacing.sm},""),children:(0,f.Y)(V.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewmetricstable.tsx_186",prefix:(0,f.Y)(V.S,{}),placeholder:o.formatMessage({id:"w2+rkp",defaultMessage:"Search metrics"}),value:l,onChange:e=>u(e.target.value),allowClear:!0})}),(0,f.FD)(i.XIK,{scrollable:!0,empty:e?(0,f.Y)("div",{css:(0,n.AH)({marginTop:4*s.spacing.md},""),children:(0,f.Y)(i.SvL,{description:(0,f.Y)(x.A,{id:"iTjTQt",defaultMessage:"No metrics match the search filter"})})}):null,children:[(0,f.Y)(i.Hjg,{isHeader:!0,children:p.getLeafHeaders().map((e=>(0,f.Y)(i.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewmetricstable.tsx_312",header:e,column:e.column,setColumnSizing:p.setColumnSizing,isResizing:e.column.getIsResizing(),style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,$.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),g.map(((e,t)=>(0,f.Y)(X,{metricsList:e.metrics,runInfo:r,header:e.header,table:p},e.header||t)))]})]})})()})]})};var ne=r(38243),ie=r(85343);var ae={name:"1flj9lk",styles:"text-align:left"},se={name:"ti75j2",styles:"margin:0"};const oe=e=>{let{dataset:t,onClick:r}=e;return(0,f.Y)(a.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_16",role:"link",css:ae,onClick:r,children:(0,f.Y)(ne.E,{datasetWithTags:t,displayTextAsLink:!0,css:se})})},le=e=>{let{tags:t,runInfo:r,datasets:s}=e;const[o,l]=(0,d.useState)(null),{theme:u}=(0,a.u)(),[c,m]=(0,d.useState)(!1);if(!s||!s.length)return null;const g=s[0],p=s.slice(1),h=e=>{var n,i,a;l({datasetWithTags:e,runData:{experimentId:null!==(n=r.experimentId)&&void 0!==n?n:void 0,runUuid:null!==(i=r.runUuid)&&void 0!==i?i:"",runName:null!==(a=r.runName)&&void 0!==a?a:void 0,datasets:s,tags:t}}),m(!0)};return(0,f.FD)("div",{css:(0,n.AH)({display:"flex",gap:u.spacing.sm,alignItems:"center"},""),children:[(0,f.Y)(oe,{dataset:g,onClick:()=>h(g)}),p.length?(0,f.FD)(i.rId.Root,{modal:!1,children:[(0,f.Y)(i.rId.Trigger,{asChild:!0,children:(0,f.FD)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_70",size:"small",children:["+",p.length]})}),(0,f.Y)(i.rId.Content,{children:p.map((e=>(0,f.Y)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_81",children:(0,f.Y)(oe,{dataset:e,onClick:()=>h(e)})},e.dataset.digest)))})]}):null,o&&(0,f.Y)(ie.O,{isOpen:c,setIsOpen:m,selectedDatasetWithRun:o,setSelectedDatasetWithRun:l})]})};var de=r(26809),ue=r(36568);const ce=e=>{let{parentRunUuid:t}=e;const r=(0,s.wA)(),n=(0,s.d4)((e=>{let{entities:r}=e;return r.runInfosByUuid[t]})),a=(0,ue.t)({runUuid:t,disabled:!(0,I.wD)()}),o=(0,d.useMemo)((()=>{var e;return(0,I.wD)()?null===a||void 0===a||null===(e=a.data)||void 0===e?void 0:e.info:n}),[a,n]);return(0,d.useEffect)((()=>{(0,I.wD)()||o||r((0,de.aO)(t))}),[r,t,o]),o?o.experimentId&&o.runUuid?(0,f.Y)(c.N_,{to:y.h.getRunPageRoute(o.experimentId,o.runUuid),children:o.runName}):null:(0,f.Y)(i.I_K,{loading:!0,label:(0,f.Y)(x.A,{id:"nfIS4i",defaultMessage:"Parent run name loading"})})};var me=r(43683),ge=r(98597),pe=r(98590);var he={name:"1wcfv52",styles:"margin-right:0"};const ve=e=>{let{runUuid:t,tags:r,onTagsUpdated:o}=e;const{theme:l}=(0,a.u)(),u=(0,s.wA)(),c=(0,L.A)(),[m,g]=(0,d.useMemo)((()=>[(0,A.keys)(r).filter(pe.oD),(0,A.values)(r).filter((e=>{let{key:t}=e;return(0,pe.oD)(t)}))]),[r]),{EditTagsModal:p,showEditTagsModal:h,isLoading:v}=(0,me.Q)({valueRequired:!0,allAvailableTags:m,saveTagsHandler:async(e,r,n)=>u((0,de.hD)(t,r,n)).then(o)}),Y=()=>{h({tags:g})},_=c.formatMessage({id:"EwAZgg",defaultMessage:"Edit tags"});return(0,f.FD)("div",{css:(0,n.AH)({paddingTop:l.spacing.xs,paddingBottom:l.spacing.xs,display:"flex",flexWrap:"wrap",alignItems:"center","> *":{marginRight:"0 !important"},gap:l.spacing.xs},""),children:[g.length<1?(0,f.Y)(a.B,{componentId:"mlflow.run_details.overview.tags.add_button",size:"small",type:"tertiary",onClick:Y,children:(0,f.Y)(x.A,{id:"dt3hj5",defaultMessage:"Add tags"})}):(0,f.FD)(f.FK,{children:[g.map((e=>(0,f.Y)(ge.t,{tag:e,enableFullViewModal:!0,css:he},`${e.key}-${e.value}`))),(0,f.Y)(N.T,{componentId:"mlflow.run_details.overview.tags.edit_button.tooltip",content:_,children:(0,f.Y)(a.B,{componentId:"mlflow.run_details.overview.tags.edit_button","aria-label":_,size:"small",icon:(0,f.Y)(i.R2l,{}),onClick:Y})})]}),v&&(0,f.Y)(a.H,{size:"small"}),p]})};var fe=r(96034),Ye=r(56928);const xe=e=>{var t;let{runUuid:r,tags:o,onDescriptionChanged:l}=e;const u=(null===(t=o[Ye.e])||void 0===t?void 0:t.value)||"",[c,m]=(0,d.useState)(!1),g=(0,L.A)(),{theme:p}=(0,a.u)(),h=(0,s.wA)(),v=!u;return(0,f.FD)("div",{css:(0,n.AH)({marginBottom:p.spacing.md},""),children:[(0,f.FD)(a.T.Title,{level:4,css:(0,n.AH)({display:"flex",alignItems:"center",gap:p.spacing.xs},""),children:[(0,f.Y)(x.A,{id:"ugwpKL",defaultMessage:"Description"}),(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdescriptionbox.tsx_46",size:"small",type:"tertiary","aria-label":g.formatMessage({id:"Mtj9Ay",defaultMessage:"Edit description"}),onClick:()=>m(!0),icon:(0,f.Y)(i.R2l,{})})]}),v&&!c&&(0,f.Y)(a.T.Hint,{children:(0,f.Y)(x.A,{id:"VcNdOq",defaultMessage:"No description"})}),(!v||c)&&(0,f.Y)(fe.V,{defaultMarkdown:u,onSubmit:e=>h((0,de.Jv)(r,Ye.e,e)).then(l).then((()=>m(!1))),onCancel:()=>m(!1),showEditor:c})]})};var _e=r(4874);var ye={name:"e0dnmk",styles:"cursor:pointer"};const Ie=e=>{let{registeredModelVersionSummaries:t}=e;const{theme:r}=(0,a.u)();return(0,f.Y)(i.nEg,{children:null===t||void 0===t?void 0:t.map((e=>(0,f.FD)(c.N_,{to:e.link,css:(0,n.AH)({display:"flex",alignItems:"center",gap:r.spacing.sm},""),children:[(0,f.Y)(b.h,{})," ",e.displayedName," ",(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewregisteredmodelsbox.tsx_40",css:ye,children:["v",e.version]})]},e.displayedName)))})};var Se=r(41261),we=r(82832);const Re=async e=>{let{queryKey:t}=e;const[,{runUuid:r}]=t;return we.M.getPromptVersionsForRun(r)};var Ae={name:"1bmnxg7",styles:"white-space:nowrap"};const Ce=e=>{let{runUuid:t}=e;const{theme:r}=(0,a.u)(),{data:s,error:o,isLoading:l}=function(e){var t;let{runUuid:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=(0,Se.I)(["run_uuid",{runUuid:r}],{queryFn:Re,retry:!1,...n});return{data:i.data,error:null!==(t=i.error)&&void 0!==t?t:void 0,isLoading:i.isLoading,refetch:i.refetch}}({runUuid:t}),d=null===s||void 0===s?void 0:s.model_versions;return l?(0,f.Y)(i.I_K,{}):o||!d||0===d.length?(0,f.Y)(a.T.Hint,{children:"\u2014"}):(0,f.Y)("div",{css:(0,n.AH)({display:"flex",flexDirection:"row",gap:r.spacing.sm,flexWrap:"wrap",padding:`${r.spacing.sm}px 0px`},""),children:d.map(((e,t)=>{const r=y.h.getPromptDetailsPageRoute(encodeURIComponent(e.name)),n=`${e.name} (v${e.version})`;return(0,f.FD)(a.T.Text,{css:Ae,children:[(0,f.Y)(c.N_,{to:r,children:n}),t<d.length-1&&","]},n)}))})},Me=e=>{let{loggedModels:t,runInfo:r}=e;const{theme:s}=(0,a.u)(),{experimentId:o,runUuid:l}=r,u=e=>(0,A.first)(e)||(0,f.Y)(x.A,{id:"0HbGko",defaultMessage:"Model"}),m=(0,d.useMemo)((()=>{const e=t.map((e=>u(e.flavors)));return new Set(e).size!==e.length}),[t]);return(0,f.Y)(i.nEg,{children:t.map(((e,t)=>(0,f.FD)(c.N_,{to:y.h.getRunPageRoute(null!==o&&void 0!==o?o:"",null!==l&&void 0!==l?l:"",e.artifactPath),css:(0,n.AH)({display:"flex",alignItems:"center",gap:s.spacing.sm,cursor:"pointer",height:m&&t>0?s.general.heightBase:s.general.heightSm},""),children:[(0,f.Y)(i.oiI,{}),(0,f.FD)("div",{children:[u(e.flavors),m&&t>0&&(0,f.Y)(a.T.Hint,{children:e.artifactPath})]})]},e.artifactPath)))})};var be=r(56412),De=r(1323);var Te={name:"1wcfv52",styles:"margin-right:0"},Ee={name:"kauk44",styles:"display:flex;gap:4px;white-space:nowrap"},Ue={name:"9gxvqt",styles:"display:flex;gap:4px;align-items:center"},ke={name:"ou8xsw",styles:"flex:0 0 auto"},Fe={name:"1wcfv52",styles:"margin-right:0"},Le={name:"kauk44",styles:"display:flex;gap:4px;white-space:nowrap"};const Ne=e=>{var t,r,s;let{runUuid:o,tags:l,search:d}=e;const u=null===l||void 0===l||null===(t=l[g.xd])||void 0===t?void 0:t.value,c=null===l||void 0===l||null===(r=l[m.A.gitCommitTag])||void 0===r?void 0:r.value,p=m.A.renderSource(l,d,o,u),{theme:h}=(0,a.u)();return p?(0,f.FD)("div",{css:(0,n.AH)({display:"flex",alignItems:"center",gap:h.spacing.sm,paddingTop:h.spacing.sm,paddingBottom:h.spacing.sm,flexWrap:"wrap"},""),children:[(0,f.Y)(De.m,{sourceType:null===(s=l[m.A.sourceTypeTag])||void 0===s?void 0:s.value,css:(0,n.AH)({color:h.colors.actionPrimaryBackgroundDefault},"")}),p," ",u&&(0,f.Y)(i.paO,{title:u,children:(0,f.Y)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewsourcebox.tsx_48",css:Te,children:(0,f.FD)("div",{css:Ee,children:[(0,f.Y)(i.OKA,{})," ",u]})})}),c&&(0,f.Y)(i.paO,{dangerouslySetAntdProps:{overlayStyle:{maxWidth:"none"}},title:(0,f.FD)("div",{css:Ue,children:[c,(0,f.Y)(be.i,{css:ke,showLabel:!1,size:"small",type:"tertiary",copyText:c,icon:(0,f.Y)(i.TdU,{})})]}),children:(0,f.Y)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewsourcebox.tsx_72",css:Fe,children:(0,f.FD)("div",{css:Le,children:[(0,f.Y)(i.fSU,{})," ",c.slice(0,7)]})})})]}):(0,f.Y)(a.T.Hint,{children:"\u2014"})};var Pe=r(14830),Oe=r(67212),He=r(76688);var Be=r(46398),ze=r(55030),Ve=r(36506),je=r(30706);const Ke=[ze.I7.RelationshipType,ze.I7.Name,ze.I7.Status,ze.I7.CreationTime,ze.I7.Dataset];var $e={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},We={name:"g030ms",styles:"display:flex;flex-direction:row;justify-content:space-between;align-items:center"},Ze={name:"ozd7xs",styles:"flex-shrink:0"};const Ge=e=>{let{inputs:t,outputs:r,runInfo:s}=e;const{theme:o}=(0,a.u)(),{models:l,isLoading:u,errors:c}=((e,t,r)=>{var n,i;const a=(0,A.compact)((0,A.uniq)(null===e||void 0===e||null===(n=e.modelInputs)||void 0===n?void 0:n.map((e=>e.modelId)))),s=(0,A.compact)((0,A.uniq)(null===t||void 0===t||null===(i=t.modelOutputs)||void 0===i?void 0:i.map((e=>e.modelId)))),o=(0,He.v)(a),l=(0,He.v)(s),u=(0,d.useMemo)((()=>o.map((e=>{var t,r;if(null!==(t=e.data)&&void 0!==t&&t.model)return{...null===(r=e.data)||void 0===r?void 0:r.model,direction:"input"}}))),[o]),c=(0,d.useMemo)((()=>l.map((e=>{var t,r;if(null!==(t=e.data)&&void 0!==t&&t.model)return{...null===(r=e.data)||void 0===r?void 0:r.model,direction:"output"}}))),[l]);return{models:(0,d.useMemo)((()=>{var e,t;return null!==(e=(0,A.uniqBy)((0,A.compact)([...u,...c]).map((t=null===r||void 0===r?void 0:r.runUuid,e=>{var r;return null!==(r=e.data)&&void 0!==r&&r.metrics?{...e,data:{...e.data,metrics:e.data.metrics.filter((e=>!t||e.run_id===t))}}:e})),(e=>{var t;return null===(t=e.info)||void 0===t?void 0:t.model_id})))&&void 0!==e?e:[]}),[u,c,r]),errors:[...o,...l].map((e=>e.error)).filter(Boolean),isLoading:[...o,...l].some((e=>e.isLoading))}})(t,r,s),[m,g]=(0,d.useState)({}),p=(0,ze.ih)({loggedModels:l,columnVisibility:m,disablePinnedColumns:!0,disableOrderBy:!0,isCompactMode:!1,supportedAttributeColumnKeys:Ke}),h=(0,d.useMemo)((()=>(0,A.first)(c)),[c]);return(0,f.FD)("div",{css:$e,children:[(0,f.FD)("div",{css:We,children:[(0,f.Y)(a.T.Title,{level:4,css:Ze,children:(0,f.Y)(x.A,{id:"km8vtM",defaultMessage:"Logged models ({length})",values:{length:l.length}})}),(0,f.Y)(je.$,{columnDefs:p,onUpdateColumns:g,columnVisibility:m,customTrigger:(0,f.Y)(a.B,{componentId:"mlflow.logged_model.list.columns",icon:(0,f.Y)(i.jng,{})})})]}),(0,f.Y)(N.S,{size:"sm",shrinks:!1}),(0,f.FD)("div",{css:(0,n.AH)({padding:o.spacing.sm,border:`1px solid ${o.colors.border}`,borderRadius:o.general.borderRadiusBase,display:"flex",flexDirection:"column",flex:1,overflow:"hidden"},""),children:[h instanceof Error&&h.message&&(0,f.FD)(f.FK,{children:[(0,f.Y)(i.FcD,{type:"error",message:h.message,closable:!1,componentId:"mlflow.run_page.logged_model.list.error"}),(0,f.Y)(N.S,{size:"sm",shrinks:!1})]}),(0,f.Y)(Ve.X,{children:(0,f.Y)(Be._,{columnDefs:p,loggedModels:l,columnVisibility:m,isLoading:u,isLoadingMore:!1,error:null,moreResultsAvailable:!1,disableLoadMore:!0,css:Je(o),displayShowExampleButton:!1})})]})]})},Je=e=>({"&.ag-theme-balham":{"--ag-border-color":e.colors.border,"--ag-row-border-color":e.colors.border,"--ag-foreground-color":e.colors.textPrimary,"--ag-background-color":"transparent","--ag-odd-row-background-color":"transparent","--ag-row-hover-color":e.colors.actionDefaultBackgroundHover,"--ag-selected-row-background-color":e.colors.actionDefaultBackgroundPress,"--ag-header-foreground-color":e.colors.textPrimary,"--ag-header-background-color":e.colors.backgroundPrimary,"--ag-modal-overlay-background-color":e.colors.overlayOverlay,".ag-header-row.ag-header-row-column-group":{"--ag-header-foreground-color":e.colors.textPrimary},borderTop:0,fontSize:e.typography.fontSizeBase,".ag-center-cols-viewport":{...(0,a.E)(e,{orientation:"horizontal"})}}});const qe=()=>(0,f.Y)(a.T.Hint,{children:"\u2014"});var Xe={name:"82a6rk",styles:"flex:1"},Qe={name:"1ctdo4k",styles:"min-height:360px;max-height:760px;overflow:hidden;display:flex"};const et=e=>{let{runUuid:t,onRunDataUpdated:r,tags:i,runInfo:s,datasets:o,params:l,latestMetrics:u,runInputs:g,runOutputs:p,registeredModelVersionSummaries:h,isLoadingLoggedModels:v=!1}=e;const{theme:Y}=(0,a.u)(),{search:_}=(0,c.zy)(),y=(0,L.A)(),S=(0,d.useMemo)((()=>m.A.getLoggedModelsFromTags(i)),[i]),w=i[P.Ol],R=!(0,A.isEmpty)(null===g||void 0===g?void 0:g.modelInputs)||!(0,A.isEmpty)(null===p||void 0===p?void 0:p.modelOutputs),C=!(0,I.Ok)()||!R,M=h,b=(null===S||void 0===S?void 0:S.length)>0;return(0,f.FD)("div",{css:Xe,children:[(0,f.Y)(xe,{runUuid:t,tags:i,onDescriptionChanged:r}),(0,f.Y)(a.T.Title,{level:4,children:(0,f.Y)(x.A,{id:"Isaf66",defaultMessage:"Details"})}),(()=>{var e,n,l;return(0,f.FD)(Pe.N,{children:[(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"PP5HZf",defaultMessage:"Created at"}),value:s.startTime?m.A.formatTimestamp(s.startTime,y):(0,f.Y)(qe,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"7KKvmS",defaultMessage:"Created by"}),value:(0,f.Y)(B,{runInfo:s,tags:i})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"ffema5",defaultMessage:"Experiment ID"}),value:(0,f.Y)(Oe.t,{value:null!==(e=null===s||void 0===s?void 0:s.experimentId)&&void 0!==e?e:""})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"vxY1CI",defaultMessage:"Status"}),value:(0,f.Y)(H,{status:s.status})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"JYVQuV",defaultMessage:"Run ID"}),value:(0,f.Y)(Oe.t,{value:null!==(n=s.runUuid)&&void 0!==n?n:""})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"ZoIjun",defaultMessage:"Duration"}),value:m.A.getDuration(s.startTime,s.endTime)}),w&&(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"zdYXP8",defaultMessage:"Parent run"}),value:(0,f.Y)(ce,{parentRunUuid:w.value})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"fwp4JP",defaultMessage:"Datasets used"}),value:null!==o&&void 0!==o&&o.length?(0,f.Y)(le,{tags:i,runInfo:s,datasets:o}):(0,f.Y)(qe,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"rs7Iic",defaultMessage:"Tags"}),value:(0,f.Y)(ve,{runUuid:null!==(l=s.runUuid)&&void 0!==l?l:"",tags:i,onTagsUpdated:r})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"WmDiY9",defaultMessage:"Source"}),value:(0,f.Y)(Ne,{tags:i,search:_,runUuid:t})}),C&&(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"mjNFJt",defaultMessage:"Logged models"}),value:v?(0,f.Y)(a.H,{}):b?(0,f.Y)(Me,{runInfo:s,loggedModels:S}):(0,f.Y)(qe,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"BCpX6U",defaultMessage:"Registered models"}),value:(null===M||void 0===M?void 0:M.length)>0?(0,f.Y)(Ie,{registeredModelVersionSummaries:M}):(0,f.Y)(qe,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"UCQwvo",defaultMessage:"Registered prompts"}),value:(0,f.Y)(Ce,{runUuid:t})})]})})(),(0,f.FD)("div",{css:(0,n.AH)({display:"flex",gap:Y.spacing.lg,minHeight:360,maxHeight:760,overflow:"hidden"},""),children:[(0,f.Y)(z.y,{params:l}),(0,f.Y)(re,{latestMetrics:u,runInfo:s})]}),(0,I.Ok)()&&R&&(0,f.FD)(f.FK,{children:[(0,f.Y)(N.S,{}),(0,f.Y)("div",{css:Qe,children:(0,f.Y)(Ge,{inputs:g,outputs:p,runInfo:s})})]}),(0,f.Y)(N.S,{})]})};var tt=r(23275),rt=r(52350),nt=r(82214);function it(e){let{runId:t}=e;return(0,f.Y)(nt.E,{statusCode:404,subMessage:`Run ID ${t} does not exist`,fallbackHomePageReactRoute:y.h.rootRoute})}var at=r(47664),st=r(64558),ot=r(33946),lt=r(75627),dt=r(59397);const ut=e=>{var t;let{contextData:{metricsForRun:r},hoverData:n,chartData:{metricKey:i},isHovering:s,mode:o}=e;const l=(0,lt.bL)(n)?n.hoveredDataPoint:n,d=(0,L.A)();if(o===lt.QS.MultipleTracesWithScanline&&(0,lt.bL)(n)&&s)return(0,f.Y)(dt.G,{hoverData:n});if(null===l||void 0===l||!l.metricEntity)return null;const{timestamp:u,step:c,value:g}=l.metricEntity,p=(null===r||void 0===r||null===(t=r[i])||void 0===t?void 0:t.length)>1,h=(0,K.bw)(i),v=p&&h&&!(0,A.isUndefined)(u),Y=p&&!h&&!(0,A.isUndefined)(c);return(0,f.FD)("div",{children:[Y&&(0,f.FD)("div",{css:ct.valueField,children:[(0,f.FD)("strong",{children:[(0,f.Y)(x.A,{id:"a02Pn6",defaultMessage:"Step"}),":"]})," ",c]}),v&&(0,f.FD)("div",{css:ct.valueField,children:[(0,f.FD)("strong",{children:[(0,f.Y)(x.A,{id:"faLIN+",defaultMessage:"Timestamp"}),":"]})," ",m.A.formatTimestamp(u,d)]}),g&&(0,f.FD)("div",{children:[(0,f.Y)(a.T.Text,{bold:!0,children:i}),(0,f.Y)(N.S,{size:"xs"}),(0,f.Y)(a.T.Text,{children:g})]})]})},ct={valueField:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}};var mt=r(30152),gt=r(36118),pt=r(39045),ht=r(19415),vt=r(62758),ft=r(32614),Yt=r(63617),xt=r(4972),_t=r(55999),yt=r(688),It=r(71932),St=r(40029);var wt={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},Rt={name:"1tc6xju",styles:"flex:1;overflow:auto"};const At=e=>{var t;let{runInfo:r,metricKeys:o,mode:l,chartUIState:u,updateChartsUIState:c,latestMetrics:m={},params:p={},tags:h={}}=e;const{theme:v}=(0,a.u)(),[Y,x]=(0,d.useState)(""),{formatMessage:_}=(0,L.A)(),{compareRunCharts:y,compareRunSections:S,chartsSearchFilter:w}=u,R=(0,d.useMemo)((()=>{var e;return null!==(e=null===y||void 0===y?void 0:y.filter((e=>!e.deleted)))&&void 0!==e?e:[]}),[y]),[C,M]=(0,d.useState)(void 0),b=(0,s.d4)((e=>{var t;let{entities:n}=e;return(0,A.mapValues)(n.sampledMetricsByRunUuid[null!==(t=r.runUuid)&&void 0!==t?t:""],(e=>(0,A.compact)((0,A.values)(e).map((e=>{let{metricsHistory:t}=e;return t})).flat())))})),D=(0,d.useMemo)((()=>({runInfo:r,metricsForRun:b})),[r,b]),{imagesByRunUuid:T}=(0,s.d4)((e=>({imagesByRunUuid:e.entities.imagesByRunUuid}))),[E,U]=(0,d.useState)(null),k=(0,vt.iO)(),F=(0,vt.cA)(),N=(0,vt.KP)(),P=(0,vt.Ez)(),O=(0,d.useMemo)((()=>{var e,t,n;return[{displayName:null!==(e=r.runName)&&void 0!==e?e:"",metrics:m,params:p,tags:h,images:T[null!==(t=r.runUuid)&&void 0!==t?t:""]||{},metricHistory:{},uuid:null!==(n=r.runUuid)&&void 0!==n?n:"",color:v.colors.primary,runInfo:r}]}),[r,m,p,h,T,v]);(0,d.useEffect)((()=>{if((!S||!y)&&O.length>0){const{resultChartSet:e,resultSectionSet:t}=mt.i$.getBaseChartAndSectionConfigs({runsData:O,enabledSectionNames:["model"===l?g.NN:g.rx],filterMetricNames:e=>{const t=e.startsWith(g.qt);return"model"===l?!t:t}});c((r=>({...r,compareRunCharts:e,compareRunSections:t})))}}),[y,S,O,l,c]),(0,d.useEffect)((()=>{c((e=>{if(!e.compareRunCharts||!e.compareRunSections)return e;const{resultChartSet:t,resultSectionSet:r,isResultUpdated:n}=mt.i$.updateChartAndSectionConfigs({compareRunCharts:e.compareRunCharts,compareRunSections:e.compareRunSections,runsData:O,isAccordionReordered:e.isAccordionReordered,filterMetricNames:e=>{const t=e.startsWith(g.qt);return"model"===l?!t:t}});return n?{...e,compareRunCharts:t,compareRunSections:r}:e}))}),[O,c,l]);const H=(0,xt.$)(),B=u.autoRefreshEnabled&&(0,I.vC)()&&H,z=Boolean(h[g.Cr]);return(0,_t.L)({runUuids:[null!==(t=r.runUuid)&&void 0!==t?t:""],runUuidsIsActive:["RUNNING"===r.status],autoRefreshEnabled:B,enabled:z}),(0,f.FD)("div",{css:wt,children:[(0,f.FD)("div",{css:(0,n.AH)({paddingBottom:v.spacing.md,display:"flex",gap:v.spacing.sm,flex:"0 0 auto"},""),children:[(0,f.Y)(St.I,{chartsSearchFilter:w}),(0,I.vC)()&&(0,f.Y)(i.ffE,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewmetricchartsv2.tsx_244",pressed:u.autoRefreshEnabled,onPressedChange:e=>{c((t=>({...t,autoRefreshEnabled:e})))},children:_({id:"zC/uKl",defaultMessage:"Auto-refresh"})}),(0,f.Y)(yt.f,{metricKeyList:o,globalLineChartConfig:u.globalLineChartConfig,updateUIState:c})]}),(0,f.Y)("div",{css:Rt,children:(0,f.Y)(lt.W,{contextData:D,component:ut,children:(0,f.Y)(It.c_,{visibleChartCards:R,children:(0,f.Y)(pt.J,{compareRunSections:S,compareRunCharts:R,reorderCharts:k,insertCharts:F,chartData:O,startEditChart:e=>U(e),removeChart:N,addNewChartCard:e=>t=>U(mt.i$.getEmptyChartCardByType(t,!1,void 0,e)),search:null!==w&&void 0!==w?w:"",supportedChartTypes:[mt.zL.LINE,mt.zL.BAR,mt.zL.IMAGE],setFullScreenChart:M,autoRefreshEnabled:B,globalLineChartConfig:u.globalLineChartConfig,groupBy:null})})})}),E&&(0,f.Y)(ht.z,{chartRunData:O,metricKeyList:o,paramKeyList:[],config:E,onSubmit:e=>{P(e),U(null)},onCancel:()=>U(null),groupBy:null,supportedChartTypes:[mt.zL.LINE,mt.zL.BAR,mt.zL.IMAGE],globalLineChartConfig:u.globalLineChartConfig}),(0,f.Y)(Yt._,{fullScreenChart:C,onCancel:()=>M(void 0),chartData:O,tooltipContextValue:D,tooltipComponent:ut,autoRefreshEnabled:B,groupBy:null})]})},Ct=e=>{const t=`${e.runInfo.runUuid}-${e.mode}`,r=(0,d.useMemo)((()=>ft.A.getStoreForComponent("RunPage",t)),[t]),[n,i]=(0,d.useState)((()=>{const e={isAccordionReordered:!1,compareRunCharts:void 0,compareRunSections:void 0,autoRefreshEnabled:(0,I.vC)(),globalLineChartConfig:{xAxisKey:gt.fj.STEP,lineSmoothness:0,selectedXAxisMetricKey:""}};try{const t=r.getItem("chartUIState");return t?JSON.parse(t):e}catch{return e}}));return(0,d.useEffect)((()=>{r.setItem("chartUIState",JSON.stringify(n))}),[n,r]),(0,f.Y)(vt.oB,{updateChartsUIState:i,children:(0,f.Y)(At,{...e,chartUIState:n,updateChartsUIState:i})})};var Mt=r(91089);const bt=[r(80171).se.runName];var Dt={name:"fxp7t8",styles:"flex:1;min-width:0"};const Tt=e=>{let{experimentId:t,runUuid:r}=e;const n=(0,d.useMemo)((()=>[t]),[t]);return(0,f.Y)("div",{css:Dt,children:(0,f.Y)(Mt.O,{experimentIds:n,runUuid:r,disabledColumns:bt})})};var Et=r(26626);const Ut=()=>(0,f.FD)(u.L,{children:[(0,f.Y)(i.oud,{loading:!0,label:(0,f.Y)(x.A,{id:"ea5zBl",defaultMessage:"Run page loading"})}),[...Array(3).keys()].map((e=>(0,f.Y)(i.I_K,{seed:`s-${e}`},e)))]}),kt=()=>{var e,t;const{runUuid:r,experimentId:o}=(0,c.g)(),h=(0,c.Zp)(),{theme:_}=(0,a.u)(),[w,R]=(0,d.useState)(!1),[A,C]=(0,d.useState)(!1);l()(r,"[RunPage] Run UUID route param not provided"),l()(o,"[RunPage] Experiment ID route param not provided");const{experiment:M,error:b,latestMetrics:D,loading:T,params:E,refetchRun:U,runInfo:k,tags:L,experimentFetchError:N,runFetchError:P,apiError:O,datasets:H,runInputs:B,runOutputs:z,registeredModelVersionSummaries:V}=(0,tt.g)({experimentId:o,runUuid:r}),[j,$]=(0,d.useMemo)((()=>D?[Object.keys(D).filter((e=>!(0,K.bw)(e))),Object.keys(D).filter((e=>(0,K.bw)(e)))]:[[],[]]),[D]),{comparedExperimentIds:W=[],hasComparedExperimentsBefore:Z=!1}=(0,s.d4)((e=>e.comparedExperiments||{})),G=S(),J=!1,q=(0,v.U)(`(min-width: ${_.responsive.breakpoints.sm}px)`),X=T&&(!k||!M);return P instanceof rt.s&&P.getErrorCode()===at.tG.RESOURCE_DOES_NOT_EXIST||(null===O||void 0===O?void 0:O.code)===at.tG.RESOURCE_DOES_NOT_EXIST||b&&(0,Et.b)(b).match(/not found$/)?(0,f.Y)(it,{runId:r}):N instanceof rt.s&&N.getErrorCode()===at.tG.RESOURCE_DOES_NOT_EXIST?(0,f.Y)(st.A,{}):P||N?null:(0,I.wD)()&&(b||O)?(0,f.Y)("div",{css:(0,n.AH)({marginTop:_.spacing.lg},""),children:(0,f.Y)(i.SvL,{title:(0,f.Y)(x.A,{id:"XaC3qF",defaultMessage:"Can't load run details"}),description:(0,Et.b)(null!==O&&void 0!==O?O:b),image:(0,f.Y)(a.j,{})})}):!X&&k&&M?(0,f.FD)(f.FK,{children:[(0,f.FD)(u.L,{usesFullHeight:q,children:[(0,f.Y)(F,{comparedExperimentIds:W,experiment:M,handleRenameRunClick:()=>R(!0),handleDeleteRunClick:()=>C(!0),hasComparedExperimentsBefore:Z,runDisplayName:m.A.getRunDisplayName(k,r),runTags:L,runParams:E,runUuid:r,artifactRootUri:null!==(e=null===k||void 0===k?void 0:k.artifactUri)&&void 0!==e?e:void 0,registeredModelVersionSummaries:V,isLoading:T||J}),(0,f.Y)("div",{css:(0,n.AH)({flex:1,overflow:"auto",marginBottom:_.spacing.sm,display:"flex"},""),children:(()=>{var e;if(!k)return null;switch(G){case g.N3.MODEL_METRIC_CHARTS:return(0,f.Y)(Ct,{mode:"model",metricKeys:j,runInfo:k,latestMetrics:D,tags:L,params:E},"model");case g.N3.SYSTEM_METRIC_CHARTS:return(0,f.Y)(Ct,{mode:"system",metricKeys:$,runInfo:k,latestMetrics:D,tags:L,params:E},"system");case g.N3.ARTIFACTS:return(0,f.Y)(Y,{runUuid:r,runTags:L,runOutputs:z,experimentId:o,artifactUri:null!==(e=k.artifactUri)&&void 0!==e?e:void 0});case g.N3.TRACES:if((0,I.oX)())return(0,f.Y)(Tt,{runUuid:r,runTags:L,experimentId:o})}return(0,f.Y)(et,{runInfo:k,tags:L,params:E,latestMetrics:D,runUuid:r,onRunDataUpdated:U,runInputs:B,runOutputs:z,datasets:H,registeredModelVersionSummaries:V,isLoadingLoggedModels:J})})()})]}),(0,f.Y)(p.j,{runUuid:r,onClose:()=>R(!1),runName:null!==(t=k.runName)&&void 0!==t?t:"",isOpen:w,onSuccess:U}),(0,f.Y)(ot.A,{selectedRunIds:[r],onClose:()=>C(!1),isOpen:A,onSuccess:()=>{h(y.h.getExperimentPageRoute(o))}})]}):(0,f.Y)(Ut,{})};var Ft=kt},82832:function(e,t,r){r.d(t,{M:function(){return o}});var n=r(39416),i=r(53962),a=r(84565);const s=async e=>{let{reject:t,response:r,err:i}=e;const a=(0,n.a$)(r),s=a instanceof n.Bk?i:a;if(r)try{var o;const e=null===(o=await r.json())||void 0===o?void 0:o.message;e&&(s.message=e)}catch{}t(s)},o={listRegisteredPrompts:(e,t)=>{const r=new URLSearchParams;let n=`tags.\`${a.PS}\` = '${a.pY}'`;e&&(n=`${n} AND name ILIKE '%${e}%'`),t&&r.append("page_token",t),r.append("filter",n);const o=["ajax-api/2.0/mlflow/registered-models/search",r.toString()].join("?");return(0,i.AC)({relativeUrl:o,error:s})},setRegisteredPromptTag:(e,t,r)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/set-tag",method:"POST",body:JSON.stringify({key:t,value:r,name:e}),error:s}),deleteRegisteredPromptTag:(e,t)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete-tag",method:"DELETE",body:JSON.stringify({key:t,name:e}),error:s}),createRegisteredPrompt:e=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/create",method:"POST",body:JSON.stringify({name:e,tags:[{key:a.PS,value:a.pY}]}),error:s}),createRegisteredPromptVersion:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/create",method:"POST",body:JSON.stringify({name:e,description:r,source:"dummy-source",tags:[{key:a.PS,value:a.pY},...t]}),error:s})},setRegisteredPromptVersionTag:(e,t,r,n)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/set-tag",method:"POST",body:JSON.stringify({key:r,value:n,name:e,version:t}),error:s}),deleteRegisteredPromptVersionTag:(e,t,r)=>{(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete-tag",method:"DELETE",body:JSON.stringify({key:r,name:e,version:t}),error:s})},getPromptDetails:e=>{const t=new URLSearchParams;t.append("name",e);const r=["ajax-api/2.0/mlflow/registered-models/get",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},getPromptVersions:e=>{const t=new URLSearchParams;t.append("filter",`name='${e}' AND tags.\`${a.PS}\` = '${a.pY}'`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},getPromptVersionsForRun:e=>{const t=new URLSearchParams;t.append("filter",`tags.\`${a.PS}\` = '${a.pY}' AND tags.\`${a.xd}\` ILIKE "%${e}%"`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},deleteRegisteredPrompt:e=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete",method:"DELETE",body:JSON.stringify({name:e}),error:s}),deleteRegisteredPromptVersion:(e,t)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete",method:"DELETE",body:JSON.stringify({name:e,version:t}),error:s})}},84565:function(e,t,r){r.d(t,{Dh:function(){return n},Dp:function(){return o},PS:function(){return a},dv:function(){return l},pY:function(){return s},xd:function(){return i}});const n="mlflow.prompt.text",i="mlflow.prompt.run_ids",a="mlflow.prompt.is_prompt",s="true";let o=function(e){return e.TABLE="table",e.PREVIEW="preview",e.COMPARE="compare",e}({});const l=e=>{var t,r;return null===e||void 0===e||null===(t=e.tags)||void 0===t||null===(r=t.find((e=>e.key===n)))||void 0===r?void 0:r.value}}}]);
//# sourceMappingURL=8618.bf8b88d1.chunk.js.map