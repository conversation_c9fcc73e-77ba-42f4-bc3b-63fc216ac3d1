{"version": 3, "file": "static/js/3122.b11ce3db.chunk.js", "mappings": "qKAEA,SAASA,EAAqBC,GAC5B,QAAgBC,IAAZD,EAGJ,MAA0B,kBAAZA,GAAwBA,aAAmBE,UAAYF,aAAmBG,KACpFH,EACAI,KAAKC,UAAUL,EACrB,CAGO,MAAMM,EAA0BC,eACrCC,GAGI,IAFJC,EAA2CC,UAAAC,OAAA,QAAAV,IAAAS,UAAA,GAAAA,UAAA,GAAG,MAC9CE,EAAUF,UAAAC,OAAA,EAAAD,UAAA,QAAAT,EAEV,MAAMY,QAAiBC,MAAMN,EAAK,CAChCC,SACAG,KAAMb,EAAqBa,GAC3BG,QAASH,EAAO,CAAE,eAAgB,oBAAuB,CAAC,IAE5D,IAAKC,EAASG,GAAI,CAChB,MAAMC,GAAkBC,EAAAA,EAAAA,IAAqBL,GAC7C,GAAII,EAAiB,CACnB,IAEE,MAAME,SAAiBN,EAASO,QAAQD,QACxCF,EAAgBE,QAAiB,OAAPA,QAAO,IAAPA,EAAAA,EAAWF,EAAgBE,OACvD,CAAE,MACA,CAEF,MAAMF,CACR,CACF,CACA,OAAOJ,EAASO,MAClB,C,yLCbO,MAAMC,EAAmDC,IAM3B,IAN4B,YAC/DC,EAAW,QACXC,GAIDF,EACC,MAAQG,iBAAkBC,IAA8BC,EAAAA,EAAAA,KAAYC,IAAA,IAAC,SAAEC,GAAsBD,EAAA,MAAM,CACjGH,iBAAkBI,EAASC,uBAAuBN,GACnD,IAED,IAAIO,EAAAA,EAAAA,MAAiD,CACnD,MAAMC,EAAuC,GACkB,IAADC,EAAAC,EAA9D,GAAe,OAAXX,QAAW,IAAXA,GAAAA,EAAaY,MAAQ,kBAAmBZ,EAAYY,KACtC,QAAhBF,EAAAV,EAAYY,YAAI,IAAAF,GAAe,QAAfC,EAAhBD,EAAkBG,qBAAa,IAAAF,GAA/BA,EAAiCG,SAASC,IACxCN,EAAOO,KAAK,CACVC,cAAeF,EAAaG,KAC5BC,QAASJ,EAAaI,QACtBC,KACEL,EAAaG,MAAQH,EAAaI,QAC9BE,EAAAA,EAAoBC,yBAAyBP,EAAaG,KAAMH,EAAaI,SAC7E,GACNI,OAAQR,EAAaQ,OACrBC,OAAQT,EAAaS,QACrB,IAGN,OAAOf,CACT,CAEA,OAAIN,EACKA,EAA0BsB,KAAKV,IACpC,MAAMG,EAAOH,EAAaG,KACpBE,EAAOC,EAAAA,EAAoBC,yBAAyBJ,EAAMH,EAAaI,SAC7E,MAAO,CACLF,cAAeF,EAAaG,KAC5BC,QAASJ,EAAaI,QACtBC,OACAG,OAAQR,EAAaQ,OACrBC,OAAQT,EAAaS,OACtB,IAIE,EAAE,EC9BLE,EAA+CC,IAGnDC,EAAAA,EAAAA,OAAMD,EAAY,OAePE,EAAqBF,GACtB,OAAVA,QAAU,IAAVA,OAAU,EAAVA,EAAYF,KAAKK,IAAY,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAM,CACjCC,QAAS,CACPC,OAAoC,QAA9Bf,EAAsB,QAAtBC,EAAEF,EAAae,eAAO,IAAAb,OAAA,EAApBA,EAAsBc,cAAM,IAAAf,EAAAA,EAAI,GACxCb,KAAgC,QAA5Be,EAAsB,QAAtBC,EAAEJ,EAAae,eAAO,IAAAX,OAAA,EAApBA,EAAsBhB,YAAI,IAAAe,EAAAA,EAAI,GACpCc,QAAsC,QAA/BZ,EAAsB,QAAtBC,EAAEN,EAAae,eAAO,IAAAT,OAAA,EAApBA,EAAsBW,eAAO,IAAAZ,EAAAA,EAAI,GAC1Ca,OAAoC,QAA9BX,EAAsB,QAAtBC,EAAER,EAAae,eAAO,IAAAP,OAAA,EAApBA,EAAsBU,cAAM,IAAAX,EAAAA,EAAI,GACxCb,OAAoC,QAA9Be,EAAsB,QAAtBC,EAAEV,EAAae,eAAO,IAAAL,OAAA,EAApBA,EAAsBhB,cAAM,IAAAe,EAAAA,EAAI,GACxCU,WAA4C,QAAlCR,EAAsB,QAAtBC,EAAEZ,EAAae,eAAO,IAAAH,OAAA,EAApBA,EAAsBO,kBAAU,IAAAR,EAAAA,EAAI,IAElDS,KAMuC,QANnCP,EACe,QADfC,EACFd,EAAaoB,YAAI,IAAAN,OAAA,EAAjBA,EACInB,KAAK0B,IAAG,IAAAC,EAAAC,EAAA,MAAM,CACdC,IAAY,QAATF,EAAED,EAAIG,WAAG,IAAAF,EAAAA,EAAI,GAChBG,MAAgB,QAAXF,EAAEF,EAAII,aAAK,IAAAF,EAAAA,EAAI,GACrB,IACAG,QAAQL,KAASM,EAAAA,EAAAA,SAAQN,EAAIG,cAAK,IAAAX,EAAAA,EAAI,GAC5C,IA8BUe,EAAwBC,IAMD,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IANG,QACpChE,EAAO,aACPiE,GAIDP,EACC,MAAMQ,GAAeC,EAAAA,EAAAA,MACfC,GAAWC,EAAAA,EAAAA,MAKjB,GAAIH,EAAc,CAAC,IAADI,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAChB,MAKMC,EALeC,MACnBC,EAAAA,EAAAA,GAAe,CACb9E,YAG+B6E,IAGnCE,EAAAA,EAAAA,YAAU,MACJxE,EAAAA,EAAAA,OAGJ6D,GAASY,EAAAA,EAAAA,IAAuB,CAAEC,OAAQjF,IAAW,GACpD,CAACoE,EAAUpE,IAEd,MAAM,cAAEkF,EAAa,KAAEjC,EAAI,OAAEkC,EAAM,SAAEC,IAAaC,EAAAA,EAAAA,UAAQ,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAvFrCtE,EAyFzB,MAAO,CACLwD,eAAee,EAAAA,EAAAA,QACbxE,GA3FqBC,EA4FiD,QAA/C4D,EAAgC,QAAhCC,EAACX,EAA2BjE,YAAI,IAAA4E,GAAM,QAANC,EAA/BD,EAAiC5E,YAAI,IAAA6E,OAAN,EAA/BA,EAAuCU,eAAO,IAAAZ,EAAAA,EAAI,GA3FlF5D,EACG6B,QAAOzD,IAAA,IAAC,IAAEuD,EAAG,MAAEC,EAAK,KAAE6C,EAAI,UAAEC,GAAWtG,EAAA,OAAa,OAARuD,GAA0B,OAAVC,GAA2B,OAAT6C,GAA+B,OAAdC,CAAkB,IACjH5E,KAAIpB,IAAA,IAAC,IAAEiD,EAAG,MAAEC,EAAK,KAAE6C,EAAI,UAAEC,GAAgBhG,EAAA,MAAM,CAC9CiD,MACAC,QACA6C,KAAME,OAAOF,GACbC,UAAWC,OAAOD,GACnB,OAsFME,GAAWA,EAAOjD,IAAIkD,OAAOpH,OAAS,IAEzC8D,MAAMgD,EAAAA,EAAAA,QACJxE,EAAkF,QAA5DgE,EAAgD,QAAhDC,EAAiBd,EAA2BjE,YAAI,IAAA+E,GAAM,QAANC,EAA/BD,EAAiC/E,YAAI,IAAAgF,OAAN,EAA/BA,EAAuC1C,YAAI,IAAAwC,EAAAA,EAAI,KACrFvC,GAAQA,EAAIG,IAAIkD,OAAOpH,OAAS,IAEnCgG,QAAQc,EAAAA,EAAAA,QACNxE,EAAoF,QAA9DmE,EAAgD,QAAhDC,EAAiBjB,EAA2BjE,YAAI,IAAAkF,GAAM,QAANC,EAA/BD,EAAiClF,YAAI,IAAAmF,OAAN,EAA/BA,EAAuCX,cAAM,IAAAS,EAAAA,EAAI,KACvFY,GAAUA,EAAMnD,IAAIkD,OAAOpH,OAAS,IAEvCiG,SAAUxD,EAAiD,QAAhCmE,EAACnB,EAA2BjE,YAAI,IAAAoF,GAAQ,QAARC,EAA/BD,EAAiCU,cAAM,IAAAT,OAAR,EAA/BA,EAAyCU,eACtE,GACA,CAAC9B,EAA2BjE,OAEzBgG,EAAkC9G,EAAiD,CACvFG,UACAD,YAAa6E,IAGf,MAAO,CACLgC,QAA8C,QAAvCtC,EAAiC,QAAjCC,EAAEK,EAA2BjE,YAAI,IAAA4D,OAAA,EAA/BA,EAAiCsC,YAAI,IAAAvC,EAAAA,OAAI7F,EAClDqI,WAAuD,QAA7CtC,EAAiC,QAAjCC,EAAEG,EAA2BjE,YAAI,IAAA8D,OAAA,EAA/BA,EAAiCqC,kBAAU,IAAAtC,EAAAA,OAAI/F,EAC3DsI,QAASnC,EAA2BmC,QACpCC,MAAOpC,EAA2BqC,YAClCC,SAAUtC,EAA2BsC,SACrCC,WAAYvC,EAA2BuC,WACvCC,UAA0C,QAAjC1C,EAAEE,EAA2BjE,YAAI,IAAA+D,OAAA,EAA/BA,EAAiC+B,OAC5CY,WAA2C,QAAjC1C,EAAEC,EAA2BjE,YAAI,IAAAgE,OAAA,EAA/BA,EAAiC2C,QAC7CX,kCACAvB,WACAF,gBACAjC,OACAkC,SAEJ,CAGA,MAAMoC,ECnKmCC,EAACxH,EAAiBiE,KAC3D,MAAOwD,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,KAC1CC,EAAqBC,IAA0BF,EAAAA,EAAAA,UAAS,IACzDvD,GAAWC,EAAAA,EAAAA,OAIX,QAAEuC,EAAO,KAAE3D,EAAI,cAAEiC,EAAa,WAAE4B,EAAU,OAAE3B,EAAM,SAAEC,IAAajF,EAAAA,EAAAA,KAAa2H,IAAiB,CACnGlB,QAASkB,EAAMzH,SAAS0H,eAAe/H,GAEvCiD,MAAMgD,EAAAA,EAAAA,QAAO6B,EAAMzH,SAAS2H,cAAchI,IAAWkD,GAAQA,EAAIG,IAAIkD,OAAOpH,OAAS,IACrF+F,eAAee,EAAAA,EAAAA,QAAO6B,EAAMzH,SAAS4H,uBAAuBjI,IAAWsG,GAAWA,EAAOjD,IAAIkD,OAAOpH,OAAS,IAC7GgG,QAAQc,EAAAA,EAAAA,QAAO6B,EAAMzH,SAAS6H,gBAAgBlI,IAAWwG,GAAUA,EAAMnD,IAAIkD,OAAOpH,OAAS,IAC7F2H,WAAYgB,EAAMzH,SAAS8H,gBAAgBlE,GAC3CmB,SAAU0C,EAAMzH,SAAS+H,kBAAkBpI,OAGvCqI,GAAWC,EAAAA,EAAAA,cAAY,KAC3B,MAAMC,GAASC,EAAAA,EAAAA,IAAUxI,GAEzB,OADA0H,EAAgBa,EAAOE,KAAKC,IACrBtE,EAASmE,EAAO,GACtB,CAACnE,EAAUpE,IAER2I,GAAkBL,EAAAA,EAAAA,cAAY,KAClC,MAAMC,GAASK,EAAAA,EAAAA,IAAiB3E,GAEhC,OADA4D,EAAuBU,EAAOE,KAAKC,IAC5BtE,EAASmE,EAAO,GACtB,CAACnE,EAAUH,IAER4E,GAAqBP,EAAAA,EAAAA,cAAY,KACrClE,GAASY,EAAAA,EAAAA,IAAuB,CAAEC,OAAQjF,IAAW,GACpD,CAACoE,EAAUpE,KAGd+E,EAAAA,EAAAA,YAAU,KACH6B,GACHyB,IAAWS,OAAOC,GAAMC,EAAAA,EAAMC,sBAAsBF,KAEtDF,GAAoB,GACnB,CAACjC,EAASyB,EAAUQ,KAEvB9D,EAAAA,EAAAA,YAAU,KACH+B,GACH6B,IAAkBG,OAAOC,GAAMC,EAAAA,EAAMC,sBAAsBF,IAC7D,GACC,CAACjC,EAAY6B,IAGhB,MAAQ5B,QAASmC,EAAYlC,MAAOmC,IAAkBhJ,EAAAA,EAAAA,KAAa2H,IAAiB,IAAAsB,EAAAC,EAAAC,EAAAC,EAAA,MAAM,CACxFxC,SAAUU,GAAgB+B,QAAkB,QAAXJ,EAACtB,EAAM2B,YAAI,IAAAL,GAAgB,QAAhBC,EAAVD,EAAa3B,UAAa,IAAA4B,OAAhB,EAAVA,EAA4BK,QAC9D1C,MAAiB,QAAZsC,EAAExB,EAAM2B,YAAI,IAAAH,GAAgB,QAAhBC,EAAVD,EAAa7B,UAAa,IAAA8B,OAAhB,EAAVA,EAA4BvC,MACpC,KAEOD,QAAS4C,EAAmB3C,MAAO4C,IAAyBzJ,EAAAA,EAAAA,KAAa2H,IAAiB,IAAA+B,EAAAC,EAAAC,EAAAC,EAAA,MAAM,CACtGjD,SAAUU,GAAgB+B,QAAkB,QAAXK,EAAC/B,EAAM2B,YAAI,IAAAI,GAAuB,QAAvBC,EAAVD,EAAajC,UAAoB,IAAAkC,OAAvB,EAAVA,EAAmCJ,QACrE1C,MAAiB,QAAZ+C,EAAEjC,EAAM2B,YAAI,IAAAM,GAAuB,QAAvBC,EAAVD,EAAanC,UAAoB,IAAAoC,OAAvB,EAAVA,EAAmChD,MAC3C,IAID,MAAO,CACLD,QAHcmC,GAAcS,EAI5BhJ,KAAM,CACJiG,UACA3D,OACAkC,SACAD,gBACA4B,aACA1B,YAEF+B,WAAYkB,EACZ4B,OAAQ,CAAEd,gBAAeS,wBAC1B,ED2F2BpC,CAA4BxH,EAASiE,GAC3D+C,EAAQO,EAAoB0C,OAAOd,eAAiB5B,EAAoB0C,OAAOL,qBAE/EjD,EAAkC9G,EAAiD,CACvFG,YAGF,MAAO,CACL4G,QAAiC,QAA1BjD,EAAE4D,EAAoB5G,YAAI,IAAAgD,OAAA,EAAxBA,EAA0BiD,QACnC1B,cAAuC,QAA1BtB,EAAE2D,EAAoB5G,YAAI,IAAAiD,OAAA,EAAxBA,EAA0BsB,cACzCjC,KAA8B,QAA1BY,EAAE0D,EAAoB5G,YAAI,IAAAkD,OAAA,EAAxBA,EAA0BZ,KAChC6D,WAAoC,QAA1BhD,EAAEyD,EAAoB5G,YAAI,IAAAmD,OAAA,EAAxBA,EAA0BgD,WACtC3B,OAAgC,QAA1BpB,EAAEwD,EAAoB5G,YAAI,IAAAoD,OAAA,EAAxBA,EAA0BoB,OAClCC,SAAkC,QAA1BpB,EAAEuD,EAAoB5G,YAAI,IAAAqD,OAAA,EAAxBA,EAA0BoB,SACpC2B,QAASQ,EAAoBR,QAC7BC,QACAmC,cAAe5B,EAAoB0C,OAAOd,cAC1CS,qBAAsBrC,EAAoB0C,OAAOL,qBACjDzC,WAAYI,EAAoBJ,WAChCR,kCACD,C,wIE9LI,MAAMuD,EAAqCpK,IAQ3C,IAR4C,YACjDqK,EAAW,cACXC,EAAa,MACbC,GAKDvK,EACC,MAAM,MAAEwK,IAAUC,EAAAA,EAAAA,MACXC,EAAuBC,IAA4B9C,EAAAA,EAAAA,WAAS,IAC7D,iBAAE+C,IAAqBC,EAAAA,EAAAA,MACvB,YAAEC,IAAgBC,EAAAA,EAAAA,MAaxB,OACEC,EAAAA,EAAAA,IAACC,EAAAA,EAAM,CACLC,KAAK,OACLC,KAAMT,GAAwBU,EAAAA,EAAAA,GAACC,EAAAA,EAAO,CAACC,KAAK,QAAQC,KAAGC,EAAAA,EAAAA,IAAE,CAAEC,YAAajB,EAAMkB,QAAQC,IAAI,OAAOP,EAAAA,EAAAA,GAACQ,EAAAA,IAAS,IAE3GC,YAAY,8BACZC,QAASA,IAjBcC,EAAC1B,EAAqBC,EAAuBC,KAClEA,IACFI,GAAyB,GACT,OAAhBC,QAAgB,IAAhBA,GAAAA,EAAmB,CAAEP,cAAaC,gBAAeC,UAC9CvB,OAAO9B,IACN4D,EAAY5D,EAAM,IAEnB8E,SAAQ,IAAMrB,GAAyB,KAC5C,EASiBoB,CAAmB1B,EAAaC,EAAeC,GAAO0B,SAAA,CAEpE5B,EAAY,MAAIC,EAAc,MAJ1B,CAACD,EAAaC,GAAe4B,KAAK,KAKhC,C,qLC1Bb,MAAMC,UAAgCC,EAAAA,GAAgBC,WAAAA,GAAA,SAAAjN,WAAA,KACpDkN,aAAeC,EAAAA,GAAaC,2BAA2B,KACvDC,UAAYC,EAAAA,GAAUP,wBAAwB,KAC9CQ,aAAc,EAAK,KACnBC,gBACExB,EAAAA,EAAAA,GAACyB,EAAAA,EAAgB,CAAAjE,GAAA,SACfkE,eAAe,sDAEf,EAQN,MAAMC,GACJC,EAAAA,EAAAA,eAAkE,CAChEpC,iBAAkBA,IAAMqC,QAAQC,YAOvBC,EAAyDnN,IAAkD,IAAjD,SAAEiM,GAAyCjM,EAChH,MAAOoN,EAAcC,IAAmBxF,EAAAA,EAAAA,WAAkB,IACnDyF,EAAwBC,IAA6B1F,EAAAA,EAAAA,aAErD2F,IAAcC,EAAAA,EAAAA,KAGfC,GAAyBC,EAAAA,EAAAA,QAAwC,MAEjE/C,GAAmBpC,EAAAA,EAAAA,cACvBvJ,SACE,IAAIgO,SAAc,CAACC,EAASU,KAAY,IAADC,EAIrC,OAF8B,QAA9BA,EAAAH,EAAuBI,eAAO,IAAAD,GAA9BA,EAAAE,KAAAL,GAEOF,EAAW,CAChBQ,QAASJ,EACTK,WAAAA,CAAYpN,GAAO,IAADqN,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAEhB,GAAqB,QAArBV,EAAIrN,EAAKgO,oBAAY,IAAAX,GAAjBA,EAAmB9G,SAAU,CAE/B,MAAMF,EACJrG,EAAKgO,aAAazH,SAAS0H,OAASC,EAAAA,GAAWC,wBAC3C,IAAI7C,EACJtL,EAAKgO,aAAazH,SAExB,YADAwG,EAAO1G,EAET,CAEA,MAAM5B,GAAW2J,EAAAA,EAAAA,GAAkD,QAAlBd,EAACtN,EAAKgO,oBAAY,IAAAV,GAAK,QAALC,EAAjBD,EAAmBe,WAAG,IAAAd,GAAQ,QAARC,EAAtBD,EAAwBzH,cAAM,IAAA0H,OAAb,EAAjBA,EAAgCzH,eAGlF,IAAKtB,GAA8B,QAAlBgJ,EAACzN,EAAKgO,oBAAY,IAAAP,GAAK,QAALC,EAAjBD,EAAmBY,WAAG,IAAAX,IAAtBA,EAAwBxH,KACxC,OAIF,MAAMoI,EAA0B,OAAR7J,QAAQ,IAARA,OAAQ,EAARA,EAAU8J,MAC/BrN,IAAY,IAAAC,EAAA,OACS,QAApBA,EAAAD,EAAae,eAAO,IAAAd,OAAA,EAApBA,EAAsBe,UAAWsC,EAAOiF,eACxCvI,EAAae,QAAQ3B,OAASkE,EAAOgF,WAAW,IAIpD,IAAK8E,EACH,OAEF,MAAM,KAAEpI,EAAMlG,KAAMwO,GAAYxO,EAAKgO,aAAaK,IAG5CI,GAAiBzN,EAAAA,EAAAA,OAA0D,QAArD2M,EAAQ,OAAPa,QAAO,IAAPA,GAAa,QAANZ,EAAPY,EAASlM,YAAI,IAAAsL,OAAN,EAAPA,EAAehL,QAAQL,GAAQA,EAAIG,KAAOH,EAAII,eAAM,IAAAgL,EAAAA,EAAI,GAAI,OAMzFnB,GAAgB,GAChBE,EAA0B,CACxBgC,gBAAiB,CACfzM,QAASqM,EAAgBrM,QACzBK,KAAMgM,EAAgBhM,MAExBkM,QAAS,CACP/J,SAAUA,EACVpF,QAAqB,QAAdwO,EAAE3H,EAAK7G,eAAO,IAAAwO,EAAAA,EAAI,GACzBvK,aAA+B,QAAnBwK,EAAE5H,EAAK5C,oBAAY,IAAAwK,EAAAA,EAAI,GACnCa,QAAqB,QAAdZ,EAAE7H,EAAKyI,eAAO,IAAAZ,EAAAA,EAAI,GACzBzL,KAAMmM,KAKVpC,IACAQ,EAAuBI,QAAU,IACnC,EACA2B,UAAW,CAAE5O,KAAM,CAAE0J,MAAOlF,EAAOkF,SACnC,KAEN,CAACiD,IAGGkC,GAAenK,EAAAA,EAAAA,UAAQ,MAASqF,sBAAqB,CAACA,IAE5D,OACEI,EAAAA,EAAAA,IAAC+B,EAA+C4C,SAAQ,CAACnM,MAAOkM,EAAazD,SAAA,CAC1EA,EACAqB,IACClC,EAAAA,EAAAA,GAACwE,EAAAA,EAA2B,CAC1BC,OAAQzC,EACRE,uBAAwBA,EACxBwC,UAAWzC,EACXE,0BAA2BA,MAGyB,EAIjD1C,EAA6CA,KACxDkF,EAAAA,EAAAA,YAAWhD,E,qJCzHPiD,EAAgB,CACpB,UACA,YACA,YACA,cACA,eACA,mBClBF,MAAMC,EAAgBC,EAAAA,EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwGZlL,EAAiBhF,IAMC,IAADkO,EAAAC,EAAA,IANC,QAC7BjO,EAAO,SACPiQ,GAAW,GAIZnQ,EACC,MAAM,KACJa,EAAI,QACJoG,EACAC,MAAOC,EAAW,QAClBiJ,IACEC,EAAAA,EAAAA,GAAkCJ,EAAe,CACnDR,UAAW,CACT5O,KAAM,CACJ0J,MAAOrK,IAGXoQ,KAAMH,IAGR,MAAO,CACLlJ,UACApG,KAAU,OAAJA,QAAI,IAAJA,GAAkB,QAAdqN,EAAJrN,EAAMgO,oBAAY,IAAAX,OAAd,EAAJA,EAAoBgB,IAC1B7H,WAAY+I,EACZjJ,cACAC,SAAc,OAAJvG,QAAI,IAAJA,GAAkB,QAAdsN,EAAJtN,EAAMgO,oBAAY,IAAAV,OAAd,EAAJA,EAAoB/G,SAC/B,EAGUqG,EAAqBA,IDjH5B,SACJ8C,EACAC,GAEA,IAAMC,GAAgBC,EAAAA,EAAAA,IACpBC,EAAAA,EAAAA,GAAgBH,GAAWA,EAAQI,QACnCL,GAGIM,GAAiBlD,EAAAA,EAAAA,UACjBmD,EAASD,EAAe/C,SAC1BiD,EAAAA,EAAAA,GAAaP,EAASK,EAAe/C,SACrC0C,EAEEQ,EAAiBP,EAAcJ,UAAQY,EAAAA,EAAAA,WAAAA,EAAAA,EAAAA,UAAC,CAAC,EAC1CH,GAAM,CACTR,MAAOO,EAAe/C,WAGlBoD,EACJF,EAAeG,WAAWX,QAAQU,oBAClCT,EAAcW,wBAEV1Q,EACJ2Q,OAAOC,OAAON,EAAgB,CAC5BO,SAAUV,EAAe/C,UAIvB0D,GAAejM,EAAAA,EAAAA,UAAQ,WAE3B,IADA,IAAMiM,EAAoC,CAAC,E,WAChCjO,GACT,IAAMpE,EAASuB,EAAO6C,GACtBiO,EAAajO,GAAO,WAMlB,OALKsN,EAAe/C,UAClB+C,EAAe/C,QAAUuD,OAAOI,OAAO,MAEvChB,EAAciB,eAETvS,EAAOwS,MAAMC,KAAMxS,UAC5B,C,EATgByS,EAAA,EAAAC,EAAA9B,EAAA6B,EAAAC,EAAAzS,OAAAwS,I,EAAJC,EAAAD,IAYd,OAAOL,CACT,GAAG,IAyBH,OAvBAH,OAAOC,OAAO5Q,EAAQ8Q,GAuBf,EArBShJ,EAAAA,EAAAA,cAEd,SAAAuJ,GACAlB,EAAe/C,QAAUiE,GAAgBd,EAAAA,EAAAA,WAAAA,EAAAA,EAAAA,UAAA,GACpCc,GAAc,CACjBC,YAAaD,EAAeC,aAAed,IACzC,CACFc,YAAad,GAGf,IAAMe,EAAUxB,EACbyB,cACAC,MAAK,SAAAlS,GAAe,OAAAoR,OAAOC,OAAOrR,EAAauR,EAA3B,IAMvB,OAFAS,EAAQjJ,OAAM,WAAO,IAEdiJ,CACT,GAAG,IAEcvR,EACnB,CC2CwC0R,CAAsCnC,E,sHCpH9E,MAAMoC,GAAgCrF,EAAAA,EAAAA,eAAkD,CACtFsF,uBAAwB,KACxBxH,YAAaA,OACbyH,cAAeA,OACfC,qBAAsBA,SAOXC,EAAyBzS,IAAgF,IAA/E,SAAEiM,EAAQ,YAAEyG,GAA6D1S,EAC9G,MAAOsS,EAAwBK,IAA6B9K,EAAAA,EAAAA,UAA0B,MAEhFiD,GAActC,EAAAA,EAAAA,cAClB,CAACtB,EAAwB0L,KACvB,GAAgB,OAAXF,QAAW,IAAXA,IAAAA,EAAcxL,GAAQ,CACzB,MAAMvH,GAAkBC,EAAAA,EAAAA,IAAqBsH,GAE7CyL,EAA0BhT,GAEtBiT,GACFA,EAAgBjT,EAEpB,IAEF,CAACgT,EAA2BD,IAGxBH,GAAgB/J,EAAAA,EAAAA,cACnByJ,IACCA,EAAQjJ,OAAO9B,IACb4D,EAAY5D,EAAM,GAClB,GAEJ,CAAC4D,IAGG0H,GAAuBhK,EAAAA,EAAAA,cAAY,KACvCmK,EAA0B,KAAK,GAC9B,CAACA,IAEJ,OACEvH,EAAAA,EAAAA,GAACiH,EAA8B1C,SAAQ,CACrCnM,OAAO+B,EAAAA,EAAAA,UACL,MACE+M,yBACAxH,cACAyH,gBACAC,0BAEF,CAACA,EAAsBF,EAAwBxH,EAAayH,IAC5DtG,SAEDA,GACsC,EAiBhClB,EAA4BA,KACvC,MAAM,uBAAEuH,EAAsB,YAAExH,EAAW,cAAEyH,EAAa,qBAAEC,IAC1DzC,EAAAA,EAAAA,YAAWsC,GAEPQ,GAA2BrK,EAAAA,EAAAA,cAC/B,CAACsK,EAA+B5L,EAAwB0L,KACtD9H,EAAY5D,EAAO0L,EAAgB,GAErC,CAAC9H,IAGH,OAAOvF,EAAAA,EAAAA,UACL,MACE+M,yBACAxH,cACAiI,qBAAsBF,EACtBN,gBACAC,0BAEF,CAACA,EAAsB1H,EAAayH,EAAeD,EAAwBO,GAC5E,C,sGC3GH,MAGMG,EAAM,MAqFNC,EAA6C,CACjDC,aAAc,QACdC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WAGGC,EAAoBxT,IAIyD,IAJxD,KAChCyT,EAAI,KACJC,EAAI,qBACJC,EAAuBV,GACYjT,EACnC,MAAM4T,EAAM,IAAIC,KACVC,EAAUC,KAAKC,OAAOJ,EAAIK,UAAYR,EAAKQ,WAAa,KAExDC,EAASC,UAAUC,UAAY,QACrC,IAAIC,EAAe,GACnB,IACEA,EAAeC,KAAKC,eAAeL,EAAQP,GAAsBa,OAAOf,EAC1E,CAAE,MAAOxK,GACP,CAGF,IAAK,MAAMwL,IArGSf,IAAgC,CACpD,CACEI,QATS,QAUTY,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,oDAGjB,CAAE6H,WAGR,CACEb,QArBU,OAsBVY,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,sDAGjB,CAAE6H,WAGR,CACEb,QAASd,EACT0B,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,kDAGjB,CAAE6H,WAGR,CACEb,QA7CS,KA8CTY,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,oDAGjB,CAAE6H,WAGR,CACEb,QAzDW,GA0DXY,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,wDAGjB,CAAE6H,WAGR,CACEb,QArEW,EAsEXY,eAAiBC,GACfjB,EAAKkB,cACH,CAAAhM,GAAA,SACEkE,eAAe,wDAGjB,CAAE6H,YAqCeE,CAAanB,GAAO,CACzC,MAAMiB,EAAQZ,KAAKe,MAAMhB,EAAUW,EAASX,SAC5C,GAAIa,GAAS,EACX,MAAO,CAAEI,YAAaN,EAASC,eAAeC,GAAQN,eAE1D,CAEA,MAAO,CACLU,YAAarB,EAAKkB,cAAc,CAAAhM,GAAA,SAC9BkE,eAAe,aAGjBuH,eACD,EAGUW,EAAkC1U,IAA0D,IAAzD,KAAEmT,EAAI,qBAAEE,EAAuBV,GAAoB3S,EACjG,MAAMoT,GAAOuB,EAAAA,EAAAA,MACP,YAAEF,EAAW,aAAEV,GAAiBb,EAAkB,CAAEC,OAAMC,OAAMC,yBACtE,OACEvI,EAAAA,EAAAA,GAAC8J,EAAAA,EAAO,CAACrJ,YAAY,sBAAsBsJ,QAASd,EAAapI,UAC/Db,EAAAA,EAAAA,GAAA,QAAAa,SAAO8I,KACC,EC1IDK,EAAqCpV,IAAoD,IAAnD,MAAEwD,GAA2CxD,EAC9F,MAAMyT,EAAO,IAAII,KAAKtN,OAAO/C,IAE7B,OAAI6R,MAAM5B,GACD,MAGFrI,EAAAA,EAAAA,GAAC4J,EAAO,CAACvB,KAAMA,GAAQ,C,8HCGzB,MAAM6B,UAAwBC,EAAAA,EAOnClJ,WAAAA,CAAYuE,EAAqB4E,GAC/BC,QAEA7D,KAAKhB,OAASA,EACdgB,KAAK4D,QAAU,GACf5D,KAAKlR,OAAS,GACdkR,KAAK8D,UAAY,GACjB9D,KAAK+D,aAAe,CAAC,EAEjBH,GACF5D,KAAKgE,WAAWJ,EAEnB,CAESK,WAAAA,GACoB,IAAxBjE,KAAKkE,UAAUxK,MACjBsG,KAAK8D,UAAU3U,SAASgV,IACtBA,EAASC,WAAWtV,IAClBkR,KAAKqE,SAASF,EAAUrV,EAAxB,GADF,GAKL,CAESwV,aAAAA,GACHtE,KAAKkE,UAAUxK,MAClBsG,KAAKuE,SAER,CAEDA,OAAAA,GACEvE,KAAKkE,UAAY,IAAIM,IACrBxE,KAAK8D,UAAU3U,SAASgV,IACtBA,EAASI,SAAT,GAEH,CAEDP,UAAAA,CACEJ,EACAa,GAEAzE,KAAK4D,QAAUA,EAEfc,EAAAA,EAAcC,OAAM,KAClB,MAAMC,EAAgB5E,KAAK8D,UAErBe,EAAqB7E,KAAK8E,sBAAsB9E,KAAK4D,SAG3DiB,EAAmB1V,SAAS4V,GAC1BA,EAAMZ,SAASa,WAAWD,EAAME,sBAAuBR,KAGzD,MAAMS,EAAeL,EAAmB/U,KAAKiV,GAAUA,EAAMZ,WACvDgB,EAAkB1F,OAAO2F,YAC7BF,EAAapV,KAAKqU,GAAa,CAACA,EAASvF,QAAQyG,UAAWlB,MAExDmB,EAAYJ,EAAapV,KAAKqU,GAClCA,EAASoB,qBAGLC,EAAiBN,EAAaO,MAClC,CAACtB,EAAUuB,IAAUvB,IAAaS,EAAcc,MAE9Cd,EAAcnX,SAAWyX,EAAazX,QAAW+X,KAIrDxF,KAAK8D,UAAYoB,EACjBlF,KAAK+D,aAAeoB,EACpBnF,KAAKlR,OAASwW,EAETtF,KAAK2F,kBAIVC,EAAAA,EAAAA,IAAWhB,EAAeM,GAAc/V,SAASgV,IAC/CA,EAASI,SAAT,KAGFqB,EAAAA,EAAAA,IAAWV,EAAcN,GAAezV,SAASgV,IAC/CA,EAASC,WAAWtV,IAClBkR,KAAKqE,SAASF,EAAUrV,EAAxB,GADF,IAKFkR,KAAK6F,UAAL,GAEH,CAEDN,gBAAAA,GACE,OAAOvF,KAAKlR,MACb,CAEDgX,UAAAA,GACE,OAAO9F,KAAK8D,UAAUhU,KAAKqU,GAAaA,EAAS4B,mBAClD,CAEDC,YAAAA,GACE,OAAOhG,KAAK8D,SACb,CAEDmC,mBAAAA,CAAoBrC,GAClB,OAAO5D,KAAK8E,sBAAsBlB,GAAS9T,KAAKiV,GAC9CA,EAAMZ,SAAS8B,oBAAoBlB,EAAME,wBAE5C,CAEOH,qBAAAA,CACNlB,GAEA,MAAMgB,EAAgB5E,KAAK8D,UACrBoC,EAAmB,IAAIC,IAC3BvB,EAAc9U,KAAKqU,GAAa,CAACA,EAASvF,QAAQyG,UAAWlB,MAGzDc,EAAwBrB,EAAQ9T,KAAK8O,GACzCoB,KAAKhB,OAAOoH,oBAAoBxH,KAG5ByH,EACJpB,EAAsBqB,SAASC,IAC7B,MAAMxB,EAAQmB,EAAiBM,IAAID,EAAiBlB,WACpD,OAAa,MAATN,EACK,CAAC,CAAEE,sBAAuBsB,EAAkBpC,SAAUY,IAExD,EAAP,IAGE0B,EAAqB,IAAIjC,IAC7B6B,EAAkBvW,KAAKiV,GAAUA,EAAME,sBAAsBI,aAEzDqB,EAAmBzB,EAAsBpT,QAC5C0U,IAAsBE,EAAmBE,IAAIJ,EAAiBlB,aAG3DuB,EAAuB,IAAIpC,IAC/B6B,EAAkBvW,KAAKiV,GAAUA,EAAMZ,YAEnC0C,EAAqBjC,EAAc/S,QACtCiV,IAAkBF,EAAqBD,IAAIG,KAGxCC,EAAenI,IACnB,MAAM2H,EAAmBvG,KAAKhB,OAAOoH,oBAAoBxH,GACnDoI,EAAkBhH,KAAK+D,aAAawC,EAAiBlB,WAC3D,OAAO,MAAA2B,EAAAA,EAAmB,IAAIC,EAAAA,EAAcjH,KAAKhB,OAAQuH,EAAzD,EAGIW,EAA6CR,EAAiB5W,KAClE,CAAC8O,EAAS8G,KACR,GAAI9G,EAAQuI,iBAAkB,CAE5B,MAAMC,EAAyBP,EAAmBnB,GAClD,QAA+B3Y,IAA3Bqa,EACF,MAAO,CACLnC,sBAAuBrG,EACvBuF,SAAUiD,EAGf,CACD,MAAO,CACLnC,sBAAuBrG,EACvBuF,SAAU4C,EAAYnI,GAFxB,IAcJ,OAAOyH,EACJgB,OAAOH,GACPI,MATiCC,CAClCC,EACAC,IAEAxC,EAAsByC,QAAQF,EAAEvC,uBAChCA,EAAsByC,QAAQD,EAAExC,wBAKnC,CAEOZ,QAAAA,CAASF,EAAyBrV,GACxC,MAAM4W,EAAQ1F,KAAK8D,UAAU4D,QAAQvD,IACtB,IAAXuB,IACF1F,KAAKlR,QAAS6Y,EAAAA,EAAAA,IAAU3H,KAAKlR,OAAQ4W,EAAO5W,GAC5CkR,KAAK6F,SAER,CAEOA,MAAAA,GACNnB,EAAAA,EAAcC,OAAM,KAClB3E,KAAKkE,UAAU/U,SAAQf,IAAkB,IAAjB,SAAEwZ,GAAHxZ,EACrBwZ,EAAS5H,KAAKlR,OAAd,GADF,GAIH,E,2DCjEI,SAAS+Y,EAATzZ,GAMe,IANsB,QAC1CwV,EAD0C,QAE1CkE,GAFK1Z,EAOL,MAAM2Z,GAAcC,EAAAA,EAAAA,IAAe,CAAEF,YAC/BG,GAAcC,EAAAA,EAAAA,KACdC,GAAqBC,EAAAA,EAAAA,KAErBC,EAAmBC,EAAAA,SACvB,IACE1E,EAAQ9T,KAAK8O,IACX,MAAM2H,EAAmBwB,EAAY3B,oBAAoBxH,GAOzD,OAJA2H,EAAiBgC,mBAAqBN,EAClC,cACA,aAEG1B,CAAP,KAEJ,CAAC3C,EAASmE,EAAaE,IAGzBI,EAAiBlZ,SAASwP,KACxB6J,EAAAA,EAAAA,IAAgB7J,IAChB8J,EAAAA,EAAAA,IAAgC9J,EAAOwJ,EAAvC,KAGFO,EAAAA,EAAAA,IAA2BP,GAE3B,MAAOhE,GAAYmE,EAAAA,UACjB,IAAM,IAAI5E,EAAgBqE,EAAaM,KAGnCM,EAAmBxE,EAAS8B,oBAAoBoC,IAEtDO,EAAAA,EAAAA,GACEN,EAAAA,aACGO,GACCZ,EACI,KADO,EAEP9D,EAASC,UAAUM,EAAAA,EAAcoE,WAAWD,KAClD,CAAC1E,EAAU8D,KAEb,IAAM9D,EAASoB,qBACf,IAAMpB,EAASoB,qBAGjB+C,EAAAA,WAAgB,KAGdnE,EAASH,WAAWqE,EAAkB,CAAEnE,WAAW,GAAnD,GACC,CAACmE,EAAkBlE,IAEtB,MAIM4E,EAJ0BJ,EAAiBlD,MAAK,CAAC3W,EAAQ4W,KAC7DsD,EAAAA,EAAAA,IAAcX,EAAiB3C,GAAQ5W,EAAQmZ,KAI7CU,EAAiBrC,SAAQ,CAACxX,EAAQ4W,KAChC,MAAM9G,EAAUyJ,EAAiB3C,GAC3BuD,EAAgB9E,EAAS6B,eAAeN,GAE9C,GAAI9G,GAAWqK,EAAe,CAC5B,IAAID,EAAAA,EAAAA,IAAcpK,EAAS9P,EAAQmZ,GACjC,OAAOiB,EAAAA,EAAAA,IAAgBtK,EAASqK,EAAed,IACtCgB,EAAAA,EAAAA,IAAUra,EAAQmZ,KACtBiB,EAAAA,EAAAA,IAAgBtK,EAASqK,EAAed,EAEhD,CACD,MAAO,EAAP,IAEF,GAEJ,GAAIY,EAAiBtb,OAAS,EAC5B,MAAM4N,QAAQ+N,IAAIL,GAEpB,MAAMM,EAAkBlF,EAAS2B,aAC3BwD,EAAoCX,EAAiBnL,MACzD,CAAC1O,EAAQ4W,KAAT,IAAA6D,EAAAC,EAAA,OACEC,EAAAA,EAAAA,IAAY,CACV3a,SACAqZ,qBACAuB,iBAAgB,OAAAH,EAAE,OAAFC,EAAEnB,EAAiB3C,SAAjB,EAAA8D,EAAyBE,mBAA3BH,EAChB5K,MAAO0K,EAAgB3D,IAL3B,IASF,SAAI4D,GAAAA,EAAmChU,MACrC,MAAMgU,EAAkChU,MAG1C,OAAOqT,CACR,C,wIC3OD,MAAMgB,EAAwBvb,IAAyD,IAAxD,OAAEwB,GAAgDxB,EAC/E,OAAIwB,IAAWga,EAAAA,GAA2BC,oBACjCrQ,EAAAA,EAAAA,GAACsQ,EAAAA,IAAe,CAACC,MAAM,YAG5Bna,IAAWga,EAAAA,GAA2BI,4BACjCxQ,EAAAA,EAAAA,GAACyQ,EAAAA,IAAW,CAACF,MAAM,WAGxBna,IAAWga,EAAAA,GAA2BM,sBACjC1Q,EAAAA,EAAAA,GAAC2Q,EAAAA,EAAS,CAACJ,MAAM,YAGnB,IAAI,EAGAK,EAAuC1b,IAA2C,IAAD2b,EAAAC,EAAA,IAAzC,KAAErb,GAAkCP,EACvF,MAAM,MAAEkK,IAAUC,EAAAA,EAAAA,KACZjJ,EAA0B,QAApBya,EAAY,QAAZC,EAAGrb,EAAKkG,YAAI,IAAAmV,OAAA,EAATA,EAAW1a,cAAM,IAAAya,EAAAA,EAAIT,EAAAA,GAA2BW,gCAiD/D,OACEnR,EAAAA,EAAAA,IAACoR,EAAAA,IAAG,CAACvQ,YAAY,6BAA6BN,KAAGC,EAAAA,EAAAA,IAAE,CAAE6Q,gBA/CjD7a,IAAWga,EAAAA,GAA2BC,mBACjCjR,EAAM8R,WAAa9R,EAAM+R,OAAOC,SAAWhS,EAAM+R,OAAOE,SAE7Djb,IAAWga,EAAAA,GAA2BI,2BACjCpR,EAAM8R,WAAa9R,EAAM+R,OAAOG,OAASlS,EAAM+R,OAAOI,OAE3Dnb,IAAWga,EAAAA,GAA2BM,qBACjCtR,EAAM8R,WAAa9R,EAAM+R,OAAOK,UAAYpS,EAAM+R,OAAOM,eADlE,GAyCqF,IAAC5Q,SAAA,CACnFzK,IAAU4J,EAAAA,EAAAA,GAACmQ,EAAqB,CAAC/Z,OAAQA,IAAY,KACtD4J,EAAAA,EAAAA,GAAC0R,EAAAA,EAAWC,KAAI,CAACxR,KAAGC,EAAAA,EAAAA,IAAE,CAAEwR,WAAYxS,EAAMkB,QAAQC,IAAI,IAACM,SAnCrDzK,IAAWga,EAAAA,GAA2BC,oBAEtCrQ,EAAAA,EAAAA,GAAC0R,EAAAA,EAAWC,KAAI,CAACpB,MAAM,UAAS1P,UAC9Bb,EAAAA,EAAAA,GAACyB,EAAAA,EAAgB,CAAAjE,GAAA,SAACkE,eAAe,YAKnCtL,IAAWga,EAAAA,GAA2BI,4BAEtCxQ,EAAAA,EAAAA,GAAC0R,EAAAA,EAAWC,KAAI,CAACpB,MAAM,QAAO1P,UAC5Bb,EAAAA,EAAAA,GAACyB,EAAAA,EAAgB,CAAAjE,GAAA,SACfkE,eAAe,oBAMnBtL,IAAWga,EAAAA,GAA2BM,sBAEtC1Q,EAAAA,EAAAA,GAAC0R,EAAAA,EAAWC,KAAI,CAACpB,MAAM,UAAS1P,UAC9Bb,EAAAA,EAAAA,GAACyB,EAAAA,EAAgB,CAAAjE,GAAA,SACfkE,eAAe,cAOhBtL,MAOD,C", "sources": ["experiment-tracking/hooks/logged-models/request.utils.ts", "experiment-tracking/components/run-page/hooks/useUnifiedRegisteredModelVersionsSummariesForRun.tsx", "experiment-tracking/components/run-page/hooks/useRunDetailsPageData.tsx", "experiment-tracking/components/run-page/useRunDetailsPageDataLegacy.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelDatasetButton.tsx", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelOpenDatasetDetails.tsx", "../node_modules/@apollo/src/react/hooks/useLazyQuery.ts", "experiment-tracking/components/run-page/hooks/useGetRunQuery.tsx", "shared/web-shared/metrics/UserActionErrorHandler.tsx", "shared/web-shared/browse/TimeAgo.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelTableDateCell.tsx", "../node_modules/@tanstack/query-core/src/queriesObserver.ts", "../node_modules/@tanstack/react-query/src/useQueries.ts", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelStatusIndicator.tsx"], "sourcesContent": ["import { matchPredefinedError } from '@databricks/web-shared/errors';\n\nfunction serializeRequestBody(payload: any | FormData | Blob) {\n  if (payload === undefined) {\n    return undefined;\n  }\n  return typeof payload === 'string' || payload instanceof FormData || payload instanceof Blob\n    ? payload\n    : JSON.stringify(payload);\n}\n\n// Helper method to make a request to the backend.\nexport const loggedModelsDataRequest = async (\n  url: string,\n  method: 'POST' | 'GET' | 'PATCH' | 'DELETE' = 'GET',\n  body?: any,\n) => {\n  const response = await fetch(url, {\n    method,\n    body: serializeRequestBody(body),\n    headers: body ? { 'Content-Type': 'application/json' } : {},\n  });\n  if (!response.ok) {\n    const predefinedError = matchPredefinedError(response);\n    if (predefinedError) {\n      try {\n        // Attempt to use message from the response\n        const message = (await response.json()).message;\n        predefinedError.message = message ?? predefinedError.message;\n      } catch {\n        // If the message can't be parsed, use default one\n      }\n      throw predefinedError;\n    }\n  }\n  return response.json();\n};\n", "import { useSelector } from 'react-redux';\nimport type { ReduxState } from '../../../../redux-types';\nimport { ModelRegistryRoutes } from '../../../../model-registry/routes';\nimport { shouldEnableGraphQLModelVersionsForRunDetails } from '../../../../common/utils/FeatureUtils';\nimport { UseGetRunQueryResponse } from './useGetRunQuery';\n\n/**\n * A unified model version summary that can be used to display model versions on the run page.\n */\nexport type RunPageModelVersionSummary = {\n  displayedName: string | null;\n  version: string | null;\n  link: string;\n  status: string | null;\n  source: string | null;\n};\n\n/**\n * We're currently using multiple ways to get model versions on the run page,\n * we also differentiate between UC and workspace registry models.\n *\n * This hook is intended to unify the way we get model versions on the run page to be displayed in overview and register model dropdown.\n */\nexport const useUnifiedRegisteredModelVersionsSummariesForRun = ({\n  queryResult,\n  runUuid,\n}: {\n  runUuid: string;\n  queryResult?: UseGetRunQueryResponse;\n}): RunPageModelVersionSummary[] => {\n  const { registeredModels: registeredModelsFromStore } = useSelector(({ entities }: ReduxState) => ({\n    registeredModels: entities.modelVersionsByRunUuid[runUuid],\n  }));\n\n  if (shouldEnableGraphQLModelVersionsForRunDetails()) {\n    const result: RunPageModelVersionSummary[] = [];\n    if (queryResult?.data && 'modelVersions' in queryResult.data) {\n      queryResult.data?.modelVersions?.forEach((modelVersion) => {\n        result.push({\n          displayedName: modelVersion.name,\n          version: modelVersion.version,\n          link:\n            modelVersion.name && modelVersion.version\n              ? ModelRegistryRoutes.getModelVersionPageRoute(modelVersion.name, modelVersion.version)\n              : '',\n          status: modelVersion.status,\n          source: modelVersion.source,\n        });\n      });\n    }\n    return result;\n  }\n\n  if (registeredModelsFromStore) {\n    return registeredModelsFromStore.map((modelVersion) => {\n      const name = modelVersion.name;\n      const link = ModelRegistryRoutes.getModelVersionPageRoute(name, modelVersion.version);\n      return {\n        displayedName: modelVersion.name,\n        version: modelVersion.version,\n        link,\n        status: modelVersion.status,\n        source: modelVersion.source,\n      };\n    });\n  }\n\n  return [];\n};\n", "import { isEmpty, keyBy } from 'lodash';\nimport { useEffect, useMemo } from 'react';\nimport { useRunDetailsPageDataLegacy } from '../useRunDetailsPageDataLegacy';\nimport {\n  type UseGetRunQueryResponseExperiment,\n  useGetRunQuery,\n  UseGetRunQueryDataApiError,\n  UseGetRunQueryResponseDataMetrics,\n  UseGetRunQueryResponseDatasetInputs,\n  type UseGetRunQueryResponseInputs,\n  type UseGetRunQueryResponseOutputs,\n  UseGetRunQueryResponseRunInfo,\n} from './useGetRunQuery';\nimport {\n  KeyValueEntity,\n  RunDatasetWithTags,\n  type ExperimentEntity,\n  type MetricEntitiesByName,\n  type MetricEntity,\n  type RunInfoEntity,\n} from '../../../types';\nimport {\n  shouldEnableGraphQLModelVersionsForRunDetails,\n  shouldEnableGraphQLRunDetailsPage,\n} from '../../../../common/utils/FeatureUtils';\nimport { ThunkDispatch } from '../../../../redux-types';\nimport { useDispatch } from 'react-redux';\nimport { searchModelVersionsApi } from '../../../../model-registry/actions';\nimport { ApolloError } from '@mlflow/mlflow/src/common/utils/graphQLHooks';\nimport { ErrorWrapper } from '../../../../common/utils/ErrorWrapper';\nimport { pickBy } from 'lodash';\nimport {\n  type RunPageModelVersionSummary,\n  useUnifiedRegisteredModelVersionsSummariesForRun,\n} from './useUnifiedRegisteredModelVersionsSummariesForRun';\n\n// Internal util: transforms an array of objects into a keyed object by the `key` field\nconst transformToKeyedObject = <Output, Input = any>(inputArray: Input[]) =>\n  // TODO: fix this type error\n  // @ts-expect-error: Conversion of type 'Dictionary<Input>' to type 'Record<string, Output>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n  keyBy(inputArray, 'key') as Record<string, Output>;\n\n// Internal util: transforms an array of metric values into an array of MetricEntity objects\n// GraphQL uses strings for steps and timestamp so we cast them to numbers\nconst transformMetricValues = (inputArray: UseGetRunQueryResponseDataMetrics): MetricEntity[] =>\n  inputArray\n    .filter(({ key, value, step, timestamp }) => key !== null && value !== null && step !== null && timestamp !== null)\n    .map(({ key, value, step, timestamp }: any) => ({\n      key,\n      value,\n      step: Number(step),\n      timestamp: Number(timestamp),\n    }));\n\n// Internal util: transforms an array of dataset inputs into an array of RunDatasetWithTags objects\nexport const transformDatasets = (inputArray?: UseGetRunQueryResponseDatasetInputs): RunDatasetWithTags[] | undefined =>\n  inputArray?.map((datasetInput) => ({\n    dataset: {\n      digest: datasetInput.dataset?.digest ?? '',\n      name: datasetInput.dataset?.name ?? '',\n      profile: datasetInput.dataset?.profile ?? '',\n      schema: datasetInput.dataset?.schema ?? '',\n      source: datasetInput.dataset?.source ?? '',\n      sourceType: datasetInput.dataset?.sourceType ?? '',\n    },\n    tags:\n      datasetInput.tags\n        ?.map((tag) => ({\n          key: tag.key ?? '',\n          value: tag.value ?? '',\n        }))\n        .filter((tag) => !isEmpty(tag.key)) ?? [],\n  }));\n\ninterface UseRunDetailsPageDataResult {\n  experiment?: ExperimentEntity | UseGetRunQueryResponseExperiment;\n  error: Error | ErrorWrapper | undefined | ApolloError;\n\n  latestMetrics: MetricEntitiesByName;\n  loading: boolean;\n  params: Record<string, KeyValueEntity>;\n  refetchRun: any;\n  runInfo?: RunInfoEntity | UseGetRunQueryResponseRunInfo;\n  tags: Record<string, KeyValueEntity>;\n  datasets?: RunDatasetWithTags[];\n  runInputs?: UseGetRunQueryResponseInputs;\n  runOutputs?: UseGetRunQueryResponseOutputs;\n\n  // Only present in legacy implementation\n  runFetchError?: Error | ErrorWrapper | undefined;\n  experimentFetchError?: Error | ErrorWrapper | undefined;\n\n  registeredModelVersionSummaries: RunPageModelVersionSummary[];\n\n  // Only present in graphQL implementation\n  apiError?: UseGetRunQueryDataApiError;\n}\n\n/**\n * An updated version of the `useRunDetailsPageData` hook that either uses the REST API-based implementation\n * or the GraphQL-based implementation to fetch run details, based on the `shouldEnableGraphQLRunDetailsPage` flag.\n */\nexport const useRunDetailsPageData = ({\n  runUuid,\n  experimentId,\n}: {\n  runUuid: string;\n  experimentId: string;\n}): UseRunDetailsPageDataResult => {\n  const usingGraphQL = shouldEnableGraphQLRunDetailsPage();\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  // If GraphQL flag is enabled, use the graphQL query to fetch the run data.\n  // We can safely disable the eslint rule since feature flag evaluation is stable\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (usingGraphQL) {\n    const graphQLQuery = () =>\n      useGetRunQuery({\n        runUuid,\n      });\n\n    const detailsPageGraphqlResponse = graphQLQuery();\n\n    // If model versions are colocated in the GraphQL response, we don't need to make an additional API call\n    useEffect(() => {\n      if (shouldEnableGraphQLModelVersionsForRunDetails()) {\n        return;\n      }\n      dispatch(searchModelVersionsApi({ run_id: runUuid }));\n    }, [dispatch, runUuid]);\n\n    const { latestMetrics, tags, params, datasets } = useMemo(() => {\n      // Filter out tags, metrics, and params that are entirely whitespace\n      return {\n        latestMetrics: pickBy(\n          transformToKeyedObject<MetricEntity>(\n            transformMetricValues(detailsPageGraphqlResponse.data?.data?.metrics ?? []),\n          ),\n          (metric) => metric.key.trim().length > 0,\n        ),\n        tags: pickBy(\n          transformToKeyedObject<KeyValueEntity>(detailsPageGraphqlResponse.data?.data?.tags ?? []),\n          (tag) => tag.key.trim().length > 0,\n        ),\n        params: pickBy(\n          transformToKeyedObject<KeyValueEntity>(detailsPageGraphqlResponse.data?.data?.params ?? []),\n          (param) => param.key.trim().length > 0,\n        ),\n        datasets: transformDatasets(detailsPageGraphqlResponse.data?.inputs?.datasetInputs),\n      };\n    }, [detailsPageGraphqlResponse.data]);\n\n    const registeredModelVersionSummaries = useUnifiedRegisteredModelVersionsSummariesForRun({\n      runUuid,\n      queryResult: detailsPageGraphqlResponse,\n    });\n\n    return {\n      runInfo: detailsPageGraphqlResponse.data?.info ?? undefined,\n      experiment: detailsPageGraphqlResponse.data?.experiment ?? undefined,\n      loading: detailsPageGraphqlResponse.loading,\n      error: detailsPageGraphqlResponse.apolloError,\n      apiError: detailsPageGraphqlResponse.apiError,\n      refetchRun: detailsPageGraphqlResponse.refetchRun,\n      runInputs: detailsPageGraphqlResponse.data?.inputs,\n      runOutputs: detailsPageGraphqlResponse.data?.outputs,\n      registeredModelVersionSummaries,\n      datasets,\n      latestMetrics,\n      tags,\n      params,\n    };\n  }\n\n  // If GraphQL flag is disabled, use the legacy implementation to fetch the run data.\n  const detailsPageResponse = useRunDetailsPageDataLegacy(runUuid, experimentId);\n  const error = detailsPageResponse.errors.runFetchError || detailsPageResponse.errors.experimentFetchError;\n\n  const registeredModelVersionSummaries = useUnifiedRegisteredModelVersionsSummariesForRun({\n    runUuid,\n  });\n\n  return {\n    runInfo: detailsPageResponse.data?.runInfo,\n    latestMetrics: detailsPageResponse.data?.latestMetrics,\n    tags: detailsPageResponse.data?.tags,\n    experiment: detailsPageResponse.data?.experiment,\n    params: detailsPageResponse.data?.params,\n    datasets: detailsPageResponse.data?.datasets,\n    loading: detailsPageResponse.loading,\n    error,\n    runFetchError: detailsPageResponse.errors.runFetchError,\n    experimentFetchError: detailsPageResponse.errors.experimentFetchError,\n    refetchRun: detailsPageResponse.refetchRun,\n    registeredModelVersionSummaries,\n  };\n};\n", "import { useCallback, useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ReduxState, ThunkDispatch } from '../../../redux-types';\nimport { getExperimentApi, getRunApi } from '../../actions';\nimport { searchModelVersionsApi } from '../../../model-registry/actions';\nimport { pickBy } from 'lodash';\nimport Utils from '../../../common/utils/Utils';\n\n/**\n * Hook fetching data for the run page: both run and experiment entities.\n * The initial fetch action is omitted if entities are already in the store.\n */\nexport const useRunDetailsPageDataLegacy = (runUuid: string, experimentId: string) => {\n  const [runRequestId, setRunRequestId] = useState('');\n  const [experimentRequestId, setExperimentRequestId] = useState('');\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  // Get the necessary data from the store\n\n  const { runInfo, tags, latestMetrics, experiment, params, datasets } = useSelector((state: ReduxState) => ({\n    runInfo: state.entities.runInfosByUuid[runUuid],\n    // Filter out tags, metrics, and params that are entirely whitespace\n    tags: pickBy(state.entities.tagsByRunUuid[runUuid], (tag) => tag.key.trim().length > 0),\n    latestMetrics: pickBy(state.entities.latestMetricsByRunUuid[runUuid], (metric) => metric.key.trim().length > 0),\n    params: pickBy(state.entities.paramsByRunUuid[runUuid], (param) => param.key.trim().length > 0),\n    experiment: state.entities.experimentsById[experimentId],\n    datasets: state.entities.runDatasetsByUuid[runUuid],\n  }));\n\n  const fetchRun = useCallback(() => {\n    const action = getRunApi(runUuid);\n    setRunRequestId(action.meta.id);\n    return dispatch(action);\n  }, [dispatch, runUuid]);\n\n  const fetchExperiment = useCallback(() => {\n    const action = getExperimentApi(experimentId);\n    setExperimentRequestId(action.meta.id);\n    return dispatch(action);\n  }, [dispatch, experimentId]);\n\n  const fetchModelVersions = useCallback(() => {\n    dispatch(searchModelVersionsApi({ run_id: runUuid }));\n  }, [dispatch, runUuid]);\n\n  // Do the initial run & experiment fetch only if it's not in the store already\n  useEffect(() => {\n    if (!runInfo) {\n      fetchRun().catch((e) => Utils.logErrorAndNotifyUser(e));\n    }\n    fetchModelVersions();\n  }, [runInfo, fetchRun, fetchModelVersions]);\n\n  useEffect(() => {\n    if (!experiment) {\n      fetchExperiment().catch((e) => Utils.logErrorAndNotifyUser(e));\n    }\n  }, [experiment, fetchExperiment]);\n\n  // Check the \"apis\" store for the requests status\n  const { loading: runLoading, error: runFetchError } = useSelector((state: ReduxState) => ({\n    loading: !runRequestId || Boolean(state.apis?.[runRequestId]?.active),\n    error: state.apis?.[runRequestId]?.error,\n  }));\n\n  const { loading: experimentLoading, error: experimentFetchError } = useSelector((state: ReduxState) => ({\n    loading: !runRequestId || Boolean(state.apis?.[experimentRequestId]?.active),\n    error: state.apis?.[experimentRequestId]?.error,\n  }));\n\n  const loading = runLoading || experimentLoading;\n\n  return {\n    loading,\n    data: {\n      runInfo,\n      tags,\n      params,\n      latestMetrics,\n      experiment,\n      datasets,\n    },\n    refetchRun: fetchRun,\n    errors: { runFetchError, experimentFetchError },\n  };\n};\n", "import { But<PERSON>, <PERSON><PERSON>, TableIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { useState } from 'react';\nimport { useExperimentLoggedModelOpenDatasetDetails } from './hooks/useExperimentLoggedModelOpenDatasetDetails';\nimport { useUserActionErrorHandler } from '@databricks/web-shared/metrics';\n\nexport const ExperimentLoggedModelDatasetButton = ({\n  datasetName,\n  datasetDigest,\n  runId,\n}: {\n  datasetName: string;\n  datasetDigest: string;\n  runId: string | null;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const [loadingDatasetDetails, setLoadingDatasetDetails] = useState(false);\n  const { onDatasetClicked } = useExperimentLoggedModelOpenDatasetDetails();\n  const { handleError } = useUserActionErrorHandler();\n\n  const handleDatasetClick = (datasetName: string, datasetDigest: string, runId: string | null) => {\n    if (runId) {\n      setLoadingDatasetDetails(true);\n      onDatasetClicked?.({ datasetName, datasetDigest, runId })\n        .catch((error) => {\n          handleError(error);\n        })\n        .finally(() => setLoadingDatasetDetails(false));\n    }\n  };\n\n  return (\n    <Button\n      type=\"link\"\n      icon={loadingDatasetDetails ? <Spinner size=\"small\" css={{ marginRight: theme.spacing.sm }} /> : <TableIcon />}\n      key={[datasetName, datasetDigest].join('.')}\n      componentId=\"mlflow.logged_model.dataset\"\n      onClick={() => handleDatasetClick(datasetName, datasetDigest, runId)}\n    >\n      {datasetName} (#{datasetDigest})\n    </Button>\n  );\n};\n", "import { createContext, useCallback, useContext, useMemo, useRef, useState } from 'react';\nimport {\n  type DatasetWithRunType,\n  ExperimentViewDatasetDrawer,\n} from '../../experiment-page/components/runs/ExperimentViewDatasetDrawer';\nimport { useLazyGetRunQuery } from '../../run-page/hooks/useGetRunQuery';\nimport { transformDatasets as transformGraphQLResponseDatasets } from '../../run-page/hooks/useRunDetailsPageData';\nimport { keyBy } from 'lodash';\nimport type { KeyValueEntity } from '../../../types';\nimport { ErrorLogType, ErrorName, PredefinedError } from '@databricks/web-shared/errors';\nimport { ErrorCodes } from '../../../../common/constants';\nimport { FormattedMessage } from 'react-intl';\n\nclass DatasetRunNotFoundError extends PredefinedError {\n  errorLogType = ErrorLogType.UnexpectedSystemStateError;\n  errorName = ErrorName.DatasetRunNotFoundError;\n  isUserError = true;\n  displayMessage = (\n    <FormattedMessage\n      defaultMessage=\"The run containing the dataset could not be found.\"\n      description=\"Error message displayed when the run for the dataset is not found\"\n    />\n  );\n}\n\ntype ExperimentLoggedModelOpenDatasetDetailsContextType = {\n  onDatasetClicked: (params: { datasetName: string; datasetDigest: string; runId: string }) => Promise<void>;\n};\n\nconst ExperimentLoggedModelOpenDatasetDetailsContext =\n  createContext<ExperimentLoggedModelOpenDatasetDetailsContextType>({\n    onDatasetClicked: () => Promise.resolve(),\n  });\n\n/**\n * Creates a context provider that allows opening the dataset details drawer from the logged model page.\n * Uses the `useGetRunQuery` GraphQL to fetch the run info for the dataset.\n */\nexport const ExperimentLoggedModelOpenDatasetDetailsContextProvider = ({ children }: { children: React.ReactNode }) => {\n  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);\n  const [selectedDatasetWithRun, setSelectedDatasetWithRun] = useState<DatasetWithRunType>();\n\n  const [getRunInfo] = useLazyGetRunQuery();\n\n  // Store the current promise's reject function\n  const rejectCurrentPromiseFn = useRef<((reason?: any) => void) | null>(null);\n\n  const onDatasetClicked = useCallback(\n    async (params: { datasetName: string; datasetDigest: string; runId: string }) =>\n      new Promise<void>((resolve, reject) => {\n        // If there's a promise in flight, reject it to remove previous loading state\n        rejectCurrentPromiseFn.current?.();\n\n        return getRunInfo({\n          onError: reject,\n          onCompleted(data) {\n            // If there's an API error in the response, reject the promise\n            if (data.mlflowGetRun?.apiError) {\n              // Special case: if the run is not found, show a different error message\n              const error =\n                data.mlflowGetRun.apiError.code === ErrorCodes.RESOURCE_DOES_NOT_EXIST\n                  ? new DatasetRunNotFoundError()\n                  : data.mlflowGetRun.apiError;\n              reject(error);\n              return;\n            }\n            // Transform the datasets into a format that can be used by the drawer UI\n            const datasets = transformGraphQLResponseDatasets(data.mlflowGetRun?.run?.inputs?.datasetInputs);\n\n            // Ensure that the datasets and run info are present\n            if (!datasets || !data.mlflowGetRun?.run?.info) {\n              return;\n            }\n\n            // Find the dataset that matches the dataset name and digest\n            const matchingDataset = datasets?.find(\n              (datasetInput) =>\n                datasetInput.dataset?.digest === params.datasetDigest &&\n                datasetInput.dataset.name === params.datasetName,\n            );\n\n            // If the dataset is not found, return early\n            if (!matchingDataset) {\n              return;\n            }\n            const { info, data: runData } = data.mlflowGetRun.run;\n\n            // Convert tags into a dictionary for easier access\n            const tagsDictionary = keyBy(runData?.tags?.filter((tag) => tag.key && tag.value) ?? [], 'key') as Record<\n              string,\n              KeyValueEntity\n            >;\n\n            // Open the drawer using the dataset and run info\n            setIsDrawerOpen(true);\n            setSelectedDatasetWithRun({\n              datasetWithTags: {\n                dataset: matchingDataset.dataset,\n                tags: matchingDataset.tags,\n              },\n              runData: {\n                datasets: datasets,\n                runUuid: info.runUuid ?? '',\n                experimentId: info.experimentId ?? '',\n                runName: info.runName ?? '',\n                tags: tagsDictionary,\n              },\n            });\n\n            // Resolve the promise\n            resolve();\n            rejectCurrentPromiseFn.current = null;\n          },\n          variables: { data: { runId: params.runId } },\n        });\n      }),\n    [getRunInfo],\n  );\n\n  const contextValue = useMemo(() => ({ onDatasetClicked }), [onDatasetClicked]);\n\n  return (\n    <ExperimentLoggedModelOpenDatasetDetailsContext.Provider value={contextValue}>\n      {children}\n      {selectedDatasetWithRun && (\n        <ExperimentViewDatasetDrawer\n          isOpen={isDrawerOpen}\n          selectedDatasetWithRun={selectedDatasetWithRun}\n          setIsOpen={setIsDrawerOpen}\n          setSelectedDatasetWithRun={setSelectedDatasetWithRun}\n        />\n      )}\n    </ExperimentLoggedModelOpenDatasetDetailsContext.Provider>\n  );\n};\n\nexport const useExperimentLoggedModelOpenDatasetDetails = () =>\n  useContext(ExperimentLoggedModelOpenDatasetDetailsContext);\n", "import { DocumentNode } from 'graphql';\nimport { TypedDocumentNode } from '@graphql-typed-document-node/core';\nimport { useCallback, useMemo, useRef } from 'react';\n\nimport { OperationVariables } from '../../core';\nimport { mergeOptions } from '../../utilities';\nimport {\n  LazyQueryHookOptions,\n  LazyQueryResultTuple,\n  QueryResult,\n} from '../types/types';\nimport { useInternalState } from './useQuery';\nimport { useApolloClient } from './useApolloClient';\n\n// The following methods, when called will execute the query, regardless of\n// whether the useLazyQuery execute function was called before.\nconst EAGER_METHODS = [\n  'refetch',\n  'reobserve',\n  'fetchMore',\n  'updateQuery',\n  'startPolling',\n  'subscribeToMore',\n] as const;\n\nexport function useLazyQuery<TData = any, TVariables = OperationVariables>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: LazyQueryHookOptions<TData, TVariables>\n): LazyQueryResultTuple<TData, TVariables> {\n  const internalState = useInternalState(\n    useApolloClient(options && options.client),\n    query,\n  );\n\n  const execOptionsRef = useRef<Partial<LazyQueryHookOptions<TData, TVariables>>>();\n  const merged = execOptionsRef.current\n    ? mergeOptions(options, execOptionsRef.current)\n    : options;\n\n  const useQueryResult = internalState.useQuery({\n    ...merged,\n    skip: !execOptionsRef.current,\n  });\n\n  const initialFetchPolicy =\n    useQueryResult.observable.options.initialFetchPolicy ||\n    internalState.getDefaultFetchPolicy();\n\n  const result: QueryResult<TData, TVariables> =\n    Object.assign(useQueryResult, {\n      called: !!execOptionsRef.current,\n    });\n\n  // We use useMemo here to make sure the eager methods have a stable identity.\n  const eagerMethods = useMemo(() => {\n    const eagerMethods: Record<string, any> = {};\n    for (const key of EAGER_METHODS) {\n      const method = result[key];\n      eagerMethods[key] = function () {\n        if (!execOptionsRef.current) {\n          execOptionsRef.current = Object.create(null);\n          // Only the first time populating execOptionsRef.current matters here.\n          internalState.forceUpdate();\n        }\n        return method.apply(this, arguments);\n      };\n    }\n\n    return eagerMethods;\n  }, []);\n\n  Object.assign(result, eagerMethods);\n\n  const execute = useCallback<\n    LazyQueryResultTuple<TData, TVariables>[0]\n  >(executeOptions => {\n    execOptionsRef.current = executeOptions ? {\n      ...executeOptions,\n      fetchPolicy: executeOptions.fetchPolicy || initialFetchPolicy,\n    } : {\n      fetchPolicy: initialFetchPolicy,\n    };\n\n    const promise = internalState\n      .asyncUpdate() // Like internalState.forceUpdate, but returns a Promise.\n      .then(queryResult => Object.assign(queryResult, eagerMethods));\n\n    // Because the return value of `useLazyQuery` is usually floated, we need\n    // to catch the promise to prevent unhandled rejections.\n    promise.catch(() => {});\n\n    return promise;\n  }, []);\n\n  return [execute, result];\n}\n", "import { type Apollo<PERSON><PERSON>r, type ApolloQueryResult, gql } from '@mlflow/mlflow/src/common/utils/graphQLHooks';\nimport type { GetRun, GetRunVariables } from '../../../../graphql/__generated__/graphql';\nimport { useQuery, useLazyQuery } from '@mlflow/mlflow/src/common/utils/graphQLHooks';\n\nconst GET_RUN_QUERY = gql`\n  query GetRun($data: MlflowGetRunInput!) @component(name: \"MLflow.ExperimentRunTracking\") {\n    mlflowGetRun(input: $data) {\n      apiError {\n        helpUrl\n        code\n        message\n      }\n      run {\n        info {\n          runName\n          status\n          runUuid\n          experimentId\n          artifactUri\n          endTime\n          lifecycleStage\n          startTime\n          userId\n        }\n        experiment {\n          experimentId\n          name\n          tags {\n            key\n            value\n          }\n          artifactLocation\n          lifecycleStage\n          lastUpdateTime\n        }\n        modelVersions {\n          status\n          version\n          name\n          source\n        }\n        data {\n          metrics {\n            key\n            value\n            step\n            timestamp\n          }\n          params {\n            key\n            value\n          }\n          tags {\n            key\n            value\n          }\n        }\n        inputs {\n          datasetInputs {\n            dataset {\n              digest\n              name\n              profile\n              schema\n              source\n              sourceType\n            }\n            tags {\n              key\n              value\n            }\n          }\n          modelInputs {\n            modelId\n          }\n        }\n        outputs {\n          modelOutputs {\n            modelId\n            step\n          }\n        }\n      }\n    }\n  }\n`;\n\nexport type UseGetRunQueryResponseRunInfo = NonNullable<NonNullable<UseGetRunQueryDataResponse>['info']>;\nexport type UseGetRunQueryResponseDatasetInputs = NonNullable<\n  NonNullable<UseGetRunQueryDataResponse>['inputs']\n>['datasetInputs'];\nexport type UseGetRunQueryResponseInputs = NonNullable<UseGetRunQueryDataResponse>['inputs'];\nexport type UseGetRunQueryResponseOutputs = NonNullable<UseGetRunQueryDataResponse>['outputs'];\nexport type UseGetRunQueryResponseExperiment = NonNullable<NonNullable<UseGetRunQueryDataResponse>['experiment']>;\nexport type UseGetRunQueryResponseDataMetrics = NonNullable<\n  NonNullable<NonNullable<UseGetRunQueryDataResponse>['data']>['metrics']\n>;\n\nexport type UseGetRunQueryDataResponse = NonNullable<GetRun['mlflowGetRun']>['run'];\nexport type UseGetRunQueryDataApiError = NonNullable<GetRun['mlflowGetRun']>['apiError'];\nexport type UseGetRunQueryResponse = {\n  data?: UseGetRunQueryDataResponse;\n  loading: boolean;\n  apolloError?: ApolloError;\n  apiError?: UseGetRunQueryDataApiError;\n  refetchRun: () => Promise<ApolloQueryResult<GetRun>>;\n};\n\nexport const useGetRunQuery = ({\n  runUuid,\n  disabled = false,\n}: {\n  runUuid: string;\n  disabled?: boolean;\n}): UseGetRunQueryResponse => {\n  const {\n    data,\n    loading,\n    error: apolloError,\n    refetch,\n  } = useQuery<GetRun, GetRunVariables>(GET_RUN_QUERY, {\n    variables: {\n      data: {\n        runId: runUuid,\n      },\n    },\n    skip: disabled,\n  });\n\n  return {\n    loading,\n    data: data?.mlflowGetRun?.run,\n    refetchRun: refetch,\n    apolloError,\n    apiError: data?.mlflowGetRun?.apiError,\n  } as const;\n};\n\nexport const useLazyGetRunQuery = () => useLazyQuery<GetRun, GetRunVariables>(GET_RUN_QUERY);\n", "import React, {\n  createContext,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  type PropsWithChildren,\n  type SyntheticEvent,\n} from 'react';\n\nimport { matchPredefinedError } from '../errors';\nimport type { HandleableError, PredefinedError } from '../errors';\n\nexport type UserActionError = PredefinedError | null;\n\ntype UserActionErrorHandlerContextProps = {\n  currentUserActionError: UserActionError;\n  handleError: (error: HandleableError, onErrorCallback?: (err: UserActionError) => void) => void;\n  handlePromise: (promise: Promise<any>) => void;\n  clearUserActionError: () => void;\n};\n\nconst UserActionErrorHandlerContext = createContext<UserActionErrorHandlerContextProps>({\n  currentUserActionError: null,\n  handleError: () => {},\n  handlePromise: () => {},\n  clearUserActionError: () => {},\n});\n\ntype UserActionErrorHandlerProps = {\n  errorFilter?: (error: any) => boolean;\n};\n\nexport const UserActionErrorHandler = ({ children, errorFilter }: PropsWithChildren<UserActionErrorHandlerProps>) => {\n  const [currentUserActionError, setCurrentUserActionError] = useState<UserActionError>(null);\n\n  const handleError = useCallback(\n    (error: HandleableError, onErrorCallback?: (err: UserActionError) => void) => {\n      if (!errorFilter?.(error)) {\n        const predefinedError = matchPredefinedError(error);\n\n        setCurrentUserActionError(predefinedError);\n\n        if (onErrorCallback) {\n          onErrorCallback(predefinedError);\n        }\n      }\n    },\n    [setCurrentUserActionError, errorFilter],\n  );\n\n  const handlePromise = useCallback(\n    (promise: Promise<any>) => {\n      promise.catch((error: HandleableError) => {\n        handleError(error);\n      });\n    },\n    [handleError],\n  );\n\n  const clearUserActionError = useCallback(() => {\n    setCurrentUserActionError(null);\n  }, [setCurrentUserActionError]);\n\n  return (\n    <UserActionErrorHandlerContext.Provider\n      value={useMemo(\n        () => ({\n          currentUserActionError,\n          handleError,\n          handlePromise,\n          clearUserActionError,\n        }),\n        [clearUserActionError, currentUserActionError, handleError, handlePromise],\n      )}\n    >\n      {children}\n    </UserActionErrorHandlerContext.Provider>\n  );\n};\n\nexport type UserErrorActionHandlerHook = {\n  currentUserActionError: UserActionError;\n  handleError: (error: HandleableError, onErrorCallback?: (err: UserActionError) => void) => void;\n  /** @deprecated Use handleError instead, or get permission from #product-analytics to use */\n  handleErrorWithEvent: (\n    event: SyntheticEvent | Event,\n    error: HandleableError,\n    onErrorCallback?: (err: UserActionError) => void,\n  ) => void;\n  handlePromise: (promise: Promise<any>) => void;\n  clearUserActionError: () => void;\n};\n\nexport const useUserActionErrorHandler = (): UserErrorActionHandlerHook => {\n  const { currentUserActionError, handleError, handlePromise, clearUserActionError } =\n    useContext(UserActionErrorHandlerContext);\n\n  const handleErrorWithEventImpl = useCallback(\n    (event: SyntheticEvent | Event, error: HandleableError, onErrorCallback?: (err: UserActionError) => void) => {\n      handleError(error, onErrorCallback);\n    },\n    [handleError],\n  );\n\n  return useMemo(\n    () => ({\n      currentUserActionError,\n      handleError,\n      handleErrorWithEvent: handleErrorWithEventImpl,\n      handlePromise,\n      clearUserActionError,\n    }),\n    [clearUserActionError, handleError, handlePromise, currentUserActionError, handleErrorWithEventImpl],\n  );\n};\n\nexport function withUserActionErrorHandler<P>(\n  Component: React.ComponentType<P>,\n  errorFilter?: (error: any) => boolean,\n): React.ComponentType<P> {\n  return function UserActionErrorHandlerWrapper(props: P) {\n    return (\n      <UserActionErrorHandler errorFilter={errorFilter}>\n        {/* @ts-expect-error Generics don't play well with WithConditionalCSSProp type coming @emotion/react jsx typing to validate css= prop values typing. More details here: emotion-js/emotion#2169 */}\n        <Component {...props} />\n      </UserActionErrorHandler>\n    );\n  };\n}\n", "import React from 'react';\n\nimport { Tooltip } from '@databricks/design-system';\nimport type { IntlShape } from 'react-intl';\nimport { useIntl } from 'react-intl';\n\n// Time intervals in seconds\nconst SECOND = 1;\nconst MINUTE = 60 * SECOND;\nconst HOUR = 60 * MINUTE;\nconst DAY = 24 * HOUR;\nconst MONTH = 30 * DAY;\nconst YEAR = 365 * DAY;\n\ntype Interval = {\n  seconds: number;\n  timeAgoMessage: (count: number) => string;\n};\n\nconst getIntervals = (intl: IntlShape): Interval[] => [\n  {\n    seconds: YEAR,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 year} other {# years}} ago',\n          description: 'Time duration in years',\n        },\n        { count },\n      ),\n  },\n  {\n    seconds: MONTH,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 month} other {# months}} ago',\n          description: 'Time duration in months',\n        },\n        { count },\n      ),\n  },\n  {\n    seconds: DAY,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 day} other {# days}} ago',\n          description: 'Time duration in days',\n        },\n        { count },\n      ),\n  },\n  {\n    seconds: HOUR,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 hour} other {# hours}} ago',\n          description: 'Time duration in hours',\n        },\n        { count },\n      ),\n  },\n  {\n    seconds: MINUTE,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 minute} other {# minutes}} ago',\n          description: 'Time duration in minutes',\n        },\n        { count },\n      ),\n  },\n  {\n    seconds: SECOND,\n    timeAgoMessage: (count: number) =>\n      intl.formatMessage(\n        {\n          defaultMessage: '{count, plural, =1 {1 second} other {# seconds}} ago',\n          description: 'Time duration in seconds',\n        },\n        { count },\n      ),\n  },\n];\n\nexport interface TimeAgoProps {\n  date: Date;\n  tooltipFormatOptions?: DateTooltipOptionsType;\n}\n\ntype DateTooltipOptionsType = Intl.DateTimeFormatOptions;\n\nconst DateTooltipOptions: DateTooltipOptionsType = {\n  timeZoneName: 'short',\n  year: 'numeric',\n  month: 'numeric',\n  day: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit',\n};\n\nexport const getTimeAgoStrings = ({\n  date,\n  intl,\n  tooltipFormatOptions = DateTooltipOptions,\n}: TimeAgoProps & { intl: IntlShape }): { displayText: string; tooltipTitle: string } => {\n  const now = new Date();\n  const seconds = Math.round((now.getTime() - date.getTime()) / 1000);\n\n  const locale = navigator.language || 'en-US';\n  let tooltipTitle = '';\n  try {\n    tooltipTitle = Intl.DateTimeFormat(locale, tooltipFormatOptions).format(date);\n  } catch (e) {\n    // ES-1357574 Do nothing; this is not a critical path, let's just not throw an error\n  }\n\n  for (const interval of getIntervals(intl)) {\n    const count = Math.floor(seconds / interval.seconds);\n    if (count >= 1) {\n      return { displayText: interval.timeAgoMessage(count), tooltipTitle };\n    }\n  }\n\n  return {\n    displayText: intl.formatMessage({\n      defaultMessage: 'just now',\n      description: 'Indicates a time duration that just passed',\n    }),\n    tooltipTitle,\n  };\n};\n\nexport const TimeAgo: React.FC<TimeAgoProps> = ({ date, tooltipFormatOptions = DateTooltipOptions }) => {\n  const intl = useIntl();\n  const { displayText, tooltipTitle } = getTimeAgoStrings({ date, intl, tooltipFormatOptions });\n  return (\n    <Tooltip componentId=\"web-shared.time-ago\" content={tooltipTitle}>\n      <span>{displayText}</span>\n    </Tooltip>\n  );\n};\n", "import React from 'react';\n\nimport { TimeAgo } from '@databricks/web-shared/browse';\n\nexport const ExperimentLoggedModelTableDateCell = ({ value }: { value?: string | number | null }) => {\n  const date = new Date(Number(value));\n\n  if (isNaN(date as any)) {\n    return null;\n  }\n\n  return <TimeAgo date={date} />;\n};\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  QueryObserverOptions,\n  QueryObserverResult,\n  DefaultedQueryObserverOptions,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import 'client-only'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { QueryKey, QueryFunction } from '@tanstack/query-core'\nimport { notifyManager, QueriesObserver } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { UseQueryOptions, UseQueryResult } from './types'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureStaleTime,\n  shouldSuspend,\n  fetchOptimistic,\n  willFetch,\n} from './suspense'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// - `context` is omitted as it is passed as a root-level option to `useQueries` instead.\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'context'>\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptionsForUseQueries<\n        TQueryFnData,\n        unknown,\n        TQueryFnData,\n        TQueryKey\n      >\n    : // Fallback\n      UseQueryOptionsForUseQueries\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? UseQueryResult<TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptionsForUseQueries[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptionsForUseQueries[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...QueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): QueriesResults<T> {\n  const queryClient = useQueryClient({ context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((options) => {\n        const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, queryClient, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureStaleTime(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries),\n  )\n\n  const optimisticResult = observer.getOptimisticResult(defaultedQueries)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result, isRestoring),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const options = defaultedQueries[index]\n        const queryObserver = observer.getObservers()[index]\n\n        if (options && queryObserver) {\n          if (shouldSuspend(options, result, isRestoring)) {\n            return fetchOptimistic(options, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(options, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const observerQueries = observer.getQueries()\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) =>\n      getHasError({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedQueries[index]?.useErrorBoundary ?? false,\n        query: observerQueries[index]!,\n      }),\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return optimisticResult as QueriesResults<T>\n}\n", "import {\n  CheckCircleIcon,\n  ClockIcon,\n  Tag,\n  Typography,\n  useDesignSystemTheme,\n  XCircleIcon,\n} from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { LoggedModelStatusProtoEnum, type LoggedModelProto } from '../../types';\n\nconst LoggedModelStatusIcon = ({ status }: { status: LoggedModelStatusProtoEnum }) => {\n  if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_READY) {\n    return <CheckCircleIcon color=\"success\" />;\n  }\n\n  if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_UPLOAD_FAILED) {\n    return <XCircleIcon color=\"danger\" />;\n  }\n\n  if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_PENDING) {\n    return <ClockIcon color=\"warning\" />;\n  }\n\n  return null;\n};\n\nexport const ExperimentLoggedModelStatusIndicator = ({ data }: { data: LoggedModelProto }) => {\n  const { theme } = useDesignSystemTheme();\n  const status = data.info?.status ?? LoggedModelStatusProtoEnum.LOGGED_MODEL_STATUS_UNSPECIFIED;\n\n  const getTagColor = () => {\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_READY) {\n      return theme.isDarkMode ? theme.colors.green800 : theme.colors.green100;\n    }\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_UPLOAD_FAILED) {\n      return theme.isDarkMode ? theme.colors.red800 : theme.colors.red100;\n    }\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_PENDING) {\n      return theme.isDarkMode ? theme.colors.yellow800 : theme.colors.yellow100;\n    }\n\n    return undefined;\n  };\n\n  const getStatusLabel = () => {\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_READY) {\n      return (\n        <Typography.Text color=\"success\">\n          <FormattedMessage defaultMessage=\"Ready\" description=\"Label for ready state of a experiment logged model\" />\n        </Typography.Text>\n      );\n    }\n\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_UPLOAD_FAILED) {\n      return (\n        <Typography.Text color=\"error\">\n          <FormattedMessage\n            defaultMessage=\"Upload failed\"\n            description=\"Label for upload failed state of a experiment logged model\"\n          />\n        </Typography.Text>\n      );\n    }\n    if (status === LoggedModelStatusProtoEnum.LOGGED_MODEL_PENDING) {\n      return (\n        <Typography.Text color=\"warning\">\n          <FormattedMessage\n            defaultMessage=\"Pending\"\n            description=\"Label for pending state of a experiment logged model\"\n          />\n        </Typography.Text>\n      );\n    }\n\n    return status;\n  };\n\n  return (\n    <Tag componentId=\"mlflow.logged_model.status\" css={{ backgroundColor: getTagColor() }}>\n      {status && <LoggedModelStatusIcon status={status} />}{' '}\n      <Typography.Text css={{ marginLeft: theme.spacing.sm }}>{getStatusLabel()}</Typography.Text>\n    </Tag>\n  );\n};\n"], "names": ["serializeRequestBody", "payload", "undefined", "FormData", "Blob", "JSON", "stringify", "loggedModelsDataRequest", "async", "url", "method", "arguments", "length", "body", "response", "fetch", "headers", "ok", "predefinedError", "matchPredefinedError", "message", "json", "useUnifiedRegisteredModelVersionsSummariesForRun", "_ref", "query<PERSON><PERSON>ult", "runUuid", "registeredModels", "registeredModelsFromStore", "useSelector", "_ref2", "entities", "modelVersionsByRunUuid", "shouldEnableGraphQLModelVersionsForRunDetails", "result", "_queryResult$data", "_queryResult$data$mod", "data", "modelVersions", "for<PERSON>ach", "modelVersion", "push", "displayedName", "name", "version", "link", "ModelRegistryRoutes", "getModelVersionPageRoute", "status", "source", "map", "transformToKeyedObject", "inputArray", "keyBy", "transformDatasets", "datasetInput", "_datasetInput$dataset", "_datasetInput$dataset2", "_datasetInput$dataset3", "_datasetInput$dataset4", "_datasetInput$dataset5", "_datasetInput$dataset6", "_datasetInput$dataset7", "_datasetInput$dataset8", "_datasetInput$dataset9", "_datasetInput$dataset10", "_datasetInput$dataset11", "_datasetInput$dataset12", "_datasetInput$tags$ma", "_datasetInput$tags", "dataset", "digest", "profile", "schema", "sourceType", "tags", "tag", "_tag$key", "_tag$value", "key", "value", "filter", "isEmpty", "useRunDetailsPageData", "_ref3", "_detailsPageResponse$", "_detailsPageResponse$2", "_detailsPageResponse$3", "_detailsPageResponse$4", "_detailsPageResponse$5", "_detailsPageResponse$6", "experimentId", "usingGraphQL", "shouldEnableGraphQLRunDetailsPage", "dispatch", "useDispatch", "_detailsPageGraphqlRe12", "_detailsPageGraphqlRe13", "_detailsPageGraphqlRe14", "_detailsPageGraphqlRe15", "_detailsPageGraphqlRe16", "_detailsPageGraphqlRe17", "detailsPageGraphqlResponse", "graphQLQuery", "useGetRunQuery", "useEffect", "searchModelVersionsApi", "run_id", "latestMetrics", "params", "datasets", "useMemo", "_detailsPageGraphqlRe", "_detailsPageGraphqlRe2", "_detailsPageGraphqlRe3", "_detailsPageGraphqlRe4", "_detailsPageGraphqlRe5", "_detailsPageGraphqlRe6", "_detailsPageGraphqlRe7", "_detailsPageGraphqlRe8", "_detailsPageGraphqlRe9", "_detailsPageGraphqlRe10", "_detailsPageGraphqlRe11", "pickBy", "metrics", "step", "timestamp", "Number", "metric", "trim", "param", "inputs", "datasetInputs", "registeredModelVersionSummaries", "runInfo", "info", "experiment", "loading", "error", "apolloError", "apiError", "refetchRun", "runInputs", "runOutputs", "outputs", "detailsPageResponse", "useRunDetailsPageDataLegacy", "runRequestId", "setRunRequestId", "useState", "experimentRequestId", "setExperimentRequestId", "state", "runInfosByUuid", "tagsByRunUuid", "latestMetricsByRunUuid", "paramsByRunUuid", "experimentsById", "runDatasetsByUuid", "fetchRun", "useCallback", "action", "getRunApi", "meta", "id", "fetchExperiment", "getExperimentApi", "fetchModelVersions", "catch", "e", "Utils", "logErrorAndNotifyUser", "runLoading", "runFetchError", "_state$apis", "_state$apis$runReques", "_state$apis2", "_state$apis2$runReque", "Boolean", "apis", "active", "experimentLoading", "experimentFetchError", "_state$apis3", "_state$apis3$experime", "_state$apis4", "_state$apis4$experime", "errors", "ExperimentLoggedModelDatasetButton", "datasetName", "datasetDigest", "runId", "theme", "useDesignSystemTheme", "loadingDatasetDetails", "setLoadingDatasetDetails", "onDatasetClicked", "useExperimentLoggedModelOpenDatasetDetails", "handleError", "useUserActionErrorHandler", "_jsxs", "<PERSON><PERSON>", "type", "icon", "_jsx", "Spinner", "size", "css", "_css", "marginRight", "spacing", "sm", "TableIcon", "componentId", "onClick", "handleDatasetClick", "finally", "children", "join", "DatasetRunNotFoundError", "PredefinedError", "constructor", "errorLogType", "ErrorLogType", "UnexpectedSystemStateError", "errorName", "ErrorName", "isUserError", "displayMessage", "FormattedMessage", "defaultMessage", "ExperimentLoggedModelOpenDatasetDetailsContext", "createContext", "Promise", "resolve", "ExperimentLoggedModelOpenDatasetDetailsContextProvider", "isDrawerOpen", "setIsDrawerOpen", "selectedDatasetWithRun", "setSelectedDatasetWithRun", "getRunInfo", "useLazyGetRunQuery", "rejectCurrentPromiseFn", "useRef", "reject", "_rejectCurrentPromise", "current", "call", "onError", "onCompleted", "_data$mlflowGetRun", "_data$mlflowGetRun2", "_data$mlflowGetRun2$r", "_data$mlflowGetRun2$r2", "_data$mlflowGetRun3", "_data$mlflowGetRun3$r", "_runData$tags$filter", "_runData$tags", "_info$runUuid", "_info$experimentId", "_info$runName", "mlflowGetRun", "code", "ErrorCodes", "RESOURCE_DOES_NOT_EXIST", "transformGraphQLResponseDatasets", "run", "matchingDataset", "find", "runData", "tagsDictionary", "datasetWithTags", "runName", "variables", "contextValue", "Provider", "ExperimentViewDatasetDrawer", "isOpen", "setIsOpen", "useContext", "EAGER_METHODS", "GET_RUN_QUERY", "gql", "disabled", "refetch", "useQuery", "skip", "query", "options", "internalState", "useInternalState", "useApolloClient", "client", "execOptionsRef", "merged", "mergeOptions", "useQueryResult", "__assign", "initialFetchPolicy", "observable", "getDefaultFetchPolicy", "Object", "assign", "called", "eagerMethods", "create", "forceUpdate", "apply", "this", "_i", "EAGER_METHODS_1", "executeOptions", "fetchPolicy", "promise", "asyncUpdate", "then", "useLazyQuery", "UserActionErrorHandlerContext", "currentUserActionError", "handlePromise", "clearUserActionError", "UserActionErrorHandler", "errorFilter", "setCurrentUserActionError", "onError<PERSON>allback", "handleErrorWithEventImpl", "event", "handleErrorWithEvent", "DAY", "DateTooltipOptions", "timeZoneName", "year", "month", "day", "hour", "minute", "getTimeAgoStrings", "date", "intl", "tooltipFormatOptions", "now", "Date", "seconds", "Math", "round", "getTime", "locale", "navigator", "language", "tooltipTitle", "Intl", "DateTimeFormat", "format", "interval", "timeAgoMessage", "count", "formatMessage", "getIntervals", "floor", "displayText", "TimeAgo", "useIntl", "<PERSON><PERSON><PERSON>", "content", "ExperimentLoggedModelTableDateCell", "isNaN", "QueriesObserver", "Subscribable", "queries", "super", "observers", "observersMap", "setQueries", "onSubscribe", "listeners", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "Set", "notifyOptions", "notify<PERSON><PERSON>ger", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "newObserversMap", "fromEntries", "queryHash", "newResult", "getCurrentResult", "hasIndexChange", "some", "index", "hasListeners", "difference", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "prevObserversMap", "Map", "defaultQueryOptions", "matchingObservers", "flatMap", "defaultedOptions", "get", "matchedQueryHashes", "unmatchedQueries", "has", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "QueryObserver", "newOrReusedObservers", "keepPreviousData", "previouslyUsedObserver", "concat", "sort", "sortMatchesByOrderOfQueries", "a", "b", "indexOf", "replaceAt", "listener", "useQueries", "context", "queryClient", "useQueryClient", "isRestoring", "useIsRestoring", "errorResetBoundary", "useQueryErrorResetBoundary", "defaultedQueries", "React", "_optimisticResults", "ensureStaleTime", "ensurePreventErrorBoundaryRetry", "useClearResetErrorBoundary", "optimisticResult", "useSyncExternalStore", "onStoreChange", "batchCalls", "suspensePromises", "shouldSuspend", "queryObserver", "fetchOptimistic", "<PERSON><PERSON><PERSON><PERSON>", "all", "observerQueries", "firstSingleResultWhichShouldThrow", "_defaultedQueries$ind", "_defaultedQueries$ind2", "getHasError", "useErrorBoundary", "LoggedModelStatusIcon", "LoggedModelStatusProtoEnum", "LOGGED_MODEL_READY", "CheckCircleIcon", "color", "LOGGED_MODEL_UPLOAD_FAILED", "XCircleIcon", "LOGGED_MODEL_PENDING", "ClockIcon", "ExperimentLoggedModelStatusIndicator", "_data$info$status", "_data$info", "LOGGED_MODEL_STATUS_UNSPECIFIED", "Tag", "backgroundColor", "isDarkMode", "colors", "green800", "green100", "red800", "red100", "yellow800", "yellow100", "Typography", "Text", "marginLeft"], "sourceRoot": ""}