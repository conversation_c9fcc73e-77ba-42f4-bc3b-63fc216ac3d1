(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[825],{14671:(e,t,r)=>{"use strict";let n;r.d(t,{sA:()=>D,Dk:()=>_,tz:()=>C});var o=r(97642),a=r(67183),i=r(3307),l=r(65848),s=r.n(l),u=r(82145);r(54788),r(61871),r(61641);var c=r(6213);let d=["en","dev","de-DE","es-ES","fr-FR","ko-KR","ja-JP","it-IT","pt-BR","pt-PT","zh-CN","zh-HK","zh-TW"],g={en:"en",es:"es-ES",fr:"fr-FR",pt:"pt-PT",ja:"ja-JP",ko:"ko-KR",it:"it-IT",de:"de-DE",zh:"zh-CN"},f={"zh-hant":"zh-TW"};r(35571),r(35107);let b={strong:e=>s().createElement("strong",null,e),em:e=>s().createElement("em",null,e),p:e=>s().createElement("p",null,e),span:e=>s().createElement("span",null,e),div:e=>s().createElement("div",null,e),b:e=>s().createElement("b",null,e),label:e=>s().createElement("label",null,e),i:e=>s().createElement("i",null,e),ul:e=>s().createElement("ul",null,e),li:e=>s().createElement("li",null,e)};function E(e,t){if(void 0===e)throw TypeError("attempted to "+t+" private static field before its declaration")}function p(e,t,r){return T(e,t),E(r,"get"),function(e,t){if(t.get)return t.get.call(e);return t.value}(e,r)}function m(e,t,r,n){return T(e,t),E(r,"set"),!function(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=r}}(e,r,n),n}function T(e,t){if(e!==t)throw TypeError("Private static access of wrong provenance")}class h{static async initI18n(e){let{loadMessages:t,onIntlContextError:r}=e;n=r;let o=h.getCurrentLocale(),a=p(h,h,y)[o],i=!!p(this,h,S).get(t);if(!a||!i){let e=await (T(h,h),w).call(h,o,t);a?Object.assign(a,e):(a=e,p(h,h,y)[o]=e)}p(h,h,I)[o]||(p(h,h,I)[o]=(T(h,h),A).call(h,o,a))}static getIntlProviderParams(){let e=h.getCurrentLocale();return{locale:e,messages:p(h,h,y)[e]||{}}}static createIntlWithLocale(){let e=h.getCurrentLocale(),t=p(h,h,I)[e];if(t)return t;return console.error("`createIntlWithLocale` was called before `initI18n`, will fallback to empty intl object which does not have translations"),(T(h,h),A).call(h,e,{})}static getCurrentLocale(){let e=(T(h,h),k).call(h);return h.sanitizeLocale(e)}static sanitizeLocale(e){var t;if(e.includes("_"))return"en";let r=d.find(t=>e===t);return null!==(t=null!=r?r:function(e){if(f[e.toLowerCase()])return f[e.toLowerCase()];let t=g[e.split("-")[0]];return t===e?void 0:t}(e))&&void 0!==t?t:"en"}static setLocalePref(e){let t=new c.A;if(e){try{window.localStorage.setItem("locale",e)}catch(e){}try{t.set("locale",e,{domain:function(e){if(!e)return;let t=e.split(".");if("azuredatabricks.net"===t.slice(-2).join("."))return"azuredatabricks.net";return t.slice(-3).join(".")}(window.location.hostname),path:"/"})}catch(e){}}else{window.localStorage.removeItem("locale");try{t.remove("locale")}catch(e){}}}static __test_only__ResetInternalState(){m(h,h,v,(0,o.MT)()),m(h,h,y,{}),m(h,h,S,new Map),m(h,h,I,{})}}var v={writable:!0,value:(0,o.MT)()},y={writable:!0,value:{}},S={writable:!0,value:new Map},I={writable:!0,value:{}};function A(e,t){return(0,a.E)({locale:e,messages:t,defaultRichTextElements:b},p(h,h,v))}function k(){let e,t;let r=new URLSearchParams(window.location.search).get("l");r&&h.setLocalePref(r);let n=new c.A;try{e=window.localStorage.getItem("locale")}catch(e){}try{t=n.get("locale"),e&&!t&&h.setLocalePref(e)}catch(e){}return t||e||"en"}async function w(e,t){let r=p(h,h,S).get(t);return r||(r=t(e),p(h,h,S).set(t,r)),await r}function C(){let e=(0,l.useContext)(i.ob);e||null==n||n();let t=null!=e?e:h.createIntlWithLocale();return(0,u.HM)(t),t}function D(e){let{formatMessage:t,textComponent:r=s().Fragment}=C(),{id:n,description:o,defaultMessage:a,values:i,children:l,tagName:u=r}=e,c=t({id:n,description:o,defaultMessage:a},i);if(Array.isArray(c)||(c=[c]),"function"==typeof l)return l(c);if(u)return s().createElement(u,null,...c);return s().createElement(s().Fragment,null,c)}function _(e){return s().createElement(a.A,{...e,defaultRichTextElements:b})}let{Consumer:P}=i.ob;!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(class{},"createIntl",a.E)},39659:(e,t,r)=>{"use strict";r.d(t,{Cx:()=>E,Hy:()=>y,Nb:()=>b,SH:()=>T,U6:()=>g,bW:()=>h,dC:()=>f,dg:()=>A,gN:()=>v,wC:()=>S,xp:()=>I});var n=r(14664),o=r(24060),a=r(19035),i=r(52118),l=r(65848),s=r.n(l),u=r(90579),c=r(80838),d=r(98358);let g=(0,l.createContext)({isDarkMode:!1}),f=(0,l.createContext)({enableAnimation:!1,getPrefixCls:e=>e?`du-bois-${e}`:"du-bois",flags:{}}),b="du-bois-enable-animation";function E(e){let t={animationDuration:"0s !important",transition:"none !important"};return e?{}:{...t,"&::before":t,"&::after":t,[`[class*=du-bois]:not(.${b}, .${b} *)`]:{...t,"&::before":t,"&::after":t}}}let p=s().createContext(null),m=s().createContext(null),T=e=>{let{isDarkMode:t=!1,children:r}=e;return(0,d.Y)(g.Provider,{value:{isDarkMode:t},children:r})},h=e=>{let{children:t,enableAnimation:r=!1,zIndexBase:i=1e3,getPopupContainer:s,flags:b={},disableLegacyAntVirtualization:E}=e,{isDarkMode:T}=(0,l.useContext)(g),h=(0,l.useMemo)(()=>(0,u.O)(T,{enableAnimation:r,zIndexBase:i}),[T,i]),v=(0,l.useMemo)(()=>({isDarkMode:T,enableAnimation:r,zIndexBase:i,getPopupContainer:s,flags:b}),[T,r,i,s,b]),y=(0,c.wA)(h),S=(0,l.useMemo)(()=>({enableAnimation:r,isDarkMode:T,getPrefixCls:e=>(0,c.Ec)(h,e),getPopupContainer:s,flags:b}),[r,h,T,s,b]);return(0,l.useEffect)(()=>()=>{a.A.destroy()},[]),(0,d.Y)(p.Provider,{value:v,children:(0,d.Y)(n.a,{theme:h,children:(0,d.Y)(m.Provider,{value:{prefixCls:y,getPopupContainer:s,virtual:!E},children:(0,d.Y)(o.Bc,{children:(0,d.Y)(f.Provider,{value:S,children:t})})})})})},v=e=>{let{enableAnimation:t,zIndexBase:r,getPopupContainer:n,flags:o,children:a}=e,i=(0,l.useContext)(p);if(null===i)throw Error("ApplyDesignSystemContextOverrides cannot be used standalone - DesignSystemProvider must exist in the React context");let s=(0,l.useMemo)(()=>({...i,enableAnimation:null!=t?t:i.enableAnimation,zIndexBase:null!=r?r:i.zIndexBase,getPopupContainer:null!=n?n:i.getPopupContainer,flags:{...i.flags,...o}}),[i,t,r,n,o]);return(0,d.Y)(h,{...s,children:a})},y=e=>{let{flags:t,children:r}=e,n=(0,l.useContext)(p);if(null===n)throw Error("ApplyDesignSystemFlags cannot be used standalone - DesignSystemProvider must exist in the React context");let o=(0,l.useMemo)(()=>({...n,flags:{...n.flags,...t}}),[n,t]);return(0,d.Y)(h,{...o,children:r})},S=e=>{let{children:t}=e,r=I();return(0,d.Y)(i.Ay,{...r,children:t})},I=()=>{var e;return null!==(e=(0,l.useContext)(m))&&void 0!==e?e:{prefixCls:void 0}},A=e=>{let{children:t}=e;return(0,d.Y)(i.Ay,{prefixCls:"ant",children:t})}},80838:(e,t,r)=>{"use strict";r.d(t,{Ec:()=>l,l1:()=>u,wA:()=>i,wn:()=>s});var n=r(14664);r(65848);var o=r(90579),a=r(98358);function i(e){let t=e.isDarkMode?"dark":"light";return`${e.general.classnamePrefix}-${t}`}function l(e,t){return[i(e),t].filter(Boolean).join("-")}function s(){let e=(0,n.u)(),t=e&&e.general?e:(0,o.O)(!1);return{theme:t,classNamePrefix:i(t),getPrefixedClassName:e=>l(t,e)}}function u(e){return function(t){let r=s();return(0,a.Y)(e,{...t,designSystemThemeApi:r})}}},27321:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n={blue100:"#F0F8FF",blue200:"#D7EDFE",blue300:"#BAE1FC",blue400:"#8ACAFF",blue500:"#4299E0",blue600:"#2272B4",blue700:"#0E538B",blue800:"#04355D",brown:"#A6630C",coral:"#C83243",green100:"#F3FCF6",green200:"#D4F7DF",green300:"#B1ECC5",green400:"#8DDDA8",green500:"#3CAA60",green600:"#277C43",green700:"#115026",green800:"#093919",grey050:"#F6F7F9",grey100:"#E8ECF0",grey200:"#D1D9E1",grey300:"#C0CDD8",grey350:"#92A4B3",grey400:"#8396A5",grey500:"#5F7281",grey600:"#445461",grey650:"#37444F",grey700:"#1F272D",grey800:"#11171C",indigo:"#434A93",lemon:"#FACB66",lime:"#308613",pink:"#B45091",purple:"#8A63BF",red100:"#FFF5F7",red200:"#FDE2E8",red300:"#FBD0D8",red400:"#F792A6",red500:"#E65B77",red600:"#C82D4C",red700:"#9E102C",red800:"#630316",teal:"#04867D",turquoise:"#137DAE",white:"#FFFFFF",yellow100:"#FFF9EB",yellow200:"#FCEACA",yellow300:"#F8D4A5",yellow400:"#F2BE88",yellow500:"#DE7921",yellow600:"#BE501E",yellow700:"#93320B",yellow800:"#5F1B02"}},58331:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o});var n=r(27321);let o={actionDangerDefaultBackgroundDefault:"rgba(200, 45, 76, 0.0000)",actionDangerDefaultBackgroundHover:"rgba(200, 45, 76, 0.0800)",actionDangerDefaultBackgroundPress:"rgba(200, 45, 76, 0.1600)",actionDangerDefaultBorderDefault:n.s.red600,actionDangerDefaultBorderHover:n.s.red700,actionDangerDefaultBorderPress:n.s.red800,actionDangerDefaultTextDefault:n.s.red600,actionDangerDefaultTextHover:n.s.red700,actionDangerDefaultTextPress:n.s.red800,actionDangerPrimaryBackgroundDefault:n.s.red600,actionDangerPrimaryBackgroundHover:n.s.red700,actionDangerPrimaryBackgroundPress:n.s.red800,actionDangerPrimaryText:n.s.white,actionDefaultBackgroundDefault:"rgba(34, 114, 180, 0.0000)",actionDefaultBackgroundHover:"rgba(34, 114, 180, 0.0800)",actionDefaultBackgroundPress:"rgba(34, 114, 180, 0.1600)",actionDefaultBorderDefault:n.s.grey300,actionDefaultBorderFocus:n.s.blue600,actionDefaultBorderHover:n.s.blue600,actionDefaultBorderPress:n.s.blue800,actionDefaultIconDefault:n.s.grey600,actionDefaultIconHover:n.s.blue700,actionDefaultIconPress:n.s.blue800,actionDefaultTextDefault:n.s.grey800,actionDefaultTextHover:n.s.blue700,actionDefaultTextPress:n.s.blue800,actionDisabledBackground:n.s.grey100,actionDisabledBorder:n.s.grey200,actionDisabledText:n.s.grey350,actionIconBackgroundDefault:"rgba(34, 114, 180, 0.0000)",actionIconBackgroundHover:"rgba(34, 114, 180, 0.0800)",actionIconBackgroundPress:"rgba(34, 114, 180, 0.1600)",actionIconIconDefault:n.s.grey600,actionIconIconHover:n.s.blue700,actionIconIconPress:n.s.blue800,actionLinkDefault:n.s.blue600,actionLinkHover:n.s.blue700,actionLinkPress:n.s.blue800,actionPrimaryBackgroundDefault:n.s.blue600,actionPrimaryBackgroundHover:n.s.blue700,actionPrimaryBackgroundPress:n.s.blue800,actionPrimaryIcon:n.s.white,actionPrimaryTextDefault:n.s.white,actionPrimaryTextHover:n.s.white,actionPrimaryTextPress:n.s.white,actionTertiaryBackgroundDefault:"rgba(34, 114, 180, 0.0000)",actionTertiaryBackgroundHover:"rgba(34, 114, 180, 0.0800)",actionTertiaryBackgroundPress:"rgba(34, 114, 180, 0.1600)",actionTertiaryIconDefault:n.s.blue600,actionTertiaryIconHover:n.s.blue700,actionTertiaryIconPress:n.s.blue800,actionTertiaryTextDefault:n.s.blue600,actionTertiaryTextHover:n.s.blue700,actionTertiaryTextPress:n.s.blue800,backgroundDanger:n.s.red100,backgroundPrimary:n.s.white,backgroundSecondary:n.s.grey050,backgroundSuccess:n.s.green100,backgroundWarning:n.s.yellow100,border:n.s.grey200,borderAccessible:n.s.grey500,borderDanger:n.s.red300,borderWarning:n.s.yellow300,codeBackground:"rgba(68, 83, 95, 0.0800)",overlayOverlay:"rgba(64, 63, 63, 0.7200)",progressFill:n.s.grey300,progressTrack:n.s.grey100,skeleton:"rgba(144, 164, 181, 0.1600)",tableBackgroundSelectedDefault:"rgba(68, 83, 95, 0.0800)",tableBackgroundSelectedHover:"rgba(68, 83, 95, 0.1200)",tableBackgroundUnselectedDefault:n.s.white,tableBackgroundUnselectedHover:"rgba(68, 83, 95, 0.0400)",textPlaceholder:n.s.grey500,textPrimary:n.s.grey800,textSecondary:n.s.grey500,textValidationDanger:n.s.red600,textValidationSuccess:n.s.green600,textValidationWarning:n.s.yellow600,tooltipBackgroundTooltip:n.s.grey800}},72646:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,Gi:()=>i,Az:()=>a,aH:()=>o});let n={classnamePrefix:"du-bois",iconfontCssPrefix:"anticon",borderRadiusBase:4,borderWidth:1,heightSm:32,heightBase:40,iconSize:24,iconFontSize:16,buttonHeight:40,buttonInnerHeight:40-2*r(86168).Ay.sm-2},o="31, 39, 45",a="0, 0, 0",i=e=>{if(e)return{shadowLow:`0px 4px 16px rgba(${a}, 0.12)`,shadowHigh:`0px 8px 24px rgba(${a}, 0.2);`};return{shadowLow:`0px 4px 16px rgba(${o}, 0.12)`,shadowHigh:`0px 8px 24px rgba(${o}, 0.2)`}},l=n},90579:(e,t,r)=>{"use strict";r.d(t,{O:()=>_});let n={borderRadius0:0,borderRadiusSm:4,borderRadiusMd:8,borderRadiusLg:12,borderRadiusFull:999},o={borderRadiusMd:4,borderRadiusLg:8},a=()=>o,i=()=>n,l={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1600},s={xs:"@media (max-width: 575.98px)",sm:`@media (min-width: ${l.sm}px)`,md:`@media (min-width: ${l.md}px)`,lg:`@media (min-width: ${l.lg}px)`,xl:`@media (min-width: ${l.xl}px)`,xxl:`@media (min-width: ${l.xxl}px)`},u={breakpoints:l,mediaQueries:s};var c=r(27321);let d={tagBackgroundBrown:"rgba(171, 86, 2, 0.1000)",tagBackgroundCharcoal:c.s.grey300,tagBackgroundCoral:"rgba(240, 0, 64, 0.1000)",tagBackgroundDefault:"rgba(232, 236, 240, 0.0800)",tagBackgroundIndigo:"rgba(1, 68, 255, 0.1200)",tagBackgroundLemon:"rgba(255, 191, 1, 0.1800)",tagBackgroundLime:"rgba(2, 179, 2, 0.0800)",tagBackgroundPink:"rgba(240, 1, 150, 0.1200)",tagBackgroundPurple:"rgba(59, 0, 255, 0.1200)",tagBackgroundTeal:"rgba(2, 192, 150, 0.1200)",tagBackgroundTurquoise:"rgba(2, 192, 213, 0.1200)",tagIconBrown:"#DAB594",tagIconCharcoal:c.s.grey650,tagIconCoral:"#FF859D",tagIconDefault:c.s.grey350,tagIconIndigo:"#99A2FF",tagIconLemon:"#FFCD4D",tagIconLime:"#70D083",tagIconPink:"#F986C9",tagIconPurple:"#B399FF",tagIconTeal:"#0AD8B6",tagIconTurquoise:"#1FD0F3",tagTextBrown:"#DAB594",tagTextCharcoal:c.s.grey800,tagTextCoral:"#FF859D",tagTextDefault:c.s.grey100,tagTextIndigo:"#99A2FF",tagTextLemon:"#FFCD4D",tagTextLime:"#70D083",tagTextPink:"#F986C9",tagTextPurple:"#B399FF",tagTextTeal:"#0AD8B6",tagTextTurquoise:"#1FD0F3"},g={tagBackgroundBrown:"rgba(171, 86, 2, 0.0800)",tagBackgroundCharcoal:c.s.grey650,tagBackgroundCoral:"rgba(240, 0, 64, 0.0600)",tagBackgroundDefault:"rgba(0, 0, 59, 0.0500)",tagBackgroundIndigo:"rgba(1, 68, 255, 0.0600)",tagBackgroundLemon:"rgba(255, 191, 1, 0.1800)",tagBackgroundLime:"rgba(2, 179, 2, 0.0800)",tagBackgroundPink:"rgba(240, 1, 150, 0.0600)",tagBackgroundPurple:"rgba(59, 0, 255, 0.0500)",tagBackgroundTeal:"rgba(2, 192, 150, 0.0900)",tagBackgroundTurquoise:"rgba(2, 192, 213, 0.0900)",tagIconBrown:"#815E46",tagIconCharcoal:c.s.grey350,tagIconCoral:"#CA244E",tagIconDefault:c.s.grey600,tagIconIndigo:"#3451B2",tagIconLemon:"#915830",tagIconLime:"#297C3B",tagIconPink:"#C41C87",tagIconPurple:"#5746AF",tagIconTeal:"#067A6F",tagIconTurquoise:"#0C7792",tagTextBrown:"#815E46",tagTextCharcoal:c.s.grey100,tagTextCoral:"#CA244E",tagTextDefault:c.s.grey800,tagTextIndigo:"#3451B2",tagTextLemon:"#915830",tagTextLime:"#227534",tagTextPink:"#C41C87",tagTextPurple:"#5746AF",tagTextTeal:"#00776B",tagTextTurquoise:"#00708D"},f={actionDangerDefaultBackgroundDefault:"rgba(247, 146, 166, 0.0000)",actionDangerDefaultBackgroundHover:"rgba(247, 146, 166, 0.0800)",actionDangerDefaultBackgroundPress:"rgba(247, 146, 166, 0.1600)",actionDangerDefaultBorderDefault:c.s.red500,actionDangerDefaultBorderHover:c.s.red400,actionDangerDefaultBorderPress:c.s.red300,actionDangerDefaultTextDefault:c.s.red500,actionDangerDefaultTextHover:c.s.red400,actionDangerDefaultTextPress:c.s.red300,actionDangerPrimaryBackgroundDefault:c.s.red500,actionDangerPrimaryBackgroundHover:c.s.red400,actionDangerPrimaryBackgroundPress:c.s.red300,actionDangerPrimaryText:c.s.grey800,actionDefaultBackgroundDefault:"rgba(138, 202, 255, 0.0000)",actionDefaultBackgroundHover:"rgba(138, 202, 255, 0.0800)",actionDefaultBackgroundPress:"rgba(138, 202, 255, 0.1600)",actionDefaultBorderDefault:c.s.grey650,actionDefaultBorderFocus:c.s.blue400,actionDefaultBorderHover:c.s.blue400,actionDefaultBorderPress:c.s.blue300,actionDefaultIconDefault:c.s.grey350,actionDefaultIconHover:c.s.blue400,actionDefaultIconPress:c.s.blue300,actionDefaultTextDefault:c.s.grey100,actionDefaultTextHover:c.s.blue400,actionDefaultTextPress:c.s.blue300,actionDisabledBackground:c.s.grey650,actionDisabledBorder:c.s.grey600,actionDisabledText:c.s.grey500,actionIconBackgroundDefault:"rgba(255, 255, 255, 0.0000)",actionIconBackgroundHover:"rgba(255, 255, 255, 0.0800)",actionIconBackgroundPress:"rgba(255, 255, 255, 0.1600)",actionIconIconDefault:c.s.grey350,actionIconIconHover:c.s.grey300,actionIconIconPress:c.s.grey200,actionLinkDefault:c.s.blue500,actionLinkHover:c.s.blue400,actionLinkPress:c.s.blue300,actionPrimaryBackgroundDefault:c.s.blue500,actionPrimaryBackgroundHover:c.s.blue400,actionPrimaryBackgroundPress:c.s.blue300,actionPrimaryIcon:c.s.grey800,actionPrimaryTextDefault:c.s.grey800,actionPrimaryTextHover:c.s.grey800,actionPrimaryTextPress:c.s.grey800,actionTertiaryBackgroundDefault:"rgba(143, 205, 255, 0.0000)",actionTertiaryBackgroundHover:"rgba(143, 205, 255, 0.0800)",actionTertiaryBackgroundPress:"rgba(143, 205, 255, 0.1600)",actionTertiaryIconDefault:c.s.blue500,actionTertiaryIconHover:c.s.blue400,actionTertiaryIconPress:c.s.blue300,actionTertiaryTextDefault:c.s.blue500,actionTertiaryTextHover:c.s.blue400,actionTertiaryTextPress:c.s.blue300,backgroundDanger:"rgba(200, 45, 76, 0.1600)",backgroundPrimary:c.s.grey800,backgroundSecondary:c.s.grey700,backgroundSuccess:"rgba(39, 124, 67, 0.1600)",backgroundWarning:"rgba(190, 80, 30, 0.1600)",border:c.s.grey650,borderAccessible:c.s.grey300,borderDanger:c.s.red500,borderWarning:c.s.yellow500,codeBackground:c.s.grey650,overlayOverlay:"rgba(31, 38, 45, 0.7200)",progressFill:c.s.grey500,progressTrack:c.s.grey650,skeleton:"rgba(144, 164, 181, 0.1600)",tableBackgroundSelectedDefault:"rgba(189, 205, 219, 0.0800)",tableBackgroundSelectedHover:"rgba(189, 205, 219, 0.1200)",tableBackgroundUnselectedDefault:c.s.grey800,tableBackgroundUnselectedHover:"rgba(189, 205, 219, 0.0400)",textPlaceholder:c.s.grey400,textPrimary:c.s.grey100,textSecondary:c.s.grey350,textValidationDanger:c.s.red500,textValidationSuccess:c.s.green500,textValidationWarning:c.s.yellow500,tooltipBackgroundTooltip:c.s.grey050};var b=r(58331);let E={ai:{gradientStart:"#4299E0",gradientMid:"#CA42E0",gradientEnd:"#FF5F46"}},p={...f,...c.s,branded:E},m={...b.Q,...c.s,branded:E},T={primary:c.s.blue600,charcoal:c.s.grey600,radioInteractiveAvailable:c.s.blue600,radioInteractiveHover:"#186099",radioInteractivePress:"#0D4F85",radioDisabled:"#A2AEB8",radioDefaultBorder:"#64727D",radioDefaultBackground:"#FFFFFF",radioInteractiveHoverSecondary:"rgba(34, 115, 181, 0.08)",radioInteractivePressSecondary:"rgba(34, 115, 181, 0.16)"},h={backgroundValidationDanger:b.Q.backgroundDanger,backgroundValidationSuccess:b.Q.backgroundSuccess,backgroundValidationWarning:b.Q.backgroundWarning,borderDecorative:b.Q.border,borderValidationDanger:b.Q.borderDanger,borderValidationWarning:b.Q.borderWarning,tableRowHover:b.Q.tableBackgroundUnselectedHover,textValidationInfo:b.Q.textSecondary,typographyCodeBg:b.Q.codeBackground,tagBrown:c.s.brown,tagCharcoal:c.s.grey600,tagCoral:c.s.coral,tagDefault:c.s.grey100,tagHover:"rgba(34, 114, 180, 0.0800)",tagIconHover:c.s.grey600,tagIconPress:c.s.grey600,tagIndigo:c.s.indigo,tagInverse:c.s.grey800,tagLemon:c.s.lemon,tagLime:c.s.lime,tagPink:c.s.pink,tagPress:"rgba(34, 114, 180, 0.1600)",tagPurple:c.s.purple,tagTeal:c.s.teal,tagText:c.s.white,tagTurquoise:c.s.turquoise},v={backgroundValidationDanger:f.backgroundDanger,backgroundValidationSuccess:f.backgroundSuccess,backgroundValidationWarning:f.backgroundWarning,borderDecorative:f.border,borderValidationDanger:f.borderDanger,borderValidationWarning:f.borderWarning,tableRowHover:f.tableBackgroundUnselectedHover,textValidationInfo:f.textSecondary,typographyCodeBg:f.codeBackground,tagBrown:"rgba(166, 99, 12, 0.8600)",tagCharcoal:"rgba(68, 83, 95, 0.8600)",tagCoral:"rgba(200, 50, 67, 0.8600)",tagDefault:c.s.grey650,tagHover:"rgba(138, 202, 255, 0.0800)",tagIconHover:c.s.grey350,tagIconPress:c.s.grey350,tagIndigo:"rgba(67, 74, 147, 0.8600)",tagInverse:c.s.grey800,tagLemon:"rgba(250, 203, 102, 0.8600)",tagLime:"rgba(48, 134, 19, 0.8600)",tagPink:"rgba(180, 80, 145, 0.8600)",tagPress:"rgba(138, 202, 255, 0.1600)",tagPurple:"rgba(138, 99, 191, 0.8600)",tagTeal:"rgba(4, 134, 125, 0.8600)",tagText:c.s.grey100,tagTurquoise:"rgba(19, 125, 174, 0.8600)"};function y(e){return{...T,...e?v:h,...e?p:m}}var S=r(72646);let I="rgba(0, 0, 0, 0.45)",A="rgba(0, 0, 0, 0.05)",k=e=>e?{xs:`0px 1px 0px 0px ${I}`,sm:` 0px 2px 3px -1px ${I}, 0px 1px 0px 0px rgba(0, 0, 0, 0.26)`,md:`0px 3px 6px 0px ${I}`,lg:"0px 2px 16px 0px rgba(0, 0, 0, 0.61)",xl:"0px 8px 40px 0px rgba(0, 0, 0, 0.87)"}:{xs:`0px 1px 0px 0px ${A}`,sm:` 0px 2px 3px -1px ${A}, 0px 1px 0px 0px rgba(0, 0, 0, 0.02)`,md:`0px 3px 6px 0px ${A}`,lg:"0px 2px 16px 0px rgba(0, 0, 0, 0.08)",xl:"0px 8px 40px 0px rgba(0, 0, 0, 0.13)"};var w=r(86168);let C={fontSizeSm:12,fontSizeBase:13,fontSizeMd:13,fontSizeLg:18,fontSizeXl:22,fontSizeXxl:32,lineHeightSm:"16px",lineHeightBase:"20px",lineHeightMd:"20px",lineHeightLg:"24px",lineHeightXl:"28px",lineHeightXxl:"40px",typographyRegularFontWeight:400,typographyBoldFontWeight:600},D={enableAnimation:!1,zIndexBase:1e3};function _(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:D;return{colors:y(e),gradients:function(e){let t=y(e);return{aiBorderGradient:`linear-gradient(135deg, ${t.branded.ai.gradientStart} 20.5%, ${t.branded.ai.gradientMid} 46.91%, ${t.branded.ai.gradientEnd} 79.5%)`}}(e),spacing:w.Ay,general:{...S.Ay,...(0,S.Gi)(e)},shadows:k(e),typography:C,legacyBorders:a(),borders:i(),responsive:u,isDarkMode:e,options:t,DU_BOIS_INTERNAL_ONLY:{colors:e?d:g}}}},86168:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,JZ:()=>n});let n=8,o={xs:4,sm:8,md:16,lg:24};o.lg,o.md,o.sm,o.sm,o.xs,o.md,o.sm,o.sm,o.xs,o.lg,o.xs,o.xs,o.xs,o.xs;let a=o},83902:(e,t,r)=>{"use strict";r.d(t,{CW:()=>f,iT:()=>b});var n=r(84559),o=r(10322),a=r(41858),i=r(19073);function l(){let e=(0,i.W)("com.databricks.alert_utils.hostsWithAlertUtilsDisabled","[]");return(0,a.o)(window.location.hostname,e,o.Es.UiSvc)||""!==function(){var e;let t=null===(e=window.__DATABRICKS_CONFIG__)||void 0===e?void 0:e.enablePrPreview;return"string"==typeof t&&""!==t.trim()?String(t):""}()}var s=r(37067),u=r(97402),c=r(36827);function d(){var e;return!!(null===(e=window.__DATABRICKS_CONFIG__)||void 0===e?void 0:e.isCuttingEdge)}var g=r(71907);let f="codeMonCounter";class b{static stringifyBlob(e,t){if(null===e)return"null";if(void 0===e)return"undefined";if("string"==typeof e)return e;if(b.isError(e))return b.jsonStringifyWithJavaScriptTypes({message:e.message,stack:e.stack,WARNING:"!!! YOU WILL NEED TO FOLLOW http://go/AlertUtils-stack-traces TO DE-MINIFY THIS ERROR !!!"});if(b.isStringRecord(e))return b.jsonStringifyWithJavaScriptTypes(e);return(0,s.y)("frontendAlertUtils",{alertSeverity:n.Op.Sev2,esComponent:o.Es.UiInfra,loadedUiVersions:b.getLoadedUiVersionsString(),eventId:"Unexpected debugBlob type",silenced:!1},JSON.stringify({alertEventId:t,unexpectedDebugBlob:e})),"WARNING: unexpected debugBlob type - contact #eng-product-infra for help"}static getLastAlertTimeLocalStorageKey(e,t){return`alertUtilsLastAlertTime-${e}-${t}`}static getLastAlertTime(e,t){let r=function(e,t){try{var r;return null!==(r=window.localStorage.getItem(e))&&void 0!==r?r:"-1"}catch(e){return"-1"}}(b.getLastAlertTimeLocalStorageKey(e,t),0);if(null!==r){let e=parseInt(r,10);if(!Number.isNaN(e))return e}return -1}static shouldThrottleAlert(e,t){let r=b.getLastAlertTime(e,t),n=b.getLastAlertTimeLocalStorageKey(e,t),o=Date.now();if(r>0&&o-r<12e4)return(0,s.y)("clientsideEvent",{eventId:"alertUtilsThrottled",eventType:n}),!0;return!function(e,t){try{window.localStorage.setItem(e,t)}catch(e){}}(n,o.toString()),!1}static isStringRecord(e){if("object"==typeof e&&null!==e){for(let t in e)if("string"!=typeof t)return!1;return!0}return!1}static jsonStringifyWithJavaScriptTypes(e){return JSON.stringify(e,(e,t)=>{if(null===t)return"null";let r=typeof t;if("object"===r||"number"===r||"boolean"===r)return t;return String(t)})}static isError(e){return"[object Error]"===Object.prototype.toString.call(e)}static prepareError(e){if(b.isError(e))return{message:e.message,stack:e.stack,WARNING:"!!! YOU WILL NEED TO FOLLOW http://go/AlertUtils-stack-traces TO DE-MINIFY THIS ERROR !!!"};return e}static sev1(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];b.alert(n.Op.Sev1,e,t,r,o)}static sev2(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];b.alert(n.Op.Sev2,e,t,r,o)}static sev3(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];b.alert(n.Op.Sev3,e,t,r,o)}static donotusethis_sev0BurnRate(e,t,r,o,a){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"";b.logBurnRateEvent(e,n.Op.Sev0,t,r,o,a,i,!1)}static sev1BurnRate(e,t,r,o,a){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",l=arguments.length>6&&void 0!==arguments[6]&&arguments[6];b.logBurnRateEvent(e,n.Op.Sev1,t,r,o,a,i,l)}static sev2BurnRate(e,t,r,o,a){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",l=arguments.length>6&&void 0!==arguments[6]&&arguments[6],s=arguments.length>7&&void 0!==arguments[7]&&arguments[7];b.logBurnRateEvent(e,n.Op.Sev2,t,r,o,a,i,l,s)}static sev3BurnRate(e,t,r,o,a){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",l=arguments.length>6&&void 0!==arguments[6]&&arguments[6];b.logBurnRateEvent(e,n.Op.Sev3,t,r,o,a,i,l)}static getLoadedUiVersionsString(){return void 0===window.getLoadedUiVersions?"`window.getLoadedUiVersions` not defined":JSON.stringify(window.getLoadedUiVersions())}static getIsSilenced(e){let t=(0,i.W)("com.databricks.alert_utils.silencedEventIdRegexes","[]");return(0,a.o)(e,t,o.Es.UiSvc)}static alert(e,t,r,n,o){if(l())return;let a=b.stringifyBlob(n,r),i=b.getIsSilenced(r);(0,s.y)("frontendAlertUtils",{alertSeverity:e,esComponent:t,fireInDev:o,loadedUiVersions:b.getLoadedUiVersionsString(),eventId:r,silenced:i,isCuttingEdge:d()},a),b.recordAlertUtilInteractionPhasesProto(t,e,r,o,i,void 0),console.warn(`AlertUtils ${e} alert: eventId=${r}, debugBlob=${a}`)}static logBurnRateEvent(e,t,r,n,o,a,i,u){let c=arguments.length>8&&void 0!==arguments[8]&&arguments[8];if(l()||c&&b.shouldThrottleAlert(e,r))return;let g=b.stringifyBlob(i,r),f=b.getIsSilenced(r);if((0,s.y)("frontendAlertUtilsBurnRate",{alertSeverity:t,esComponent:e,fireInDev:u,loadedUiVersions:b.getLoadedUiVersionsString(),eventId:r,sloTarget:n,minFailures:o,success:a,silenced:f,isCuttingEdge:d()},g),b.recordAlertUtilInteractionPhasesProto(e,t,r,u,f,{sloTarget:n,minFailures:o,isSuccess:a}),!a){let e=""===g?"":`, debugBlob=${g}`;console.warn(`AlertUtils burn rate failure event: ${r}${e}`)}}static log(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";(0,s.y)(f,{esComponent:t,loadedUiVersions:b.getLoadedUiVersionsString(),eventId:e},b.stringifyBlob(r,e))}static sev2IfThrows(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"";try{return{value:r(),error:null}}catch(r){if(b.isError(r))b.sev2(e,`sev2IfThrows-${t} uncaught exception`,r,o);else if("string"==typeof r){if("out of memory"===r){if(!n){var i;b.sev2(e,`sev2IfThrows-${t} uncaught "out of memory" string exception`,{error:r,jsHeapSizeLimit:null===(i=performance.memory)||void 0===i?void 0:i.jsHeapSizeLimit,debugBlob:a},o)}}else b.sev2(e,`sev2IfThrows-${t} uncaught string exception`,r,o)}else b.sev2(e,`sev2IfThrows-${t} uncaught error of non-Error type`,r,o);return{value:null,error:r}}}static getResult(e){try{return{value:e(),error:null}}catch(e){return{value:null,error:e}}}static recordAlertUtilInteractionPhasesProto(e,t,r,n,o,a){let i=a?{severity:E[t],slo_target:m[a.sloTarget],min_failure:p[a.minFailures],fire_in_dev:n,silenced:o,alert_type:c.WT.BURN_RATE}:{severity:E[t],fire_in_dev:n,silenced:o,alert_type:c.WT.IMMEDIATE},l={type:c.Vj.INTERACTION,sub_type:c.jj.ALERT_UTILS,entity_id:r},s=Date.now(),d=g.n.generateUuidV4();(0,u.F)({frontend_log_event_id:d,inferred_timestamp_millis:s,context:{client_context:{es_component:e}},entry:{observability_log:{type:c.AX.INTERACTION_PHASE,client_source:c.Ye.ALERT_UTILS,entity:l,payload:{interaction_phase_payload:{name:a?"alert_utils_burn_rate_start":"alert_utils_start",type:c.jq.START,status:c.Qf.SUCCESS,alerting_spec:i}}}}}),(0,u.F)({frontend_log_event_id:d,inferred_timestamp_millis:s,context:{client_context:{es_component:e}},entry:{observability_log:{type:c.AX.INTERACTION_PHASE,client_source:c.Ye.ALERT_UTILS,entity:l,payload:{interaction_phase_payload:{name:a?"alert_utils_burn_rate_complete":"alert_utils_complete",type:c.jq.COMPLETE,status:null!=a&&a.isSuccess?c.Qf.SUCCESS:c.Qf.FAILURE,alerting_spec:i}}}}})}}let E={[n.Op.Sev0]:c.bI.SEV_0,[n.Op.Sev1]:c.bI.SEV_1,[n.Op.Sev2]:c.bI.SEV_2,[n.Op.Sev3]:c.bI.SEV_3},p={[n.Ip.Min0]:c.yW.MIN_0,[n.Ip.Min10]:c.yW.MIN_10,[n.Ip.Min100]:c.yW.MIN_100},m={[n.i1.P95]:c.Rl.P95,[n.i1.P99]:c.Rl.P99,[n.i1.P999]:c.Rl.P999,[n.i1.P9999]:c.Rl.P9999}},84559:(e,t,r)=>{"use strict";r.d(t,{Ip:()=>a,Op:()=>n,i1:()=>o});let n=function(e){return e.Sev0="sev0",e.Sev1="sev1",e.Sev2="sev2",e.Sev3="sev3",e}({}),o=function(e){return e.P95="P95",e.P99="P99",e.P999="P999",e.P9999="P9999",e}({}),a=function(e){return e.Min0="Min0",e.Min10="Min10",e.Min100="Min100",e}({})},10322:(e,t,r)=>{"use strict";r.d(t,{Es:()=>n});let n=function(e){return e.AccessMgmt="LegacyDataAccess.TableACLs",e.Assistant="Workspace.Assistant",e.Autocomplete="Workspace.Autocomplete",e.Autoreload="Notebook.PythonExecution",e.Brickindex="VectorSearch.Brickindex",e.Brickstore="DBSQLX.Brickstore",e.CleanRoom="LakehouseCollaboration.CleanRoom",e.ClustersUi="Clusters.UI",e.CustomerPlatform="AdminExp.AccountConsole",e.DNC="Notebook.DirectNotebookChannel",e.DabsAuthoring="DeveloperEcosystem.DabsAuthoring",e.DataExplorer="DBSQLX.CatalogExplorer",e.DataRooms="Lakeview.DataRooms",e.DbsqlUi="ServingInfra.DBSQLUIService",e.DeltaSharing="LakehouseCollaboration.DeltaSharing",e.DevEx="AppPlatform.DevEx",e.Editor="Workspace.Editor",e.FastFeedback="ZZZ Deprecated - Fast Feedback",e.FileBrowser="DBSQLX.FileBrowser",e.GPU="ModelTraining.GPURuntime",e.GenAIFrontend="MosaicClassicTraining.MosaicMLConsoles",e.GqlInfra="ServingInfra.GraphQLService",e.Ingestion="Ingestion.IngestionConnectorFramework",e.LakehouseApps="LakehouseApps.Apps",e.Lakeview="Lakeview.Dashboards",e.LibraryUI="Workspace.LibrariesUI",e.Marketplace="LakehouseCollaboration.Marketplace",e.MlData="MLData.LakehouseMonitoring",e.MlExperiments="MLflow.ExperimentRunTracking",e.MlFeatureStore="FeatureStore.MLFeatureStore",e.MlFlow="MLflow.Models",e.MlFlowOSS="MLflow.MLflowOSS",e.MlModelServing="ModelServing.MLModelServing",e.MlRagEval="RAG Studio.RAGEval",e.ModelRegistry="MLflow.ModelRegistry",e.Navigation="WorkspaceExp.Navigation",e.Notebooks="Notebook.Notebooks",e.NotebookVariableExplorer="Notebook.VariableExplorer",e.OAuth="Authentication.OAuth",e.Onboarding="WorkspaceExp.Onboarding",e.PartnerConnect="PartnerEco.PartnerConnect",e.Pipelines="LakeFlow.Pipelines.Execution",e.Preferences="WorkspaceExp.Preferences",e.ProductionVisibility="AppPlatform.ProductionVisibility",e.Pylsp="Notebook.PythonLSP",e.PythonAutoComplete="Notebook.PythonAutoComplete",e.PythonDebugger="Notebook.PythonDebugger",e.PythonDiagnostics="Notebook.PythonDiagnostics",e.PythonGoToDefinition="Notebook.PythonGoToDefinition",e.QueryEditor="DBSQLX.QueryEditor",e.QueryHistory="DBSQLX.QueryHistory",e.QueryProfile="DBSQLX.QueryProfile",e.RedashCore="DBSQLX.RedashCoreUI",e.Repos="Workspace.Repos",e.Search="Search.WorkspaceSearch",e.ServerlessRepl="Notebook.PythonExecution",e.SignInSignUp="EnduserExp.Sign-in & Sign-Up",e.StorageConsole="StoragePlatform.DatabaseBestPractices",e.StoragePlatformDbInfra="StoragePlatform.DatabaseInfra",e.UiInfra="UIPlatform.UIInfra",e.UiObservability="UIPlatform.UIObservability",e.UiSvc="ServingInfra.UIService",e.UserAuthenticationAndManagement="IdentityMgmt.InternalServices",e.Visualizations="Lakeview.Visualizations",e.WarehouseUI="DBSQLX.WareHouseUI",e.WebTerminal="Workspace.WebTerminal",e.Widgets="Notebook.PythonExecution",e.WorkflowsAuthoring="Workflows.Authoring.UI",e.WorkflowsAuthoringDABs="Workflows.Authoring.DABs",e.WorkflowsObservabilityAcls="Workflows.Observability.ACLs",e.WorkflowsObservabilityAlerts="Workflows.Observability.Alerts",e.WorkflowsObservabilityGraphs="Workflows.Observability.Graphs",e.WorkflowsObservabilityLists="Workflows.Observability.Lists",e.WorkflowsObservabilityMetrics="Workflows.Observability.Metrics",e.WorkflowsObservabilityNotifications="Workflows.Observability.Notifications",e.WorkflowsObservabilitySystemTables="Workflows.Observability.SystemTables",e.WorkflowsOrchestration="Workflows.Orchestration.Other",e.WorkflowsOrchestrationConditionals="Workflows.Orchestration.Conditionals",e.WorkflowsOrchestrationDependencies="Workflows.Orchestration.Dependencies",e.WorkflowsOrchestrationNotebookWorkflows="Workflows.Orchestration.NotebookWorkflows",e.WorkflowsOrchestrationParameters="Workflows.Orchestration.Parameters",e.WorkflowsOrchestrationQueueing="Workflows.Orchestration.Queuing",e.WorkflowsServiceBackgroundCompute="Workflows.Service.BackgroundCompute",e.WorkflowsServiceScheduleButton="Workflows.Service.ScheduleButton",e.WorkflowsServiceTasks="Workflows.Tasks.Other",e.WorkflowsStability="Workflows.Stability.Outage",e.WorkflowsTasksDbt="Workflows.Tasks.DBT",e.WorkflowsTasksDlt="Workflows.Tasks.DLT",e.WorkflowsTasksForEach="Workflows.Tasks.ForEach",e.WorkflowsTasksIfElseCondition="Workflows.Tasks.IfElseCondition",e.WorkflowsTasksIngestion="Workflows.Tasks.Ingestion",e.WorkflowsTasksJar="Workflows.Tasks.Jar",e.WorkflowsTasksNotebook="Workflows.Tasks.Notebook",e.WorkflowsTasksOther="Workflows.Tasks.Other",e.WorkflowsTasksPython="Workflows.Tasks.Python",e.WorkflowsTasksRunJob="Workflows.Tasks.RunJob",e.WorkflowsTasksSparkSubmit="Workflows.Tasks.SparkSubmit",e.WorkflowsTasksSql="Workflows.Tasks.SQL",e.WorkflowsTriggersContinuous="Workflows.Triggers.Continuous",e.WorkflowsTriggersFile="Workflows.Triggers.File",e.WorkflowsTriggersRunNow="Workflows.Triggers.RunNow",e.WorkflowsTriggersRunSubmit="Workflows.Triggers.RunSubmit",e.WorkflowsTriggersSchedule="Workflows.Triggers.Schedule",e.WorkflowsTriggersTable="Workflows.Triggers.Table",e}({})},41858:(e,t,r)=>{"use strict";r.d(t,{o:()=>o});var n=r(83902);function o(e,t,r){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];return(function(e,t){let r=[];try{r=JSON.parse(e)}catch(r){return n.iT.sev2(t,"getStringArray: failed to parse rawFlagValue",`rawFlagValue=${e}, exception=${r}`),[]}if(!Array.isArray(r))return n.iT.sev2(t,"getStringArray: rawFlagValue is not an array",`rawFlagValue=${e}`),[];if(r.some(e=>"string"!=typeof e))return n.iT.sev2(t,"getStringArray: found non-string item",`rawFlagValue=${e}`),[];return r})(t,r).concat(o).some(t=>null==e?void 0:e.match(t))}},74270:(e,t,r)=>{"use strict";function n(){return!!window.__DATABRICKS_NOTEBOOK_MODEL}r.d(t,{Yz:()=>n}),r(85318)},19073:(e,t,r)=>{"use strict";r.d(t,{W:()=>E});var n,o=r(63707);function a(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let i={},l=0;class s{constructor(e){a(this,"stack",[]),a(this,"available",0),a(this,"missing",0),a(this,"children",[]),this.stack=e}getOrCreateNode(e,t){if(0===e.length)return this;let r=this.children.find(t=>t.stack[0]===e[0]);if(!r){if(!t)return;return r=new s(e),this.children.push(r),r}let n=r.stack.findIndex((t,r)=>t!==e[r]);if(-1===n)return r.getOrCreateNode(e.slice(r.stack.length),t);if(!t)return;return r.split(n),r.getNode(e.slice(n))}getNode(e){return this.getOrCreateNode(e,!0)}peekNode(e){return this.getOrCreateNode(e,!1)}split(e){let t=new s(this.stack.slice(e));t.available=this.available,t.missing=this.missing,t.children=this.children,this.children=[t],this.missing=0,this.available=0,this.stack=this.stack.slice(0,e)}toImplicitContextUsageStack(){return this.generateImplicitContextUsageStack(1,void 0).stacks}generateImplicitContextUsageStack(e,t){if(this.missing>1||this.available>1)throw Error(`Stack trie node had more than 1 missing (${this.missing}) or available (${this.available}) counts.`);let r=e,n=[{stack_id:r,parent_stack_id:t,stack:this.stack.join("\n"),context_was_available:this.available>0,context_was_missing:this.missing>0}];for(let t of this.children){let{lastId:o,stacks:a}=t.generateImplicitContextUsageStack(e+1,r);if(o<e)throw Error("Stack trie last ID cannot be less than the current next ID");e=o,n=n.concat(a)}return{lastId:e,stacks:n}}}var u=r(65848),c=r.n(u);r(21340);let d=new WeakMap,g=(0,u.createContext)({get current(){return"undefined"!=typeof Zone?Zone.root:void 0}});var f=r(74270),b=r(33724);function E(e,t){if((0,o.isImplicitContextEnabled)()&&function(e,t){var r,n,a,s;if(!(0,o.isWebpackRuntimeBootstrapped)()||"undefined"==typeof Zone)return;let u=Zone.current.get("implicitContextTrackerContext");if((null==u?void 0:u.type)==="contextlessTask"||t)return;null!==(r=i[e])&&void 0!==r||(i[e]={});let c=null!==(n=Error(e).stack)&&void 0!==n?n:"";if(!i[e][c]){if(c.includes("describeNativeComponentFrame")||l>1e4)return;if(1e4===l){l++;return}null!==(s=(a=i[e])[c])&&void 0!==s||(a[c]={useCase:e,stack:c,available:!1,missing:!1}),l++}i[e][c]&&(i[e][c].missing=!0)}("SAFEX",void 0!==function(e){var t,r;let n=null!==(t=null===(r=function(e){if(!function(){if(!1===window.__databricks_allow_context_read||function(e){if(null===e)return!0;let t=d.get(e);return void 0===t&&(t=["useCallback","useState","useMemo","useEffect"].every(t=>e[t]===e.useContext),d.set(e,t)),t}(c().__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher.current))return!1;return!0}())return;return c().__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher.current.readContext(e)}(g))||void 0===r?void 0:r.current)&&void 0!==t?t:"undefined"!=typeof Zone?Zone.current:void 0;return null==n?void 0:n.get(e)}("configurationContext")),b.e.isAvailable())return b.e.call({flagName:e,defaultValue:t});return(0,f.Yz)()||window.STORIES||console.warn("safex("+e+") not loaded."),t}window.__debug__safex=null!==(n=window.__debug__safex)&&void 0!==n?n:E},63707:(e,t,r)=>{"use strict";let n;function o(){if(void 0===n){var e,t;let r="databricks.fe.infra.enableImplicitContext",o=null===(e=localStorage)||void 0===e?void 0:e.getItem(r);n=null!=o?"true"===o:(null===(t=window.__DATABRICKS_SAFE_FLAGS__)||void 0===t?void 0:t[r])===!0}return n}r.r(t),r.d(t,{isImplicitContextEnabled:()=>o,isWebpackRuntimeBootstrapped:()=>i,markWebpackRuntimeBootstrapped:()=>l});let a=!1;function i(){return a}function l(){a=!0}},26827:(e,t,r)=>{"use strict";r.d(t,{fH:()=>u,WW:()=>c,af:()=>a});var n=r(65848);let o=r(34448).m;function a(){return void 0!==l()}function i(){let e=l();if(void 0===e)throw Error("RPC API is not defined.");return e}function l(){var e,t;return null!==(e=window.__databricks_mfe_rpc)&&void 0!==e?e:null===(t=o())||void 0===t?void 0:t.__databricks_mfe_rpc}function s(e,t){let r=1;return{isAvailable:function(){return a()&&i().hasHandlerFor(e)},call:function(t){return i().makeCall(e,t)},register:t,useRegister:function(e){let{enabled:o=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=(0,n.useRef)();void 0===a.current&&(a.current=r++);let i=(0,n.useRef)(0);(0,n.useEffect)(()=>{if(!o)return;let r=t(e,{debugLabel:`componentId=${a.current},registerId=${i.current}`});return i.current++,r},[e,o])}}}function u(e){return s(e,(t,r)=>i().registerHandler(e,t,r))}function c(e){return s(e,(t,r)=>i().registerSyncHandler(e,t,r))}},33724:(e,t,r)=>{"use strict";r.d(t,{e:()=>n});let n=(0,r(26827).WW)("Flags::GetFlagValueSync")},35035:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=[]},37067:(e,t,r)=>{"use strict";r.d(t,{y:()=>l});let n=(0,r(26827).fH)("Observability::RecordEvent");var o=r(35035);function a(e,t,r,n,o,a,i){try{var l=e[a](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function l(e){a(i,n,o,l,s,"next",e)}function s(e){a(i,n,o,l,s,"throw",e)}l(void 0)})}}function l(e,t,r){return s.apply(this,arguments)}function s(){return(s=i(function*(e,t,r){var a;let l=(a=i(function*(){yield n.call({metric:e,tags:{...t},eventData:r})}),function(){return a.apply(this,arguments)});if(!n.isAvailable()){o.J.push(l);return}yield l()})).apply(this,arguments)}},97402:(e,t,r)=>{"use strict";r.d(t,{F:()=>u,w:()=>l});let n=(0,r(26827).fH)("Observability::RecordProto");var o=r(35035);function a(e,t,r,n,o,a,i){try{var l=e[a](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function l(e){a(i,n,o,l,s,"next",e)}function s(e){a(i,n,o,l,s,"throw",e)}l(void 0)})}}function l(e){return s.apply(this,arguments)}function s(){return(s=i(function*(e){var t;let r=(t=i(function*(){yield n.call({entry:e})}),function(){return t.apply(this,arguments)});if(!n.isAvailable()){o.J.push(r);return}yield r()})).apply(this,arguments)}function u(e){return c.apply(this,arguments)}function c(){return(c=i(function*(e){var t;let r=(t=i(function*(){yield n.call(e)}),function(){return t.apply(this,arguments)});if(!n.isAvailable()){o.J.push(r);return}yield r()})).apply(this,arguments)}},36827:(e,t,r)=>{"use strict";r.d(t,{AX:()=>i,Qf:()=>s,Rl:()=>c,Vj:()=>n,WT:()=>g,Ye:()=>a,bI:()=>u,jj:()=>o,jq:()=>l,yW:()=>d});let n=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.PAGE="PAGE",e.COMPONENT="COMPONENT",e.INTERACTION="INTERACTION",e}({}),o=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.ALERT="ALERT",e.BANNER="BANNER",e.BUTTON="BUTTON",e.CHECKBOX="CHECKBOX",e.DIALOG_COMBOBOX="DIALOG_COMBOBOX",e.DIV="DIV",e.DROPDOWN_MENU_CHECKBOX_ITEM="DROPDOWN_MENU_CHECKBOX_ITEM",e.DROPDOWN_MENU_ITEM="DROPDOWN_MENU_ITEM",e.DROPDOWN_MENU_RADIO_GROUP="DROPDOWN_MENU_RADIO_GROUP",e.FILE_INPUT="FILE_INPUT",e.FORM="FORM",e.INITIAL_LOAD="INITIAL_LOAD",e.INPUT="INPUT",e.INTERACTION="INTERACTION",e.LINK="LINK",e.MEASUREMENT="MEASUREMENT",e.MODAL="MODAL",e.NAVIGATION="NAVIGATION",e.NOTIFICATION="NOTIFICATION",e.PILL_CONTROL="PILL_CONTROL",e.RADIO_GROUP="RADIO_GROUP",e.SEGMENTED_CONTROL_GROUP="SEGMENTED_CONTROL_GROUP",e.SIMPLE_SELECT="SIMPLE_SELECT",e.SWITCH="SWITCH",e.TABS="TABS",e.TAG="TAG",e.TEXT_AREA="TEXT_AREA",e.TOGGLE_BUTTON="TOGGLE_BUTTON",e.TYPOGRAPHY_LINK="TYPOGRAPHY_LINK",e.CONTEXT_MENU_CHECKBOX_ITEM="CONTEXT_MENU_CHECKBOX_ITEM",e.CONTEXT_MENU_ITEM="CONTEXT_MENU_ITEM",e.CONTEXT_MENU_RADIO_GROUP="CONTEXT_MENU_RADIO_GROUP",e.LEGACY_SELECT="LEGACY_SELECT",e.PAGINATION="PAGINATION",e.REACT_SELECT="REACT_SELECT",e.ACCORDION="ACCORDION",e.CARD="CARD",e.DRAWER_CONTENT="DRAWER_CONTENT",e.PREVIEW_CARD="PREVIEW_CARD",e.TABLE_HEADER="TABLE_HEADER",e.TOOLTIP="TOOLTIP",e.TYPEAHEAD_COMBOBOX="TYPEAHEAD_COMBOBOX",e.POPOVER="POPOVER",e.RADIO="RADIO",e.ALERT_UTILS="ALERT_UTILS",e.PANEL_BOUNDARY="PANEL_BOUNDARY",e}({}),a=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.ADHOC="ADHOC",e.DESIGN_SYSTEM="DESIGN_SYSTEM",e.UNIFIED_ROUTER="UNIFIED_ROUTER",e.LEGACY_ROUTER="LEGACY_ROUTER",e.REACT_INTERACTION_TRACING="REACT_INTERACTION_TRACING",e.ALERT_UTILS="ALERT_UTILS",e}({}),i=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.PAGE_VIEW="PAGE_VIEW",e.COMPONENT_CLICK="COMPONENT_CLICK",e.COMPONENT_VALUE_CHANGE="COMPONENT_VALUE_CHANGE",e.COMPONENT_VIEW="COMPONENT_VIEW",e.INTERACTION_PHASE="INTERACTION_PHASE",e.FORM_SUBMIT="FORM_SUBMIT",e.CLIPBOARD_CUT="CLIPBOARD_CUT",e.CLIPBOARD_COPY="CLIPBOARD_COPY",e.CLIPBOARD_PASTE="CLIPBOARD_PASTE",e.COMPONENT_ZOOM="COMPONENT_ZOOM",e}({}),l=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.START="START",e.IN_PROGRESS="IN_PROGRESS",e.COMPLETE="COMPLETE",e}({}),s=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.SUCCESS="SUCCESS",e.FAILURE="FAILURE",e.CANCELLED="CANCELLED",e.INTERRUPTED="INTERRUPTED",e.TIMED_OUT="TIMED_OUT",e}({}),u=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.SEV_0="SEV_0",e.SEV_1="SEV_1",e.SEV_2="SEV_2",e.SEV_3="SEV_3",e}({}),c=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.P95="P95",e.P99="P99",e.P999="P999",e.P9999="P9999",e}({}),d=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.MIN_0="MIN_0",e.MIN_10="MIN_10",e.MIN_100="MIN_100",e}({}),g=function(e){return e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.IMMEDIATE="IMMEDIATE",e.BURN_RATE="BURN_RATE",e.LOG_ONLY="LOG_ONLY",e}({})},11656:(e,t,r)=>{"use strict";var n;r.r(t),r.d(t,{ReactVersionSettings:()=>i,log:()=>l,useReact18:()=>o,useReact18NewAPI:()=>a});let o=!0,a=!!(localStorage.hasOwnProperty("useReact18NewAPI")?"true"===localStorage.getItem("useReact18NewAPI"):null===(n=window.__DATABRICKS_SAFE_FLAGS__)||void 0===n?void 0:n["databricks.fe.infra.useReact18NewAPI"]),i={useReact18:o,useReact18NewAPI:a,setReact18:e=>{},setReact18NewAPI:e=>{if(e)try{localStorage.setItem("useReact18NewAPI","true")}catch{}else localStorage.removeItem("useReact18NewAPI");i.useReact18NewAPI=e},isVerboseLog:!!localStorage.getItem("react18VerboseLog"),setVerboseLog:e=>{if(e)try{localStorage.setItem("react18VerboseLog","true")}catch{}else localStorage.removeItem("react18VerboseLog");i.isVerboseLog=e}};function l(){if(!i.isVerboseLog)return;for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log("[react18]",...t)}l.warn=function(){if(!i.isVerboseLog)return;for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.warn("[react18]",...t)}},71907:(e,t,r)=>{"use strict";r.d(t,{n:()=>n});class n{static getRandomIdentifier(){return Math.floor(1e10*Math.random()).toString()}static generateUUID(){let e=Math.random().toString(36).substring(2,10),t=new Date().getTime().toString();return`${t}${e}`}static generateUuidV4(){var e,t;let r=null===(e=window.crypto)||void 0===e||null===(t=e.randomUUID)||void 0===t?void 0:t.call(e);if(r)return r;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}static getRandomHexString(e){return Array.from(window.crypto.getRandomValues(new Uint32Array(Math.ceil(e/8)))).map(e=>e.toString(16)).join("").substring(0,e)}}},34448:(e,t,r)=>{"use strict";function n(){try{let e=window.top;return null==e||e.document,e}catch(e){return null}}r.d(t,{m:()=>n})},8617:(e,t,r)=>{"use strict";let n,o,a,i;r.r(t);var l,s=r(65848),u=r.n(s),c=r(8155),d=r.n(c),g=r(14671),f=r(39659);let b=function(e){return e.AutoLaunchAndAttach="autoLaunchAndAttach",e.ConfirmCommandDelete="confirmCommandDelete",e.DefaultPythonIndentUnit="defaultPythonIndentUnit",e.EditorFontSize="editorFontSize",e.EnableAutoTriggerAssistantQuickFix="enableAutoInlineQuickFix",e.EnableAutoTriggerAssistantQuickFixOptOut="enableAutoInlineQuickFixOptOut",e.EnableAutoTriggerDiagnose="enableAutoTriggerDiagnose",e.EnableAutoTriggerDiagnoseOptOut="enableAutoTriggerDiagnoseOptOut",e.EnableCenteredLayout="enableCenteredLayout",e.EnableClassicShortcuts="enableClassicShortcuts",e.EnableCommandNumbers="enableCommandNumbers",e.EnableCompletionsAsYouType="enableCompletionsAsYouType",e.EnableCursorBlink="enableCursorBlink",e.EnableEditorCustomDarkTheme="editorCustomDarkTheme",e.EnableEditorCustomLightTheme="editorCustomLightTheme",e.EnableDarkMode="enableDarkMode",e.EnableEnterAcceptsSuggestions="enableEnterAcceptsSuggestions",e.EnableHoverForEditor="enableHoverForEditor",e.EnableInlineSuggestionAsYouType="enableInlineSuggestionAsYouType",e.EnableInlineSuggestionAsYouTypeOptOut="enableInlineSuggestionAsYouTypeOptOut",e.EnableLineNumbers="enableLineNumbers",e.EnableLineNumbersV2="enableLineNumbersV2",e.EnableLineWrap="enableLineWrap",e.EnableMonacoMultiCursor="enableMonacoMultiCursor",e.EnableNotebookDebuggerOptOut="enableNotebookDebuggerOptOut",e.EnableSchemaBrowserSearchInTreeFormatKey="databricks-schema-browser-search-in-tree-format",e.EnableShowCommandStatsInNewCellUi="enableShowCommandStatsInNewCellUi",e.EnableShowPromotedCellTitles="enableShowPromotedCellTitles",e.EnableSimpleHomepage="enableSimpleHomepage",e.EnableSmartQuotes="enableSmartQuotes",e.EnableSoftTabsForNotebooksAndFiles="enableSoftTabsForNotebooksAndFiles",e.EnableSqlSyntaxErrorHighlighting="enableSqlSyntaxErrorHighlighting",e.EnableSwitchToCommandModeAfterExecution="enableSwitchToCommandModeAfterExecution",e.EnableVegaLiteRenderer="enableVegaLiteRenderer",e.EnableVegaLiteRendererOptOut="enableVegaLiteRendererOptOut",e.EnableVegaLiteRendererPreviewTag="enableVegaLiteRendererPreviewTag",e.ExtendedBottomPanel="extendedBottomPanel",e.LakeFlowPipelinesMultifileAuthoring="pipelinesMultifileAuthoring",e.NotebookDefaultExportFormat="notebookDefaultExportFormat",e.OptionArrowShortcutMode="optionArrowShortcutMode",e.SendCommentEmailNotifications="sendCommentEmailNotifications",e.ShowSparkAdvisor="showSparkAdvisor",e.ShowSparkErrorTips="showSparkErrorTips",e.UnifiedSqlEditorCreateAndConvertQueriesOnOpenKey="databricks-unified-sql-editor-create-and-convert-queries-on-open-pref",e.UserActivatesPythonFormatErrorHighlighting="userActivatesPythonFormatErrorHighlighting",e.UserActivatesPythonSyntaxErrorHighlightingOptOut="userActivatesPythonSyntaxErrorHighlightingOptOut",e.UserActivatesPythonTypeChecking="userActivatesPythonTypeChecking",e.UserActivatesServerlessSnapshotRestore="userActivatesSnapshotRestore",e}({});var E=r(83902),p=r(10322),m=r(84559),T=r(37067),h=r(21340),v=r.n(h),y=r(25656);let S=["string","number","boolean","bigint"];var I=r(19073);function A(e){let t=`__databricks:${e}`;function r(e){let r=t=>{v()(t instanceof CustomEvent&&t.type.startsWith("__databricks"),"Expected custom event to be delivered"),e(t.detail,t)};return window.addEventListener(t,r),()=>{window.removeEventListener(t,r)}}return{dispatch:function(e){let r=new CustomEvent(t,{detail:function e(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(null==t||S.includes(typeof t)||(0,y.isDate)(t))return t;if(r.has(t))throw Error("Cannot sanitize circular structure");r.add(t);try{if(t instanceof AbortSignal||t instanceof Blob||t instanceof FormData)return t;if(t instanceof Headers)return new Headers(t);if(t instanceof ReadableStream||t instanceof ArrayBuffer||ArrayBuffer.isView(t))return t;if(t instanceof URLSearchParams)return new URLSearchParams(t);if(Array.isArray(t))return t.map(t=>e(t,r));if("function"==typeof t)return function(t){let r=function(){let r=[];for(let t=0;t<arguments.length;t++)r[t]=e(arguments[t]);return e(t(...r))};return Object.defineProperty(r,"name",{value:`${t.name}__sanitized`,writable:!1}),r}(t);if((0,y.isPlainObject)(t)){let n={};return Object.keys(t).forEach(o=>{n[o]=e(t[o],r)}),n}throw Error("Unsupported value type encountered while sanitizing during RPC call")}finally{r.delete(t)}}(e),bubbles:!1,cancelable:!0});if(!(0,I.W)("databricks.fe.infra.batchUpdatesWhenDispatchEvent",!1))return window.dispatchEvent(r);{let e=!0;return(0,c.unstable_batchedUpdates)(()=>e=window.dispatchEvent(r)),e}},listen:r,useListener:function(e){(0,s.useEffect)(()=>r(e),[e])}}}let k="dark-mode",w="light-mode",C="system",D="databricks-dark-mode-pref",_=A("designsystem::localStorageUserPrefChanged"),P=window.matchMedia("(prefers-color-scheme: dark)");function O(){var e;let t=null!==(e=ei(b.EnableDarkMode))&&void 0!==e?e:function(e,t){try{return window.localStorage.getItem(e)}catch(r){return E.iT.sev2BurnRate(p.Es.UiObservability,"get-local-storage-item",m.i1.P99,m.Ip.Min10,!1,{key:e,error:"Error getting local storage item"}),t}}(D,C)||C;if("system"!==t&&"dark"!==t&&"light"!==t)return C;return t}function x(e){(0,T.y)("clientsideEvent",{eventType:"sessionColorScheme",eventName:e})}var N=r(74270),R=r(33724);function L(e,t){var r;if(R.e.isAvailable())return R.e.call({flagName:e,defaultValue:t});if((0,N.Yz)()||console.warn("confx("+e+") not loaded."),null!==(r=window.location)&&void 0!==r&&null!==(r=r.host)&&void 0!==r&&r.startsWith("dev.local"))throw Error(`Cannot call confx(${e}) before app init is complete`);return t}window.__debug__confx=null!==(l=window.__debug__confx)&&void 0!==l?l:L;var B=r(41858);let F=new Set,M=new Set,U=new Set,W={confMigratedMatch:F,confMigratedMismatch:M,confMigratedNewValueError:U,confMigratedOtherError:U},H=e=>{if((0,I.W)("databricks.fe.conf_migrated.enableServingNewValue",!1))return void 0===n&&(n=(0,I.W)("databricks.fe.conf_migrated.confsToServeNewValue","[]")),(0,B.o)(e,n,p.Es.GqlInfra)},V=(e,t,r)=>{let n=(t,r,n)=>{let o=W[t];window.recordEvent&&!o.has(e)&&(window.recordEvent("clientsideEvent",{eventName:t,settingName:e,...null!=n?n:{}},r),o.add(e))};try{let o,a;try{o=r()}catch(e){e instanceof Error&&(a=e),o=t}let i=`confKey: ${e}
newValueThunk: ${r}

oldValue: ${JSON.stringify(t)}, oldValueType: ${typeof t}
newValue: ${JSON.stringify(o)}, newValueType ${typeof o}`;if(void 0===t&&H(e))return o;if(a)n("confMigratedNewValueError",a.toString());else if(Y(t,o))Y(t,o)&&n("confMigratedMatch");else{let e="boolean"==typeof o&&!!t===o,r=""===o&&void 0===t,a={...e&&{errorName:"coercedOldValueIsNewBooleanValue"},...r&&{errorName:"coercedOldValueIsNewStringValue"}};n("confMigratedMismatch",i,a)}if(H(e))return o}catch(e){e instanceof Error?n("confMigratedOtherError",e.toString()):n("confMigratedOtherError")}return t},Y=(e,t)=>{if("object"==typeof e&&"object"==typeof t)return(0,y.isEqual)(e,t);return e===t};var $=r(34448);let G=function(e,t){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=`${e}_v${t}`;if(r){var i,l,s;let e=function(){var e,t,r,n,i,l,s,u;let c=(0,I.W)("databricks.fe.wexp.enableSpogRequestsMiddleware",!1)||(null!==(l=a)&&void 0!==l?l:a=null!==(s=null===(u=window.__DATABRICKS_CONFIG__)||void 0===u?void 0:u.isSpogDomain)&&void 0!==s?s:["spog-proxy-fake.dev.databricks.com","spog.dev.local"].includes(window.location.hostname)||!1)?new URL(document.location.href).searchParams.get("o"):void 0,d=null!==(e=null===(t=window.settings)||void 0===t?void 0:t.orgId)&&void 0!==e?e:null===(r=(0,$.m)())||void 0===r||null===(r=r.settings)||void 0===r?void 0:r.orgId,g=null!==(n=c||d)&&void 0!==n?n:o;return(0,I.W)("databricks.fe.wexp.preferOrgIdFromWindowSettings",!1)&&(g=null!==(i=d||c)&&void 0!==i?i:o),null!=g&&""!==g?String(g):void 0}(),t=null!==(i=null===(l=window.settings)||void 0===l?void 0:l.user)&&void 0!==i?i:null===(s=(0,$.m)())||void 0===s||null===(s=s.settings)||void 0===s?void 0:s.user;return`${n}_user_${t}_org_${e}`}return n},Q=[b.EnableAutoTriggerAssistantQuickFix,b.EnableAutoTriggerAssistantQuickFixOptOut,b.EnableAutoTriggerDiagnose,b.EnableAutoTriggerDiagnoseOptOut,b.UnifiedSqlEditorCreateAndConvertQueriesOnOpenKey],z="-default",K=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e===D)return D;if(Q.includes(e))return`${G(e,1,!0)}${t?z:""}`;let r=L("user",""),n=V("orgId",L("orgId",0),()=>L("orgId_new",0)),o="unknown";return r&&n&&(o=`${r}-${n}`),`${o}-global-${e}${t?z:""}`},j=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let n=K(e,r),o=localStorage.getItem(n);if("string"!=typeof o)return t;try{return JSON.parse(o)}catch(e){return null!=o?o:t}}catch(e){}return t};function X(e,t){return j(e,j(e,t,!0))}let q=(e,t)=>t?e:void 0;b.DefaultPythonIndentUnit,b.EnableClassicShortcuts,b.EnableDarkMode,b.EnableEditorCustomDarkTheme,b.EnableEditorCustomLightTheme,b.EnableMonacoMultiCursor,b.NotebookDefaultExportFormat,b.OptionArrowShortcutMode,b.EditorFontSize,b.AutoLaunchAndAttach,b.ConfirmCommandDelete,b.DefaultPythonIndentUnit,b.EditorFontSize,b.EnableAutoTriggerAssistantQuickFix,b.EnableAutoTriggerAssistantQuickFixOptOut,b.EnableAutoTriggerDiagnose,b.EnableAutoTriggerDiagnoseOptOut,b.EnableCenteredLayout,b.EnableClassicShortcuts,b.EnableCommandNumbers,b.EnableCompletionsAsYouType,b.EnableCursorBlink,b.EnableEditorCustomDarkTheme,b.EnableEditorCustomLightTheme,b.EnableDarkMode,b.EnableEnterAcceptsSuggestions,b.EnableHoverForEditor,b.EnableInlineSuggestionAsYouType,b.EnableInlineSuggestionAsYouTypeOptOut,b.EnableLineNumbers,b.EnableLineNumbersV2,b.EnableLineWrap,b.EnableMonacoMultiCursor,b.EnableNotebookDebuggerOptOut,b.EnableSchemaBrowserSearchInTreeFormatKey,b.EnableShowCommandStatsInNewCellUi,b.EnableShowPromotedCellTitles,b.EnableSimpleHomepage,b.EnableSmartQuotes,b.EnableSoftTabsForNotebooksAndFiles,b.EnableSqlSyntaxErrorHighlighting,b.EnableSwitchToCommandModeAfterExecution,b.EnableVegaLiteRenderer,b.EnableVegaLiteRendererOptOut,b.EnableVegaLiteRendererPreviewTag,b.ExtendedBottomPanel,b.LakeFlowPipelinesMultifileAuthoring,b.NotebookDefaultExportFormat,b.OptionArrowShortcutMode,b.SendCommentEmailNotifications,b.ShowSparkAdvisor,b.ShowSparkErrorTips,b.UnifiedSqlEditorCreateAndConvertQueriesOnOpenKey,b.UserActivatesPythonFormatErrorHighlighting,b.UserActivatesPythonSyntaxErrorHighlightingOptOut,b.UserActivatesPythonTypeChecking,b.UserActivatesServerlessSnapshotRestore;let J=()=>{var e;return{[b.AutoLaunchAndAttach]:X(b.AutoLaunchAndAttach,!1),[b.ConfirmCommandDelete]:X(b.ConfirmCommandDelete,!0),[b.DefaultPythonIndentUnit]:X(b.DefaultPythonIndentUnit,""),[b.EditorFontSize]:X(b.EditorFontSize,"13"),[b.EnableAutoTriggerAssistantQuickFix]:X(b.EnableAutoTriggerAssistantQuickFix,!1),[b.EnableAutoTriggerAssistantQuickFixOptOut]:X(b.EnableAutoTriggerAssistantQuickFixOptOut,!1),[b.EnableAutoTriggerDiagnose]:X(b.EnableAutoTriggerDiagnose,!1),[b.EnableAutoTriggerDiagnoseOptOut]:X(b.EnableAutoTriggerDiagnoseOptOut,!1),[b.EnableCenteredLayout]:X(b.EnableCenteredLayout,!0),[b.EnableClassicShortcuts]:X(b.EnableClassicShortcuts,"default"),[b.EnableCommandNumbers]:X(b.EnableCommandNumbers,!0),[b.EnableCompletionsAsYouType]:X(b.EnableCompletionsAsYouType,!0),[b.EnableCursorBlink]:X(b.EnableCursorBlink,!0),[b.EnableDarkMode]:(e="system",j(b.EnableDarkMode,j(D,j(b.EnableDarkMode,e,!0)))),[b.EnableEditorCustomDarkTheme]:X(b.EnableEditorCustomDarkTheme,"defaultTheme"),[b.EnableEditorCustomLightTheme]:X(b.EnableEditorCustomLightTheme,"defaultTheme"),[b.EnableEnterAcceptsSuggestions]:X(b.EnableEnterAcceptsSuggestions,!0),[b.EnableHoverForEditor]:X(b.EnableHoverForEditor,!0),[b.EnableInlineSuggestionAsYouType]:X(b.EnableInlineSuggestionAsYouType,!1),[b.EnableInlineSuggestionAsYouTypeOptOut]:X(b.EnableInlineSuggestionAsYouTypeOptOut,!0),[b.EnableLineNumbers]:X(b.EnableLineNumbers,!0),[b.EnableLineNumbersV2]:X(b.EnableLineNumbersV2,!1),[b.EnableLineWrap]:X(b.EnableLineWrap,!0),[b.EnableMonacoMultiCursor]:X(b.EnableMonacoMultiCursor,"alt"),[b.EnableNotebookDebuggerOptOut]:X(b.EnableNotebookDebuggerOptOut,!1),[b.EnableSchemaBrowserSearchInTreeFormatKey]:X(b.EnableSchemaBrowserSearchInTreeFormatKey,!0),[b.EnableShowCommandStatsInNewCellUi]:X(b.EnableShowCommandStatsInNewCellUi,!1),[b.EnableShowPromotedCellTitles]:X(b.EnableShowPromotedCellTitles,!1),[b.EnableSimpleHomepage]:X(b.EnableSimpleHomepage,!1),[b.EnableSmartQuotes]:X(b.EnableSmartQuotes,!0),[b.EnableSoftTabsForNotebooksAndFiles]:X(b.EnableSoftTabsForNotebooksAndFiles,!1),[b.EnableSqlSyntaxErrorHighlighting]:X(b.EnableSqlSyntaxErrorHighlighting,!0),[b.EnableSwitchToCommandModeAfterExecution]:X(b.EnableSwitchToCommandModeAfterExecution,!1),[b.EnableVegaLiteRenderer]:X(b.EnableVegaLiteRenderer,!1),[b.EnableVegaLiteRendererOptOut]:X(b.EnableVegaLiteRendererOptOut,!0),[b.EnableVegaLiteRendererPreviewTag]:X(b.EnableVegaLiteRendererPreviewTag,!1),[b.ExtendedBottomPanel]:X(b.ExtendedBottomPanel,!1),[b.LakeFlowPipelinesMultifileAuthoring]:X(b.LakeFlowPipelinesMultifileAuthoring,!1),[b.NotebookDefaultExportFormat]:X(b.NotebookDefaultExportFormat,"JUPYTER"),[b.OptionArrowShortcutMode]:X(b.OptionArrowShortcutMode,"navigate"),[b.SendCommentEmailNotifications]:X(b.SendCommentEmailNotifications,!0),[b.ShowSparkAdvisor]:X(b.ShowSparkAdvisor,!0),[b.ShowSparkErrorTips]:X(b.ShowSparkErrorTips,!0),[b.UnifiedSqlEditorCreateAndConvertQueriesOnOpenKey]:X(b.UnifiedSqlEditorCreateAndConvertQueriesOnOpenKey,!1),[b.UserActivatesPythonFormatErrorHighlighting]:X(b.UserActivatesPythonFormatErrorHighlighting,!1),[b.UserActivatesPythonSyntaxErrorHighlightingOptOut]:X(b.UserActivatesPythonSyntaxErrorHighlightingOptOut,!0),[b.UserActivatesPythonTypeChecking]:X(b.UserActivatesPythonTypeChecking,!1),[b.UserActivatesServerlessSnapshotRestore]:X(b.UserActivatesServerlessSnapshotRestore,!1)}};var Z=r(26827),ee=r(32669);let et=function(e,t){let r=(0,Z.WW)(`MfeVariable::GetValue::${e}`),n=A(`MfeVariable::changed::${e}`);return{useProvider:function(e){let[t,o]=(0,s.useState)(e),a=(0,s.useRef)(t);r.useRegister(()=>t);let i=(0,s.useCallback)(e=>{o(e)},[]);return(0,s.useEffect)(()=>{a.current!==t&&(a.current=t,n.dispatch(t))},[t]),i},useProviderStore:function(e,t){r.useRegister(e),(0,s.useEffect)(()=>t(()=>{n.dispatch(e())}),[e,t])},useValue:function(){let e=(0,s.useMemo)(()=>{let e;return e=null,function(t,r){if(null!==e&&r(e.value,t))return e.value;return e={value:t},t}},[]),o=(0,s.useCallback)(()=>{if(!r.isAvailable())return;return e(r.call(),t)},[e]);return(0,ee.useSyncExternalStore)(n.listen,o)},registerProvider:function(e){let t=e(),o=[r.register(()=>t)];return{unregister(){o.forEach(e=>e())},set:e=>{t=e(t),n.dispatch(t)}}},registerProviderStore:function(e,t){let o=[r.register(e),t(()=>n.dispatch(e()))];return()=>{o.forEach(e=>e())}},get:function(){return r.call()},listen:function(e){return n.listen(e)}}}("user_preferences",y.isEqual),er=(0,Z.WW)("Preferences::UpdatePreferencesV2"),en={},eo=()=>{let e=et.useValue(),t=(0,s.useCallback)(e=>{es(e)},[]);return[null!=e?e:J(),t]},ea=e=>{var t;let[r,n]=eo();return[null!==(t=en[e])&&void 0!==t?t:r[e],(0,s.useCallback)(t=>{if(void 0!==en[e])return;n({[e]:t})},[n,e])]},ei=e=>{var t,r;let n;return(0,Z.af)()&&er.isAvailable()&&(n=et.get()),null!==(t=en[e])&&void 0!==t?t:(null!==(r=n)&&void 0!==r?r:J())[e]},el={},es=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,Z.af)()&&er.isAvailable()){if(t){er.call({preferences:e});return}void 0===i?er.call({preferences:e}):(clearTimeout(i),el={...el,...e}),i=setTimeout(()=>{Object.keys(el).length&&er.call({preferences:el}),el={},i=void 0},100)}else throw Error("Preferences cannot be updated when RPC is not supported.")};var eu=r(98358);let ec=e=>"system"===e&&P.matches||"dark"===e,ed=()=>{let e=ec(O());return e?(document.body.classList.contains(w)&&document.body.classList.remove(w),document.body.classList.contains(k)||(x("dark"),document.body.classList.add(k))):(document.body.classList.contains(k)&&document.body.classList.remove(k),document.body.classList.contains(w)||(x("light"),document.body.classList.add(w))),e},eg=e=>{let{disabled:t=!1,forceTheme:r,children:n}=e,[o]=ea(b.EnableDarkMode),[a,i]=(0,s.useState)(ec(null!=o?o:O())),l=(0,s.useCallback)(()=>{if(r)return;i(ed())},[r]);(0,s.useEffect)(()=>(t||(P.addEventListener("change",l),document.body.classList.add("dark-mode-supported")),function(){P.removeEventListener("change",l)}),[t,l]),(0,s.useEffect)(()=>{t||l()},[t,o,l]),_.useListener(l);let u=r?ec(r):a;return(0,eu.Y)(f.SH,{isDarkMode:!t&&u,children:n})},ef=u().lazy(()=>(()=>{if("undefined"!=typeof window&&"object"==typeof performance){let e=window.__dbModuleImportTracking=window.__dbModuleImportTracking||{},t=window.__dbModuleImportTrackingSeen=window.__dbModuleImportTrackingSeen||new Set;if(!t.has(87409)&&void 0===e[87409])return e[87409]={import:"@databricks/web-shared/model-trace-explorer",type:"lazy",loadTime:{start:window.performance.now()}},Promise.all([r.e(54),r.e(307),r.e(632),r.e(381),r.e(513),r.e(409)]).then(r.bind(r,87409)).finally(()=>{e[87409].loadTime.end=window.performance.now()}).then(e=>(t.add(87409),e))}return Promise.all([r.e(54),r.e(307),r.e(632),r.e(381),r.e(513),r.e(409)]).then(r.bind(r,87409))})().then(e=>({default:e.ModelTraceExplorer}))),eb=u().lazy(()=>(()=>{if("undefined"!=typeof window&&"object"==typeof performance){let e=window.__dbModuleImportTracking=window.__dbModuleImportTracking||{},t=window.__dbModuleImportTrackingSeen=window.__dbModuleImportTrackingSeen||new Set;if(!t.has(21799)&&void 0===e[21799])return e[21799]={import:"./telemetryContext",type:"lazy",loadTime:{start:window.performance.now()}},Promise.all([r.e(54),r.e(381),r.e(799)]).then(r.bind(r,21799)).finally(()=>{e[21799].loadTime.end=window.performance.now()}).then(e=>(t.add(21799),e))}return Promise.all([r.e(54),r.e(381),r.e(799)]).then(r.bind(r,21799))})()),eE=()=>(()=>{if("undefined"!=typeof window&&"object"==typeof performance){let e=window.__dbModuleImportTracking=window.__dbModuleImportTracking||{},t=window.__dbModuleImportTrackingSeen=window.__dbModuleImportTrackingSeen||new Set;if(!t.has(97049)&&void 0===e[97049])return e[97049]={import:"@databricks/design-system",type:"lazy",loadTime:{start:window.performance.now()}},Promise.all([r.e(54),r.e(307),r.e(183),r.e(381),r.e(513),r.e(49)]).then(r.bind(r,97049)).finally(()=>{e[97049].loadTime.end=window.performance.now()}).then(e=>(t.add(97049),e))}return Promise.all([r.e(54),r.e(307),r.e(183),r.e(381),r.e(513),r.e(49)]).then(r.bind(r,97049))})(),ep=u().lazy(()=>eE().then(e=>({default:e.DesignSystemProvider}))),em=u().lazy(()=>eE().then(e=>({default:e.ApplyGlobalStyles}))),eT=e=>{let{children:t}=e;return(0,eu.Y)(eg,{children:(0,eu.FD)(ep,{children:[(0,eu.Y)(em,{}),t]})})};d().render((0,eu.Y)(()=>{let[e,t]=(0,s.useState)(null);return(0,s.useEffect)(()=>{let e=e=>{if(e.source!==window.parent)return;"UPDATE_TRACE"===e.data.type&&t(e.data.traceData)};return window.addEventListener("message",e),window.parent.postMessage({type:"READY"}),()=>{window.removeEventListener("message",e)}},[]),window.__databricks_mfe_rpc||Object.defineProperty(window,"__databricks_mfe_rpc",{configurable:!1,writable:!1,value:{makeCall:(e,t)=>{if((null==t?void 0:t.flagName)==="databricks.fe.designsystem.useNewTagColors")return!0;return null==t?void 0:t.defaultValue},hasHandlerFor:()=>!0,registerHandler:()=>{},unregisterHandler:()=>{}}}),(0,eu.Y)(u().Suspense,{fallback:null,children:(0,eu.Y)(eT,{children:(0,eu.Y)(g.Dk,{locale:"en",children:(0,eu.Y)(eb,{children:e&&(0,eu.Y)(ef,{modelTrace:e})})})})})},{}),document.getElementById("root"))},8155:(e,t,r)=>{var n;let o;let{log:a}=r(11656);a(`using react-dom-18${window.__DATABRICKS_REACT_18_2_0_React?" (from global override)":""}`),a("### ReactDOM:",(o=null!==(n=window.__DATABRICKS_REACT_18_2_0_ReactDOM)&&void 0!==n?n:r(97142)).version,o),e.exports=o},65848:(e,t,r)=>{var n;let{isImplicitContextEnabled:o}=r(63707),{log:a}=r(11656),i=null!==(n=window.__DATABRICKS_REACT_18_2_0_React)&&void 0!==n?n:r(987);function l(e,t){return function(){let r=window[e];for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];if(!r)return t(...o);return r(t,...o)}}o()&&(i.useEffect=l("__DATABRICKS_REACT_USE_EFFECT",i.useEffect),i.useLayoutEffect=l("__DATABRICKS_REACT_USE_LAYOUT_EFFECT",i.useLayoutEffect),i.useMemo=l("__DATABRICKS_REACT_USE_MEMO",i.useMemo),i.useCallback=l("__DATABRICKS_REACT_USE_CALLBACK",i.useCallback),i.useState=l("__DATABRICKS_REACT_USE_STATE",i.useState),i.useReducer=l("__DATABRICKS_REACT_USE_REDUCER",i.useReducer)),a("### React:",i.version,i),e.exports=i},60501:(e,t,r)=>{var n;e.exports=null!==(n=window.__DATABRICKS_REACT_18_2_0_JSXRuntime)&&void 0!==n?n:r(86187)},49914:()=>{},14168:()=>{}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/825.cfb2b39f.chunk.js.map