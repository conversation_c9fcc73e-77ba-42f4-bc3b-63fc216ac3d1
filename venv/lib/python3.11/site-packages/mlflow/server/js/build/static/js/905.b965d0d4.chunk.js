"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[905],{28486:function(e,t,n){n.d(t,{tH:function(){return s}});var r=n(31014);function l(e,t,n,r){Object.defineProperty(e,t,{get:n,set:r,enumerable:!0,configurable:!0})}l({},"ErrorBoundary",(()=>s));l({},"ErrorBoundaryContext",(()=>i));const i=(0,r.createContext)(null),o={didCatch:!1,error:null};class s extends r.Component{state=(()=>o)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];e.props.onReset?.({args:r,reason:"imperative-api"}),e.setState(o)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:n}=this.state,{resetKeys:r}=this.props;n&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,n)=>!Object.is(e,t[n])))}(e.resetKeys,r)&&(this.props.onReset?.({next:r,prev:e.resetKeys,reason:"keys"}),this.setState(o))}render(){const{children:e,fallbackRender:t,FallbackComponent:n,fallback:l}=this.props,{didCatch:o,error:s}=this.state;let d=e;if(o){const e={error:s,resetErrorBoundary:this.resetErrorBoundary};if((0,r.isValidElement)(l))d=l;else if("function"===typeof t)d=t(e);else{if(!n)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");d=(0,r.createElement)(n,e)}}return(0,r.createElement)(i.Provider,{value:{didCatch:o,error:s,resetErrorBoundary:this.resetErrorBoundary}},d)}}function d(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function a(){const e=(0,r.useContext)(i);d(e);const[t,n]=(0,r.useState)({error:null,hasError:!1}),l=(0,r.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),n({error:null,hasError:!1})},showBoundary:e=>n({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return l}l({},"useErrorBoundary",(()=>a));function u(e,t){const n=n=>(0,r.createElement)(s,t,(0,r.createElement)(e,n)),l=e.displayName||e.name||"Unknown";return n.displayName=`withErrorBoundary(${l})`,n}l({},"withErrorBoundary",(()=>u))},42864:function(e,t,n){n.r(t),n.d(t,{default:function(){return be}});var r=n(89555),l=n(77520),i=n(3293),o=n(4877),s=n.n(o),d=n(93215),a=n(88464),u=n(88443),c=n(58481),g=n(79085),m=n(85466),v=n(31014),h=n(52350),f=(n(37616),n(56412),n(77484)),p=n(25866);const M=e=>(0,v.useCallback)((async()=>{var t,r,l,i;if(null===e||void 0===e||null===(t=e.info)||void 0===t||!t.model_id||null===e||void 0===e||null===(r=e.info)||void 0===r||!r.artifact_uri)return!0;const o=(0,f.qk)(p.eh,e.info.model_id),s=await(0,f.UQ)(o),d=(await Promise.resolve().then(n.t.bind(n,77948,23))).load(await s.text());return void 0!==(null===d||void 0===d||null===(l=d.signature)||void 0===l?void 0:l.inputs)&&void 0!==(null===d||void 0===d||null===(i=d.signature)||void 0===i?void 0:i.outputs)}),[e]);var Y=n(76010),y=n(50111);const x=e=>{var t;let{loggedModel:n,onSuccess:r}=e;const l=(0,a.A)(),i=(0,v.useCallback)((e=>{var t,n;null===r||void 0===r||r();const i=l.formatMessage({id:"UwpML4",defaultMessage:"Model registered successfully"});Y.A.displayGlobalInfoNotification(`${i} ${null!==(t=null===e||void 0===e||null===(n=e.value)||void 0===n?void 0:n.status)&&void 0!==t?t:""}`)}),[l,r]),o=(0,v.useCallback)((e=>{var t;const n=l.formatMessage({id:"0Rao9q",defaultMessage:"Error registering model"}),r=null!==(t=e instanceof h.s?e.getMessageField():null===e||void 0===e?void 0:e.message)&&void 0!==t?t:String(e);Y.A.displayGlobalErrorNotification(`${n} ${r}`)}),[l]);M(n);return null!==n&&void 0!==n&&null!==(t=n.info)&&void 0!==t&&t.artifact_uri&&n.info.model_id?(0,y.Y)(m.v,{modelPath:n.info.artifact_uri,modelRelativePath:"",disabled:!1,loggedModelId:n.info.model_id,buttonType:"primary",showButton:!0,onRegisterSuccess:i,onRegisterFailure:o}):null};var _=n(77020),w=n(42848),b=n(8986);var R={name:"ozd7xs",styles:"flex-shrink:0"};const E=e=>{var t;let{experimentId:n,experiment:r,loading:o=!1,loggedModel:s,onSuccess:m}=e;const{theme:h}=(0,l.u)(),f=null===s||void 0===s||null===(t=s.info)||void 0===t?void 0:t.name,M=(0,d.Zp)(),Y=(0,a.A)(),{modalElement:E,openModal:I}=(e=>{var t;let{loggedModel:n,onSuccess:r}=e;const[l,o]=(0,v.useState)(!1),s=(0,_.n)({mutationFn:async e=>{let{loggedModelId:t}=e;await(0,b.G)(`/ajax-api/2.0/mlflow/logged-models/${t}`,"DELETE")}}),{mutate:d,isLoading:a,reset:c}=s;return{modalElement:(0,y.FD)(w.d,{componentId:"mlflow.logged_model.details.delete_modal",visible:l,onCancel:()=>o(!1),title:(0,y.Y)(u.A,{id:"0IVF6w",defaultMessage:"Delete logged model"}),okText:(0,y.Y)(u.A,{id:"Wm6dYs",defaultMessage:"Delete"}),okButtonProps:{danger:!0,loading:a},onOk:async()=>{var e;null!==n&&void 0!==n&&null!==(e=n.info)&&void 0!==e&&e.model_id?d({loggedModelId:n.info.model_id},{onSuccess:()=>{null===r||void 0===r||r(),o(!1)}}):o(!1)},cancelText:(0,y.Y)(u.A,{id:"6vII7y",defaultMessage:"Cancel"}),children:[(null===(t=s.error)||void 0===t?void 0:t.message)&&(0,y.FD)(y.FK,{children:[(0,y.Y)(i.FcD,{componentId:"mlflow.logged_model.details.delete_modal.error",closable:!1,message:s.error.message,type:"error"}),(0,y.Y)(w.S,{})]}),(0,y.Y)(u.A,{id:"AzdZPn",defaultMessage:"Are you sure you want to delete this logged model?"})]}),openModal:(0,v.useCallback)((()=>{c(),o(!0)}),[c])}})({loggedModel:s,onSuccess:()=>{M(c.h.getExperimentPageTabRoute(n,p.fM.Models))}}),D=[(0,y.Y)(d.N_,{to:c.h.getExperimentPageTabRoute(n,p.fM.Models),children:r&&"name"in r?null===r||void 0===r?void 0:r.name:n}),(0,y.Y)(d.N_,{to:c.h.getExperimentPageTabRoute(n,p.fM.Models),children:(0,y.Y)(u.A,{id:"Uue10g",defaultMessage:"Models"})})];return(0,y.FD)("div",{css:R,children:[o?(0,y.Y)(C,{}):(0,y.FD)(g.z,{title:(0,y.FD)(y.FK,{children:[(0,y.Y)(A,{}),(0,y.Y)(y.FK,{children:f})]}),dangerouslyAppendEmotionCSS:{h2:{display:"flex",gap:h.spacing.sm}},breadcrumbs:D,children:[(0,y.FD)(i.rId.Root,{children:[(0,y.Y)(i.rId.Trigger,{asChild:!0,children:(0,y.Y)(l.B,{componentId:"mlflow.logged_model.details.more_actions",icon:(0,y.Y)(i.ssM,{}),"aria-label":Y.formatMessage({id:"uB7rmB",defaultMessage:"More actions"})})}),(0,y.Y)(i.rId.Content,{align:"end",children:(0,y.Y)(i.rId.Item,{componentId:"mlflow.logged_model.details.delete_button",onClick:I,children:(0,y.Y)(u.A,{id:"U+2XCK",defaultMessage:"Delete"})})})]}),(0,y.Y)(x,{loggedModel:s,onSuccess:m})]}),E]})},A=()=>{const{theme:e}=(0,l.u)();return(0,y.Y)("div",{css:(0,r.AH)({display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:e.colors.tagDefault,width:e.general.heightSm,height:e.general.heightSm,borderRadius:e.legacyBorders.borderRadiusMd},""),children:(0,y.Y)(i.oiI,{css:(0,r.AH)({color:e.colors.textSecondary},"")})})};var I={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};const C=()=>{const{theme:e}=(0,l.u)();return(0,y.FD)("div",{css:(0,r.AH)({height:2*e.general.heightSm,marginBottom:e.spacing.sm},""),children:[(0,y.Y)("div",{css:(0,r.AH)({height:e.spacing.lg},""),children:(0,y.Y)(i.xUE,{css:(0,r.AH)({width:100,height:e.spacing.md},""),loading:!0})}),(0,y.FD)("div",{css:I,children:[(0,y.FD)("div",{css:(0,r.AH)({display:"flex",gap:e.spacing.sm,marginTop:.5*e.spacing.xs},""),children:[(0,y.Y)(i.xUE,{css:(0,r.AH)({width:e.general.heightSm,height:e.general.heightSm},""),loading:!0}),(0,y.Y)(i.xUE,{css:(0,r.AH)({width:160,height:e.general.heightSm},""),loading:!0})]}),(0,y.FD)("div",{css:(0,r.AH)({display:"flex",gap:e.spacing.sm},""),children:[(0,y.Y)(i.xUE,{css:(0,r.AH)({width:100,height:e.general.heightSm},""),loading:!0}),(0,y.Y)(i.xUE,{css:(0,r.AH)({width:60,height:e.general.heightSm},""),loading:!0})]})]})]})};var D=n(92246);const S=e=>{let{experimentId:t,modelId:n,activeTabName:r}=e;return(0,y.Y)(i.KSe.Root,{children:(0,y.FD)(i.KSe.List,{children:[(0,y.Y)(i.KSe.Item,{active:!r,children:(0,y.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,n),children:(0,y.Y)(u.A,{id:"FBR3Q/",defaultMessage:"Overview"})})},"overview"),(0,y.Y)(i.KSe.Item,{active:"traces"===r,children:(0,y.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,n,"traces"),children:(0,y.Y)(u.A,{id:"TeN9hs",defaultMessage:"Traces"})})},"traces"),(0,y.Y)(i.KSe.Item,{active:"artifacts"===r,children:(0,y.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,n,"artifacts"),children:(0,y.Y)(u.A,{id:"Kwz1fc",defaultMessage:"Artifacts"})})},"artifacts")]})})};var F=n(14830),N=n(4874),B=n(58710),k=n(83858),H=n(67212),L=n(96034),z=n(56928),T=n(9133);var K=n(37368);const U=e=>{var t,n,o,s,d;let{loggedModel:c,onDescriptionChanged:g}=e;const m=null!==(t=null===c||void 0===c||null===(n=c.info)||void 0===n||null===(o=n.tags)||void 0===o||null===(s=o.find((e=>e.key===z.e)))||void 0===s?void 0:s.value)&&void 0!==t?t:void 0,[h,f]=(0,v.useState)(!1),p=(0,a.A)(),{theme:M}=(0,l.u)(),{patch:Y}=(e=>{let{loggedModelId:t}=e;const{isLoading:n,error:r,mutateAsync:l}=(0,_.n)({mutationFn:async e=>{const n={tags:(0,T.entries)(e).map((e=>{let[t,n]=e;return{key:t,value:n}}))};return(0,b.G)(`/ajax-api/2.0/mlflow/logged-models/${t}/tags`,"PATCH",n)}});return{isLoading:n,error:r,patch:l}})({loggedModelId:null===c||void 0===c||null===(d=c.info)||void 0===d?void 0:d.model_id}),{handleError:x}=(0,K.tF)(),w=!m;return(0,y.FD)("div",{css:(0,r.AH)({marginBottom:M.spacing.md},""),children:[(0,y.FD)(l.T.Title,{level:4,css:(0,r.AH)({display:"flex",alignItems:"center",gap:M.spacing.xs},""),children:[(0,y.Y)(u.A,{id:"IB0MR8",defaultMessage:"Description"}),(0,y.Y)(l.B,{componentId:"mlflow.logged_models.details.description.edit",size:"small",type:"tertiary","aria-label":p.formatMessage({id:"7pRcdg",defaultMessage:"Edit description"}),onClick:()=>f(!0),icon:(0,y.Y)(i.R2l,{})})]}),w&&!h&&(0,y.Y)(l.T.Hint,{children:(0,y.Y)(u.A,{id:"npKSv/",defaultMessage:"No description"})}),(!w||h)&&(0,y.Y)(L.V,{defaultMarkdown:m,onSubmit:async e=>{try{await Y({[z.e]:e}),await g(),f(!1)}catch(t){x(t)}},onCancel:()=>f(!1),showEditor:h})]})};var O=n(68109),j=n(65418),G=n(70618),P=n(9856);const $=e=>{var t;let{getValue:n}=e;const{runName:r,runId:l}=null!==(t=n())&&void 0!==t?t:{};return(0,y.Y)(d.N_,{to:c.h.getDirectRunPageRoute(null!==l&&void 0!==l?l:""),children:r||l})};var q=n(28684);const X=e=>{let{getValue:t}=e;const{datasetDigest:n,datasetName:r,runId:l}=t();return r?(0,y.Y)(q.f,{datasetName:r,datasetDigest:n,runId:l}):"-"};var V={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"},Z={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"};const Q=e=>{let{loggedModel:t,relatedRunsData:n,relatedRunsLoading:o}=e;const{theme:s}=(0,l.u)(),d=(0,a.A)(),[c,g]=(0,v.useState)(""),m=(0,v.useMemo)((()=>{var e,r,l;return o?[]:null!==(e=null===t||void 0===t||null===(r=t.data)||void 0===r||null===(l=r.metrics)||void 0===l?void 0:l.map((e=>{var r,l,i;const o=null===n||void 0===n||null===(r=n.find((t=>{var n;return(null===(n=t.info)||void 0===n?void 0:n.runUuid)===e.run_id})))||void 0===r||null===(l=r.info)||void 0===l?void 0:l.runName;return{...e,experimentId:null===(i=t.info)||void 0===i?void 0:i.experiment_id,runName:o}})))&&void 0!==e?e:[]}),[t,o,n]),h=(0,v.useMemo)((()=>m.filter((e=>{let{key:t,dataset_name:n,dataset_digest:r,runName:l}=e;const i=c.toLowerCase();return(null===t||void 0===t?void 0:t.toLowerCase().includes(i))||(null===n||void 0===n?void 0:n.toLowerCase().includes(i))||(null===r||void 0===r?void 0:r.toLowerCase().includes(i))||(null===l||void 0===l?void 0:l.toLowerCase().includes(i))}))),[c,m]),f=(0,v.useMemo)((()=>[{id:"metric",accessorKey:"key",header:d.formatMessage({id:"R/4/8G",defaultMessage:"Metric"}),enableResizing:!0,size:240},{id:"dataset",header:d.formatMessage({id:"lCfyQO",defaultMessage:"Dataset"}),accessorFn:e=>{let{dataset_name:t,dataset_digest:n,run_id:r}=e;return{datasetName:t,datasetDigest:n,runId:r}},enableResizing:!0,cell:X},{id:"sourceRun",header:d.formatMessage({id:"z42Rsj",defaultMessage:"Source run"}),accessorFn:e=>{let{run_id:t,runName:n,experimentId:r}=e;return{runId:t,runName:n,experimentId:r}},enableResizing:!0,cell:$},{id:"value",header:d.formatMessage({id:"NU5iHM",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!0}]),[d]),p=(0,G.N4)({data:h,getCoreRowModel:(0,P.HT)(),getExpandedRowModel:(0,P.D0)(),getRowId:e=>{var t;return null!==(t=[e.key,e.dataset_digest,e.run_id].join("."))&&void 0!==t?t:""},enableColumnResizing:!0,columnResizeMode:"onChange",columns:f});return(0,y.FD)("div",{css:Z,children:[(0,y.Y)(l.T.Title,{level:4,children:(0,y.Y)(u.A,{id:"lPNTq7",defaultMessage:"Metrics ({length})",values:{length:m.length}})}),(0,y.Y)("div",{css:(0,r.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(()=>{if(o)return(0,y.Y)(i.QvX,{lines:3});if(!m.length)return(0,y.Y)("div",{css:V,children:(0,y.Y)(i.SvL,{description:(0,y.Y)(u.A,{id:"XwkX9B",defaultMessage:"No metrics recorded"})})});const e=h.length<1;return(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,r.AH)({marginBottom:s.spacing.sm},""),children:(0,y.Y)(j.I,{componentId:"mlflow.logged_model.details.metrics.table.search",prefix:(0,y.Y)(j.S,{}),placeholder:d.formatMessage({id:"+Cr7Gu",defaultMessage:"Search metrics"}),value:c,onChange:e=>g(e.target.value),allowClear:!0})}),(0,y.FD)(i.XIK,{ref:e=>null===e||void 0===e?void 0:e.setAttribute("data-testid","logged-model-details-metrics-table"),scrollable:!0,empty:e?(0,y.Y)("div",{css:(0,r.AH)({marginTop:4*s.spacing.md},""),children:(0,y.Y)(i.SvL,{description:(0,y.Y)(u.A,{id:"hyHL1n",defaultMessage:"No metrics match the search filter"})})}):null,children:[(0,y.Y)(i.Hjg,{isHeader:!0,children:p.getLeafHeaders().map(((e,t)=>(0,y.Y)(i.A0N,{componentId:"mlflow.logged_model.details.metrics.table.header",header:e,column:e.column,setColumnSizing:p.setColumnSizing,isResizing:e.column.getIsResizing(),css:(0,r.AH)({flexGrow:e.column.getCanResize()?0:1},""),style:{flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,G.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),p.getRowModel().rows.map((e=>(0,y.Y)(i.Hjg,{children:e.getAllCells().map((e=>(0,y.Y)(i.nA6,{style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,G.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})]})})()})]})};const W=e=>{var t;let{getValue:n}=e;const r=null!==(t=n())&&void 0!==t?t:[];return(0,T.isEmpty)(r)?(0,y.Y)(y.FK,{children:"-"}):(0,y.Y)(i.nEg,{children:r.map((e=>{let{datasetDigest:t,datasetName:n,runId:r}=e;return(0,y.Y)(q.f,{datasetName:n,datasetDigest:t,runId:r},[n,t].join("."))}))})};var J={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"},ee={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},te={name:"11g4mt0",styles:"font-size:16px"};const ne=e=>{let{loggedModel:t,relatedRunsData:n,relatedRunsLoading:o}=e;const{theme:s}=(0,l.u)(),d=(0,a.A)(),[c,g]=(0,v.useState)(""),m=(0,v.useMemo)((()=>{var e,r,l;if(o)return[];const i=null!==(e=null===t||void 0===t||null===(r=t.data)||void 0===r?void 0:r.metrics)&&void 0!==e?e:[],s=(0,T.groupBy)(i,"run_id");return null!==t&&void 0!==t&&null!==(l=t.info)&&void 0!==l&&l.source_run_id&&!s[t.info.source_run_id]&&(s[t.info.source_run_id]=[]),(0,T.entries)(s).map((e=>{var r,l,i;let[o,s]=e;const d=(0,T.uniqBy)(s,"dataset_name").map((e=>{let{dataset_digest:t,dataset_name:n}=e;return{datasetDigest:t,datasetName:n,runId:o}})).filter((e=>Boolean(e.datasetName)||Boolean(e.datasetDigest))),a=null===n||void 0===n||null===(r=n.find((e=>{var t;return(null===(t=e.info)||void 0===t?void 0:t.runUuid)===o})))||void 0===r||null===(l=r.info)||void 0===l?void 0:l.runName;return{runId:o,runName:a,datasets:d,experimentId:null===t||void 0===t||null===(i=t.info)||void 0===i?void 0:i.experiment_id}}))}),[t,o,n]),h=(0,v.useMemo)((()=>m.filter((e=>{let{runName:t,datasets:n}=e;const r=c.toLowerCase();return(null===t||void 0===t?void 0:t.toLowerCase().includes(r))||n.find((e=>{var t;return null===(t=e.datasetName)||void 0===t?void 0:t.toLowerCase().includes(r)}))}))),[c,m]),f=(0,v.useMemo)((()=>[{id:"run",header:d.formatMessage({id:"/4Aok8",defaultMessage:"Run"}),enableResizing:!0,size:240,accessorFn:e=>{let{runId:t,runName:n,experimentId:r}=e;return{runId:t,runName:n,experimentId:r}},cell:$},{id:"input",header:d.formatMessage({id:"ubeHow",defaultMessage:"Input"}),accessorKey:"datasets",enableResizing:!1,cell:W}]),[d]),p=(0,G.N4)({data:h,getCoreRowModel:(0,P.HT)(),getExpandedRowModel:(0,P.D0)(),getRowId:e=>e.key,enableColumnResizing:!0,columnResizeMode:"onChange",columns:f});return(0,y.FD)("div",{css:ee,children:[(0,y.Y)(l.T.Title,{css:te,children:"Runs"}),(0,y.Y)("div",{css:(0,r.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(()=>{if(o)return(0,y.Y)(i.QvX,{lines:3});if(!m.length)return(0,y.Y)("div",{css:J,children:(0,y.Y)(i.SvL,{description:(0,y.Y)(u.A,{id:"TC9IhO",defaultMessage:"No runs"})})});const e=h.length<1;return(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,r.AH)({marginBottom:s.spacing.sm},""),children:(0,y.Y)(j.I,{componentId:"mlflow.logged_model.details.runs.table.search",prefix:(0,y.Y)(j.S,{}),placeholder:d.formatMessage({id:"eQV8Hs",defaultMessage:"Search runs"}),value:c,onChange:e=>g(e.target.value),allowClear:!0})}),(0,y.FD)(i.XIK,{scrollable:!0,ref:e=>null===e||void 0===e?void 0:e.setAttribute("data-testid","logged-model-details-runs-table"),empty:e?(0,y.Y)("div",{css:(0,r.AH)({marginTop:4*s.spacing.md},""),children:(0,y.Y)(i.SvL,{description:(0,y.Y)(u.A,{id:"5bBqLk",defaultMessage:"No runs match the search filter"})})}):null,children:[(0,y.Y)(i.Hjg,{isHeader:!0,children:p.getLeafHeaders().map(((e,t)=>(0,y.Y)(i.A0N,{componentId:"mlflow.logged_model.details.runs.table.header",header:e,column:e.column,setColumnSizing:p.setColumnSizing,isResizing:e.column.getIsResizing(),css:(0,r.AH)({flexGrow:e.column.getCanResize()?0:1},""),style:{flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,G.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),p.getRowModel().rows.map((e=>(0,y.Y)(i.Hjg,{children:e.getAllCells().map((e=>(0,y.Y)(i.nA6,{style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},multiline:!0,children:(0,G.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})]})})()})]})};var re=n(88457);const le=e=>{let{loggedModel:t}=e;const n=(0,v.useMemo)((()=>{var e,n;return(null!==(e=null===(n=t.data)||void 0===n?void 0:n.metrics)&&void 0!==e?e:[]).reduce(((e,t)=>{let{dataset_digest:n,dataset_name:r,run_id:l}=t;return r&&n&&!e.find((e=>e.dataset_name===r&&e.dataset_digest===n))&&e.push({dataset_name:r,dataset_digest:n,run_id:l}),e}),[])}),[t]);return n.length?(0,y.Y)(i.nEg,{children:n.map((e=>{let{dataset_digest:t,dataset_name:n,run_id:r}=e;return(0,y.Y)(q.f,{datasetName:n,datasetDigest:t,runId:null!==r&&void 0!==r?r:null},[n,t].join("."))}))}):(0,y.Y)(y.FK,{children:"-"})};var ie=n(36506);const oe=(e,t)=>`/explore/data/models/${e.replace(/\./g,"/")}/version/${t}`,se=(e,t)=>(0,d.Oz)(`/models/${e}/versions/${t}`),de=e=>{try{var t,n,r;const l=null===(t=e.info)||void 0===t||null===(n=t.tags)||void 0===n||null===(r=n.find((e=>"mlflow.modelVersions"===e.key)))||void 0===r?void 0:r.value;if(l)return JSON.parse(l)}catch(l){return null}return null},ae=e=>{let{loggedModels:t}=e;const n=(0,v.useMemo)((()=>(0,T.compact)(t.map(de)).flat()),[t]);return(0,v.useMemo)((()=>{var e;return null!==(e=n.map((e=>{var t;const n=(t=e.name,Boolean(t.match(/^[^. /]+\.[^. /]+\.[^. /]+$/)))?oe:se;return{displayedName:e.name,version:e.version,link:n(e.name,e.version),source:null,status:null}})))&&void 0!==e?e:[]}),[n])};var ue=n(21317);const ce=e=>{let{loggedModel:t}=e;const n=(0,v.useMemo)((()=>[t]),[t]),{theme:o}=(0,l.u)(),s=ae({loggedModels:n});return(0,T.isEmpty)(s)?(0,y.Y)(y.FK,{children:"-"}):(0,y.Y)(i.nEg,{children:null===s||void 0===s?void 0:s.map((e=>{let{displayedName:t,version:n,link:l}=e;return(0,y.FD)(d.N_,{to:l,css:(0,r.AH)({display:"flex",alignItems:"center",gap:o.spacing.sm},""),children:[(0,y.Y)(ue.h,{})," ",t," ",(0,y.FD)(i.vwO,{componentId:"mlflow.logged_model.details.registered_model_version_tag",children:["v",n]})]},`${t}-${n}`)}))})};var ge={name:"82a6rk",styles:"flex:1"};const me=e=>{var t,n;let{onDataUpdated:o,loggedModel:s}=e;const{theme:a}=(0,l.u)(),{data:g,loading:m,error:h}=(0,re.s)({loggedModels:s?[s]:[]}),f=(0,v.useMemo)((()=>null===g||void 0===g?void 0:g.find((e=>{var t,n;return(null===(t=e.info)||void 0===t?void 0:t.runUuid)===(null===s||void 0===s||null===(n=s.info)||void 0===n?void 0:n.source_run_id)}))),[null===s||void 0===s||null===(t=s.info)||void 0===t?void 0:t.source_run_id,g]),p=(0,v.useMemo)((()=>{var e,t;return(0,T.keyBy)((null!==(e=null===s||void 0===s||null===(t=s.data)||void 0===t?void 0:t.params)&&void 0!==e?e:[]).filter((e=>{let{key:t,value:n}=e;return!(0,T.isEmpty)(t)&&!(0,T.isEmpty)(n)})),"key")}),[null===s||void 0===s||null===(n=s.data)||void 0===n?void 0:n.params]);return(0,y.Y)(ie.X,{children:(0,y.FD)("div",{css:ge,children:[(0,y.Y)(U,{loggedModel:s,onDescriptionChanged:o}),(0,y.Y)(l.T.Title,{level:4,children:(0,y.Y)(u.A,{id:"XcKgD5",defaultMessage:"Details"})}),(()=>{var e,t,n,l,o,g,v,h,p,M,Y;return s?(0,y.FD)(F.N,{children:[(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"MUUYWZ",defaultMessage:"Created at"}),value:(0,y.Y)(B.P,{value:null===(e=s.info)||void 0===e?void 0:e.creation_timestamp_ms})}),(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"UgF7ax",defaultMessage:"Status"}),value:(0,y.Y)(k.a,{data:s})}),(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"A+5KMV",defaultMessage:"Model ID"}),value:(0,y.Y)(H.t,{value:null!==(t=null===(n=s.info)||void 0===n?void 0:n.model_id)&&void 0!==t?t:""})}),(null===(l=s.info)||void 0===l?void 0:l.source_run_id)&&(null===(o=s.info)||void 0===o?void 0:o.experiment_id)&&(m||f)&&(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"d8cdYc",defaultMessage:"Source run"}),value:m?(0,y.Y)(i.xUE,{css:(0,r.AH)({width:200,height:a.spacing.md},"")}):(0,y.Y)(d.N_,{to:c.h.getRunPageRoute(null===(g=s.info)||void 0===g?void 0:g.experiment_id,null===(v=s.info)||void 0===v?void 0:v.source_run_id),children:null===f||void 0===f||null===(h=f.info)||void 0===h?void 0:h.runName})}),(null===(p=s.info)||void 0===p?void 0:p.source_run_id)&&(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"wFaOf0",defaultMessage:"Source run ID"}),value:(0,y.Y)(H.t,{value:null!==(M=null===(Y=s.info)||void 0===Y?void 0:Y.source_run_id)&&void 0!==M?M:""})}),(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"msllnR",defaultMessage:"Datasets used"}),value:(0,y.Y)(le,{loggedModel:s})}),(0,y.Y)(N.Z,{title:(0,y.Y)(u.A,{id:"LpdcPw",defaultMessage:"Model versions"}),value:(0,y.Y)(ce,{loggedModel:s})})]}):null})(),(null===h||void 0===h?void 0:h.message)&&(0,y.FD)(y.FK,{children:[(0,y.Y)(i.FcD,{closable:!1,message:(0,y.Y)(u.A,{id:"nNIors",defaultMessage:"Error when fetching related runs data: {error}",values:{error:h.message}}),type:"error",componentId:"mlflow.logged_model.details.related_runs.error"}),(0,y.Y)(w.S,{size:"md"})]}),(0,y.FD)("div",{css:(0,r.AH)({display:"grid",gridTemplateColumns:"1fr 1fr",gridTemplateRows:"400px 400px",gap:a.spacing.lg,overflow:"hidden",marginBottom:a.spacing.md},""),children:[(0,y.Y)(O.y,{params:p}),(0,y.Y)(Q,{loggedModel:s,relatedRunsLoading:m,relatedRunsData:null!==g&&void 0!==g?g:void 0}),(0,y.Y)(ne,{loggedModel:s,relatedRunsLoading:m,relatedRunsData:null!==g&&void 0!==g?g:void 0}),(0,y.Y)("div",{})]})]})})};var ve=n(76688),he=n(72314),fe=n(18006);var pe={name:"1jv19ey",styles:"height:100%;overflow:hidden;display:flex"};const Me=e=>{var t,n,r,l;let{loggedModel:i}=e;return(0,y.Y)("div",{css:pe,children:(0,y.Y)(fe.Ay,{isLoggedModelsMode:!0,loggedModelId:null!==(t=null===(n=i.info)||void 0===n?void 0:n.model_id)&&void 0!==t?t:"",artifactRootUri:null!==(r=null===i||void 0===i||null===(l=i.info)||void 0===l?void 0:l.artifact_uri)&&void 0!==r?r:"",useAutoHeight:!0})})};var Ye=n(91089);var ye={name:"3ytxc3",styles:"height:100%;overflow:hidden"};const xe=e=>{var t,n,r;let{loggedModel:l}=e;const i=(0,v.useMemo)((()=>{var e,t;return[null!==(e=null===(t=l.info)||void 0===t?void 0:t.experiment_id)&&void 0!==e?e:""]}),[null===(t=l.info)||void 0===t?void 0:t.experiment_id]);return null!==(n=l.info)&&void 0!==n&&n.experiment_id?(0,y.Y)("div",{css:ye,children:(0,y.Y)(Ye.O,{experimentIds:i,loggedModelId:null===(r=l.info)||void 0===r?void 0:r.model_id})}):null};var _e={name:"1pbmicl",styles:"overflow:auto;flex:1"};const we=()=>{var e;const{experimentId:t,loggedModelId:n,tabName:o}=(0,d.g)(),{clearUserActionError:a,currentUserActionError:c}=(0,K.tF)();s()(t,"Experiment ID must be defined"),s()(n,"Logged model ID must be defined");const{theme:g}=(0,l.u)(),{data:m,isLoading:v,error:h,refetch:f}=(0,ve.b)({loggedModelId:n}),{data:p,loading:M,apiError:Y,apolloError:x}=(0,he.L)({experimentId:t});if(h)throw h;const _=null!==Y&&void 0!==Y?Y:x;return(0,y.FD)(y.FK,{children:[(0,y.Y)(E,{experimentId:t,experiment:p,loggedModel:m,loading:v||M,onSuccess:f}),c&&(0,y.Y)(i.FcD,{componentId:"mlflow.logged_model.details.user-action-error",css:(0,r.AH)({marginBottom:g.spacing.sm},""),type:"error",message:null!==(e=c.displayMessage)&&void 0!==e?e:c.message,onClose:a}),(null===_||void 0===_?void 0:_.message)&&(0,y.Y)(i.FcD,{componentId:"mlflow.logged_model.details.experiment-error",css:(0,r.AH)({marginBottom:g.spacing.sm},""),type:"error",message:(0,y.Y)(u.A,{id:"E4Te7L",defaultMessage:"Experiment load error: {errorMessage}",values:{errorMessage:_.message}}),closable:!1}),(0,y.Y)(S,{experimentId:t,modelId:n,activeTabName:o}),(0,y.Y)("div",{css:_e,children:v?(0,y.Y)(i.QvX,{lines:12}):m?"traces"===o?(0,y.Y)(xe,{loggedModel:m}):"artifacts"===o?(0,y.Y)(Me,{loggedModel:m}):(0,y.Y)(me,{onDataUpdated:f,loggedModel:m}):null})]})};var be=()=>{const{theme:e}=(0,l.u)();return(0,y.Y)(D.X,{children:(0,y.Y)(i.ffj,{css:(0,r.AH)({paddingTop:e.spacing.md,display:"flex",paddingBottom:e.spacing.md,overflow:"hidden",height:"100%",flexDirection:"column"},""),children:(0,y.Y)(we,{})})})}},72314:function(e,t,n){n.d(t,{L:function(){return o}});var r=n(56675),l=n(95947);const i=r.J1`
  query MlflowGetExperimentQuery($input: MlflowGetExperimentInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetExperiment(input: $input) {
      apiError {
        code
        message
      }
      experiment {
        artifactLocation
        creationTime
        experimentId
        lastUpdateTime
        lifecycleStage
        name
        tags {
          key
          value
        }
      }
    }
  }
`,o=e=>{var t;let{experimentId:n,options:r={}}=e;const{data:o,loading:s,error:d,refetch:a}=(0,l.I)(i,{variables:{input:{experimentId:n}},skip:!n,...r});return{loading:s,data:null===o||void 0===o||null===(t=o.mlflowGetExperiment)||void 0===t?void 0:t.experiment,refetch:a,apolloError:d,apiError:(()=>{var e;return null===o||void 0===o||null===(e=o.mlflowGetExperiment)||void 0===e?void 0:e.apiError})()}}},77020:function(e,t,n){n.d(t,{n:function(){return g}});var r=n(31014),l=n(61226),i=n(28999),o=n(84865),s=n(95904),d=n(21363);class a extends d.Q{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;const n=this.options;this.options=this.client.defaultMutationOptions(e),(0,i.f8)(n,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.currentMutation)||e.removeObserver(this))}onMutationUpdate(e){this.updateResult();const t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const e=this.currentMutation?this.currentMutation.state:(0,o.$)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){s.j.batch((()=>{var t,n,r,l;if(this.mutateOptions&&this.hasListeners())if(e.onSuccess)null==(t=(n=this.mutateOptions).onSuccess)||t.call(n,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(l=this.mutateOptions).onSettled)||r.call(l,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(e.onError){var i,o,s,d;null==(i=(o=this.mutateOptions).onError)||i.call(o,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(s=(d=this.mutateOptions).onSettled)||s.call(d,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}e.listeners&&this.listeners.forEach((e=>{let{listener:t}=e;t(this.currentResult)}))}))}}var u=n(27288),c=n(71233);function g(e,t,n){const o=(0,i.GR)(e,t,n),d=(0,u.jE)({context:o.context}),[g]=r.useState((()=>new a(d,o)));r.useEffect((()=>{g.setOptions(o)}),[g,o]);const v=(0,l.r)(r.useCallback((e=>g.subscribe(s.j.batchCalls(e))),[g]),(()=>g.getCurrentResult()),(()=>g.getCurrentResult())),h=r.useCallback(((e,t)=>{g.mutate(e,t).catch(m)}),[g]);if(v.error&&(0,c.G)(g.options.useErrorBoundary,[v.error]))throw v.error;return{...v,mutate:h,mutateAsync:v.mutate}}function m(){}},88457:function(e,t,n){n.d(t,{s:function(){return a}});var r=n(31014),l=n(9133),i=n(77735),o=n(63528);const s=e=>["USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS",{runUuid:e}],d=async e=>{let{queryKey:[,{runUuid:t}]}=e;try{const e=await o.x.getRun({run_id:t});return null===e||void 0===e?void 0:e.run}catch(n){return null}},a=e=>{var t;let{loggedModels:n=[]}=e;const o=(0,r.useMemo)((()=>{const e=(0,l.compact)(null===n||void 0===n?void 0:n.flatMap((e=>{var t,n;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(n=t.metrics)||void 0===n?void 0:n.map((e=>e.run_id))}))),t=(0,l.compact)(null===n||void 0===n?void 0:n.map((e=>{var t;return null===e||void 0===e||null===(t=e.info)||void 0===t?void 0:t.source_run_id})));return(0,l.sortBy)((0,l.uniq)([...e,...t]))}),[n]),a=(0,i.E)({queries:o.map((e=>({queryKey:s(e),queryFn:d})))}),u=a.some((e=>{let{isLoading:t}=e;return t})),c=null===(t=a.find((e=>{let{error:t}=e;return t})))||void 0===t?void 0:t.error;return{data:(0,r.useMemo)((()=>a.map((e=>{let{data:t}=e;return t})).filter(Boolean)),[a]),loading:u,error:c}}},92246:function(e,t,n){n.d(t,{X:function(){return c}});var r=n(37368),l=n(28486),i=n(3293),o=n(77520),s=n(88443),d=n(50111);var a={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const u=e=>{var t;let{error:n}=e;return(0,d.Y)(i.ffj,{css:a,children:(0,d.Y)(i.SvL,{"data-testid":"fallback",title:(0,d.Y)(s.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===n||void 0===n?void 0:n.message)&&void 0!==t?t:(0,d.Y)(s.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,d.Y)(o.j,{})})})},c=e=>{let{children:t,resetKey:n}=e;return(0,d.Y)(l.tH,{FallbackComponent:u,resetKeys:[n],children:(0,d.Y)(r.Au,{children:t})})}}}]);
//# sourceMappingURL=905.b965d0d4.chunk.js.map