{"version": 3, "file": "static/js/9801.54781e2b.chunk.js", "mappings": "8RAuBA,SAASA,EAAcC,EAAcC,GACnC,GAAID,EAAQE,YAAcF,EAAQE,WAAWC,aAAc,CACzD,MAAM,aAAEA,GAAiBH,EAAQE,WACjCD,EAAMG,UAAUD,EAClB,CACF,CAUA,MAAME,UAA4BC,EAAAA,UAChCC,WAAAA,CAAYC,GACVC,MAAMD,GAAO,KAUfE,gBAAU,OACVC,cAAQ,OACRC,OAAgC,KAAK,KAErCC,MAAQ,CACNC,SAAS,EACTC,WAAOC,EACPC,cAAUD,GAhBVE,KAAKC,eAAiBD,KAAKC,eAAeC,KAAKF,MAC/CA,KAAKR,gBAAaM,EAClBE,KAAKP,SAAW,KAClB,CAgBAU,iBAAAA,GACEH,KAAKC,gBACP,CAEAG,kBAAAA,CAAmBC,GAKjB,GAJIL,KAAKV,MAAMgB,OAASD,EAAUC,MAAQN,KAAKV,MAAMiB,UAAYF,EAAUE,SACzEP,KAAKC,sBAGiBH,IAApBE,KAAKR,YACHQ,KAAKR,WAAWgB,eAAe,WAAY,CAC7CR,KAAKR,WAAWiB,MAChBT,KAAKR,WAAWkB,SAChB,MAAMC,EAAQ,YAAcX,KAAKP,SAAW,WAC5CmB,SAASC,uBAAuB,iBAAiB,GAAGC,UAAYH,EAChEX,KAAKR,gBAAaM,CACpB,CAGF,QAA4BA,IAAxBE,KAAKL,MAAMI,UAA0BC,KAAKN,OAAQ,CACpD,MAAMqB,EAAMC,IAAAA,IAAMhB,KAAKN,QAIjBuB,EAAW,qDACXC,EAAO,2EAEbF,IAAAA,UAAYC,EAAU,CACpBE,YAAaD,IACZE,MAAML,GAET,MAAMM,EAAeL,IAAAA,QAAUhB,KAAKL,MAAMI,SAAU,CAClDuB,KAAAA,CAAMxC,GACJ,OAAOA,EAAQE,YAAcF,EAAQE,WAAWsC,KAClD,EACAC,YAAAA,CAAazC,EAAc0C,GACzB,OAAI1C,EAAQE,YAAcF,EAAQE,WAAWsC,MACpCN,IAAAA,aAAeQ,EAAQ1C,EAAQE,YAAcF,EAAQE,WAAWsC,OAC9DxC,EAAQE,YAAcF,EAAQE,WAAWyC,KAC3CT,IAAAA,OAASQ,EAAQ,CACtBC,KAAMT,IAAAA,KAAOlC,EAAQE,YAAcF,EAAQE,WAAWyC,QAGnDT,IAAAA,OAASQ,EAAQ,CACtBC,KAAMT,IAAAA,KAAO,CACXU,cAAeC,EACfC,QAASH,EACTI,UAAWC,EACXC,SAAU,CAAC,GAAI,IACfC,WAAY,CAAC,GAAI,OAGvB,EACAnD,cAAeA,IACduC,MAAML,GACTA,EAAIkB,UAAUZ,EAAaa,aAC3BlC,KAAKR,WAAauB,CACpB,CACF,CAEAoB,MAAAA,GACE,OAAInC,KAAKL,MAAMC,SACNwC,EAAAA,EAAAA,GAACC,EAAAA,EAAoB,CAACC,UAAU,8BAErCtC,KAAKL,MAAME,OACNuC,EAAAA,EAAAA,GAACG,EAAAA,EAAsB,CAACD,UAAU,6BAGvCF,EAAAA,EAAAA,GAAA,OAAKE,UAAU,gBAAeE,UAC5BJ,EAAAA,EAAAA,GAAA,OACEK,GAAIzC,KAAKP,SACTiD,IAAMA,IACJ1C,KAAKN,OAASgD,CAAG,KAM7B,CAGAzC,cAAAA,GAAkB,IAAD0C,EAAAC,EACf,MAAM,KAAEtC,EAAI,QAAEC,EAAO,mBAAEsC,EAAkB,cAAEC,GAAkB9C,KAAKV,MAE5DyD,EACJF,GAAsBC,GAClBE,EAAAA,EAAAA,IAAkC1C,EAAMwC,IACxCG,EAAAA,EAAAA,IAAuB3C,EAAMC,GAGrB,QADdoC,GAAAC,EAAA5C,KAAKV,OACF4D,mBAAW,IAAAP,GADdA,EAAAQ,KAAAP,EACiBG,GACdK,MAAMC,IACL,MAAMC,EAAiBC,KAAKC,MAAMH,GAClCrD,KAAKyD,SAAS,CAAE1D,SAAUuD,EAAgB1D,SAAS,GAAQ,IAE5D8D,OAAO7D,IACNG,KAAKyD,SAAS,CAAE5D,MAAOA,EAAOD,SAAS,EAAOG,cAAUD,GAAY,GAE1E,EAxHIX,EAQGwE,aAAe,CACpBT,YAAaU,EAAAA,IAkHjB,O", "sources": ["experiment-tracking/components/artifact-view-components/ShowArtifactMapView.tsx"], "sourcesContent": ["/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport {\n  getArtifactContent,\n  getArtifactLocationUrl,\n  getLoggedModelArtifactLocationUrl,\n} from '../../../common/utils/ArtifactUtils';\nimport './ShowArtifactMapView.css';\nimport 'leaflet/dist/leaflet.css';\nimport L from 'leaflet';\nimport icon from 'leaflet/dist/images/marker-icon.png';\nimport iconRetina from 'leaflet/dist/images/marker-icon-2x.png';\nimport iconShadow from 'leaflet/dist/images/marker-shadow.png';\nimport { ArtifactViewSkeleton } from './ArtifactViewSkeleton';\nimport { ArtifactViewErrorState } from './ArtifactViewErrorState';\nimport { LoggedModelArtifactViewerProps } from './ArtifactViewComponents.types';\n\nfunction onEachFeature(feature: any, layer: any) {\n  if (feature.properties && feature.properties.popupContent) {\n    const { popupContent } = feature.properties;\n    layer.bindPopup(popupContent);\n  }\n}\n\ntype Props = {\n  runUuid: string;\n  path: string;\n  getArtifact?: (...args: any[]) => any;\n} & LoggedModelArtifactViewerProps;\n\ntype State = any;\n\nclass ShowArtifactMapView extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.fetchArtifacts = this.fetchArtifacts.bind(this);\n    this.leafletMap = undefined;\n    this.mapDivId = 'map';\n  }\n\n  static defaultProps = {\n    getArtifact: getArtifactContent,\n  };\n\n  leafletMap: any;\n  mapDivId: any;\n  mapRef: HTMLDivElement | null = null;\n\n  state = {\n    loading: true,\n    error: undefined,\n    features: undefined,\n  };\n\n  componentDidMount() {\n    this.fetchArtifacts();\n  }\n\n  componentDidUpdate(prevProps: Props) {\n    if (this.props.path !== prevProps.path || this.props.runUuid !== prevProps.runUuid) {\n      this.fetchArtifacts();\n    }\n\n    if (this.leafletMap !== undefined) {\n      if (this.leafletMap.hasOwnProperty('_layers')) {\n        this.leafletMap.off();\n        this.leafletMap.remove();\n        const inner = \"<div id='\" + this.mapDivId + \"'></div>\";\n        document.getElementsByClassName('map-container')[0].innerHTML = inner;\n        this.leafletMap = undefined;\n      }\n    }\n\n    if (this.state.features !== undefined && this.mapRef) {\n      const map = L.map(this.mapRef);\n\n      // Load tiles from OSM with the corresponding attribution\n      // Potentially, these could be set in an ENV VAR to use a custom map\n      const tilesURL = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';\n      const attr = '&copy; <a href=\"http://osm.org/copyright\">OpenStreetMap</a> contributors';\n\n      L.tileLayer(tilesURL, {\n        attribution: attr,\n      }).addTo(map);\n\n      const geojsonLayer = L.geoJSON(this.state.features, {\n        style(feature: any) {\n          return feature.properties && feature.properties.style;\n        },\n        pointToLayer(feature: any, latlng: any) {\n          if (feature.properties && feature.properties.style) {\n            return L.circleMarker(latlng, feature.properties && feature.properties.style);\n          } else if (feature.properties && feature.properties.icon) {\n            return L.marker(latlng, {\n              icon: L.icon(feature.properties && feature.properties.icon),\n            });\n          }\n          return L.marker(latlng, {\n            icon: L.icon({\n              iconRetinaUrl: iconRetina,\n              iconUrl: icon,\n              shadowUrl: iconShadow,\n              iconSize: [24, 36],\n              iconAnchor: [12, 36],\n            }),\n          });\n        },\n        onEachFeature: onEachFeature,\n      }).addTo(map);\n      map.fitBounds(geojsonLayer.getBounds());\n      this.leafletMap = map;\n    }\n  }\n\n  render() {\n    if (this.state.loading) {\n      return <ArtifactViewSkeleton className=\"artifact-map-view-loading\" />;\n    }\n    if (this.state.error) {\n      return <ArtifactViewErrorState className=\"artifact-map-view-error\" />;\n    } else {\n      return (\n        <div className=\"map-container\">\n          <div\n            id={this.mapDivId}\n            ref={(ref) => {\n              this.mapRef = ref;\n            }}\n          ></div>\n        </div>\n      );\n    }\n  }\n\n  /** Fetches artifacts and updates component state with the result */\n  fetchArtifacts() {\n    const { path, runUuid, isLoggedModelsMode, loggedModelId } = this.props;\n\n    const artifactLocation =\n      isLoggedModelsMode && loggedModelId\n        ? getLoggedModelArtifactLocationUrl(path, loggedModelId)\n        : getArtifactLocationUrl(path, runUuid);\n\n    this.props\n      .getArtifact?.(artifactLocation)\n      .then((rawFeatures: any) => {\n        const parsedFeatures = JSON.parse(rawFeatures);\n        this.setState({ features: parsedFeatures, loading: false });\n      })\n      .catch((error: any) => {\n        this.setState({ error: error, loading: false, features: undefined });\n      });\n  }\n}\n\nexport default ShowArtifactMapView;\n"], "names": ["onEachFeature", "feature", "layer", "properties", "popup<PERSON><PERSON>nt", "bindPopup", "ShowArtifactMapView", "Component", "constructor", "props", "super", "leafletMap", "mapDivId", "mapRef", "state", "loading", "error", "undefined", "features", "this", "fetchArtifacts", "bind", "componentDidMount", "componentDidUpdate", "prevProps", "path", "runUuid", "hasOwnProperty", "off", "remove", "inner", "document", "getElementsByClassName", "innerHTML", "map", "L", "tilesURL", "attr", "attribution", "addTo", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "latlng", "icon", "iconRetinaUrl", "iconRetina", "iconUrl", "shadowUrl", "iconShadow", "iconSize", "iconAnchor", "fitBounds", "getBounds", "render", "_jsx", "ArtifactViewSkeleton", "className", "ArtifactViewErrorState", "children", "id", "ref", "_this$props$getArtifa", "_this$props", "isLoggedModelsMode", "loggedModelId", "artifactLocation", "getLoggedModelArtifactLocationUrl", "getArtifactLocationUrl", "getArtifact", "call", "then", "rawFeatures", "parsedFeatures", "JSON", "parse", "setState", "catch", "defaultProps", "getArtifactContent"], "sourceRoot": ""}