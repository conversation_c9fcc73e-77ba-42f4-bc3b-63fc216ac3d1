"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[2708],{28486:function(e,t,r){r.d(t,{tH:function(){return s}});var o=r(31014);function n(e,t,r,o){Object.defineProperty(e,t,{get:r,set:o,enumerable:!0,configurable:!0})}n({},"ErrorBoundary",(()=>s));n({},"ErrorBoundaryContext",(()=>a));const a=(0,o.createContext)(null),i={didCatch:!1,error:null};class s extends o.Component{state=(()=>i)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var r=arguments.length,o=new Array(r),n=0;n<r;n++)o[n]=arguments[n];e.props.onReset?.({args:o,reason:"imperative-api"}),e.setState(i)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:r}=this.state,{resetKeys:o}=this.props;r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,r)=>!Object.is(e,t[r])))}(e.resetKeys,o)&&(this.props.onReset?.({next:o,prev:e.resetKeys,reason:"keys"}),this.setState(i))}render(){const{children:e,fallbackRender:t,FallbackComponent:r,fallback:n}=this.props,{didCatch:i,error:s}=this.state;let l=e;if(i){const e={error:s,resetErrorBoundary:this.resetErrorBoundary};if((0,o.isValidElement)(n))l=n;else if("function"===typeof t)l=t(e);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");l=(0,o.createElement)(r,e)}}return(0,o.createElement)(a.Provider,{value:{didCatch:i,error:s,resetErrorBoundary:this.resetErrorBoundary}},l)}}function l(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function d(){const e=(0,o.useContext)(a);l(e);const[t,r]=(0,o.useState)({error:null,hasError:!1}),n=(0,o.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:e=>r({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return n}n({},"useErrorBoundary",(()=>d));function c(e,t){const r=r=>(0,o.createElement)(s,t,(0,o.createElement)(e,r)),n=e.displayName||e.name||"Unknown";return r.displayName=`withErrorBoundary(${n})`,r}n({},"withErrorBoundary",(()=>c))},42708:function(e,t,r){r.r(t),r.d(t,{default:function(){return ne}});var o=r(89555),n=r(77520),a=r(42848),i=r(3293),s=r(4877),l=r.n(s),d=r(93215),c=r(58481),u=r(92246),m=r(91144),p=r(31014),h=r(46398),g=r(28999),f=r(45586),v=r(14430);class y extends f.${constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,v.PL)()},t)}getOptimisticResult(e){return e.behavior=(0,v.PL)(),super.getOptimisticResult(e)}fetchNextPage(){let{pageParam:e,...t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...t,meta:{fetchMore:{direction:"forward",pageParam:e}}})}fetchPreviousPage(){let{pageParam:e,...t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...t,meta:{fetchMore:{direction:"backward",pageParam:e}}})}createResult(e,t){var r,o,n,a,i,s;const{state:l}=e,d=super.createResult(e,t),{isFetching:c,isRefetching:u}=d,m=c&&"forward"===(null==(r=l.fetchMeta)||null==(o=r.fetchMore)?void 0:o.direction),p=c&&"backward"===(null==(n=l.fetchMeta)||null==(a=n.fetchMore)?void 0:a.direction);return{...d,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,v.rB)(t,null==(i=l.data)?void 0:i.pages),hasPreviousPage:(0,v.RQ)(t,null==(s=l.data)?void 0:s.pages),isFetchingNextPage:m,isFetchingPreviousPage:p,isRefetching:u&&!m&&!p}}}var C=r(44200);var R=r(9133),b=r(8986);const E=function(e){var t;let{experimentIds:r,orderByAsc:o,orderByField:n}=e,{enabled:a=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=["SEARCH_LOGGED_MODELS",JSON.stringify(r),n,o],{data:s,isLoading:l,isFetching:d,fetchNextPage:c,refetch:u,error:m}=function(e,t,r){const o=(0,g.vh)(e,t,r);return(0,C.t)(o,y)}({queryKey:i,queryFn:async e=>{let{pageParam:t}=e;const a={experiment_ids:r,order_by:[{field_name:null!==n&&void 0!==n?n:"creation_time",ascending:null!==o&&void 0!==o&&o}],page_token:t};return(0,b.G)("/ajax-api/2.0/mlflow/logged-models/search","POST",a)},cacheTime:0,getNextPageParam:e=>e.next_page_token,refetchOnWindowFocus:!1,retry:!1,enabled:a});return{isLoading:l,isFetching:d,data:(0,p.useMemo)((()=>null===s||void 0===s?void 0:s.pages.flatMap((e=>null===e||void 0===e?void 0:e.models)).filter(Boolean)),[s]),nextPageToken:null===(t=(0,R.last)(null===s||void 0===s?void 0:s.pages))||void 0===t?void 0:t.next_page_token,refetch:u,error:m,loadMoreResults:c}};var M=r(88464),S=r(88443),w=r(30706),B=r(26571);let A=function(e){return e.TABLE="TABLE",e.CHART="CHART",e}({});const x="viewMode";var D=r(50111);const P=e=>{let{orderByField:t,orderByAsc:r,onChangeOrderBy:s,onUpdateColumns:l,columnDefs:d,columnVisibility:c={},viewMode:u,setViewMode:m}=e;const p=(0,M.A)(),{theme:h}=(0,n.u)();return(0,D.FD)("div",{css:(0,o.AH)({display:"flex",flexWrap:"wrap",gap:h.spacing.sm},""),children:[(0,D.FD)(i.d98,{componentId:"mlflow.logged_model.list.view-mode",name:"view-mode",value:u,onChange:e=>{m((0,B.S)(A,e.target.value,A.TABLE))},children:[(0,D.FD)(i.EPn,{value:"TABLE",children:[(0,D.Y)(a.T,{componentId:"mlflow.logged_model.list.view-mode-table-tooltip",content:p.formatMessage({id:"F9Aau+",defaultMessage:"Table view"}),children:(0,D.Y)(a.L,{})}),(0,D.Y)("span",{css:n.M,children:p.formatMessage({id:"F9Aau+",defaultMessage:"Table view"})})]}),(0,D.FD)(i.EPn,{value:"CHART",children:[(0,D.Y)(a.T,{componentId:"mlflow.logged_model.list.view-mode-chart-tooltip",content:p.formatMessage({id:"44XPr5",defaultMessage:"Chart view"}),children:(0,D.Y)(i.SCH,{})}),(0,D.Y)("span",{css:n.M,children:p.formatMessage({id:"44XPr5",defaultMessage:"Chart view"})})]})]}),(0,D.Y)(n.B,{componentId:"mlflow.logged_model.list.sort",icon:r?(0,D.Y)(i.GCP,{}):(0,D.Y)(i.MMv,{}),onClick:()=>{t&&s(t,!r)},children:(0,D.Y)(S.A,{id:"eDoQXM",defaultMessage:"Sort: Created"})}),(0,D.Y)(w.$,{columnDefs:d,columnVisibility:c,onUpdateColumns:l,disabled:u===A.CHART})]})};var k=r(55030),Y=r(36506),T=r(31798),_=r(88457),L=r(42550),F=r(65418),I=r(71932),N=r(63617),K=r(73150),O=r(39045),H=r(75627),U=r(62758),V=r(30152),$=r(23734),q=r(88421);const z=(e,t)=>t?JSON.stringify([t,e]):null!==e&&void 0!==e?e:"",j=function(e){return`experiment-logged-models-charts-ui-state-v${arguments.length>1&&void 0!==arguments[1]?arguments[1]:1}-${e}`},G=()=>({compareRunCharts:void 0,compareRunSections:void 0,autoRefreshEnabled:!1,isAccordionReordered:!1,chartsSearchFilter:"",globalLineChartConfig:void 0,isDirty:!1}),X=(e,t)=>{if("UPDATE"===t.type)return{...t.stateSetter(e),isDirty:!0};if("METRICS_UPDATED"===t.type){const{compareRunCharts:r,compareRunSections:o}=(e=>{const t=e.map((e=>{let{dataAccessKey:t,metricKey:r,datasetName:o}=e;return{deleted:!1,type:V.zL.BAR,uuid:`autogen-${t}`,metricSectionId:o?`autogen-${o}`:"default",isGenerated:!0,metricKey:r,dataAccessKey:t,datasetName:o,displayName:o?`(${o}) ${r}`:void 0}})),r=(0,R.uniq)(e.map((e=>{let{datasetName:t}=e;return t}))).map((e=>({display:!0,name:null!==e&&void 0!==e?e:"Metrics",uuid:e?`autogen-${e}`:"default",isReordered:!1})));return(0,R.isEmpty)(r)&&r.push({display:!0,name:"Metrics",uuid:"default",isReordered:!1}),{compareRunCharts:t,compareRunSections:r}})(t.metricsByDatasets);return((e,t)=>{var r,o;if((!e.compareRunCharts||!e.compareRunSections||!e.isDirty)&&(t.compareRunCharts.length>0||t.compareRunSections.length>0))return{...e,compareRunCharts:null!==(r=t.compareRunCharts)&&void 0!==r?r:[],compareRunSections:null!==(o=t.compareRunSections)&&void 0!==o?o:[]};const n=t.compareRunCharts.filter((t=>{var r;return!(null!==(r=e.compareRunCharts)&&void 0!==r&&r.find((e=>e.uuid===t.uuid)))})),a=t.compareRunSections.filter((t=>{var r;return n.find((e=>e.metricSectionId===t.uuid))&&!(null!==(r=e.compareRunSections)&&void 0!==r&&r.find((e=>e.uuid===t.uuid)))}));return a.length>0||n.length>0?{...e,compareRunCharts:e.compareRunCharts?[...e.compareRunCharts,...n]:t.compareRunCharts,compareRunSections:e.compareRunSections?[...e.compareRunSections,...a]:t.compareRunSections}:e})(e,{compareRunCharts:r,compareRunSections:o})}return"INITIALIZE"===t.type&&t.initialConfig?t.initialConfig:e},J=(e,t)=>{const[r,o]=(0,p.useReducer)(X,void 0,G),[n,a]=(0,p.useState)(!0);(0,p.useEffect)((()=>{a(!0),(async e=>{const t=localStorage.getItem(j(e));if(t)try{return JSON.parse(t)}catch{return}})(t).then((e=>{o({type:"INITIALIZE",initialConfig:e}),a(!1)}))}),[t]),(0,p.useEffect)((()=>{n||o({type:"METRICS_UPDATED",metricsByDatasets:e})}),[e,n]),(0,p.useEffect)((()=>{r.isDirty&&(async(e,t)=>{localStorage.setItem(j(e),JSON.stringify(t))})(t,r)}),[t,r]);const i=(0,p.useCallback)((e=>o({type:"UPDATE",stateSetter:e})),[]);return{chartUIState:r,updateUIState:i,loading:n}};var W=r(19415);var Z={name:"1qmr6ab",styles:"overflow:auto"};const Q=(0,p.memo)((e=>{var t,r;let{chartData:a,uiState:s,metricKeysByDataset:l}=e;const{theme:d}=(0,n.u)(),{formatMessage:u}=(0,M.A)(),m=(0,p.useMemo)((()=>(0,R.uniq)(a.flatMap((e=>Object.keys(e.metrics))))),[a]),h=(0,p.useMemo)((()=>(0,R.uniq)(a.flatMap((e=>Object.keys(e.params))))),[a]),g=(0,U.g_)(),f=(0,p.useCallback)((e=>{g((t=>({...t,chartsSearchFilter:e})))}),[g]),[v,y]=(0,p.useState)(null),C=(0,p.useCallback)((e=>t=>y(V.i$.getEmptyChartCardByType(t,!1,void 0,e))),[]),b=(0,U.Ez)(),E=(0,U.KP)(),[w,B]=(0,p.useState)(void 0),A=(0,p.useMemo)((()=>({runs:a})),[a]),x=(0,p.useMemo)((()=>({runs:a,getDataTraceLink:c.h.getExperimentLoggedModelDetailsPageRoute})),[a]),P=(0,D.Y)("div",{css:(0,o.AH)({marginTop:d.spacing.lg},""),children:(0,D.Y)(i.SvL,{description:(0,D.Y)(S.A,{id:"oKgZFA",defaultMessage:"No models found in experiment or all models are hidden. Select at least one model to view charts."})})});return(0,D.Y)("div",{css:(0,o.AH)({backgroundColor:d.colors.backgroundSecondary,paddingLeft:d.spacing.md,paddingRight:d.spacing.md,paddingBottom:d.spacing.md,borderTop:`1px solid ${d.colors.border}`,borderLeft:`1px solid ${d.colors.border}`,flex:1,overflow:"hidden",display:"flex"},""),children:(0,D.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:d.spacing.sm,paddingTop:d.spacing.sm,overflow:"hidden",flex:1},""),children:[(0,D.Y)(F.I,{componentId:"mlflow.logged_model.list.charts.search",role:"searchbox",prefix:(0,D.Y)(F.S,{}),value:null!==(t=s.chartsSearchFilter)&&void 0!==t?t:"",allowClear:!0,onChange:e=>{let{target:t}=e;return f(t.value)},placeholder:u({id:"7teFL4",defaultMessage:"Search metric charts"})}),(0,D.FD)("div",{css:Z,children:[(0,D.Y)(H.W,{contextData:x,component:K.X,children:(0,D.Y)(I.c_,{visibleChartCards:s.compareRunCharts,children:(0,D.Y)(O.J,{compareRunSections:s.compareRunSections,compareRunCharts:s.compareRunCharts,reorderCharts:R.noop,insertCharts:R.noop,chartData:a,startEditChart:y,removeChart:E,addNewChartCard:C,search:null!==(r=s.chartsSearchFilter)&&void 0!==r?r:"",groupBy:null,setFullScreenChart:B,autoRefreshEnabled:!1,hideEmptyCharts:!1,globalLineChartConfig:void 0,supportedChartTypes:[V.zL.BAR,V.zL.SCATTER],noRunsSelectedEmptyState:P})})}),(0,D.Y)(N._,{fullScreenChart:w,onCancel:()=>B(void 0),chartData:a,groupBy:null,tooltipContextValue:A,tooltipComponent:K.X,autoRefreshEnabled:!1,globalLineChartConfig:void 0}),v&&(0,D.Y)(W.z,{chartRunData:a,metricKeyList:m,metricKeysByDataset:l,paramKeyList:h,config:v,onSubmit:e=>{b({...e,displayName:void 0}),y(null)},onCancel:()=>y(null),groupBy:null,supportedChartTypes:[V.zL.BAR,V.zL.SCATTER]})]})]})})})),ee=(0,p.memo)((e=>{let{loggedModels:t,experimentId:r}=e;const{theme:a}=(0,n.u)(),i=((e,t)=>{const r=(0,p.useRef)();return r.current&&(0,R.isEqual)(t,r.current.deps)||(r.current={deps:t,value:e()}),r.current.value})((()=>t),[t]),s=(e=>(0,p.useMemo)((()=>{const t=[];return e.forEach((e=>{var r,o;null===(r=e.data)||void 0===r||null===(o=r.metrics)||void 0===o||o.forEach((e=>{let{key:r,dataset_name:o}=e;if(r&&!t.find((e=>e.metricKey===r&&e.datasetName===o))){const e=z(r,o);t.push({metricKey:r,datasetName:o,dataAccessKey:e})}}))})),(0,R.orderBy)(t,(e=>{let{datasetName:t}=e;return!t}))}),[e]))(i),{chartUIState:l,updateUIState:d,loading:c}=J(s,r),u=(e=>{const{isRowHidden:t}=(0,q.GP)();return(0,p.useMemo)((()=>(0,R.compact)(e.map(((e,r)=>{var o,n,a,i,s,l,d,c,u,m;return null!==(o=e.info)&&void 0!==o&&o.model_id?{displayName:null!==(n=null!==(a=null===(i=e.info)||void 0===i?void 0:i.name)&&void 0!==a?a:null===(s=e.info)||void 0===s?void 0:s.model_id)&&void 0!==n?n:"Unknown",images:{},metrics:(0,R.keyBy)(null===(l=e.data)||void 0===l||null===(d=l.metrics)||void 0===d?void 0:d.map((e=>{let{dataset_name:t,key:r,value:o,timestamp:n,step:a}=e;return{dataKey:z(r,t),key:null!==r&&void 0!==r?r:"",value:null!==o&&void 0!==o?o:0,timestamp:null!==n&&void 0!==n?n:0,step:null!==a&&void 0!==a?a:0}})),"dataKey"),params:(0,R.keyBy)(null!==(c=null===(u=e.data)||void 0===u||null===(m=u.params)||void 0===m?void 0:m.map((e=>{let{key:t,value:r}=e;return{key:null!==t&&void 0!==t?t:"",value:null!==r&&void 0!==r?r:""}})).filter((e=>{let{key:t}=e;return t})))&&void 0!==c?c:[],"key"),tags:{},uuid:e.info.model_id,hidden:t(e.info.model_id,r),color:(0,$.T6)(e.info.model_id)}:null})))),[e,t])})(i);return c?(0,D.Y)("div",{css:(0,o.AH)({backgroundColor:a.colors.backgroundSecondary,paddingTop:a.spacing.lg,borderTop:`1px solid ${a.colors.border}`,borderLeft:`1px solid ${a.colors.border}`,flex:1,justifyContent:"center",alignItems:"center",display:"flex"},""),children:(0,D.Y)(n.H,{})}):(0,D.Y)(U.oB,{updateChartsUIState:d,children:(0,D.Y)(Q,{chartData:u,uiState:l,metricKeysByDataset:s})})}));var te=r(25790);var re={name:"oww3ap",styles:"display:flex;flex:1;overflow:hidden;position:relative"};const oe=()=>{const{experimentId:e}=(0,d.g)(),{theme:t}=(0,n.u)(),r=(0,d.Zp)(),{state:{orderByField:s,orderByAsc:u,columnVisibility:g,rowVisibilityMap:f,rowVisibilityMode:v},setOrderBy:y,setColumnVisibility:C,setRowVisibilityMode:R,toggleRowVisibility:b}=(0,T.k)();l()(e,"Experiment ID must be defined"),(0,p.useEffect)((()=>{!(0,m.Dz)()&&e&&r(c.h.getExperimentPageRoute(e),{replace:!0})}),[e,r]);const{viewMode:M,setViewMode:S}=(()=>{const[e,t]=(0,d.ok)();return{viewMode:(0,B.S)(A,e.get(x),A.TABLE),setViewMode:e=>{t({[x]:e})}}})(),{data:w,isFetching:F,isLoading:I,error:N,nextPageToken:K,loadMoreResults:O}=E({experimentIds:[e],orderByAsc:u,orderByField:s}),{data:H}=(0,_.s)({loggedModels:w}),U=(0,k.ih)({loggedModels:w,columnVisibility:g,isCompactMode:M!==A.TABLE}),[V,$]=(0,p.useState)(295),[z,j]=(0,p.useState)(!1),G=M!==A.TABLE,X=G&&z?(0,D.Y)("div",{css:(0,o.AH)({width:t.spacing.md},"")}):(0,D.Y)(h._,{columnDefs:U,loggedModels:null!==w&&void 0!==w?w:[],isLoading:I,isLoadingMore:F,error:N,moreResultsAvailable:Boolean(K),onLoadMore:O,onOrderByChange:y,orderByAsc:u,orderByField:s,columnVisibility:g,relatedRunsData:H});return(0,D.Y)(Y.X,{children:(0,D.FD)(q.Ap,{visibilityMap:f,visibilityMode:v,setRowVisibilityMode:R,toggleRowVisibility:b,children:[(0,D.Y)(P,{columnDefs:U,columnVisibility:g,onChangeOrderBy:y,onUpdateColumns:C,orderByField:s,orderByAsc:u,viewMode:M,setViewMode:S}),(0,D.Y)(a.S,{size:"sm",shrinks:!1}),(null===N||void 0===N?void 0:N.message)&&(0,D.FD)(D.FK,{children:[(0,D.Y)(i.FcD,{componentId:"mlflow.logged_models.list.error",message:N.message,type:"error",closable:!1}),(0,D.Y)(a.S,{size:"sm",shrinks:!1})]}),G?(0,D.Y)(te.Co,{children:(0,D.FD)("div",{css:re,children:[(0,D.Y)(L.t,{onResize:$,runListHidden:z,width:V,onHiddenChange:j,children:X}),M===A.CHART&&(0,D.Y)(ee,{loggedModels:null!==w&&void 0!==w?w:[],experimentId:e})]})}):X]})})};var ne=()=>(0,D.Y)(u.X,{children:(0,D.Y)(oe,{})})},88457:function(e,t,r){r.d(t,{s:function(){return d}});var o=r(31014),n=r(9133),a=r(77735),i=r(63528);const s=e=>["USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS",{runUuid:e}],l=async e=>{let{queryKey:[,{runUuid:t}]}=e;try{const e=await i.x.getRun({run_id:t});return null===e||void 0===e?void 0:e.run}catch(r){return null}},d=e=>{var t;let{loggedModels:r=[]}=e;const i=(0,o.useMemo)((()=>{const e=(0,n.compact)(null===r||void 0===r?void 0:r.flatMap((e=>{var t,r;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(r=t.metrics)||void 0===r?void 0:r.map((e=>e.run_id))}))),t=(0,n.compact)(null===r||void 0===r?void 0:r.map((e=>{var t;return null===e||void 0===e||null===(t=e.info)||void 0===t?void 0:t.source_run_id})));return(0,n.sortBy)((0,n.uniq)([...e,...t]))}),[r]),d=(0,a.E)({queries:i.map((e=>({queryKey:s(e),queryFn:l})))}),c=d.some((e=>{let{isLoading:t}=e;return t})),u=null===(t=d.find((e=>{let{error:t}=e;return t})))||void 0===t?void 0:t.error;return{data:(0,o.useMemo)((()=>d.map((e=>{let{data:t}=e;return t})).filter(Boolean)),[d]),loading:c,error:u}}},92246:function(e,t,r){r.d(t,{X:function(){return u}});var o=r(37368),n=r(28486),a=r(3293),i=r(77520),s=r(88443),l=r(50111);var d={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const c=e=>{var t;let{error:r}=e;return(0,l.Y)(a.ffj,{css:d,children:(0,l.Y)(a.SvL,{"data-testid":"fallback",title:(0,l.Y)(s.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===r||void 0===r?void 0:r.message)&&void 0!==t?t:(0,l.Y)(s.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(i.j,{})})})},u=e=>{let{children:t,resetKey:r}=e;return(0,l.Y)(n.tH,{FallbackComponent:c,resetKeys:[r],children:(0,l.Y)(o.Au,{children:t})})}}}]);
//# sourceMappingURL=2708.5659aeb9.chunk.js.map