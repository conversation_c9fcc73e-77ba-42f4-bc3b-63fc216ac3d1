{"version": 3, "file": "static/js/2708.5659aeb9.chunk.js", "mappings": "qUAQO,MAAMA,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVC,MAAO,MAGF,MAAMC,UAAsBC,EAAAA,UAIjCC,MAAA,KAAQL,EAAR,GAEA,+BAAOM,CAAyBJ,GAC9B,MAAO,CAAED,UAAU,E,MAAMC,EAC3B,CAEAK,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAMN,MAAEA,GAAUM,EAAKH,MAEvB,GAAc,OAAVH,EAAgB,SAAAO,EAAAC,UAAAC,OAHGC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAIrBN,EAAKO,MAAMC,UAAU,C,KACnBJ,EACAK,OAAQ,mBAGVT,EAAKU,SAASlB,EAChB,CACF,CAAC,EAXD,GAaAmB,iBAAAA,CAAkBjB,EAAckB,GAC9BC,KAAKN,MAAMO,UAAUpB,EAAOkB,EAC9B,CAEAG,kBAAAA,CACEC,EACAC,GAEA,MAAMxB,SAAEA,GAAaoB,KAAKhB,OACpBqB,UAAEA,GAAcL,KAAKN,MAQzBd,GACoB,OAApBwB,EAAUvB,OAqDhB,WAAuD,IAA9ByB,EAAAjB,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAW,GAAImB,EAAAnB,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAW,GACjD,OACEiB,EAAEhB,SAAWkB,EAAElB,QAAUgB,EAAEG,MAAK,CAACC,EAAMC,KAAWC,OAAOC,GAAGH,EAAMF,EAAEG,KAExE,CAxDMG,CAAgBX,EAAUE,UAAWA,KAErCL,KAAKN,MAAMC,UAAU,CACnBoB,KAAMV,EACNW,KAAMb,EAAUE,UAChBT,OAAQ,SAGVI,KAAKH,SAASlB,GAElB,CAEAsC,MAAAA,GACE,MAAMC,SAAEA,EAAQC,eAAEA,EAAcC,kBAAEA,EAAiBC,SAAEA,GACnDrB,KAAKN,OACDd,SAAEA,EAAQC,MAAEA,GAAUmB,KAAKhB,MAEjC,IAAIsC,EAAgBJ,EAEpB,GAAItC,EAAU,CACZ,MAAMc,EAAuB,C,MAC3Bb,EACAK,mBAAoBc,KAAKd,oBAG3B,IAAI,EAAAqC,EAAAA,gBAAeF,GACjBC,EAAgBD,OACX,GAA8B,oBAAnBF,EAChBG,EAAgBH,EAAezB,OAC1B,KAAI0B,EAGT,MAAM,IAAII,MACR,8FAHFF,GAAgB,EAAAG,EAAAA,eAAcL,EAAmB1B,EAG/C,CAGN,CAEA,OAAO,EAAA+B,EAAAA,eACLhD,EAAqBiD,SACrB,CACEC,MAAO,C,SACL/C,E,MACAC,EACAK,mBAAoBc,KAAKd,qBAG7BoC,EAEJ,EC5GK,SAASM,EACdD,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAM/C,UACuB,oBAA7B+C,EAAMzC,mBAEb,MAAM,IAAIsC,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASK,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAWtD,GAE3BmD,EAA2BE,GAE3B,MAAO9C,EAAOa,IAAY,EAAAmC,EAAAA,UAGvB,CACDnD,MAAO,KACPoD,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAS5C,qBACTW,EAAS,CAAEhB,MAAO,KAAMoD,UAAU,GAAQ,EAE5CI,aAAexD,GACbgB,EAAS,C,MACPhB,EACAoD,UAAU,OAGhB,CAACH,GAAS5C,qBAGZ,GAAIF,EAAMiD,SACR,MAAMjD,EAAMH,MAGd,OAAOqD,CACT,C,iCCtCO,SAASI,EACdC,EACAC,GAEA,MAAMC,EAAiC/C,IAC9B,EAAA+B,EAAAA,eACL3C,EACA0D,GACA,EAAAf,EAAAA,eAAcc,EAAW7C,IAKvBgD,EAAOH,EAAUI,aAAeJ,EAAUG,MAAQ,UAGxD,OAFAD,EAAQE,YAAc,qBAAqBD,KAEpCD,CACT,C,4QCEO,MAAMG,UAMHC,EAAAA,EAqBRC,WAAAA,CACEC,EACAC,GAQAC,MAAMF,EAAQC,EACf,CAESE,WAAAA,GACRD,MAAMC,cACNlD,KAAKmD,cAAgBnD,KAAKmD,cAAcC,KAAKpD,MAC7CA,KAAKqD,kBAAoBrD,KAAKqD,kBAAkBD,KAAKpD,KACtD,CAEDsD,UAAAA,CACEN,EAOAO,GAEAN,MAAMK,WACJ,IACKN,EACHQ,UAAUC,EAAAA,EAAAA,OAEZF,EAEH,CAEDG,mBAAAA,CACEV,GASA,OADAA,EAAQQ,UAAWC,EAAAA,EAAAA,MACZR,MAAMS,oBAAoBV,EAIlC,CAEDG,aAAAA,GAEE,IAFY,UAAEQ,KAAcX,GAAhB3D,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAkD,CAAC,EAG/D,OAAOW,KAAK4D,MAAM,IACbZ,EACHa,KAAM,CACJC,UAAW,CAAEC,UAAW,UAAWJ,eAGxC,CAEDN,iBAAAA,GAKE,IALgB,UAChBM,KACGX,GAFa3D,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAGY,CAAC,EAG7B,OAAOW,KAAK4D,MAAM,IACbZ,EACHa,KAAM,CACJC,UAAW,CAAEC,UAAW,WAAYJ,eAGzC,CAESK,YAAAA,CACRC,EACAjB,GAO4C,IAAAkB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC5C,MAAM,MAAEvF,GAAUiF,EACZO,EAASvB,MAAMe,aAAaC,EAAOjB,IAEnC,WAAEyB,EAAF,aAAcC,GAAiBF,EAE/BG,EACJF,GAAwD,aAAzB,OAAjBP,EAAAlF,EAAM4F,YAAsB,OAAXT,EAAAD,EAAAJ,gBAAA,EAAWK,EAAAJ,WAEtCc,EACJJ,GAAwD,cAAzB,OAAjBL,EAAApF,EAAM4F,YAAsB,OAAXP,EAAAD,EAAAN,gBAAA,EAAWO,EAAAN,WAE5C,MAAO,IACFS,EACHrB,cAAenD,KAAKmD,cACpBE,kBAAmBrD,KAAKqD,kBACxByB,aAAaA,EAAAA,EAAAA,IAAY9B,EAAD,OAAAsB,EAAUtF,EAAM+F,WAAhB,EAAUT,EAAYU,OAC9CC,iBAAiBA,EAAAA,EAAAA,IAAgBjC,EAAD,OAAAuB,EAAUvF,EAAM+F,WAAhB,EAAUR,EAAYS,OACtDL,qBACAE,yBACAH,aACEA,IAAiBC,IAAuBE,EAE7C,E,uCCtJI,MAAMK,EAA6B,SAAAC,GAepC,IAADC,EAAA,IAdH,cACEC,EAAa,WACbC,EAAU,aACVC,GAKDJ,GACD,QACEK,GAAU,GAGXnG,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAG,CAAC,EAGL,MAAMoG,EAAW,CAAC,uBAAwBC,KAAKC,UAAUN,GAAgBE,EAAcD,IAEjF,KAAEP,EAAI,UAAEa,EAAS,WAAEnB,EAAU,cAAEtB,EAAa,QAAE0C,EAAO,MAAEhH,GCiCxD,SAMLiH,EASAC,EASAC,GAQA,MAAMhD,GAAUiD,EAAAA,EAAAA,IAAeH,EAAMC,EAAMC,GAC3C,OAAOE,EAAAA,EAAAA,GACLlD,EACAJ,EAEH,CDtEwEuD,CAGrE,CACAV,WACAW,QAASC,UAA0B,IAAnB,UAAE1C,GAAW2C,EAC3B,MAAMC,EAAc,CAClBC,eAAgBnB,EAChBoB,SAAU,CAAC,CAAEC,WAAwB,OAAZnB,QAAY,IAAZA,EAAAA,EAAgB,gBAAiBoB,UAAqB,OAAVrB,QAAU,IAAVA,GAAAA,IACrEsB,WAAYjD,GAGd,OAAOkD,EAAAA,EAAAA,GAAwB,4CAA6C,OAAQN,EAAY,EAElGO,UAAW,EACXC,iBAAmBC,GAAaA,EAASC,gBACzCC,sBAAsB,EACtBC,OAAO,EACP3B,YASF,MAAO,CACLI,YACAnB,aACAM,MARiBqC,EAAAA,EAAAA,UAAQ,IAAU,OAAJrC,QAAI,IAAJA,OAAI,EAAJA,EAAMC,MAAMqC,SAASC,GAAa,OAAJA,QAAI,IAAJA,OAAI,EAAJA,EAAMC,SAAQC,OAAOC,UAAU,CAAC1C,IAS7F2C,cANqC,QAApBtC,GAAGuC,EAAAA,EAAAA,MAAS,OAAJ5C,QAAI,IAAJA,OAAI,EAAJA,EAAMC,cAAM,IAAAI,OAAA,EAAjBA,EAAmB6B,gBAOvCpB,UACAhH,QACA+I,gBAAiBzE,EAErB,E,gDE/DO,IAAK0E,EAAiC,SAAjCA,GAAiC,OAAjCA,EAAiC,cAAjCA,EAAiC,cAAjCA,CAAiC,MAK7C,MAAMC,EAAwB,W,eCYvB,MAAMC,EAAwC5C,IAkB9C,IAlB+C,aACpDI,EAAY,WACZD,EAAU,gBACV0C,EAAe,gBACfC,EAAe,WACfC,EAAU,iBACVC,EAAmB,CAAC,EAAC,SACrBC,EAAQ,YACRC,GAUDlD,EACC,MAAMmD,GAAOC,EAAAA,EAAAA,MACP,MAAEC,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,IAAA,OAAKC,KAAGC,EAAAA,EAAAA,IAAE,CAAEC,QAAS,OAAQC,SAAU,OAAQC,IAAKP,EAAMQ,QAAQC,IAAI,IAAC/H,SAAA,EACrEwH,EAAAA,EAAAA,IAACQ,EAAAA,IAAqB,CACpBC,YAAY,qCACZzG,KAAK,YACLf,MAAOyG,EACPgB,SAAWC,IACThB,GACEiB,EAAAA,EAAAA,GAAazB,EAAmCwB,EAAEE,OAAO5H,MAAOkG,EAAkC2B,OACnG,EACDtI,SAAA,EAEFwH,EAAAA,EAAAA,IAACe,EAAAA,IAAsB,CAAC9H,MAAM,QAAOT,SAAA,EACnCwI,EAAAA,EAAAA,GAACC,EAAAA,EAAO,CACNR,YAAY,mDACZS,QAAStB,EAAKuB,cAAc,CAAAC,GAAA,SAC1BC,eAAe,eAEd7I,UAEHwI,EAAAA,EAAAA,GAACM,EAAAA,EAAQ,OAEXN,EAAAA,EAAAA,GAAA,QAAMf,IAAKsB,EAAAA,EAAe/I,SACvBoH,EAAKuB,cAAc,CAAAC,GAAA,SAClBC,eAAe,qBAKrBrB,EAAAA,EAAAA,IAACe,EAAAA,IAAsB,CAAC9H,MAAM,QAAOT,SAAA,EACnCwI,EAAAA,EAAAA,GAACC,EAAAA,EAAO,CACNR,YAAY,mDACZS,QAAStB,EAAKuB,cAAc,CAAAC,GAAA,SAC1BC,eAAe,eAEd7I,UAEHwI,EAAAA,EAAAA,GAACQ,EAAAA,IAAa,OAEhBR,EAAAA,EAAAA,GAAA,QAAMf,IAAKsB,EAAAA,EAAe/I,SACvBoH,EAAKuB,cAAc,CAAAC,GAAA,SAClBC,eAAe,wBAkCvBL,EAAAA,EAAAA,GAACS,EAAAA,EAAM,CACLhB,YAAY,gCACZiB,KAAM9E,GAAaoE,EAAAA,EAAAA,GAACW,EAAAA,IAAiB,KAAMX,EAAAA,EAAAA,GAACY,EAAAA,IAAkB,IAC9DC,QAASA,KACPhF,GAAgByC,EAAgBzC,GAAeD,EAAW,EAC1DpE,UAEFwI,EAAAA,EAAAA,GAACc,EAAAA,EAAgB,CAAAV,GAAA,SACfC,eAAe,qBAInBL,EAAAA,EAAAA,GAACe,EAAAA,EAA2C,CAC1CvC,WAAYA,EACZC,iBAAkBA,EAClBF,gBAAiBA,EACjByC,SAAUtC,IAAaP,EAAkC8C,UAEvD,E,yKChIH,MAAMC,EAAiCA,CAACC,EAAoBC,IACjEA,EAAcpF,KAAKC,UAAU,CAACmF,EAAaD,IAAuB,OAATA,QAAS,IAATA,EAAAA,EAAa,GCclEE,EAAwB,SAACC,GAAoC,MACjE,6CAD6D3L,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAG,KACR2L,GAAkB,EAoFtEC,EAA2BA,KAAA,CAC/BC,sBAAkB3K,EAClB4K,wBAAoB5K,EACpB6K,oBAAoB,EACpBC,sBAAsB,EACtBC,mBAAoB,GACpBC,2BAAuBhL,EACvBiL,SAAS,IAILC,EAAuBA,CAACzM,EAA0C0M,KAEtE,GAAoB,WAAhBA,EAAOC,KACT,MAAO,IAAKD,EAAOE,YAAY5M,GAAQwM,SAAS,GAGlD,GAAoB,oBAAhBE,EAAOC,KAA4B,CACrC,MAAM,iBAAET,EAAgB,mBAAEC,GAjGmBU,KAC/C,MAAMX,EAA8CW,EAAkBC,KACpE3G,IAAA,IAAC,cAAE4G,EAAa,UAAElB,EAAS,YAAEC,GAAa3F,EAAA,MAAM,CAC9C6G,SAAS,EACTL,KAAMM,EAAAA,GAAcC,IACpBC,KAAM,WAAWJ,IACjBK,gBAAiBtB,EAAc,WAAWA,IAAgB,UAC1DuB,aAAa,EACbxB,YACAkB,gBACAjB,cACAnI,YAAamI,EAAc,IAAIA,MAAgBD,SAActK,EAC9D,IAGG4K,GAA2CmB,EAAAA,EAAAA,MAAKT,EAAkBC,KAAIxF,IAAA,IAAC,YAAEwE,GAAaxE,EAAA,OAAKwE,CAAW,KAAGgB,KAC5GhB,IAAW,CACVjC,SAAS,EACTnG,KAAiB,OAAXoI,QAAW,IAAXA,EAAAA,EAAe,UACrBqB,KAAMrB,EAAc,WAAWA,IAAgB,UAC/CyB,aAAa,MAajB,OATIC,EAAAA,EAAAA,SAAQrB,IACVA,EAAmBsB,KAAK,CACtB5D,SAAS,EACTnG,KAAM,UACNyJ,KAAM,UACNI,aAAa,IAIV,CACLrB,mBACAC,qBACD,EA6DkDuB,CAAwChB,EAAOG,mBAEhG,MA3D+Bc,EACjCC,EACAC,KAIyF,IAADC,EAAAC,EADxF,KAAKH,EAAa1B,mBAAqB0B,EAAazB,qBAAuByB,EAAapB,WAClFqB,EAAU3B,iBAAiB5L,OAAS,GAAKuN,EAAU1B,mBAAmB7L,OAAS,GACjF,MAAO,IACFsN,EACH1B,iBAA4C,QAA5B4B,EAAED,EAAU3B,wBAAgB,IAAA4B,EAAAA,EAAI,GAChD3B,mBAAgD,QAA9B4B,EAAEF,EAAU1B,0BAAkB,IAAA4B,EAAAA,EAAI,IAM1D,MAAMC,EAAiBH,EAAU3B,iBAAiB1D,QAC/CyF,IAAQ,IAAAC,EAAA,QAAmC,QAA9BA,EAACN,EAAa1B,wBAAgB,IAAAgC,GAA7BA,EAA+BC,MAAMC,GAAUA,EAAMjB,OAASc,EAASd,OAAK,IAEvFkB,EAAmBR,EAAU1B,mBAAmB3D,QACnD8F,IAAU,IAAAC,EAAA,OACTP,EAAeG,MAAMF,GAAaA,EAASb,kBAAoBkB,EAAWnB,UAC1C,QAAhCoB,EAACX,EAAazB,0BAAkB,IAAAoC,GAA/BA,EAAiCJ,MAAMK,GAAYA,EAAQrB,OAASmB,EAAWnB,OAAK,IAGzF,OAAIkB,EAAiB/N,OAAS,GAAK0N,EAAe1N,OAAS,EAClD,IACFsN,EACH1B,iBAAkB0B,EAAa1B,iBAC3B,IAAI0B,EAAa1B,oBAAqB8B,GACtCH,EAAU3B,iBACdC,mBAAoByB,EAAazB,mBAC7B,IAAIyB,EAAazB,sBAAuBkC,GACxCR,EAAU1B,oBAGXyB,CAAY,EAsBAD,CAA2B3N,EAAO,CAAEkM,mBAAkBC,sBAEzE,CACA,MAAoB,eAAhBO,EAAOC,MACLD,EAAO+B,cACF/B,EAAO+B,cAGXzO,CAAK,EAoBD0O,EAAyCA,CACpD7B,EACAb,KAEA,MAAO2C,EAAcC,IAAwBC,EAAAA,EAAAA,YAAWpC,OAAsBlL,EAAW0K,IAClF6C,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAGvCC,EAAAA,EAAAA,YAAU,KACRF,GAAW,GA1BsB1H,WAEnC,MAAM6H,EAAiBC,aAAaC,QAAQrD,EAAsBC,IAClE,GAAKkD,EAGL,IACE,OAAOxI,KAAK2I,MAAMH,EACpB,CAAE,MACA,MACF,GAiBEI,CAA6BtD,GAAiBuD,MAAMxJ,IAClD6I,EAAqB,CAAEjC,KAAM,aAAc8B,cAAe1I,IAC1DgJ,GAAW,EAAM,GACjB,GACD,CAAC/C,KAGJiD,EAAAA,EAAAA,YAAU,KACJH,GAGJF,EAAqB,CAAEjC,KAAM,kBAAmBE,qBAAoB,GACnE,CAACA,EAAmBiC,KAGvBG,EAAAA,EAAAA,YAAU,KACJN,EAAanC,SA9BKnF,OAAO2E,EAAyBwD,KACxDL,aAAaM,QAAQ1D,EAAsBC,GAAkBtF,KAAKC,UAAU6I,GAAe,EA8BvFE,CAAkB1D,EAAiB2C,EACrC,GACC,CAAC3C,EAAiB2C,IAGrB,MAAMgB,GAAgBC,EAAAA,EAAAA,cACnBhD,GACCgC,EAAqB,CACnBjC,KAAM,SACNC,iBAEJ,IAGF,MAAO,CAAE+B,eAAcgB,gBAAeb,UAAS,E,eC3Kf,IAAAe,EAAA,CAAAnM,KAAA,UAAAoM,OAAA,iBAElC,MAAMC,GAAsCC,EAAAA,EAAAA,OAC1C7J,IAQO,IAAD8J,EAAAC,EAAA,IARL,UACCC,EAAS,QACTC,EAAO,oBACPC,GAKDlK,EACC,MAAM,MAAEqD,IAAUC,EAAAA,EAAAA,MACZ,cAAEoB,IAAkBtB,EAAAA,EAAAA,KAEpB+G,GAAsBlI,EAAAA,EAAAA,UAAQ,KAAMkF,EAAAA,EAAAA,MAAK6C,EAAU9H,SAASkI,GAAQ3O,OAAO4O,KAAKD,EAAIE,aAAY,CAACN,IACjGO,GAAqBtI,EAAAA,EAAAA,UAAQ,KAAMkF,EAAAA,EAAAA,MAAK6C,EAAU9H,SAASkI,GAAQ3O,OAAO4O,KAAKD,EAAII,YAAW,CAACR,IAE/FS,GAAsBC,EAAAA,EAAAA,MAEtBC,GAAYlB,EAAAA,EAAAA,cACfmB,IACCH,GAAqB5Q,IAAK,IAAWA,EAAOsM,mBAAoByE,KAAU,GAE5E,CAACH,KAGII,EAAsBC,IAA2BjC,EAAAA,EAAAA,UAAsC,MAExFkC,GAAkBtB,EAAAA,EAAAA,cACrBxC,GAA6BT,GAC5BsE,EAAwBE,EAAAA,GAAqBC,wBAAwBzE,GAAM,OAAOpL,EAAW6L,KAC/F,IAGIiE,GAAgCC,EAAAA,EAAAA,MAChCC,GAAcC,EAAAA,EAAAA,OAEbC,EAAiBC,IAAsB1C,EAAAA,EAAAA,eAO5CzN,GAEIoQ,GAAgCvJ,EAAAA,EAAAA,UAAQ,MAASwJ,KAAMzB,KAAc,CAACA,IAEtE0B,GAAsBzJ,EAAAA,EAAAA,UAC1B,MAASwJ,KAAMzB,EAAW2B,iBAAkBC,EAAAA,EAAOC,4CACnD,CAAC7B,IAGG8B,GACJvH,EAAAA,EAAAA,GAAA,OAAKf,KAAGC,EAAAA,EAAAA,IAAE,CAAEsI,UAAW1I,EAAMQ,QAAQmI,IAAI,IAACjQ,UACxCwI,EAAAA,EAAAA,GAAC0H,EAAAA,IAAK,CACJC,aACE3H,EAAAA,EAAAA,GAACc,EAAAA,EAAgB,CAAAV,GAAA,SACfC,eAAe,0GAQzB,OACEL,EAAAA,EAAAA,GAAA,OACEf,KAAGC,EAAAA,EAAAA,IAAE,CACH0I,gBAAiB9I,EAAM+I,OAAOC,oBAC9BC,YAAajJ,EAAMQ,QAAQ0I,GAC3BC,aAAcnJ,EAAMQ,QAAQ0I,GAC5BE,cAAepJ,EAAMQ,QAAQ0I,GAE7BG,UAAW,aAAarJ,EAAM+I,OAAOO,SACrCC,WAAY,aAAavJ,EAAM+I,OAAOO,SAEtCE,KAAM,EACNC,SAAU,SACVpJ,QAAS,QACV,IAAC3H,UAEFwH,EAAAA,EAAAA,IAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,QAAS,OACTqJ,cAAe,SACfnJ,IAAKP,EAAMQ,QAAQC,GACnBkJ,WAAY3J,EAAMQ,QAAQC,GAC1BgJ,SAAU,SACVD,KAAM,GACP,IAAC9Q,SAAA,EAEFwI,EAAAA,EAAAA,GAAC0I,EAAAA,EAAK,CACJjJ,YAAY,yCACZkJ,KAAK,YACLC,QAAQ5I,EAAAA,EAAAA,GAAC6I,EAAAA,EAAU,IACnB5Q,MAAiC,QAA5BsN,EAAEG,EAAQ9D,0BAAkB,IAAA2D,EAAAA,EAAI,GACrCuD,YAAU,EACVpJ,SAAU9C,IAAA,IAAC,OAAEiD,GAAQjD,EAAA,OAAKwJ,EAAUvG,EAAO5H,MAAM,EACjD8Q,YAAa5I,EAAc,CAAAC,GAAA,SACzBC,eAAe,4BAInBrB,EAAAA,EAAAA,IAAA,OAAKC,IAAGkG,EAAuB3N,SAAA,EAC7BwI,EAAAA,EAAAA,GAACgJ,EAAAA,EAAwB,CAACC,YAAa9B,EAAqB+B,UAAWC,EAAAA,EAAsB3R,UAC3FwI,EAAAA,EAAAA,GAACoJ,EAAAA,GAA2C,CAACC,kBAAmB3D,EAAQlE,iBAAiBhK,UACvFwI,EAAAA,EAAAA,GAACsJ,EAAAA,EAA0B,CACzB7H,mBAAoBiE,EAAQjE,mBAC5BD,iBAAkBkE,EAAQlE,iBAC1B+H,cAAeC,EAAAA,KACfC,aAAcD,EAAAA,KACd/D,UAAWA,EACXiE,eAAgBnD,EAChBM,YAAaA,EACbL,gBAAiBA,EACjBH,OAAkC,QAA5Bb,EAAEE,EAAQ9D,0BAAkB,IAAA4D,EAAAA,EAAI,GACtCmE,QAAS,KACT3C,mBAAoBA,EACpBtF,oBAAoB,EACpBkI,iBAAiB,EACjB/H,2BAAuBhL,EACvBgT,oBAAqB,CAACtH,EAAAA,GAAcC,IAAKD,EAAAA,GAAcuH,SACvDC,yBAA0BxC,SAIhCvH,EAAAA,EAAAA,GAACgK,EAAAA,EAAyB,CACxBjD,gBAAiBA,EACjBkD,SAAUA,IAAMjD,OAAmBnQ,GACnC4O,UAAWA,EACXkE,QAAS,KACTxC,oBAAqBF,EACrBiD,iBAAkBf,EAAAA,EAClBzH,oBAAoB,EACpBG,2BAAuBhL,IAExByP,IACCtG,EAAAA,EAAAA,GAACmK,EAAAA,EAAwB,CACvBC,aAAc3E,EACd4E,cAAezE,EACfD,oBAAqBA,EACrB2E,aAActE,EACduE,OAAQjE,EACRkE,SAAWlE,IACTK,EAA8B,IAAKL,EAAsBrN,iBAAapC,IACtE0P,EAAwB,KAAK,EAE/B0D,SAAUA,IAAM1D,EAAwB,MACxCoD,QAAS,KACTE,oBAAqB,CAACtH,EAAAA,GAAcC,IAAKD,EAAAA,GAAcuH,kBAK3D,IAKCW,IAAkCnF,EAAAA,EAAAA,OAC7CoF,IAAiG,IAAhG,aAAEC,EAAY,aAAEC,GAA0EF,EACzF,MAAM,MAAE5L,IAAUC,EAAAA,EAAAA,KAIZ8L,ECxLiBC,EAAIC,EAAkBC,KAC/C,MAAMC,GAAMC,EAAAA,EAAAA,UAMZ,OAJKD,EAAIE,UAAYC,EAAAA,EAAAA,SAAQJ,EAAMC,EAAIE,QAAQH,QAC7CC,EAAIE,QAAU,CAAEH,OAAM/S,MAAO8S,MAGxBE,EAAIE,QAAQlT,KAAK,EDiLK6S,EAAY,IAAMH,GAAc,CAACA,IAEtDU,EE3LkDV,KAC1DjN,EAAAA,EAAAA,UAAQ,KACN,MAAM2N,EAAqD,GAS3D,OARAV,EAAaW,SAASC,IAAW,IAADC,EAAAC,EACpB,QAAVD,EAAAD,EAAMlQ,YAAI,IAAAmQ,GAAS,QAATC,EAAVD,EAAYzF,eAAO,IAAA0F,GAAnBA,EAAqBH,SAAQ7P,IAAoD,IAAjDiQ,IAAKvK,EAAWwK,aAAcvK,GAAa3F,EACzE,GAAI0F,IAAckK,EAAiB5H,MAAM9D,GAAMA,EAAEwB,YAAcA,GAAaxB,EAAEyB,cAAgBA,IAAc,CAC1G,MAAMiB,EAAgBnB,EAA+BC,EAAWC,GAChEiK,EAAiBtI,KAAK,CAAE5B,YAAWC,cAAaiB,iBAClD,IACA,KAEGuJ,EAAAA,EAAAA,SAAQP,GAAkBzO,IAAA,IAAC,YAAEwE,GAAaxE,EAAA,OAAMwE,CAAW,GAAC,GAClE,CAACuJ,IF+KuBkB,CAA4ChB,IAE/D,aACJ5G,EAAY,cACZgB,EACAb,QAAS0H,GACP9H,EAAuCqH,EAAkBT,GACvDnF,EF1L0CkF,KAClD,MAAM,YAAEoB,IAAgBC,EAAAA,EAAAA,MACxB,OAAOtO,EAAAA,EAAAA,UACL,KACEuO,EAAAA,EAAAA,SACEtB,EAAavI,KAA8B,CAACmJ,EAAOtU,KAAK,IAAAiV,EAAAzQ,EAAA0Q,EAAAC,EAAAC,EAAAb,EAAAC,EAAAa,EAAAC,EAAAC,EAAA,OAC5C,QAAVN,EAAAX,EAAMlV,YAAI,IAAA6V,GAAVA,EAAYO,SACR,CACExT,YAAqD,QAA1CwC,EAAkB,QAAlB0Q,EAAY,QAAZC,EAAEb,EAAMlV,YAAI,IAAA+V,OAAA,EAAVA,EAAYpT,YAAI,IAAAmT,EAAAA,EAAc,QAAdE,EAAId,EAAMlV,YAAI,IAAAgW,OAAA,EAAVA,EAAYI,gBAAQ,IAAAhR,EAAAA,EAAI,UACzDiR,OAAQ,CAAC,EAET3G,SAAS4G,EAAAA,EAAAA,OACG,QADEnB,EACZD,EAAMlQ,YAAI,IAAAmQ,GAAS,QAATC,EAAVD,EAAYzF,eAAO,IAAA0F,OAAT,EAAVA,EAAqBrJ,KAAIxF,IAAA,IAAC,aAAE+O,EAAY,IAAED,EAAG,MAAEzT,EAAK,UAAE2U,EAAS,KAAEC,GAAMjQ,EAAA,MAAM,CAE3EkQ,QAAS5L,EAA+BwK,EAAKC,GAC7CD,IAAQ,OAAHA,QAAG,IAAHA,EAAAA,EAAO,GACZzT,MAAY,OAALA,QAAK,IAALA,EAAAA,EAAS,EAChB2U,UAAoB,OAATA,QAAS,IAATA,EAAAA,EAAa,EACxBC,KAAU,OAAJA,QAAI,IAAJA,EAAAA,EAAQ,EACf,IACD,WAEF5G,QAAQ0G,EAAAA,EAAAA,OAGqB,QAHhBL,EACD,QADCC,EACXhB,EAAMlQ,YAAI,IAAAkR,GAAQ,QAARC,EAAVD,EAAYtG,cAAM,IAAAuG,OAAR,EAAVA,EACIpK,KAAI+C,IAAA,IAAC,IAAEuG,EAAG,MAAEzT,GAAOkN,EAAA,MAAM,CAAEuG,IAAQ,OAAHA,QAAG,IAAHA,EAAAA,EAAO,GAAIzT,MAAY,OAALA,QAAK,IAALA,EAAAA,EAAS,GAAI,IAChE6F,QAAO4M,IAAA,IAAC,IAAEgB,GAAKhB,EAAA,OAAKgB,CAAG,WAAC,IAAAY,EAAAA,EAAI,GAC/B,OAEFS,KAAM,CAAC,EACPtK,KAAM8I,EAAMlV,KAAKoW,SACjBO,OAAQjB,EAAYR,EAAMlV,KAAKoW,SAAUxV,GACzCgW,OAAOC,EAAAA,EAAAA,IAAqB3B,EAAMlV,KAAKoW,WAEzC,IAAI,MAGd,CAAC9B,EAAcoB,GAChB,EEqJmBoB,CAAoCtC,GAEtD,OAAIiB,GAEA9L,EAAAA,EAAAA,GAAA,OACEf,KAAGC,EAAAA,EAAAA,IAAE,CACH0I,gBAAiB9I,EAAM+I,OAAOC,oBAC9BW,WAAY3J,EAAMQ,QAAQmI,GAC1BU,UAAW,aAAarJ,EAAM+I,OAAOO,SACrCC,WAAY,aAAavJ,EAAM+I,OAAOO,SACtCE,KAAM,EACN8E,eAAgB,SAChBC,WAAY,SACZlO,QAAS,QACV,IAAC3H,UAEFwI,EAAAA,EAAAA,GAACsN,EAAAA,EAAO,OAKZtN,EAAAA,EAAAA,GAACuN,EAAAA,GAAwC,CAACrH,oBAAqBjB,EAAczN,UAC3EwI,EAAAA,EAAAA,GAACqF,EAAmC,CAClCI,UAAWA,EACXC,QAASzB,EACT0B,oBAAqB0F,KAEkB,I,gBG1Mb,IAAA5P,GAAA,CAAAzC,KAAA,SAAAoM,OAAA,yDAEpC,MAAMoI,GAAoCA,KACxC,MAAM,aAAE5C,IAAiB6C,EAAAA,EAAAA,MACnB,MAAE3O,IAAUC,EAAAA,EAAAA,KACZ2O,GAAWC,EAAAA,EAAAA,OAGfrY,OAAO,aAAEuG,EAAY,WAAED,EAAU,iBAAE6C,EAAgB,iBAAEmP,EAAgB,kBAAEC,GAAmB,WAC1FC,EAAU,oBACVC,EAAmB,qBACnBC,EAAoB,oBACpBC,IACEC,EAAAA,EAAAA,KAEJC,IAAUvD,EAAc,kCAExBrG,EAAAA,EAAAA,YAAU,OACH6J,EAAAA,EAAAA,OAAuCxD,GAC1C8C,EAASrG,EAAAA,EAAOgH,uBAAuBzD,GAAe,CAAE0D,SAAS,GACnE,GACC,CAAC1D,EAAc8C,IAElB,MAAM,SAAEhP,EAAQ,YAAEC,GPtCgC4P,MAClD,MAAOtI,EAAQuI,IAAaC,EAAAA,EAAAA,MAS5B,MAAO,CAAE/P,UARQkB,EAAAA,EAAAA,GACfzB,EACA8H,EAAOyI,IAAItQ,GACXD,EAAkC2B,OAKjBnB,YAHEgQ,IACnBH,EAAU,CAAE,CAACpQ,GAAwBuQ,GAAO,EAEd,EO4BEJ,IAGhClT,KAAMsP,EACN5P,WAAY6T,EACZ1S,UAAW2S,EACX1Z,MAAO2Z,EAAiB,cACxB9Q,EAAa,gBACbE,GACE1C,EAA2B,CAC7BG,cAAe,CAACiP,GAChBhP,aACAC,kBAGMR,KAAM0T,IAAoBC,EAAAA,EAAAA,GAAkC,CAAErE,iBAEhEnM,GAAayQ,EAAAA,EAAAA,IAA6C,CAC9DtE,eACAlM,mBACAyQ,cAAexQ,IAAaP,EAAkC2B,SAGzDqP,EAAgBC,IAAqB9K,EAAAA,EAAAA,UA9Cd,MA+CvB+K,EAAaC,IAAkBhL,EAAAA,EAAAA,WAAS,GAEzCiL,EAAqB7Q,IAAaP,EAAkC2B,MAEpE0P,EACJD,GAAsBF,GACpBrP,EAAAA,EAAAA,GAAA,OAAKf,KAAGC,EAAAA,EAAAA,IAAE,CAAEuQ,MAAO3Q,EAAMQ,QAAQ0I,IAAI,OAErChI,EAAAA,EAAAA,GAAC0P,EAAAA,EAAkC,CACjClR,WAAYA,EACZmM,aAA0B,OAAZA,QAAY,IAAZA,EAAAA,EAAgB,GAC9BzO,UAAW2S,EACXc,cAAef,EACfzZ,MAAO2Z,EACPc,qBAAsB7R,QAAQC,GAC9B6R,WAAY3R,EACZ4R,gBAAiBhC,EACjBlS,WAAYA,EACZC,aAAcA,EACd4C,iBAAkBA,EAClBsQ,gBAAiBA,IAIvB,OACE/O,EAAAA,EAAAA,GAAC+P,EAAAA,EAAsD,CAAAvY,UACrDwH,EAAAA,EAAAA,IAACgR,EAAAA,GAAyD,CACxDC,cAAerC,EACfsC,eAAgBrC,EAChBG,qBAAsBA,EACtBC,oBAAqBA,EAAoBzW,SAAA,EAEzCwI,EAAAA,EAAAA,GAAC3B,EAAqC,CACpCG,WAAYA,EACZC,iBAAkBA,EAClBH,gBAAiBwP,EACjBvP,gBAAiBwP,EACjBlS,aAAcA,EACdD,WAAYA,EACZ8C,SAAUA,EACVC,YAAaA,KAEfqB,EAAAA,EAAAA,GAACmQ,EAAAA,EAAM,CAACC,KAAK,KAAKC,SAAS,KACT,OAAjBvB,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmBwB,WAClBtR,EAAAA,EAAAA,IAAAuR,EAAAA,GAAA,CAAA/Y,SAAA,EACEwI,EAAAA,EAAAA,GAACwQ,EAAAA,IAAK,CACJ/Q,YAAY,kCACZ6Q,QAASxB,EAAkBwB,QAC3BrO,KAAK,QACLwO,UAAU,KAEZzQ,EAAAA,EAAAA,GAACmQ,EAAAA,EAAM,CAACC,KAAK,KAAKC,SAAS,OAG9Bd,GACCvP,EAAAA,EAAAA,GAAC0Q,GAAAA,GAAqC,CAAAlZ,UACpCwH,EAAAA,EAAAA,IAAA,OAAKC,IAAGxD,GAAyEjE,SAAA,EAC/EwI,EAAAA,EAAAA,GAAC2Q,EAAAA,EAA8B,CAC7BC,SAAUxB,EACVyB,cAAexB,EACfI,MAAON,EACP2B,eAAgBxB,EAAe9X,SAE9BgY,IAEF9Q,IAAaP,EAAkC8C,QAC9CjB,EAAAA,EAAAA,GAACyK,GAA+B,CAACE,aAA0B,OAAZA,QAAY,IAAZA,EAAAA,EAAgB,GAAIC,aAAcA,SAKvF4E,MAGmD,EAU7D,OANsCuB,KACpC/Q,EAAAA,EAAAA,GAACgR,EAAAA,EAAgC,CAAAxZ,UAC/BwI,EAAAA,EAAAA,GAACwN,GAAiC,K,uGChJtC,MAAMyD,EAAeC,GAA+D,CAClF,0CACA,CAAEA,YAGExU,EAAUC,UAEmF,IADjGZ,UAAW,EAAE,QAAEmV,KACiDzV,EAChE,IACE,MAAMJ,QAAa8V,EAAAA,EAAcC,OAAO,CAAEC,OAAQH,IAClD,OAAW,OAAJ7V,QAAI,IAAJA,OAAI,EAAJA,EAAMwK,GACf,CAAE,MAAOlG,GACP,OAAO,IACT,GAMWqP,EAAoCpS,IAAmE,IAAD0U,EAAA,IAAjE,aAAE3G,EAAe,IAA2C/N,EAC5G,MAAM2U,GAAW7T,EAAAA,EAAAA,UAAQ,KAEvB,MAAM8T,GAAoBvF,EAAAA,EAAAA,SACZ,OAAZtB,QAAY,IAAZA,OAAY,EAAZA,EAAchN,SAAS8T,IAAW,IAAAC,EAAAC,EAAA,OAAgB,OAAXF,QAAW,IAAXA,GAAiB,QAANC,EAAXD,EAAapW,YAAI,IAAAqW,GAAS,QAATC,EAAjBD,EAAmB3L,eAAO,IAAA4L,OAAf,EAAXA,EAA4BvP,KAAKwP,GAAWA,EAAOP,QAAO,KAE7FQ,GAAoB5F,EAAAA,EAAAA,SAAoB,OAAZtB,QAAY,IAAZA,OAAY,EAAZA,EAAcvI,KAAKqP,IAAW,IAAAK,EAAA,OAAgB,OAAXL,QAAW,IAAXA,GAAiB,QAANK,EAAXL,EAAapb,YAAI,IAAAyb,OAAN,EAAXA,EAAmBC,aAAa,KAGrG,OAFyBC,EAAAA,EAAAA,SAAOpP,EAAAA,EAAAA,MAAK,IAAI4O,KAAsBK,IAExC,GACtB,CAAClH,IAEEsH,GAAeC,EAAAA,EAAAA,GAAW,CAC9BC,QAASZ,EAASnP,KAAK8O,IAAO,CAC5BnV,SAAUkV,EAAYC,GACtBxU,gBAIE0H,EAAU6N,EAAalb,MAAKoO,IAAA,IAAC,UAAEjJ,GAAWiJ,EAAA,OAAKjJ,CAAS,IACxD/G,EAA+C,QAA1Cmc,EAAGW,EAAaxO,MAAKiH,IAAA,IAAC,MAAEvV,GAAOuV,EAAA,OAAKvV,CAAK,WAAC,IAAAmc,OAAA,EAAvCA,EAAyCnc,MAIvD,MAAO,CACLkG,MAHWqC,EAAAA,EAAAA,UAAQ,IAAMuU,EAAa7P,KAAIgQ,IAAA,IAAC,KAAE/W,GAAM+W,EAAA,OAAK/W,CAAI,IAAEyC,OAAOC,UAAyB,CAACkU,IAI/F7N,UACAjP,QACD,C,6HCnD2C,IAAAyH,EAAA,CAAA5D,KAAA,UAAAoM,OAAA,iEAE9C,MAAMiN,EAAe5W,IAAmC,IAAD6W,EAAA,IAAjC,MAAEnd,GAA0BsG,EAChD,OACEuE,EAAAA,EAAAA,GAACuS,EAAAA,IAAW,CAACtT,IAAGrC,EAA+EpF,UAC7FwI,EAAAA,EAAAA,GAAC0H,EAAAA,IAAK,CACJ,cAAY,WACZ8K,OACExS,EAAAA,EAAAA,GAACc,EAAAA,EAAgB,CAAAV,GAAA,SACfC,eAAe,UAInBsH,YACgB,QADL2K,EACJ,OAALnd,QAAK,IAALA,OAAK,EAALA,EAAOmb,eAAO,IAAAgC,EAAAA,GACZtS,EAAAA,EAAAA,GAACc,EAAAA,EAAgB,CAAAV,GAAA,SACfC,eAAe,sDAKrBoS,OAAOzS,EAAAA,EAAAA,GAAC0S,EAAAA,EAAU,OAER,EAQL1B,EAAmC7L,IAMzC,IAN0C,SAC/C3N,EAAQ,SACRmb,GAIDxN,EACC,OACEnF,EAAAA,EAAAA,GAAC4S,EAAAA,GAAa,CAAClb,kBAAmB2a,EAAc1b,UAAW,CAACgc,GAAUnb,UACpEwI,EAAAA,EAAAA,GAAC6S,EAAAA,GAAsB,CAAArb,SAAEA,KACX,C", "sources": ["../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "../node_modules/@tanstack/query-core/src/infiniteQueryObserver.ts", "experiment-tracking/hooks/logged-models/useSearchLoggedModelsQuery.tsx", "../node_modules/@tanstack/react-query/src/useInfiniteQuery.ts", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelListPageMode.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListPageControls.tsx", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelsChartsData.tsx", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelsChartsUIState.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListCharts.tsx", "common/hooks/useMemoDeep.ts", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelAllMetricsByDataset.tsx", "experiment-tracking/pages/experiment-logged-models/ExperimentLoggedModelListPage.tsx", "experiment-tracking/hooks/logged-models/useRelatedRunsDataForLoggedModels.tsx", "experiment-tracking/pages/experiment-logged-models/ExperimentLoggedModelPageWrapper.tsx"], "sourcesContent": ["import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "import type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  Query<PERSON><PERSON>,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryKey\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n", "import { useInfiniteQuery } from '@mlflow/mlflow/src/common/utils/reactQueryHooks';\nimport { last } from 'lodash';\nimport { LoggedModelProto } from '../../types';\nimport { loggedModelsDataRequest } from './request.utils';\nimport { useMemo } from 'react';\n\ntype UseSearchLoggedModelsQueryResponseType = {\n  models: LoggedModelProto[];\n  next_page_token?: string;\n};\n\nexport const useSearchLoggedModelsQuery = (\n  {\n    experimentIds,\n    orderByAsc,\n    orderByField,\n  }: {\n    experimentIds?: string[];\n    orderByAsc?: boolean;\n    orderByField?: string;\n  },\n  {\n    enabled = true,\n  }: {\n    enabled?: boolean;\n  } = {},\n) => {\n  // Uniquely identify the query by the experiment IDs, order by field, and order by asc\n  const queryKey = ['SEARCH_LOGGED_MODELS', JSON.stringify(experimentIds), orderByField, orderByAsc];\n\n  const { data, isLoading, isFetching, fetchNextPage, refetch, error } = useInfiniteQuery<\n    UseSearchLoggedModelsQueryResponseType,\n    Error\n  >({\n    queryKey,\n    queryFn: async ({ pageParam }) => {\n      const requestBody = {\n        experiment_ids: experimentIds,\n        order_by: [{ field_name: orderByField ?? 'creation_time', ascending: orderByAsc ?? false }],\n        page_token: pageParam,\n      };\n\n      return loggedModelsDataRequest('/ajax-api/2.0/mlflow/logged-models/search', 'POST', requestBody);\n    },\n    cacheTime: 0,\n    getNextPageParam: (lastPage) => lastPage.next_page_token,\n    refetchOnWindowFocus: false,\n    retry: false,\n    enabled,\n  });\n\n  // Concatenate all the models from all the result pages\n  const modelsData = useMemo(() => data?.pages.flatMap((page) => page?.models).filter(Boolean), [data]);\n\n  // The current page token is the one from the last page\n  const nextPageToken = last(data?.pages)?.next_page_token;\n\n  return {\n    isLoading,\n    isFetching,\n    data: modelsData,\n    nextPageToken,\n    refetch,\n    error,\n    loadMoreResults: fetchNextPage,\n  } as const;\n};\n", "import 'client-only'\nimport type {\n  QueryObserver,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport { InfiniteQueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n", "import { coerceToEnum } from '@databricks/web-shared/utils';\nimport { useSearchParams } from '../../../../common/utils/RoutingUtils';\n\nexport enum ExperimentLoggedModelListPageMode {\n  TABLE = 'TABLE',\n  CHART = 'CHART',\n}\n\nconst VIEW_MODE_QUERY_PARAM = 'viewMode';\n\nexport const useExperimentLoggedModelListPageMode = () => {\n  const [params, setParams] = useSearchParams();\n  const viewMode = coerceToEnum(\n    ExperimentLoggedModelListPageMode,\n    params.get(VIEW_MODE_QUERY_PARAM),\n    ExperimentLoggedModelListPageMode.TABLE,\n  );\n  const setViewMode = (mode: ExperimentLoggedModelListPageMode) => {\n    setParams({ [VIEW_MODE_QUERY_PARAM]: mode });\n  };\n  return { viewMode, setViewMode } as const;\n};\n", "import {\n  Button,\n  ChartLineIcon,\n  ListIcon,\n  SegmentedControlButton,\n  SegmentedControlGroup,\n  SortAscendingIcon,\n  SortDescendingIcon,\n  Tooltip,\n  useDesignSystemTheme,\n  visuallyHidden,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { FormattedMessage } from 'react-intl';\nimport type { ColDef, ColGroupDef } from '@ag-grid-community/core';\nimport { ExperimentLoggedModelListPageColumnSelector } from './ExperimentLoggedModelListPageColumnSelector';\nimport { coerceToEnum } from '@databricks/web-shared/utils';\nimport { ExperimentLoggedModelListPageMode } from './hooks/useExperimentLoggedModelListPageMode';\n\nexport const ExperimentLoggedModelListPageControls = ({\n  orderByField,\n  orderByAsc,\n  onChangeOrderBy,\n  onUpdateColumns,\n  columnDefs,\n  columnVisibility = {},\n  viewMode,\n  setViewMode,\n}: {\n  orderByField?: string;\n  orderByAsc?: boolean;\n  onChangeOrderBy: (orderByField: string, orderByAsc: boolean) => void;\n  onUpdateColumns: (columnVisibility: Record<string, boolean>) => void;\n  columnDefs?: (ColDef | ColGroupDef)[];\n  columnVisibility?: Record<string, boolean>;\n  viewMode: ExperimentLoggedModelListPageMode;\n  setViewMode: (mode: ExperimentLoggedModelListPageMode) => void;\n}) => {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <div css={{ display: 'flex', flexWrap: 'wrap', gap: theme.spacing.sm }}>\n      <SegmentedControlGroup\n        componentId=\"mlflow.logged_model.list.view-mode\"\n        name=\"view-mode\"\n        value={viewMode}\n        onChange={(e) => {\n          setViewMode(\n            coerceToEnum(ExperimentLoggedModelListPageMode, e.target.value, ExperimentLoggedModelListPageMode.TABLE),\n          );\n        }}\n      >\n        <SegmentedControlButton value=\"TABLE\">\n          <Tooltip\n            componentId=\"mlflow.logged_model.list.view-mode-table-tooltip\"\n            content={intl.formatMessage({\n              defaultMessage: 'Table view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          >\n            <ListIcon />\n          </Tooltip>\n          <span css={visuallyHidden}>\n            {intl.formatMessage({\n              defaultMessage: 'Table view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          </span>\n        </SegmentedControlButton>\n        <SegmentedControlButton value=\"CHART\">\n          <Tooltip\n            componentId=\"mlflow.logged_model.list.view-mode-chart-tooltip\"\n            content={intl.formatMessage({\n              defaultMessage: 'Chart view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          >\n            <ChartLineIcon />\n          </Tooltip>\n          <span css={visuallyHidden}>\n            {intl.formatMessage({\n              defaultMessage: 'Chart view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          </span>\n        </SegmentedControlButton>\n      </SegmentedControlGroup>\n      {/* TODO: enable when filtering is available */}\n      {/* <Input\n        prefix={<SearchIcon />}\n        componentId=\"mlflow.logged_model.list.search\"\n        css={{ width: 430 }}\n        placeholder={intl.formatMessage({\n          defaultMessage: 'Search models',\n          description: 'Placeholder for the search input in the logged model list page',\n        })}\n        allowClear\n        value={searchQuery}\n        onChange={(e) => onChangeSearchQuery(e.target.value)}\n      /> */}\n\n      {/* TODO: enable when filtering is available */}\n      {/* <DropdownMenu.Root>\n        <DropdownMenu.Trigger asChild>\n          <Button componentId=\"mlflow.logged_model.list.filter\" icon={<FilterIcon />}>\n            <FormattedMessage\n              defaultMessage=\"Filter\"\n              description=\"Label for the filter button in the logged model list page\"\n            />\n          </Button>\n        </DropdownMenu.Trigger>\n        <DropdownMenu.Content>\n          <DropdownMenu.Item componentId=\"\">[TODO: implement filters]</DropdownMenu.Item>\n        </DropdownMenu.Content>\n      </DropdownMenu.Root> */}\n      <Button\n        componentId=\"mlflow.logged_model.list.sort\"\n        icon={orderByAsc ? <SortAscendingIcon /> : <SortDescendingIcon />}\n        onClick={() => {\n          orderByField && onChangeOrderBy(orderByField, !orderByAsc);\n        }}\n      >\n        <FormattedMessage\n          defaultMessage=\"Sort: Created\"\n          description=\"Label for the sort button in the logged model list page\"\n        />\n      </Button>\n      <ExperimentLoggedModelListPageColumnSelector\n        columnDefs={columnDefs}\n        columnVisibility={columnVisibility}\n        onUpdateColumns={onUpdateColumns}\n        disabled={viewMode === ExperimentLoggedModelListPageMode.CHART}\n      />\n    </div>\n  );\n};\n", "import { compact, keyBy } from 'lodash';\nimport { useMemo } from 'react';\nimport { LoggedModelProto } from '../../../types';\nimport { getStableColorForRun } from '../../../utils/RunNameUtils';\nimport type { RunsChartsRunData } from '../../runs-charts/components/RunsCharts.common';\nimport { useExperimentLoggedModelListPageRowVisibilityContext } from './useExperimentLoggedModelListPageRowVisibility';\n\nexport const getMetricByDatasetChartDataKey = (metricKey?: string, datasetName?: string) =>\n  datasetName ? JSON.stringify([datasetName, metricKey]) : metricKey ?? '';\n\n/**\n * Creates chart-consumable data based on logged models, including metrics and parameters.\n * TODO: optimize, add unit tests\n */\nexport const useExperimentLoggedModelsChartsData = (loggedModels: LoggedModelProto[]) => {\n  const { isRowHidden } = useExperimentLoggedModelListPageRowVisibilityContext();\n  return useMemo<RunsChartsRunData[]>(\n    () =>\n      compact(\n        loggedModels.map<RunsChartsRunData | null>((model, index) =>\n          model.info?.model_id\n            ? {\n                displayName: model.info?.name ?? model.info?.model_id ?? 'Unknown',\n                images: {},\n\n                metrics: keyBy(\n                  model.data?.metrics?.map(({ dataset_name, key, value, timestamp, step }) => ({\n                    // Instead of using plain metric key, we will use specific data access key generated based on metric key and dataset\n                    dataKey: getMetricByDatasetChartDataKey(key, dataset_name),\n                    key: key ?? '',\n                    value: value ?? 0,\n                    timestamp: timestamp ?? 0,\n                    step: step ?? 0,\n                  })),\n                  'dataKey',\n                ),\n                params: keyBy(\n                  model.data?.params\n                    ?.map(({ key, value }) => ({ key: key ?? '', value: value ?? '' }))\n                    .filter(({ key }) => key) ?? [],\n                  'key',\n                ),\n                tags: {},\n                uuid: model.info.model_id,\n                hidden: isRowHidden(model.info.model_id, index),\n                color: getStableColorForRun(model.info.model_id),\n              }\n            : null,\n        ),\n      ),\n    [loggedModels, isRowHidden],\n  );\n};\n", "import { useCallback, useEffect, useReducer, useState } from 'react';\nimport type { ExperimentRunsChartsUIConfiguration } from '../../experiment-page/models/ExperimentPageUIState';\nimport { ChartSectionConfig } from '../../../types';\nimport {\n  RunsChartsBarCardConfig,\n  RunsChartsCardConfig,\n  RunsChartsMetricByDatasetEntry,\n  RunsChartType,\n} from '../../runs-charts/runs-charts.types';\nimport { isEmpty, uniq } from 'lodash';\nimport { RunsChartsUIConfigurationSetter } from '../../runs-charts/hooks/useRunsChartsUIConfiguration';\n\ntype UpdateChartStateAction = { type: 'UPDATE'; stateSetter: RunsChartsUIConfigurationSetter };\ntype InitializeChartStateAction = { type: 'INITIALIZE'; initialConfig?: LoggedModelsChartsUIConfiguration };\ntype NewLoggedModelsStateAction = { type: 'METRICS_UPDATED'; metricsByDatasets: RunsChartsMetricByDatasetEntry[] };\n\ntype ChartsReducerAction = UpdateChartStateAction | NewLoggedModelsStateAction | InitializeChartStateAction;\n\ninterface LoggedModelsChartsUIConfiguration extends ExperimentRunsChartsUIConfiguration {\n  isDirty: boolean;\n}\n\nconst createLocalStorageKey = (storeIdentifier: string, version = 1) =>\n  `experiment-logged-models-charts-ui-state-v${version}-${storeIdentifier}`;\n\n/**\n * Generates a list of chart tiles based on logged models metrics and datasets.\n */\nconst getExperimentLoggedModelsPageChartSetup = (metricsByDatasets: RunsChartsMetricByDatasetEntry[]) => {\n  const compareRunCharts: RunsChartsBarCardConfig[] = metricsByDatasets.map(\n    ({ dataAccessKey, metricKey, datasetName }) => ({\n      deleted: false,\n      type: RunsChartType.BAR,\n      uuid: `autogen-${dataAccessKey}`,\n      metricSectionId: datasetName ? `autogen-${datasetName}` : 'default',\n      isGenerated: true,\n      metricKey,\n      dataAccessKey,\n      datasetName,\n      displayName: datasetName ? `(${datasetName}) ${metricKey}` : undefined,\n    }),\n  );\n\n  const compareRunSections: ChartSectionConfig[] = uniq(metricsByDatasets.map(({ datasetName }) => datasetName)).map(\n    (datasetName) => ({\n      display: true,\n      name: datasetName ?? 'Metrics',\n      uuid: datasetName ? `autogen-${datasetName}` : 'default',\n      isReordered: false,\n    }),\n  );\n\n  if (isEmpty(compareRunSections)) {\n    compareRunSections.push({\n      display: true,\n      name: 'Metrics',\n      uuid: 'default',\n      isReordered: false,\n    });\n  }\n\n  return {\n    compareRunCharts,\n    compareRunSections,\n  };\n};\n\n// Internal utility function  used to merge the current charts state with potentially incoming new charts and sections\nconst reconcileChartsAndSections = (\n  currentState: LoggedModelsChartsUIConfiguration,\n  newCharts: { compareRunCharts: RunsChartsCardConfig[]; compareRunSections: ChartSectionConfig[] },\n) => {\n  // If there are no charts / sections, or if the state is in pristine state, just set the new charts if they're not empty\n  if (!currentState.compareRunCharts || !currentState.compareRunSections || !currentState.isDirty) {\n    if (newCharts.compareRunCharts.length > 0 || newCharts.compareRunSections.length > 0) {\n      return {\n        ...currentState,\n        compareRunCharts: newCharts.compareRunCharts ?? [],\n        compareRunSections: newCharts.compareRunSections ?? [],\n      };\n    }\n  }\n\n  // Otherwise, detect new sections and charts and add them to the list\n  const newChartsToAdd = newCharts.compareRunCharts.filter(\n    (newChart) => !currentState.compareRunCharts?.find((chart) => chart.uuid === newChart.uuid),\n  );\n  const newSectionsToAdd = newCharts.compareRunSections.filter(\n    (newSection) =>\n      newChartsToAdd.find((newChart) => newChart.metricSectionId === newSection.uuid) &&\n      !currentState.compareRunSections?.find((section) => section.uuid === newSection.uuid),\n  );\n\n  if (newSectionsToAdd.length > 0 || newChartsToAdd.length > 0) {\n    return {\n      ...currentState,\n      compareRunCharts: currentState.compareRunCharts\n        ? [...currentState.compareRunCharts, ...newChartsToAdd]\n        : newCharts.compareRunCharts,\n      compareRunSections: currentState.compareRunSections\n        ? [...currentState.compareRunSections, ...newSectionsToAdd]\n        : newCharts.compareRunSections,\n    };\n  }\n  return currentState;\n};\n\nconst chartsUIStateInitializer = (): LoggedModelsChartsUIConfiguration => ({\n  compareRunCharts: undefined,\n  compareRunSections: undefined,\n  autoRefreshEnabled: false,\n  isAccordionReordered: false,\n  chartsSearchFilter: '',\n  globalLineChartConfig: undefined,\n  isDirty: false,\n});\n\n// Reducer to manage the state of the charts UI\nconst chartsUIStateReducer = (state: LoggedModelsChartsUIConfiguration, action: ChartsReducerAction) => {\n  // 'UPDATE' is sent by controls that updates the UI state directly\n  if (action.type === 'UPDATE') {\n    return { ...action.stateSetter(state), isDirty: true };\n  }\n  // 'METRICS_UPDATED' is sent when new logged models data is available and potentially new charts need to be added\n  if (action.type === 'METRICS_UPDATED') {\n    const { compareRunCharts, compareRunSections } = getExperimentLoggedModelsPageChartSetup(action.metricsByDatasets);\n    const newState = reconcileChartsAndSections(state, { compareRunCharts, compareRunSections });\n    return newState;\n  }\n  if (action.type === 'INITIALIZE') {\n    if (action.initialConfig) {\n      return action.initialConfig;\n    }\n  }\n  return state;\n};\n\nconst loadPersistedDataFromStorage = async (storeIdentifier: string) => {\n  // This function is async on purpose to accommodate potential asynchoronous storage mechanisms (e.g. IndexedDB) in the future\n  const serializedData = localStorage.getItem(createLocalStorageKey(storeIdentifier));\n  if (!serializedData) {\n    return undefined;\n  }\n  try {\n    return JSON.parse(serializedData);\n  } catch {\n    return undefined;\n  }\n};\n\nconst saveDataToStorage = async (storeIdentifier: string, dataToPersist: LoggedModelsChartsUIConfiguration) => {\n  localStorage.setItem(createLocalStorageKey(storeIdentifier), JSON.stringify(dataToPersist));\n};\n\nexport const useExperimentLoggedModelsChartsUIState = (\n  metricsByDatasets: RunsChartsMetricByDatasetEntry[],\n  storeIdentifier: string,\n) => {\n  const [chartUIState, dispatchChartUIState] = useReducer(chartsUIStateReducer, undefined, chartsUIStateInitializer);\n  const [loading, setLoading] = useState(true);\n\n  // Attempt to load the persisted data when the component mounts\n  useEffect(() => {\n    setLoading(true);\n    loadPersistedDataFromStorage(storeIdentifier).then((data) => {\n      dispatchChartUIState({ type: 'INITIALIZE', initialConfig: data });\n      setLoading(false);\n    });\n  }, [storeIdentifier]);\n\n  // Attempt to update the charts state when the logged models change\n  useEffect(() => {\n    if (loading) {\n      return;\n    }\n    dispatchChartUIState({ type: 'METRICS_UPDATED', metricsByDatasets });\n  }, [metricsByDatasets, loading]);\n\n  // Attempt persist the data when the state changes\n  useEffect(() => {\n    if (chartUIState.isDirty) {\n      saveDataToStorage(storeIdentifier, chartUIState);\n    }\n  }, [storeIdentifier, chartUIState]);\n\n  // Create an updater function to pass it to chart controls\n  const updateUIState = useCallback(\n    (stateSetter: RunsChartsUIConfigurationSetter) =>\n      dispatchChartUIState({\n        type: 'UPDATE',\n        stateSetter,\n      }),\n    [],\n  );\n\n  return { chartUIState, updateUIState, loading };\n};\n", "import { Empty, Input, SearchIcon, Spinner, useDesignSystemTheme } from '@databricks/design-system';\nimport { noop, uniq } from 'lodash';\nimport { memo, ReactNode, useMemo, useCallback, useState } from 'react';\nimport { LoggedModelProto } from '../../types';\nimport { ExperimentRunsChartsUIConfiguration } from '../experiment-page/models/ExperimentPageUIState';\nimport { RunsChartsRunData } from '../runs-charts/components/RunsCharts.common';\nimport { RunsChartsDraggableCardsGridContextProvider } from '../runs-charts/components/RunsChartsDraggableCardsGridContext';\nimport { RunsChartsFullScreenModal } from '../runs-charts/components/RunsChartsFullScreenModal';\nimport { RunsChartsTooltipBody } from '../runs-charts/components/RunsChartsTooltipBody';\nimport { RunsChartsSectionAccordion } from '../runs-charts/components/sections/RunsChartsSectionAccordion';\nimport { RunsChartsTooltipWrapper } from '../runs-charts/hooks/useRunsChartsTooltip';\nimport {\n  RunsChartsUIConfigurationContextProvider,\n  useConfirmChartCardConfigurationFn,\n  useRemoveRunsChartFn,\n  useUpdateRunsChartsUIConfiguration,\n} from '../runs-charts/hooks/useRunsChartsUIConfiguration';\nimport { RunsChartsCardConfig, RunsChartsMetricByDatasetEntry, RunsChartType } from '../runs-charts/runs-charts.types';\nimport { useExperimentLoggedModelsChartsData } from './hooks/useExperimentLoggedModelsChartsData';\nimport { useExperimentLoggedModelsChartsUIState } from './hooks/useExperimentLoggedModelsChartsUIState';\nimport { useExperimentLoggedModelAllMetricsByDataset } from './hooks/useExperimentLoggedModelAllMetricsByDataset';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { useMemoDeep } from '../../../common/hooks/useMemoDeep';\nimport { RunsChartsConfigureModal } from '../runs-charts/components/RunsChartsConfigureModal';\nimport Routes from '../../routes';\n\nconst ExperimentLoggedModelListChartsImpl = memo(\n  ({\n    chartData,\n    uiState,\n    metricKeysByDataset,\n  }: {\n    chartData: RunsChartsRunData[];\n    uiState: ExperimentRunsChartsUIConfiguration;\n    metricKeysByDataset: RunsChartsMetricByDatasetEntry[];\n  }) => {\n    const { theme } = useDesignSystemTheme();\n    const { formatMessage } = useIntl();\n\n    const availableMetricKeys = useMemo(() => uniq(chartData.flatMap((run) => Object.keys(run.metrics))), [chartData]);\n    const availableParamKeys = useMemo(() => uniq(chartData.flatMap((run) => Object.keys(run.params))), [chartData]);\n\n    const updateChartsUIState = useUpdateRunsChartsUIConfiguration();\n\n    const setSearch = useCallback(\n      (search: string) => {\n        updateChartsUIState((state) => ({ ...state, chartsSearchFilter: search }));\n      },\n      [updateChartsUIState],\n    );\n\n    const [configuredCardConfig, setConfiguredCardConfig] = useState<RunsChartsCardConfig | null>(null);\n\n    const addNewChartCard = useCallback(\n      (metricSectionId: string) => (type: RunsChartType) =>\n        setConfiguredCardConfig(RunsChartsCardConfig.getEmptyChartCardByType(type, false, undefined, metricSectionId)),\n      [],\n    );\n\n    const confirmChartCardConfiguration = useConfirmChartCardConfigurationFn();\n    const removeChart = useRemoveRunsChartFn();\n\n    const [fullScreenChart, setFullScreenChart] = useState<\n      | {\n          config: RunsChartsCardConfig;\n          title: string | ReactNode;\n          subtitle: ReactNode;\n        }\n      | undefined\n    >(undefined);\n\n    const fullscreenTooltipContextValue = useMemo(() => ({ runs: chartData }), [chartData]);\n\n    const tooltipContextValue = useMemo(\n      () => ({ runs: chartData, getDataTraceLink: Routes.getExperimentLoggedModelDetailsPageRoute }),\n      [chartData],\n    );\n\n    const emptyState = (\n      <div css={{ marginTop: theme.spacing.lg }}>\n        <Empty\n          description={\n            <FormattedMessage\n              defaultMessage=\"No models found in experiment or all models are hidden. Select at least one model to view charts.\"\n              description=\"Label displayed in logged models chart view when no models are visible or selected\"\n            />\n          }\n        />\n      </div>\n    );\n\n    return (\n      <div\n        css={{\n          backgroundColor: theme.colors.backgroundSecondary,\n          paddingLeft: theme.spacing.md,\n          paddingRight: theme.spacing.md,\n          paddingBottom: theme.spacing.md,\n\n          borderTop: `1px solid ${theme.colors.border}`,\n          borderLeft: `1px solid ${theme.colors.border}`,\n\n          flex: 1,\n          overflow: 'hidden',\n          display: 'flex',\n        }}\n      >\n        <div\n          css={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: theme.spacing.sm,\n            paddingTop: theme.spacing.sm,\n            overflow: 'hidden',\n            flex: 1,\n          }}\n        >\n          <Input\n            componentId=\"mlflow.logged_model.list.charts.search\"\n            role=\"searchbox\"\n            prefix={<SearchIcon />}\n            value={uiState.chartsSearchFilter ?? ''}\n            allowClear\n            onChange={({ target }) => setSearch(target.value)}\n            placeholder={formatMessage({\n              defaultMessage: 'Search metric charts',\n              description: 'Placeholder for chart search input on the logged model chart view',\n            })}\n          />\n          <div css={{ overflow: 'auto' }}>\n            <RunsChartsTooltipWrapper contextData={tooltipContextValue} component={RunsChartsTooltipBody}>\n              <RunsChartsDraggableCardsGridContextProvider visibleChartCards={uiState.compareRunCharts}>\n                <RunsChartsSectionAccordion\n                  compareRunSections={uiState.compareRunSections}\n                  compareRunCharts={uiState.compareRunCharts}\n                  reorderCharts={noop}\n                  insertCharts={noop}\n                  chartData={chartData}\n                  startEditChart={setConfiguredCardConfig}\n                  removeChart={removeChart}\n                  addNewChartCard={addNewChartCard}\n                  search={uiState.chartsSearchFilter ?? ''}\n                  groupBy={null}\n                  setFullScreenChart={setFullScreenChart}\n                  autoRefreshEnabled={false}\n                  hideEmptyCharts={false}\n                  globalLineChartConfig={undefined}\n                  supportedChartTypes={[RunsChartType.BAR, RunsChartType.SCATTER]}\n                  noRunsSelectedEmptyState={emptyState}\n                />\n              </RunsChartsDraggableCardsGridContextProvider>\n            </RunsChartsTooltipWrapper>\n            <RunsChartsFullScreenModal\n              fullScreenChart={fullScreenChart}\n              onCancel={() => setFullScreenChart(undefined)}\n              chartData={chartData}\n              groupBy={null}\n              tooltipContextValue={fullscreenTooltipContextValue}\n              tooltipComponent={RunsChartsTooltipBody}\n              autoRefreshEnabled={false}\n              globalLineChartConfig={undefined}\n            />\n            {configuredCardConfig && (\n              <RunsChartsConfigureModal\n                chartRunData={chartData}\n                metricKeyList={availableMetricKeys}\n                metricKeysByDataset={metricKeysByDataset}\n                paramKeyList={availableParamKeys}\n                config={configuredCardConfig}\n                onSubmit={(configuredCardConfig) => {\n                  confirmChartCardConfiguration({ ...configuredCardConfig, displayName: undefined });\n                  setConfiguredCardConfig(null);\n                }}\n                onCancel={() => setConfiguredCardConfig(null)}\n                groupBy={null}\n                supportedChartTypes={[RunsChartType.BAR, RunsChartType.SCATTER]}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  },\n);\n\nexport const ExperimentLoggedModelListCharts = memo(\n  ({ loggedModels, experimentId }: { loggedModels: LoggedModelProto[]; experimentId: string }) => {\n    const { theme } = useDesignSystemTheme();\n\n    // Perform deep comparison on the logged models to avoid re-rendering the charts when the logged models change.\n    // Deep comparison should still be cheaper than rerendering all charts.\n    const cachedLoggedModels = useMemoDeep(() => loggedModels, [loggedModels]);\n\n    const metricsByDataset = useExperimentLoggedModelAllMetricsByDataset(cachedLoggedModels);\n\n    const {\n      chartUIState,\n      updateUIState,\n      loading: loadingState,\n    } = useExperimentLoggedModelsChartsUIState(metricsByDataset, experimentId);\n    const chartData = useExperimentLoggedModelsChartsData(cachedLoggedModels);\n\n    if (loadingState) {\n      return (\n        <div\n          css={{\n            backgroundColor: theme.colors.backgroundSecondary,\n            paddingTop: theme.spacing.lg,\n            borderTop: `1px solid ${theme.colors.border}`,\n            borderLeft: `1px solid ${theme.colors.border}`,\n            flex: 1,\n            justifyContent: 'center',\n            alignItems: 'center',\n            display: 'flex',\n          }}\n        >\n          <Spinner />\n        </div>\n      );\n    }\n    return (\n      <RunsChartsUIConfigurationContextProvider updateChartsUIState={updateUIState}>\n        <ExperimentLoggedModelListChartsImpl\n          chartData={chartData}\n          uiState={chartUIState}\n          metricKeysByDataset={metricsByDataset}\n        />\n      </RunsChartsUIConfigurationContextProvider>\n    );\n  },\n);\n", "import { isEqual } from 'lodash';\nimport { useRef } from 'react';\n\n/**\n * Utility hook that memoizes value based on deep comparison.\n * Dedicated to a few limited use cases where deep comparison is still cheaper than resulting re-renders.\n */\nexport const useMemoDeep = <T>(factory: () => T, deps: unknown[]): T => {\n  const ref = useRef<{ deps: unknown[]; value: T }>();\n\n  if (!ref.current || !isEqual(deps, ref.current.deps)) {\n    ref.current = { deps, value: factory() };\n  }\n\n  return ref.current.value;\n};\n", "import { orderBy } from 'lodash';\nimport type { LoggedModelProto } from '../../../types';\nimport { useMemo } from 'react';\nimport type { RunsChartsMetricByDatasetEntry } from '../../runs-charts/runs-charts.types';\nimport { getMetricByDatasetChartDataKey } from './useExperimentLoggedModelsChartsData';\n\nexport const useExperimentLoggedModelAllMetricsByDataset = (loggedModels: LoggedModelProto[]) =>\n  useMemo(() => {\n    const metricsByDataset: RunsChartsMetricByDatasetEntry[] = [];\n    loggedModels.forEach((model) => {\n      model.data?.metrics?.forEach(({ key: metricKey, dataset_name: datasetName }) => {\n        if (metricKey && !metricsByDataset.find((e) => e.metricKey === metricKey && e.datasetName === datasetName)) {\n          const dataAccessKey = getMetricByDatasetChartDataKey(metricKey, datasetName);\n          metricsByDataset.push({ metricKey, datasetName, dataAccessKey });\n        }\n      });\n    });\n    return orderBy(metricsByDataset, ({ datasetName }) => !datasetName);\n  }, [loggedModels]);\n", "import { Alert, Spacer, useDesignSystemTheme } from '@databricks/design-system';\nimport invariant from 'invariant';\nimport { useNavigate, useParams } from '../../../common/utils/RoutingUtils';\nimport Routes from '../../routes';\nimport { ExperimentLoggedModelPageWrapper } from './ExperimentLoggedModelPageWrapper';\n\nimport { isExperimentLoggedModelsUIEnabled } from '../../../common/utils/FeatureUtils';\nimport { useEffect, useState } from 'react';\nimport { ExperimentLoggedModelListPageTable } from '../../components/experiment-logged-models/ExperimentLoggedModelListPageTable';\nimport { useSearchLoggedModelsQuery } from '../../hooks/logged-models/useSearchLoggedModelsQuery';\nimport { ExperimentLoggedModelListPageControls } from '../../components/experiment-logged-models/ExperimentLoggedModelListPageControls';\nimport { useExperimentLoggedModelListPageTableColumns } from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageTableColumns';\nimport { ExperimentLoggedModelOpenDatasetDetailsContextProvider } from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelOpenDatasetDetails';\nimport { useLoggedModelsListPageState } from '../../components/experiment-logged-models/hooks/useLoggedModelsListPagePageState';\nimport { useRelatedRunsDataForLoggedModels } from '../../hooks/logged-models/useRelatedRunsDataForLoggedModels';\nimport {\n  ExperimentLoggedModelListPageMode,\n  useExperimentLoggedModelListPageMode,\n} from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageMode';\nimport { ExperimentViewRunsTableResizer } from '../../components/experiment-page/components/runs/ExperimentViewRunsTableResizer';\nimport { ExperimentLoggedModelListCharts } from '../../components/experiment-logged-models/ExperimentLoggedModelListCharts';\nimport { ExperimentLoggedModelListPageRowVisibilityContextProvider } from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageRowVisibility';\nimport { RUNS_VISIBILITY_MODE } from '../../components/experiment-page/models/ExperimentPageUIState';\nimport { RunsChartsSetHighlightContextProvider } from '../../components/runs-charts/hooks/useRunsChartTraceHighlight';\n\nconst INITIAL_RUN_COLUMN_SIZE = 295;\n\nconst ExperimentLoggedModelListPageImpl = () => {\n  const { experimentId } = useParams();\n  const { theme } = useDesignSystemTheme();\n  const navigate = useNavigate();\n\n  const {\n    state: { orderByField, orderByAsc, columnVisibility, rowVisibilityMap, rowVisibilityMode },\n    setOrderBy,\n    setColumnVisibility,\n    setRowVisibilityMode,\n    toggleRowVisibility,\n  } = useLoggedModelsListPageState();\n\n  invariant(experimentId, 'Experiment ID must be defined');\n\n  useEffect(() => {\n    if (!isExperimentLoggedModelsUIEnabled() && experimentId) {\n      navigate(Routes.getExperimentPageRoute(experimentId), { replace: true });\n    }\n  }, [experimentId, navigate]);\n\n  const { viewMode, setViewMode } = useExperimentLoggedModelListPageMode();\n\n  const {\n    data: loggedModels,\n    isFetching: isFetchingLoggedModels,\n    isLoading: isLoadingLoggedModels,\n    error: loggedModelsError,\n    nextPageToken,\n    loadMoreResults,\n  } = useSearchLoggedModelsQuery({\n    experimentIds: [experimentId],\n    orderByAsc,\n    orderByField,\n  });\n\n  const { data: relatedRunsData } = useRelatedRunsDataForLoggedModels({ loggedModels });\n\n  const columnDefs = useExperimentLoggedModelListPageTableColumns({\n    loggedModels,\n    columnVisibility,\n    isCompactMode: viewMode !== ExperimentLoggedModelListPageMode.TABLE,\n  });\n\n  const [tableAreaWidth, setTableAreaWidth] = useState<number>(INITIAL_RUN_COLUMN_SIZE);\n  const [tableHidden, setTableHidden] = useState(false);\n\n  const isCompactTableMode = viewMode !== ExperimentLoggedModelListPageMode.TABLE;\n\n  const tableElement =\n    isCompactTableMode && tableHidden ? (\n      <div css={{ width: theme.spacing.md }} />\n    ) : (\n      <ExperimentLoggedModelListPageTable\n        columnDefs={columnDefs}\n        loggedModels={loggedModels ?? []}\n        isLoading={isLoadingLoggedModels}\n        isLoadingMore={isFetchingLoggedModels}\n        error={loggedModelsError}\n        moreResultsAvailable={Boolean(nextPageToken)}\n        onLoadMore={loadMoreResults}\n        onOrderByChange={setOrderBy}\n        orderByAsc={orderByAsc}\n        orderByField={orderByField}\n        columnVisibility={columnVisibility}\n        relatedRunsData={relatedRunsData}\n      />\n    );\n\n  return (\n    <ExperimentLoggedModelOpenDatasetDetailsContextProvider>\n      <ExperimentLoggedModelListPageRowVisibilityContextProvider\n        visibilityMap={rowVisibilityMap}\n        visibilityMode={rowVisibilityMode}\n        setRowVisibilityMode={setRowVisibilityMode}\n        toggleRowVisibility={toggleRowVisibility}\n      >\n        <ExperimentLoggedModelListPageControls\n          columnDefs={columnDefs}\n          columnVisibility={columnVisibility}\n          onChangeOrderBy={setOrderBy}\n          onUpdateColumns={setColumnVisibility}\n          orderByField={orderByField}\n          orderByAsc={orderByAsc}\n          viewMode={viewMode}\n          setViewMode={setViewMode}\n        />\n        <Spacer size=\"sm\" shrinks={false} />\n        {loggedModelsError?.message && (\n          <>\n            <Alert\n              componentId=\"mlflow.logged_models.list.error\"\n              message={loggedModelsError.message}\n              type=\"error\"\n              closable={false}\n            />\n            <Spacer size=\"sm\" shrinks={false} />\n          </>\n        )}\n        {isCompactTableMode ? (\n          <RunsChartsSetHighlightContextProvider>\n            <div css={{ display: 'flex', flex: 1, overflow: 'hidden', position: 'relative' }}>\n              <ExperimentViewRunsTableResizer\n                onResize={setTableAreaWidth}\n                runListHidden={tableHidden}\n                width={tableAreaWidth}\n                onHiddenChange={setTableHidden}\n              >\n                {tableElement}\n              </ExperimentViewRunsTableResizer>\n              {viewMode === ExperimentLoggedModelListPageMode.CHART && (\n                <ExperimentLoggedModelListCharts loggedModels={loggedModels ?? []} experimentId={experimentId} />\n              )}\n            </div>\n          </RunsChartsSetHighlightContextProvider>\n        ) : (\n          tableElement\n        )}\n      </ExperimentLoggedModelListPageRowVisibilityContextProvider>\n    </ExperimentLoggedModelOpenDatasetDetailsContextProvider>\n  );\n};\n\nconst ExperimentLoggedModelListPage = () => (\n  <ExperimentLoggedModelPageWrapper>\n    <ExperimentLoggedModelListPageImpl />\n  </ExperimentLoggedModelPageWrapper>\n);\n\nexport default ExperimentLoggedModelListPage;\n", "import type { LoggedModelProto, RunEntity } from '../../types';\nimport { useMemo } from 'react';\nimport { compact, sortBy, uniq } from 'lodash';\nimport { QueryFunctionContext, useQueries } from '@mlflow/mlflow/src/common/utils/reactQueryHooks';\nimport { MlflowService } from '../../sdk/MlflowService';\n\ntype UseRegisteredModelRelatedRunNamesQueryKey = ['USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS', { runUuid: string }];\n\nconst getQueryKey = (runUuid: string): UseRegisteredModelRelatedRunNamesQueryKey => [\n  'USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS',\n  { runUuid },\n];\n\nconst queryFn = async ({\n  queryKey: [, { runUuid }],\n}: QueryFunctionContext<UseRegisteredModelRelatedRunNamesQueryKey>): Promise<RunEntity | null> => {\n  try {\n    const data = await MlflowService.getRun({ run_id: runUuid });\n    return data?.run;\n  } catch (e) {\n    return null;\n  }\n};\n\n/**\n * Hook used to fetch necessary run data based on metadata found in logged models\n */\nexport const useRelatedRunsDataForLoggedModels = ({ loggedModels = [] }: { loggedModels?: LoggedModelProto[] }) => {\n  const runUuids = useMemo(() => {\n    // Extract all run ids found in metrics and source run ids\n    const allMetricRunUuids = compact(\n      loggedModels?.flatMap((loggedModel) => loggedModel?.data?.metrics?.map((metric) => metric.run_id)),\n    );\n    const allSourceRunUuids = compact(loggedModels?.map((loggedModel) => loggedModel?.info?.source_run_id));\n    const distinctRunUuids = sortBy(uniq([...allMetricRunUuids, ...allSourceRunUuids]));\n\n    return distinctRunUuids;\n  }, [loggedModels]);\n\n  const queryResults = useQueries({\n    queries: runUuids.map((runUuid) => ({\n      queryKey: getQueryKey(runUuid),\n      queryFn,\n    })),\n  });\n\n  const loading = queryResults.some(({ isLoading }) => isLoading);\n  const error = queryResults.find(({ error }) => error)?.error as Error | undefined;\n\n  const data = useMemo(() => queryResults.map(({ data }) => data).filter(Boolean) as RunEntity[], [queryResults]);\n\n  return {\n    data,\n    loading,\n    error,\n  };\n};\n", "import { UserActionErrorHandler } from '@databricks/web-shared/metrics';\nimport { QueryClient, QueryClientProvider } from '@mlflow/mlflow/src/common/utils/reactQueryHooks';\nimport { ErrorBoundary } from 'react-error-boundary';\nimport { DangerIcon, Empty, PageWrapper } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nconst PageFallback = ({ error }: { error?: Error }) => {\n  return (\n    <PageWrapper css={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n      <Empty\n        data-testid=\"fallback\"\n        title={\n          <FormattedMessage\n            defaultMessage=\"Error\"\n            description=\"Title for error fallback component in prompts management UI\"\n          />\n        }\n        description={\n          error?.message ?? (\n            <FormattedMessage\n              defaultMessage=\"An error occurred while rendering this component.\"\n              description=\"Description for default error message in prompts management UI\"\n            />\n          )\n        }\n        image={<DangerIcon />}\n      />\n    </PageWrapper>\n  );\n};\n\n/**\n * Wrapper for all experiment logged model pages.\n * Provides error boundaries and user action error handling.\n */\nexport const ExperimentLoggedModelPageWrapper = ({\n  children,\n  resetKey,\n}: {\n  children: React.ReactNode;\n  resetKey?: unknown;\n}) => {\n  return (\n    <ErrorBoundary FallbackComponent={PageFallback} resetKeys={[resetKey]}>\n      <UserActionErrorHandler>{children}</UserActionErrorHandler>\n    </ErrorBoundary>\n  );\n};\n"], "names": ["$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "error", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "state", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "arguments", "length", "args", "Array", "_key", "props", "onReset", "reason", "setState", "componentDidCatch", "info", "this", "onError", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "a", "undefined", "b", "some", "item", "index", "Object", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "render", "children", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "Error", "$hgUW1$createElement", "Provider", "value", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "Component", "errorBoundaryProps", "Wrapped", "name", "displayName", "InfiniteQueryObserver", "QueryObserver", "constructor", "client", "options", "super", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "infiniteQueryBehavior", "getOptimisticResult", "pageParam", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "_state$fetchMeta", "_state$fetchMeta$fetc", "_state$fetchMeta2", "_state$fetchMeta2$fet", "_state$data", "_state$data2", "result", "isFetching", "isRefetching", "isFetchingNextPage", "fetchMeta", "isFetchingPreviousPage", "hasNextPage", "data", "pages", "hasPreviousPage", "useSearchLoggedModelsQuery", "_ref", "_last", "experimentIds", "orderByAsc", "orderByField", "enabled", "query<PERSON><PERSON>", "JSON", "stringify", "isLoading", "refetch", "arg1", "arg2", "arg3", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBaseQuery", "useInfiniteQuery", "queryFn", "async", "_ref2", "requestBody", "experiment_ids", "order_by", "field_name", "ascending", "page_token", "loggedModelsDataRequest", "cacheTime", "getNextPageParam", "lastPage", "next_page_token", "refetchOnWindowFocus", "retry", "useMemo", "flatMap", "page", "models", "filter", "Boolean", "nextPageToken", "last", "loadMoreResults", "ExperimentLoggedModelListPageMode", "VIEW_MODE_QUERY_PARAM", "ExperimentLoggedModelListPageControls", "onChangeOrderBy", "onUpdateColumns", "columnDefs", "columnVisibility", "viewMode", "setViewMode", "intl", "useIntl", "theme", "useDesignSystemTheme", "_jsxs", "css", "_css", "display", "flexWrap", "gap", "spacing", "sm", "SegmentedControlGroup", "componentId", "onChange", "e", "coerceToEnum", "target", "TABLE", "SegmentedControlButton", "_jsx", "<PERSON><PERSON><PERSON>", "content", "formatMessage", "id", "defaultMessage", "ListIcon", "visuallyHidden", "ChartLineIcon", "<PERSON><PERSON>", "icon", "SortAscendingIcon", "SortDescendingIcon", "onClick", "FormattedMessage", "ExperimentLoggedModelListPageColumnSelector", "disabled", "CHART", "getMetricByDatasetChartDataKey", "metricKey", "datasetName", "createLocalStorageKey", "storeIdentifier", "chartsUIStateInitializer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compareRunSections", "autoRefreshEnabled", "isAccordionReordered", "chartsSearchFilter", "globalLineChartConfig", "isDirty", "chartsUIStateReducer", "action", "type", "stateSetter", "metricsByDatasets", "map", "dataAccessKey", "deleted", "RunsChartType", "BAR", "uuid", "metricSectionId", "isGenerated", "uniq", "isReordered", "isEmpty", "push", "getExperimentLoggedModelsPageChartSetup", "reconcileChartsAndSections", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "_newCharts$compareRun", "_newCharts$compareRun2", "newChartsToAdd", "new<PERSON>hart", "_currentState$compare", "find", "chart", "newSectionsToAdd", "newSection", "_currentState$compare2", "section", "initialConfig", "useExperimentLoggedModelsChartsUIState", "chartUIState", "dispatchChartUIState", "useReducer", "loading", "setLoading", "useState", "useEffect", "serializedData", "localStorage", "getItem", "parse", "loadPersistedDataFromStorage", "then", "dataToPersist", "setItem", "saveDataToStorage", "updateUIState", "useCallback", "_ref3", "styles", "ExperimentLoggedModelListChartsImpl", "memo", "_uiState$chartsSearch", "_uiState$chartsSearch2", "chartData", "uiState", "metricKeysByDataset", "availableMetricKeys", "run", "keys", "metrics", "availableParamKeys", "params", "updateChartsUIState", "useUpdateRunsChartsUIConfiguration", "setSearch", "search", "configuredCardConfig", "setConfiguredCardConfig", "addNewChartCard", "RunsChartsCardConfig", "getEmptyChartCardByType", "confirmChartCardConfiguration", "useConfirmChartCardConfigurationFn", "<PERSON><PERSON><PERSON>", "useRemoveRunsChartFn", "fullScreenChart", "setFullScreenChart", "fullscreenTooltipContextValue", "runs", "tooltipContextValue", "getDataTraceLink", "Routes", "getExperimentLoggedModelDetailsPageRoute", "emptyState", "marginTop", "lg", "Empty", "description", "backgroundColor", "colors", "backgroundSecondary", "paddingLeft", "md", "paddingRight", "paddingBottom", "borderTop", "border", "borderLeft", "flex", "overflow", "flexDirection", "paddingTop", "Input", "role", "prefix", "SearchIcon", "allowClear", "placeholder", "RunsChartsTooltipWrapper", "contextData", "component", "RunsChartsTooltipBody", "RunsChartsDraggableCardsGridContextProvider", "visibleChartCards", "RunsChartsSectionAccordion", "reorder<PERSON><PERSON><PERSON>", "noop", "<PERSON><PERSON><PERSON><PERSON>", "startEditChart", "groupBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supportedChartTypes", "SCATTER", "noRunsSelectedEmptyState", "RunsChartsFullScreenModal", "onCancel", "tooltipComponent", "RunsChartsConfigureModal", "chartRunData", "metricKeyList", "paramKeyList", "config", "onSubmit", "ExperimentLoggedModelListCharts", "_ref4", "loggedModels", "experimentId", "cachedLoggedModels", "useMemoDeep", "factory", "deps", "ref", "useRef", "current", "isEqual", "metricsByDataset", "for<PERSON>ach", "model", "_model$data", "_model$data$metrics", "key", "dataset_name", "orderBy", "useExperimentLoggedModelAllMetricsByDataset", "loadingState", "isRowHidden", "useExperimentLoggedModelListPageRowVisibilityContext", "compact", "_model$info", "_model$info$name", "_model$info2", "_model$info3", "_model$data$params$ma", "_model$data2", "_model$data2$params", "model_id", "images", "keyBy", "timestamp", "step", "dataKey", "tags", "hidden", "color", "getStableColorForRun", "useExperimentLoggedModelsChartsData", "justifyContent", "alignItems", "Spinner", "RunsChartsUIConfigurationContextProvider", "ExperimentLoggedModelListPageImpl", "useParams", "navigate", "useNavigate", "rowVisibilityMap", "rowVisibilityMode", "setOrderBy", "setColumnVisibility", "setRowVisibilityMode", "toggleRowVisibility", "useLoggedModelsListPageState", "invariant", "isExperimentLoggedModelsUIEnabled", "getExperimentPageRoute", "replace", "useExperimentLoggedModelListPageMode", "setParams", "useSearchParams", "get", "mode", "isFetchingLoggedModels", "isLoadingLoggedModels", "loggedModelsError", "relatedRunsData", "useRelatedRunsDataForLoggedModels", "useExperimentLoggedModelListPageTableColumns", "isCompactMode", "tableAreaWidth", "setTableAreaWidth", "tableHidden", "setTableHidden", "isCompactTableMode", "tableElement", "width", "ExperimentLoggedModelListPageTable", "isLoadingMore", "moreResultsAvailable", "onLoadMore", "onOrderByChange", "ExperimentLoggedModelOpenDatasetDetailsContextProvider", "ExperimentLoggedModelListPageRowVisibilityContextProvider", "visibilityMap", "visibilityMode", "Spacer", "size", "shrinks", "message", "_Fragment", "<PERSON><PERSON>", "closable", "RunsChartsSetHighlightContextProvider", "ExperimentViewRunsTableResizer", "onResize", "runListHidden", "onHiddenChange", "ExperimentLoggedModelListPage", "ExperimentLoggedModelPageWrapper", "get<PERSON><PERSON>y<PERSON>ey", "runUuid", "MlflowService", "getRun", "run_id", "_queryResults$find", "runUuids", "allMetricRunUuids", "loggedModel", "_loggedModel$data", "_loggedModel$data$met", "metric", "allSourceRunUuids", "_loggedModel$info", "source_run_id", "sortBy", "queryResults", "useQueries", "queries", "_ref5", "<PERSON><PERSON><PERSON><PERSON>", "_error$message", "PageWrapper", "title", "image", "DangerIcon", "reset<PERSON>ey", "Error<PERSON>ou<PERSON><PERSON>", "UserActionErrorHandler"], "sourceRoot": ""}