"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[3122],{8986:function(e,t,r){r.d(t,{G:function(){return o}});var s=r(39416);function n(e){if(void 0!==e)return"string"===typeof e||e instanceof FormData||e instanceof Blob?e:JSON.stringify(e)}const o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",r=arguments.length>2?arguments[2]:void 0;const o=await fetch(e,{method:t,body:n(r),headers:r?{"Content-Type":"application/json"}:{}});if(!o.ok){const e=(0,s.a$)(o);if(e){try{const t=(await o.json()).message;e.message=null!==t&&void 0!==t?t:e.message}catch{}throw e}}return o.json()}},23275:function(e,t,r){r.d(t,{u:function(){return f},g:function(){return p}});var s=r(9133),n=r(31014),o=r(10811),i=r(26809),a=r(69708),u=r(76010);var l=r(36568),d=r(91144),c=r(69869);const v=e=>{let{queryResult:t,runUuid:r}=e;const{registeredModels:s}=(0,o.d4)((e=>{let{entities:t}=e;return{registeredModels:t.modelVersionsByRunUuid[r]}}));if((0,d._O)()){const e=[];var n,i;if(null!==t&&void 0!==t&&t.data&&"modelVersions"in t.data)null===(n=t.data)||void 0===n||null===(i=n.modelVersions)||void 0===i||i.forEach((t=>{e.push({displayedName:t.name,version:t.version,link:t.name&&t.version?c.f.getModelVersionPageRoute(t.name,t.version):"",status:t.status,source:t.source})}));return e}return s?s.map((e=>{const t=e.name,r=c.f.getModelVersionPageRoute(t,e.version);return{displayedName:e.name,version:e.version,link:r,status:e.status,source:e.source}})):[]},m=e=>(0,s.keyBy)(e,"key"),f=e=>null===e||void 0===e?void 0:e.map((e=>{var t,r,n,o,i,a,u,l,d,c,v,m,f,p;return{dataset:{digest:null!==(t=null===(r=e.dataset)||void 0===r?void 0:r.digest)&&void 0!==t?t:"",name:null!==(n=null===(o=e.dataset)||void 0===o?void 0:o.name)&&void 0!==n?n:"",profile:null!==(i=null===(a=e.dataset)||void 0===a?void 0:a.profile)&&void 0!==i?i:"",schema:null!==(u=null===(l=e.dataset)||void 0===l?void 0:l.schema)&&void 0!==u?u:"",source:null!==(d=null===(c=e.dataset)||void 0===c?void 0:c.source)&&void 0!==d?d:"",sourceType:null!==(v=null===(m=e.dataset)||void 0===m?void 0:m.sourceType)&&void 0!==v?v:""},tags:null!==(f=null===(p=e.tags)||void 0===p?void 0:p.map((e=>{var t,r;return{key:null!==(t=e.key)&&void 0!==t?t:"",value:null!==(r=e.value)&&void 0!==r?r:""}})).filter((e=>!(0,s.isEmpty)(e.key))))&&void 0!==f?f:[]}})),p=e=>{var t,r,c,p,h,g;let{runUuid:y,experimentId:E}=e;const M=(0,d.wD)(),b=(0,o.wA)();if(M){var O,D,k,R,w,U;const e=(()=>(0,l.t)({runUuid:y}))();(0,n.useEffect)((()=>{(0,d._O)()||b((0,a.hY)({run_id:y}))}),[b,y]);const{latestMetrics:t,tags:r,params:o,datasets:i}=(0,n.useMemo)((()=>{var t,r,n,o,i,a,u,l,d,c,v,p;return{latestMetrics:(0,s.pickBy)(m((p=null!==(t=null===(r=e.data)||void 0===r||null===(n=r.data)||void 0===n?void 0:n.metrics)&&void 0!==t?t:[],p.filter((e=>{let{key:t,value:r,step:s,timestamp:n}=e;return null!==t&&null!==r&&null!==s&&null!==n})).map((e=>{let{key:t,value:r,step:s,timestamp:n}=e;return{key:t,value:r,step:Number(s),timestamp:Number(n)}})))),(e=>e.key.trim().length>0)),tags:(0,s.pickBy)(m(null!==(o=null===(i=e.data)||void 0===i||null===(a=i.data)||void 0===a?void 0:a.tags)&&void 0!==o?o:[]),(e=>e.key.trim().length>0)),params:(0,s.pickBy)(m(null!==(u=null===(l=e.data)||void 0===l||null===(d=l.data)||void 0===d?void 0:d.params)&&void 0!==u?u:[]),(e=>e.key.trim().length>0)),datasets:f(null===(c=e.data)||void 0===c||null===(v=c.inputs)||void 0===v?void 0:v.datasetInputs)}}),[e.data]),u=v({runUuid:y,queryResult:e});return{runInfo:null!==(O=null===(D=e.data)||void 0===D?void 0:D.info)&&void 0!==O?O:void 0,experiment:null!==(k=null===(R=e.data)||void 0===R?void 0:R.experiment)&&void 0!==k?k:void 0,loading:e.loading,error:e.apolloError,apiError:e.apiError,refetchRun:e.refetchRun,runInputs:null===(w=e.data)||void 0===w?void 0:w.inputs,runOutputs:null===(U=e.data)||void 0===U?void 0:U.outputs,registeredModelVersionSummaries:u,datasets:i,latestMetrics:t,tags:r,params:o}}const _=((e,t)=>{const[r,l]=(0,n.useState)(""),[d,c]=(0,n.useState)(""),v=(0,o.wA)(),{runInfo:m,tags:f,latestMetrics:p,experiment:h,params:g,datasets:y}=(0,o.d4)((r=>({runInfo:r.entities.runInfosByUuid[e],tags:(0,s.pickBy)(r.entities.tagsByRunUuid[e],(e=>e.key.trim().length>0)),latestMetrics:(0,s.pickBy)(r.entities.latestMetricsByRunUuid[e],(e=>e.key.trim().length>0)),params:(0,s.pickBy)(r.entities.paramsByRunUuid[e],(e=>e.key.trim().length>0)),experiment:r.entities.experimentsById[t],datasets:r.entities.runDatasetsByUuid[e]}))),E=(0,n.useCallback)((()=>{const t=(0,i.aO)(e);return l(t.meta.id),v(t)}),[v,e]),M=(0,n.useCallback)((()=>{const e=(0,i.yc)(t);return c(e.meta.id),v(e)}),[v,t]),b=(0,n.useCallback)((()=>{v((0,a.hY)({run_id:e}))}),[v,e]);(0,n.useEffect)((()=>{m||E().catch((e=>u.A.logErrorAndNotifyUser(e))),b()}),[m,E,b]),(0,n.useEffect)((()=>{h||M().catch((e=>u.A.logErrorAndNotifyUser(e)))}),[h,M]);const{loading:O,error:D}=(0,o.d4)((e=>{var t,s,n,o;return{loading:!r||Boolean(null===(t=e.apis)||void 0===t||null===(s=t[r])||void 0===s?void 0:s.active),error:null===(n=e.apis)||void 0===n||null===(o=n[r])||void 0===o?void 0:o.error}})),{loading:k,error:R}=(0,o.d4)((e=>{var t,s,n,o;return{loading:!r||Boolean(null===(t=e.apis)||void 0===t||null===(s=t[d])||void 0===s?void 0:s.active),error:null===(n=e.apis)||void 0===n||null===(o=n[d])||void 0===o?void 0:o.error}}));return{loading:O||k,data:{runInfo:m,tags:f,params:g,latestMetrics:p,experiment:h,datasets:y},refetchRun:E,errors:{runFetchError:D,experimentFetchError:R}}})(y,E),I=_.errors.runFetchError||_.errors.experimentFetchError,A=v({runUuid:y});return{runInfo:null===(t=_.data)||void 0===t?void 0:t.runInfo,latestMetrics:null===(r=_.data)||void 0===r?void 0:r.latestMetrics,tags:null===(c=_.data)||void 0===c?void 0:c.tags,experiment:null===(p=_.data)||void 0===p?void 0:p.experiment,params:null===(h=_.data)||void 0===h?void 0:h.params,datasets:null===(g=_.data)||void 0===g?void 0:g.datasets,loading:_.loading,error:I,runFetchError:_.errors.runFetchError,experimentFetchError:_.errors.experimentFetchError,refetchRun:_.refetchRun,registeredModelVersionSummaries:A}}},28684:function(e,t,r){r.d(t,{f:function(){return d}});var s=r(89555),n=r(77520),o=r(3293),i=r(31014),a=r(36506),u=r(37368),l=r(50111);const d=e=>{let{datasetName:t,datasetDigest:r,runId:d}=e;const{theme:c}=(0,n.u)(),[v,m]=(0,i.useState)(!1),{onDatasetClicked:f}=(0,a.s)(),{handleError:p}=(0,u.tF)();return(0,l.FD)(n.B,{type:"link",icon:v?(0,l.Y)(n.H,{size:"small",css:(0,s.AH)({marginRight:c.spacing.sm},"")}):(0,l.Y)(o.KbA,{}),componentId:"mlflow.logged_model.dataset",onClick:()=>((e,t,r)=>{r&&(m(!0),null===f||void 0===f||f({datasetName:e,datasetDigest:t,runId:r}).catch((e=>{p(e)})).finally((()=>m(!1))))})(t,r,d),children:[t," (#",r,")"]},[t,r].join("."))}},36506:function(e,t,r){r.d(t,{X:function(){return f},s:function(){return p}});var s=r(31014),n=r(85343),o=r(36568),i=r(23275),a=r(9133),u=r(39416),l=r(47664),d=r(88443),c=r(50111);class v extends u.ZR{constructor(){super(...arguments),this.errorLogType=u.ZQ.UnexpectedSystemStateError,this.errorName=u.UW.DatasetRunNotFoundError,this.isUserError=!0,this.displayMessage=(0,c.Y)(d.A,{id:"vwDBPr",defaultMessage:"The run containing the dataset could not be found."})}}const m=(0,s.createContext)({onDatasetClicked:()=>Promise.resolve()}),f=e=>{let{children:t}=e;const[r,u]=(0,s.useState)(!1),[d,f]=(0,s.useState)(),[p]=(0,o.T)(),h=(0,s.useRef)(null),g=(0,s.useCallback)((async e=>new Promise(((t,r)=>{var s;return null===(s=h.current)||void 0===s||s.call(h),p({onError:r,onCompleted(s){var n,o,d,c,m,p,g,y,E,M,b;if(null!==(n=s.mlflowGetRun)&&void 0!==n&&n.apiError){const e=s.mlflowGetRun.apiError.code===l.tG.RESOURCE_DOES_NOT_EXIST?new v:s.mlflowGetRun.apiError;return void r(e)}const O=(0,i.u)(null===(o=s.mlflowGetRun)||void 0===o||null===(d=o.run)||void 0===d||null===(c=d.inputs)||void 0===c?void 0:c.datasetInputs);if(!O||null===(m=s.mlflowGetRun)||void 0===m||null===(p=m.run)||void 0===p||!p.info)return;const D=null===O||void 0===O?void 0:O.find((t=>{var r;return(null===(r=t.dataset)||void 0===r?void 0:r.digest)===e.datasetDigest&&t.dataset.name===e.datasetName}));if(!D)return;const{info:k,data:R}=s.mlflowGetRun.run,w=(0,a.keyBy)(null!==(g=null===R||void 0===R||null===(y=R.tags)||void 0===y?void 0:y.filter((e=>e.key&&e.value)))&&void 0!==g?g:[],"key");u(!0),f({datasetWithTags:{dataset:D.dataset,tags:D.tags},runData:{datasets:O,runUuid:null!==(E=k.runUuid)&&void 0!==E?E:"",experimentId:null!==(M=k.experimentId)&&void 0!==M?M:"",runName:null!==(b=k.runName)&&void 0!==b?b:"",tags:w}}),t(),h.current=null},variables:{data:{runId:e.runId}}})}))),[p]),y=(0,s.useMemo)((()=>({onDatasetClicked:g})),[g]);return(0,c.FD)(m.Provider,{value:y,children:[t,d&&(0,c.Y)(n.O,{isOpen:r,selectedDatasetWithRun:d,setIsOpen:u,setSelectedDatasetWithRun:f})]})},p=()=>(0,s.useContext)(m)},36568:function(e,t,r){r.d(t,{t:function(){return c},T:function(){return v}});var s=r(56675),n=r(95947),o=r(97779),i=r(31014),a=r(86896),u=r(18815),l=["refetch","reobserve","fetchMore","updateQuery","startPolling","subscribeToMore"];const d=s.J1`
  query GetRun($data: MlflowGetRunInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetRun(input: $data) {
      apiError {
        helpUrl
        code
        message
      }
      run {
        info {
          runName
          status
          runUuid
          experimentId
          artifactUri
          endTime
          lifecycleStage
          startTime
          userId
        }
        experiment {
          experimentId
          name
          tags {
            key
            value
          }
          artifactLocation
          lifecycleStage
          lastUpdateTime
        }
        modelVersions {
          status
          version
          name
          source
        }
        data {
          metrics {
            key
            value
            step
            timestamp
          }
          params {
            key
            value
          }
          tags {
            key
            value
          }
        }
        inputs {
          datasetInputs {
            dataset {
              digest
              name
              profile
              schema
              source
              sourceType
            }
            tags {
              key
              value
            }
          }
          modelInputs {
            modelId
          }
        }
        outputs {
          modelOutputs {
            modelId
            step
          }
        }
      }
    }
  }
`,c=e=>{var t,r;let{runUuid:s,disabled:o=!1}=e;const{data:i,loading:a,error:u,refetch:l}=(0,n.I)(d,{variables:{data:{runId:s}},skip:o});return{loading:a,data:null===i||void 0===i||null===(t=i.mlflowGetRun)||void 0===t?void 0:t.run,refetchRun:l,apolloError:u,apiError:null===i||void 0===i||null===(r=i.mlflowGetRun)||void 0===r?void 0:r.apiError}},v=()=>function(e,t){var r=(0,n.k)((0,u.m)(t&&t.client),e),s=(0,i.useRef)(),d=s.current?(0,a.l)(t,s.current):t,c=r.useQuery((0,o.__assign)((0,o.__assign)({},d),{skip:!s.current})),v=c.observable.options.initialFetchPolicy||r.getDefaultFetchPolicy(),m=Object.assign(c,{called:!!s.current}),f=(0,i.useMemo)((function(){for(var e={},t=function(t){var n=m[t];e[t]=function(){return s.current||(s.current=Object.create(null),r.forceUpdate()),n.apply(this,arguments)}},n=0,o=l;n<o.length;n++)t(o[n]);return e}),[]);return Object.assign(m,f),[(0,i.useCallback)((function(e){s.current=e?(0,o.__assign)((0,o.__assign)({},e),{fetchPolicy:e.fetchPolicy||v}):{fetchPolicy:v};var t=r.asyncUpdate().then((function(e){return Object.assign(e,f)}));return t.catch((function(){})),t}),[]),m]}(d)},37368:function(e,t,r){r.d(t,{Au:function(){return a},tF:function(){return u}});var s=r(31014),n=r(39416),o=r(50111);const i=(0,s.createContext)({currentUserActionError:null,handleError:()=>{},handlePromise:()=>{},clearUserActionError:()=>{}}),a=e=>{let{children:t,errorFilter:r}=e;const[a,u]=(0,s.useState)(null),l=(0,s.useCallback)(((e,t)=>{if(null===r||void 0===r||!r(e)){const r=(0,n.a$)(e);u(r),t&&t(r)}}),[u,r]),d=(0,s.useCallback)((e=>{e.catch((e=>{l(e)}))}),[l]),c=(0,s.useCallback)((()=>{u(null)}),[u]);return(0,o.Y)(i.Provider,{value:(0,s.useMemo)((()=>({currentUserActionError:a,handleError:l,handlePromise:d,clearUserActionError:c})),[c,a,l,d]),children:t})},u=()=>{const{currentUserActionError:e,handleError:t,handlePromise:r,clearUserActionError:n}=(0,s.useContext)(i),o=(0,s.useCallback)(((e,r,s)=>{t(r,s)}),[t]);return(0,s.useMemo)((()=>({currentUserActionError:e,handleError:t,handleErrorWithEvent:o,handlePromise:r,clearUserActionError:n})),[n,t,r,e,o])}},58710:function(e,t,r){r.d(t,{P:function(){return d}});r(31014);var s=r(42848),n=r(88464),o=r(50111);const i=86400,a={timeZoneName:"short",year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"},u=e=>{let{date:t,intl:r,tooltipFormatOptions:s=a}=e;const n=new Date,o=Math.round((n.getTime()-t.getTime())/1e3),u=navigator.language||"en-US";let l="";try{l=Intl.DateTimeFormat(u,s).format(t)}catch(d){}for(const a of(e=>[{seconds:31536e3,timeAgoMessage:t=>e.formatMessage({id:"NF6ePS",defaultMessage:"{count, plural, =1 {1 year} other {# years}} ago"},{count:t})},{seconds:2592e3,timeAgoMessage:t=>e.formatMessage({id:"N79Rdb",defaultMessage:"{count, plural, =1 {1 month} other {# months}} ago"},{count:t})},{seconds:i,timeAgoMessage:t=>e.formatMessage({id:"XwD3hV",defaultMessage:"{count, plural, =1 {1 day} other {# days}} ago"},{count:t})},{seconds:3600,timeAgoMessage:t=>e.formatMessage({id:"i1Hj20",defaultMessage:"{count, plural, =1 {1 hour} other {# hours}} ago"},{count:t})},{seconds:60,timeAgoMessage:t=>e.formatMessage({id:"ZO8PZt",defaultMessage:"{count, plural, =1 {1 minute} other {# minutes}} ago"},{count:t})},{seconds:1,timeAgoMessage:t=>e.formatMessage({id:"gQB+Vs",defaultMessage:"{count, plural, =1 {1 second} other {# seconds}} ago"},{count:t})}])(r)){const e=Math.floor(o/a.seconds);if(e>=1)return{displayText:a.timeAgoMessage(e),tooltipTitle:l}}return{displayText:r.formatMessage({id:"ZOsiNc",defaultMessage:"just now"}),tooltipTitle:l}},l=e=>{let{date:t,tooltipFormatOptions:r=a}=e;const i=(0,n.A)(),{displayText:l,tooltipTitle:d}=u({date:t,intl:i,tooltipFormatOptions:r});return(0,o.Y)(s.T,{componentId:"web-shared.time-ago",content:d,children:(0,o.Y)("span",{children:l})})},d=e=>{let{value:t}=e;const r=new Date(Number(t));return isNaN(r)?null:(0,o.Y)(l,{date:r})}},77735:function(e,t,r){r.d(t,{E:function(){return p}});var s=r(31014),n=r(61226),o=r(28999),i=r(95904),a=r(45586),u=r(21363);class l extends u.Q{constructor(e,t){super(),this.client=e,this.queries=[],this.result=[],this.observers=[],this.observersMap={},t&&this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.observers.forEach((e=>{e.subscribe((t=>{this.onUpdate(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.observers.forEach((e=>{e.destroy()}))}setQueries(e,t){this.queries=e,i.j.batch((()=>{const e=this.observers,r=this.findMatchingObservers(this.queries);r.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,t)));const s=r.map((e=>e.observer)),n=Object.fromEntries(s.map((e=>[e.options.queryHash,e]))),i=s.map((e=>e.getCurrentResult())),a=s.some(((t,r)=>t!==e[r]));(e.length!==s.length||a)&&(this.observers=s,this.observersMap=n,this.result=i,this.hasListeners()&&((0,o.iv)(e,s).forEach((e=>{e.destroy()})),(0,o.iv)(s,e).forEach((e=>{e.subscribe((t=>{this.onUpdate(e,t)}))})),this.notify()))}))}getCurrentResult(){return this.result}getQueries(){return this.observers.map((e=>e.getCurrentQuery()))}getObservers(){return this.observers}getOptimisticResult(e){return this.findMatchingObservers(e).map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)))}findMatchingObservers(e){const t=this.observers,r=new Map(t.map((e=>[e.options.queryHash,e]))),s=e.map((e=>this.client.defaultQueryOptions(e))),n=s.flatMap((e=>{const t=r.get(e.queryHash);return null!=t?[{defaultedQueryOptions:e,observer:t}]:[]})),o=new Set(n.map((e=>e.defaultedQueryOptions.queryHash))),i=s.filter((e=>!o.has(e.queryHash))),u=new Set(n.map((e=>e.observer))),l=t.filter((e=>!u.has(e))),d=e=>{const t=this.client.defaultQueryOptions(e),r=this.observersMap[t.queryHash];return null!=r?r:new a.$(this.client,t)},c=i.map(((e,t)=>{if(e.keepPreviousData){const r=l[t];if(void 0!==r)return{defaultedQueryOptions:e,observer:r}}return{defaultedQueryOptions:e,observer:d(e)}}));return n.concat(c).sort(((e,t)=>s.indexOf(e.defaultedQueryOptions)-s.indexOf(t.defaultedQueryOptions)))}onUpdate(e,t){const r=this.observers.indexOf(e);-1!==r&&(this.result=(0,o._D)(this.result,r,t),this.notify())}notify(){i.j.batch((()=>{this.listeners.forEach((e=>{let{listener:t}=e;t(this.result)}))}))}}var d=r(27288),c=r(26737),v=r(35067),m=r(52165),f=r(41698);function p(e){let{queries:t,context:r}=e;const o=(0,d.jE)({context:r}),a=(0,c.w)(),u=(0,v.h)(),p=s.useMemo((()=>t.map((e=>{const t=o.defaultQueryOptions(e);return t._optimisticResults=a?"isRestoring":"optimistic",t}))),[t,o,a]);p.forEach((e=>{(0,f.tu)(e),(0,m.LJ)(e,u)})),(0,m.wZ)(u);const[h]=s.useState((()=>new l(o,p))),g=h.getOptimisticResult(p);(0,n.r)(s.useCallback((e=>a?()=>{}:h.subscribe(i.j.batchCalls(e))),[h,a]),(()=>h.getCurrentResult()),(()=>h.getCurrentResult())),s.useEffect((()=>{h.setQueries(p,{listeners:!1})}),[p,h]);const y=g.some(((e,t)=>(0,f.EU)(p[t],e,a)))?g.flatMap(((e,t)=>{const r=p[t],s=h.getObservers()[t];if(r&&s){if((0,f.EU)(r,e,a))return(0,f.iL)(r,s,u);(0,f.nE)(e,a)&&(0,f.iL)(r,s,u)}return[]})):[];if(y.length>0)throw Promise.all(y);const E=h.getQueries(),M=g.find(((e,t)=>{var r,s;return(0,m.$1)({result:e,errorResetBoundary:u,useErrorBoundary:null!=(r=null==(s=p[t])?void 0:s.useErrorBoundary)&&r,query:E[t]})}));if(null!=M&&M.error)throw M.error;return g}},83858:function(e,t,r){r.d(t,{a:function(){return c}});var s=r(89555),n=r(3293),o=r(65418),i=r(77520),a=r(88443),u=r(32378),l=r(50111);const d=e=>{let{status:t}=e;return t===u.Fq.LOGGED_MODEL_READY?(0,l.Y)(n.C1y,{color:"success"}):t===u.Fq.LOGGED_MODEL_UPLOAD_FAILED?(0,l.Y)(n.qhh,{color:"danger"}):t===u.Fq.LOGGED_MODEL_PENDING?(0,l.Y)(o.C,{color:"warning"}):null},c=e=>{var t,r;let{data:o}=e;const{theme:c}=(0,i.u)(),v=null!==(t=null===(r=o.info)||void 0===r?void 0:r.status)&&void 0!==t?t:u.Fq.LOGGED_MODEL_STATUS_UNSPECIFIED;return(0,l.FD)(n.vwO,{componentId:"mlflow.logged_model.status",css:(0,s.AH)({backgroundColor:v===u.Fq.LOGGED_MODEL_READY?c.isDarkMode?c.colors.green800:c.colors.green100:v===u.Fq.LOGGED_MODEL_UPLOAD_FAILED?c.isDarkMode?c.colors.red800:c.colors.red100:v===u.Fq.LOGGED_MODEL_PENDING?c.isDarkMode?c.colors.yellow800:c.colors.yellow100:void 0},""),children:[v&&(0,l.Y)(d,{status:v})," ",(0,l.Y)(i.T.Text,{css:(0,s.AH)({marginLeft:c.spacing.sm},""),children:v===u.Fq.LOGGED_MODEL_READY?(0,l.Y)(i.T.Text,{color:"success",children:(0,l.Y)(a.A,{id:"Rs+SVS",defaultMessage:"Ready"})}):v===u.Fq.LOGGED_MODEL_UPLOAD_FAILED?(0,l.Y)(i.T.Text,{color:"error",children:(0,l.Y)(a.A,{id:"z0e/Kg",defaultMessage:"Upload failed"})}):v===u.Fq.LOGGED_MODEL_PENDING?(0,l.Y)(i.T.Text,{color:"warning",children:(0,l.Y)(a.A,{id:"jo4LfR",defaultMessage:"Pending"})}):v})]})}}}]);
//# sourceMappingURL=3122.b11ce3db.chunk.js.map