{"version": 3, "file": "static/js/2402.1c147bbf.chunk.js", "mappings": "wOAGA,MAQaA,EAAqBA,KAChC,MAAMC,GAAsBC,EAAAA,EAAAA,QAAwB,MAepD,OAbAC,EAAAA,EAAAA,YAAU,KACR,GAAIF,EAAoBG,SAAWH,EAAoBG,QAAQC,gBAAkBC,SAAU,CACzF,MAAMC,EAAuBD,SAASE,cAAc,SAKpD,OAJAD,EAAqBE,UAAY,4BACjCF,EAAqBG,YAAYJ,SAASK,eAf1B,8tQAgBhBL,SAASM,KAAKF,YAAYH,GAEnB,IAAMA,EAAqBM,QACpC,CAEA,MAAO,MAAQ,GACd,KAEIC,EAAAA,EAAAA,GAAA,QAAMC,IAAKd,GAAuB,ECV3C,MAPsBe,IACpBC,EAAAA,EAAAA,IAAAC,EAAAA,GAAA,CAAAC,SAAA,EACEL,EAAAA,EAAAA,GAACd,EAAkB,KACnBc,EAAAA,EAAAA,GAACM,EAAAA,YAAW,CAACC,QAAS,CAACC,EAAAA,MAA+BN,M", "sources": ["common/components/ag-grid/AgGridFontInjector.tsx", "common/components/ag-grid/AgGrid.tsx"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n// eslint-disable-next-line max-len\nconst stylesContent = `@font-face { font-family: \"agGridBalham\"; src: url(\"data:application/font-woff;charset=utf-8;base64,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\") format(\"woff\"); font-weight: normal; font-style: normal; }`;\n\n/**\n * Embedding agGrid inside shadow DOM imposes a problem with its embedded fonts\n * that are not being present on the main document level which results in erroneous\n * checkbox rendering. This components checks if the component is being rendered inside\n * shadow DOM part and if true, it reinjects the snapshotted agGrid styles later on.\n */\nexport const AgGridFontInjector = () => {\n  const domElementReference = useRef<HTMLSpanElement>(null);\n\n  useEffect(() => {\n    if (domElementReference.current && domElementReference.current.getRootNode() !== document) {\n      const injectedStyleElement = document.createElement('style');\n      injectedStyleElement.className = `ag-grid-snapshot-base-css`;\n      injectedStyleElement.appendChild(document.createTextNode(stylesContent));\n      document.head.appendChild(injectedStyleElement);\n\n      return () => injectedStyleElement.remove();\n    }\n\n    return () => {};\n  }, []);\n\n  return <span ref={domElementReference} />;\n};\n", "import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';\nimport { AgGridReact, AgGridReactProps, AgReactUiProps } from '@ag-grid-community/react/main';\nimport '@ag-grid-community/core/dist/styles/ag-grid.css';\nimport '@ag-grid-community/core/dist/styles/ag-theme-balham.css';\nimport { AgGridFontInjector } from './AgGridFontInjector';\n\n/**\n * A local wrapper component that embeds imported AgGrid instance.\n * Extracted to a separate module to ensure that it will be in placed a single chunk.\n */\nconst MLFlowAgGrid = (props: AgGridReactProps | AgReactUiProps) => (\n  <>\n    <AgGridFontInjector />\n    <AgGridReact modules={[ClientSideRowModelModule]} {...props} />\n  </>\n);\n\nexport default MLFlowAgGrid;\n"], "names": ["AgGridFontInjector", "domElementReference", "useRef", "useEffect", "current", "getRootNode", "document", "injectedStyleElement", "createElement", "className", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "remove", "_jsx", "ref", "props", "_jsxs", "_Fragment", "children", "AgGridReact", "modules", "ClientSideRowModelModule"], "sourceRoot": ""}