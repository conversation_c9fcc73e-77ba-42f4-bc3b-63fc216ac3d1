Metadata-Version: 2.3
Name: sensei-ai
Version: 1.0.0
Summary: Optimisation de la prospection marketing & commerciale grâce à l'IA
Author: Sensei Team
Author-email: <EMAIL>
Requires-Python: >=3.11,<4.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: catboost (>=1.2.0,<2.0.0)
Requires-Dist: db-dtypes (>=1.1.0,<2.0.0)
Requires-Dist: google-cloud-bigquery (>=3.11.0,<4.0.0)
Requires-Dist: google-cloud-secret-manager (>=2.16.0,<3.0.0)
Requires-Dist: hdbscan (>=0.8.0,<0.9.0)
Requires-Dist: jinja2 (>=3.1.0,<4.0.0)
Requires-Dist: lightgbm (>=4.1.0,<5.0.0)
Requires-Dist: mlflow (>=2.7.0,<3.0.0)
Requires-Dist: numpy (>=1.24.0,<2.0.0)
Requires-Dist: optuna (>=3.4.0,<4.0.0)
Requires-Dist: pandas (>=2.1.0,<3.0.0)
Requires-Dist: pydantic (>=2.4.0,<3.0.0)
Requires-Dist: scikit-learn (>=1.3.0,<2.0.0)
Requires-Dist: sentence-transformers (>=2.2.0,<3.0.0)
Requires-Dist: structlog (>=23.1.0,<24.0.0)
Requires-Dist: typer[all] (>=0.9.0,<0.10.0)
Requires-Dist: umap-learn (>=0.5.0,<0.6.0)
Description-Content-Type: text/markdown

# Sensei AI Suite v1.0

Optimisation de la prospection marketing & commerciale grâce à l'intelligence artificielle.

## 🎯 Objectifs

Ce projet implémente trois algorithmes d'IA pour optimiser la prospection :

1. **Scoring de conversion** (LightGBM) - Prédiction de la probabilité de conversion des prospects
2. **Recommandation de canal & timing** (CatBoost multiclasses) - Optimisation du canal et du moment de contact
3. **Analyse NLP des transcriptions** (Sentence-BERT + UMAP/HDBSCAN) - Extraction d'insights des appels Modjo

## 🏗️ Architecture

```
sensei-ai/
├── src/sensei/
│   ├── cli.py                # Interface CLI Typer
│   ├── data/                 # Connecteurs BigQuery sécurisés
│   ├── features/             # Construction feature store
│   ├── models/               # Modèles ML (conversion, channel, nlp_signals)
│   ├── pipelines/            # Pipelines sécurisés avec audit RGPD
│   └── utils/                # Logging, secrets, sécurité
├── tests/                    # Tests unitaires & intégration (>80% couverture)
├── scripts/                  # Scripts d'installation et maintenance
├── pyproject.toml            # Configuration Poetry avec toutes dépendances
├── Dockerfile                # Image production optimisée
├── docker-compose.yml        # Environnement de développement
├── Makefile                  # Commandes de développement
└── .github/workflows/        # CI/CD complet avec sécurité
```

## ⚡ Installation rapide

```bash
# Installation automatique (recommandé)
./scripts/setup.sh --dev

# Ou installation manuelle
poetry install
cp .env.example .env
# Éditez .env avec vos configurations
```

## 🔧 Configuration

### 1. Variables d'environnement

Copiez `.env.example` vers `.env` et configurez :

```bash
# Configuration GCP
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Configuration sécurité
PII_HASH_SALT=your-secure-salt
ENABLE_AUDIT=true
ENABLE_CONSENT_CHECK=true
```

### 2. Credentials Google Cloud

```bash
# Option 1: Service Account Key
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"

# Option 2: Secret Manager (recommandé en production)
# Stockez vos credentials dans Google Secret Manager
```

### 3. Permissions BigQuery

Le service account doit avoir :
- `roles/bigquery.dataViewer` sur `serving_layer`
- `roles/bigquery.dataEditor` sur `serving_layer_ml`
- `roles/bigquery.jobUser` pour exécuter les requêtes

## 🚀 Utilisation

### Interface CLI

```bash
# Vérifier le statut du système
sensei status

# Construire le feature store quotidien
sensei build-features --date 2024-01-15

# Lister les modèles disponibles
sensei list-models

# Entraîner un modèle avec optimisation
sensei train conversion --optimize --save-path ./models

# Effectuer des prédictions
sensei score conversion --model-path ./models/conversion/latest --date 2024-01-15
```

### Utilisation programmatique

```python
from sensei.pipelines.secure_pipeline import SecureMLPipeline
from sensei.models.conversion import ConversionModel

# Pipeline sécurisé avec audit RGPD
pipeline = SecureMLPipeline(
    project_id="your-project",
    user_role="data_scientist",
    enable_audit=True
)

# Entraînement sécurisé
model, metadata = pipeline.secure_train(
    model_name="conversion",
    train_data_query="SELECT * FROM serving_layer_ml.features_daily WHERE date_features >= '2024-01-01'"
)

# Prédictions avec audit
predictions = pipeline.secure_predict(
    model=model,
    prediction_data_query="SELECT * FROM serving_layer_ml.features_daily WHERE date_features = '2024-01-15'"
)
```

## 🔒 Sécurité & RGPD

### Mesures de sécurité implémentées

- ✅ **Isolation des données** : Lecture seule sur `serving_layer.*`, écriture sur `serving_layer_ml.*`
- ✅ **Anonymisation PII** : Hashage SHA256 automatique des identifiants personnels
- ✅ **Audit complet** : Traçabilité de toutes les prédictions avec métadonnées
- ✅ **Gestion du consentement** : Vérification RGPD avant traitement
- ✅ **Rétention des données** : Politiques automatiques de suppression
- ✅ **Contrôle d'accès** : Matrice de permissions par rôle utilisateur
- ✅ **Logs sécurisés** : Logging structuré sans PII avec rétention 30j

### Conformité RGPD

```python
# Exemple de rapport de confidentialité
from sensei.pipelines.secure_pipeline import SecureMLPipeline

pipeline = SecureMLPipeline("your-project")
report = pipeline.generate_privacy_report(
    model_name="conversion",
    date_range=("2024-01-01", "2024-01-31")
)
```

## 📊 Sources de données

### Serving Layer (lecture seule)

- **`serving_layer.vw_reponses_typeform_deno`** : Réponses Typeform anonymisées
- **`serving_layer.vw_dim_contact`** : Contacts HubSpot avec scores
- **`serving_layer.vw_fact_transaction`** : Transactions et conversions
- **`serving_layer.vw_dim_modjo_transcript`** : Transcriptions d'appels Modjo

### Serving Layer ML (lecture/écriture)

- **`serving_layer_ml.features_daily`** : Features quotidiennes consolidées
- **`serving_layer_ml.audit`** : Logs d'audit des prédictions
- **`serving_layer_ml.predictions_*`** : Tables de prédictions par modèle

## 🎯 Modèles ML

### 1. Conversion Model (LightGBM)

```bash
# Entraînement avec optimisation bayésienne
sensei train conversion --optimize --train-start 2024-01-01 --train-end 2024-03-31

# Objectifs : AUC > 0.85, Precision@200 > 25%
```

### 2. Channel Model (CatBoost)

```bash
# Recommandation canal/timing optimal
sensei train channel --val-split 0.2

# Objectifs : Accuracy multiclasse > 70%
```

### 3. NLP Signals Model (Sentence-BERT)

```bash
# Analyse des transcriptions Modjo
sensei train nlp_signals

# Clustering automatique des thématiques d'appels
```

## 🧪 Tests et qualité

```bash
# Tests complets avec couverture
make test

# Vérifications de qualité
make quality

# Tests de sécurité
make security

# Pipeline CI complète
make ci
```

### Couverture de tests

- **Objectif** : ≥80% de couverture
- **Tests unitaires** : Modèles, connecteurs, utilitaires
- **Tests d'intégration** : Feature store, pipelines sécurisés
- **Tests de sécurité** : Validation des permissions, audit RGPD

## 🐳 Docker

```bash
# Construction de l'image
make docker-build

# Environnement de développement
make docker-dev

# Tests dans Docker
make docker-test
```

## 🚀 Déploiement

### Staging

```bash
# Déploiement automatique sur push main
git push origin main
```

### Production

```bash
# Déploiement sur release
git tag v1.0.0
git push origin v1.0.0
```

### Cloud Run

L'application est déployée automatiquement sur Google Cloud Run avec :
- **Staging** : 2 vCPU, 2GB RAM, scaling 0-10
- **Production** : 2 vCPU, 4GB RAM, scaling 1-50

## 📈 Monitoring

### Métriques de performance

- **Latence des prédictions** : <500ms P95
- **Throughput** : >1000 prédictions/minute
- **Disponibilité** : >99.9%

### Alertes configurées

- Échec d'entraînement de modèle
- Dérive de performance (AUC < seuil)
- Erreurs de pipeline de données
- Violations de sécurité

## 🎯 Objectifs de performance

| Modèle | Métrique principale | Objectif | Métrique secondaire | Objectif |
|--------|-------------------|----------|-------------------|----------|
| Conversion | AUC | > 0.85 | Precision@200 | > 25% |
| Channel | Accuracy | > 70% | F1-score macro | > 0.65 |
| NLP Signals | Silhouette Score | > 0.4 | Nb clusters | 5-15 |

## 🛠️ Développement

### Commandes utiles

```bash
# Configuration de l'environnement de développement
make dev-setup

# Formatage du code
make format

# Surveillance des fichiers avec tests automatiques
make monitor

# Nettoyage complet
make clean-all
```

### Structure des commits

```
feat: nouvelle fonctionnalité
fix: correction de bug
docs: mise à jour documentation
style: formatage code
refactor: refactoring
test: ajout/modification tests
chore: maintenance
```

## 📚 Documentation

- **API Reference** : Documentation auto-générée des modules
- **Architecture Decision Records** : Décisions techniques documentées
- **Runbooks** : Procédures opérationnelles
- **Security Guidelines** : Guide de sécurité et RGPD

## 🤝 Contribution

1. Fork le projet
2. Créez une branche feature (`git checkout -b feature/amazing-feature`)
3. Committez vos changements (`git commit -m 'feat: add amazing feature'`)
4. Poussez vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrez une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- **Issues GitHub** : Pour les bugs et demandes de fonctionnalités
- **Discussions** : Pour les questions et discussions
- **Email** : <EMAIL> pour le support critique

---

**Sensei AI Suite** - Optimisez votre prospection avec l'intelligence artificielle 🚀

