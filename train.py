#!/usr/bin/env python3
"""
Production training script for Sensei AI Suite.

Optimized training pipeline with:
- Hyperparameter optimization using Optuna
- Advanced model validation and overfitting detection
- Automated model versioning and cleanup
- Comprehensive performance metrics
- Production-ready logging and monitoring
"""

import argparse
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
import optuna

from config.settings import settings, ServerCapacity
from src.sensei.data.bq_client import SecureBigQueryClient
from src.sensei.models.base import MODEL_REGISTRY
from src.sensei.utils.logging import get_logger
from src.sensei.utils.overfitting_detector import OverfittingDetector, OverfittingLevel

logger = get_logger(__name__)


class OptimizedModelTrainer:
    """
    Production-ready model trainer with hyperparameter optimization.
    
    Features:
    - Automated hyperparameter tuning with Optuna
    - Cross-validation with overfitting detection
    - Model versioning and automatic cleanup
    - Comprehensive performance metrics
    - Production monitoring and logging
    """
    
    def __init__(self, server_capacity: Optional[ServerCapacity] = None):
        """
        Initialize trainer with server capacity configuration.
        
        Args:
            server_capacity: Server capacity for resource allocation
        """
        self.server_capacity = server_capacity or settings.server_capacity
        self.limits = settings.get_training_limits()
        self.client = SecureBigQueryClient(use_secret_manager=False)
        self.overfitting_detector = OverfittingDetector()
        
        logger.info(
            "Trainer initialized",
            server_capacity=self.server_capacity.value,
            limits=self.limits
        )
    
    def optimize_hyperparameters(
        self, 
        model_name: str, 
        X: pd.DataFrame, 
        y: pd.Series,
        n_trials: int = 100
    ) -> Dict:
        """
        Optimize model hyperparameters using Optuna.
        
        Args:
            model_name: Name of model to optimize
            X: Feature matrix
            y: Target vector
            n_trials: Number of optimization trials
            
        Returns:
            Best hyperparameters found
        """
        logger.info(f"Starting hyperparameter optimization for {model_name}")
        
        def objective(trial):
            """Optuna objective function for hyperparameter optimization."""
            # Get model class
            model_class = MODEL_REGISTRY[model_name]
            model = model_class()
            
            # Define hyperparameter search space based on model type
            if model_name == "conversion":
                params = {
                    'num_leaves': trial.suggest_int('num_leaves', 10, 50),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1),
                    'n_estimators': trial.suggest_int('n_estimators', 50, 200),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
                    'min_child_samples': trial.suggest_int('min_child_samples', 20, 100)
                }
            elif model_name == "channel":
                params = {
                    'iterations': trial.suggest_int('iterations', 10, 50),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1),
                    'depth': trial.suggest_int('depth', 1, 3),
                    'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1.0, 100.0),
                    'min_data_in_leaf': trial.suggest_int('min_data_in_leaf', 50, 200)
                }
            else:
                # Default parameters for other models
                params = {}
            
            # Train model with trial parameters
            try:
                metrics = model.train(X, y, **params)
                
                # Return primary metric for optimization
                if model_name == "conversion":
                    return metrics.get('val_auc', 0.0)
                elif model_name == "channel":
                    return metrics.get('val_accuracy', 0.0)
                else:
                    return metrics.get('score', 0.0)
                    
            except Exception as e:
                logger.warning(f"Trial failed: {e}")
                return 0.0
        
        # Run optimization
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials)
        
        best_params = study.best_params
        logger.info(
            f"Hyperparameter optimization completed for {model_name}",
            best_params=best_params,
            best_value=study.best_value
        )
        
        return best_params
    
    def train_model_with_validation(
        self, 
        model_name: str, 
        optimize_hyperparams: bool = True
    ) -> Dict:
        """
        Train model with comprehensive validation and overfitting detection.
        
        Args:
            model_name: Name of model to train
            optimize_hyperparams: Whether to optimize hyperparameters
            
        Returns:
            Training results and metrics
        """
        logger.info(f"Starting training for {model_name}")
        
        # Get model class and initialize
        if model_name not in MODEL_REGISTRY:
            raise ValueError(f"Unknown model: {model_name}")
        
        model_class = MODEL_REGISTRY[model_name]
        model = model_class()
        
        # Load and prepare data
        data = self._load_training_data(model_name)
        X, y = model.prepare_features(data)
        
        logger.info(
            f"Data prepared for {model_name}",
            samples=len(X),
            features=len(X.columns),
            target_distribution=y.value_counts().to_dict()
        )
        
        # Optimize hyperparameters if requested
        if optimize_hyperparams:
            best_params = self.optimize_hyperparameters(model_name, X, y)
        else:
            best_params = {}
        
        # Train model with best parameters
        training_metrics = model.train(X, y, **best_params)
        
        # Perform cross-validation
        cv_scores = self._perform_cross_validation(model, X, y)
        
        # Detect overfitting
        overfitting_level, overfitting_alerts = self._detect_overfitting(
            model_name, training_metrics, cv_scores
        )
        
        # Save model if validation passes
        if overfitting_level not in [OverfittingLevel.SEVERE, OverfittingLevel.CRITICAL]:
            model_path = model.save_model()
            self._update_model_registry(model_name, model_path, training_metrics)
            self._cleanup_old_models(model_name)
        else:
            logger.error(
                f"Model {model_name} failed overfitting validation",
                level=overfitting_level.value
            )
            return {"status": "failed", "reason": "overfitting_detected"}
        
        # Compile final results
        results = {
            "status": "success",
            "model_name": model_name,
            "training_metrics": training_metrics,
            "cv_scores": cv_scores.tolist() if cv_scores is not None else [],
            "overfitting_level": overfitting_level.value,
            "overfitting_alerts": len(overfitting_alerts),
            "best_params": best_params,
            "model_path": model_path
        }
        
        logger.info(f"Training completed for {model_name}", **results)
        return results
    
    def _load_training_data(self, model_name: str) -> pd.DataFrame:
        """Load training data based on model type and server capacity."""
        limit = self.limits.get(f"max_samples_{model_name}", 100000)
        
        if model_name == "conversion":
            query = f"""
            SELECT * FROM `{settings.database.project_id}.{settings.database.dataset_id}.vw_dim_contact`
            WHERE email IS NOT NULL AND nom IS NOT NULL
            ORDER BY RAND()
            LIMIT {limit}
            """
        elif model_name == "channel":
            query = f"""
            SELECT * FROM `{settings.database.project_id}.{settings.database.dataset_id}.vw_dim_contact`
            WHERE email IS NOT NULL AND nom IS NOT NULL
            ORDER BY RAND()
            LIMIT {limit}
            """
        elif model_name == "nlp_signals":
            query = f"""
            SELECT * FROM `{settings.database.project_id}.{settings.database.dataset_id}.vw_dim_modjo_transcript`
            WHERE LENGTH(content) > 50
            ORDER BY RAND()
            LIMIT {limit}
            """
        else:
            raise ValueError(f"Unknown model: {model_name}")
        
        return self.client.query_df(query)
    
    def _perform_cross_validation(
        self, 
        model, 
        X: pd.DataFrame, 
        y: pd.Series
    ) -> Optional[np.ndarray]:
        """Perform stratified cross-validation."""
        try:
            cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
            scores = cross_val_score(model.model, X, y, cv=cv, scoring='roc_auc')
            return scores
        except Exception as e:
            logger.warning(f"Cross-validation failed: {e}")
            return None
    
    def _detect_overfitting(
        self, 
        model_name: str, 
        metrics: Dict, 
        cv_scores: Optional[np.ndarray]
    ) -> tuple:
        """Detect overfitting using advanced metrics."""
        if model_name == "conversion":
            return self.overfitting_detector.detect_binary_overfitting(
                train_auc=metrics.get('train_auc', 0.5),
                val_auc=metrics.get('val_auc', 0.5),
                cv_scores=cv_scores.tolist() if cv_scores is not None else [],
                train_precision=metrics.get('train_precision', 0.0),
                train_recall=metrics.get('train_recall', 0.0),
                val_precision=metrics.get('val_precision', 0.0),
                val_recall=metrics.get('val_recall', 0.0),
                positive_rate=metrics.get('positive_rate', 0.0)
            )
        else:
            return OverfittingLevel.NONE, []
    
    def _update_model_registry(
        self, 
        model_name: str, 
        model_path: str, 
        metrics: Dict
    ) -> None:
        """Update model registry with new model version."""
        registry_path = Path("models") / "registry.json"
        
        # Load existing registry
        if registry_path.exists():
            with open(registry_path, 'r') as f:
                registry = json.load(f)
        else:
            registry = {}
        
        # Add new model entry
        if model_name not in registry:
            registry[model_name] = []
        
        registry[model_name].append({
            "version": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "path": model_path,
            "metrics": metrics,
            "created_at": datetime.now().isoformat()
        })
        
        # Save updated registry
        with open(registry_path, 'w') as f:
            json.dump(registry, f, indent=2)
    
    def _cleanup_old_models(self, model_name: str) -> None:
        """Remove old model versions keeping only the best ones."""
        max_versions = settings.models.max_model_versions
        
        registry_path = Path("models") / "registry.json"
        if not registry_path.exists():
            return
        
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        if model_name in registry and len(registry[model_name]) > max_versions:
            # Sort by performance metric and keep best versions
            sorted_models = sorted(
                registry[model_name],
                key=lambda x: x.get('metrics', {}).get('val_auc', 0.0),
                reverse=True
            )
            
            # Remove old model files
            for old_model in sorted_models[max_versions:]:
                old_path = Path(old_model['path'])
                if old_path.exists():
                    old_path.unlink()
                    logger.info(f"Removed old model: {old_path}")
            
            # Update registry
            registry[model_name] = sorted_models[:max_versions]
            
            with open(registry_path, 'w') as f:
                json.dump(registry, f, indent=2)


def main():
    """Main training script entry point."""
    parser = argparse.ArgumentParser(description="Train Sensei AI models")
    parser.add_argument(
        "--models", 
        nargs="+", 
        choices=list(MODEL_REGISTRY.keys()),
        default=list(MODEL_REGISTRY.keys()),
        help="Models to train"
    )
    parser.add_argument(
        "--capacity", 
        type=str, 
        choices=[c.value for c in ServerCapacity],
        default="medium",
        help="Server capacity for training"
    )
    parser.add_argument(
        "--optimize", 
        action="store_true",
        help="Optimize hyperparameters"
    )
    
    args = parser.parse_args()
    
    # Initialize trainer
    capacity = ServerCapacity(args.capacity)
    trainer = OptimizedModelTrainer(capacity)
    
    # Train models
    results = {}
    for model_name in args.models:
        try:
            result = trainer.train_model_with_validation(
                model_name, 
                optimize_hyperparams=args.optimize
            )
            results[model_name] = result
        except Exception as e:
            logger.error(f"Training failed for {model_name}: {e}")
            results[model_name] = {"status": "error", "error": str(e)}
    
    # Print summary
    print("\n" + "="*60)
    print("TRAINING SUMMARY")
    print("="*60)
    
    for model_name, result in results.items():
        status = result.get("status", "unknown")
        print(f"{model_name:15} | {status.upper():10}")
        
        if status == "success":
            metrics = result.get("training_metrics", {})
            if "val_auc" in metrics:
                print(f"{'':15} | AUC: {metrics['val_auc']:.3f}")
            if "val_accuracy" in metrics:
                print(f"{'':15} | Accuracy: {metrics['val_accuracy']:.3f}")
    
    print("="*60)
    
    return 0 if all(r.get("status") == "success" for r in results.values()) else 1


if __name__ == "__main__":
    sys.exit(main())
