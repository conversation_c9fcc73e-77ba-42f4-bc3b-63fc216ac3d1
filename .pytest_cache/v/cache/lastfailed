{"tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_read_allowed": true, "tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_forbidden_operations": true, "tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_write_to_ml_dataset": true, "tests/unit/test_data.py::TestSecureBigQueryClient::test_execute_query_validation": true, "tests/unit/test_data.py::TestSecretManager::test_initialization_without_project_id": true, "tests/unit/test_models.py::TestConversionModel::test_train": true, "tests/unit/test_models.py::TestChannelModel::test_train": true, "tests/unit/test_models.py::TestNlpSignalsModel::test_preprocess_text": true, "tests/unit/test_models.py::TestNlpSignalsModel::test_train": true}