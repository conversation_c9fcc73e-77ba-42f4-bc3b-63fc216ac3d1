["tests/test_validation.py::test_config_import", "tests/test_validation.py::test_data_validation", "tests/test_validation.py::test_docker_file_validity", "tests/test_validation.py::test_environment_variables", "tests/test_validation.py::test_logging_functionality", "tests/test_validation.py::test_model_metrics_validation", "tests/test_validation.py::test_model_registry", "tests/test_validation.py::test_model_training_pipeline", "tests/test_validation.py::test_project_structure", "tests/test_validation.py::test_required_files", "tests/test_validation.py::test_requirements_compatibility", "tests/test_validation.py::test_simple_training_models"]