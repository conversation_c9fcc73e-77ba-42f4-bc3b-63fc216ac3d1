"""
Configuration globale des tests pytest.
"""

import os
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from datetime import date, datetime, timedelta

# Configuration des variables d'environnement pour les tests
os.environ["GOOGLE_CLOUD_PROJECT"] = "test-project"
os.environ["ENVIRONMENT"] = "test"


@pytest.fixture
def mock_bq_client():
    """Mock du client BigQuery pour les tests."""
    with patch('sensei.data.bq_client.get_bq_client') as mock:
        client = Mock()
        client.project_id = "test-project"
        client.query_df.return_value = pd.DataFrame()
        client.execute_query.return_value = Mock()
        mock.return_value = client
        yield client


@pytest.fixture
def sample_features_df():
    """DataFrame d'exemple pour les tests de modèles."""
    np.random.seed(42)
    n_samples = 1000
    
    data = {
        'id_prospect': [f'prospect_{i}' for i in range(n_samples)],
        'date_features': [date.today() - timedelta(days=1)] * n_samples,
        
        # Features Typeform
        'heure_soumission': np.random.randint(8, 20, n_samples),
        'jour_semaine_soumission': np.random.randint(1, 8, n_samples),
        'duree_reponses_minutes': np.random.exponential(5, n_samples),
        'vitesse_reponse': np.random.choice(['tres_rapide', 'rapide', 'normal', 'lent'], n_samples),
        'nb_reponses_formulaire': np.random.randint(1, 20, n_samples),
        'abandon_probable': np.random.choice([0, 1], n_samples, p=[0.8, 0.2]),
        'budget_declare': np.random.choice(['<10k', '10k-50k', '50k-100k', '>100k'], n_samples),
        'secteur_activite': np.random.choice(['tech', 'finance', 'retail', 'sante'], n_samples),
        'taille_entreprise': np.random.choice(['startup', 'pme', 'eti', 'grand_groupe'], n_samples),
        'taux_completion_profil': np.random.uniform(0.3, 1.0, n_samples),
        
        # Features HubSpot
        'hubspotscore': np.random.randint(0, 100, n_samples),
        'num_unique_conversion_events': np.random.randint(0, 10, n_samples),
        'num_conversion_events': np.random.randint(0, 20, n_samples),
        'num_associated_deals': np.random.randint(0, 5, n_samples),
        'hs_analytics_num_page_views': np.random.randint(0, 50, n_samples),
        'statut_du_lead': np.random.choice(['new', 'qualified', 'opportunity', 'customer'], n_samples),
        'relationship_status': np.random.choice(['subscriber', 'lead', 'opportunity', 'customer'], n_samples),
        'lead_traite': np.random.choice([0, 1], n_samples, p=[0.7, 0.3]),
        'ip_country': np.random.choice(['FR', 'BE', 'CH', 'CA'], n_samples),
        'anciennete_contact_jours': np.random.randint(1, 365, n_samples),
        'nb_emails_30j': np.random.randint(0, 20, n_samples),
        'nb_appels_30j': np.random.randint(0, 10, n_samples),
        'nb_reunions_30j': np.random.randint(0, 5, n_samples),
        
        # Features Modjo
        'nb_appels_modjo_total': np.random.randint(0, 20, n_samples),
        'nb_appels_modjo_30j': np.random.randint(0, 10, n_samples),
        'duree_moyenne_appels_modjo': np.random.uniform(300, 3600, n_samples),
        'score_prequai_moy_30j': np.random.uniform(0, 1, n_samples),
        'score_decouverte_moy_30j': np.random.uniform(0, 1, n_samples),
        'dernier_score_prequai': np.random.uniform(0, 1, n_samples),
        
        # Target
        'y_converted_90j': np.random.choice([0, 1], n_samples, p=[0.85, 0.15])
    }
    
    return pd.DataFrame(data)


@pytest.fixture
def sample_transcriptions_df():
    """DataFrame d'exemple pour les tests NLP."""
    transcriptions = [
        "Bonjour, je vous appelle concernant votre demande de formation en data science. Pouvez-vous me parler de vos objectifs ?",
        "Nous cherchons à former notre équipe sur les outils d'analyse de données. Nous avons un budget de 50k euros.",
        "Je ne suis pas sûr que ce soit le bon moment pour nous. Pouvez-vous me rappeler dans 3 mois ?",
        "Parfait, nous sommes très intéressés. Quand pouvons-nous commencer la formation ?",
        "Le prix me semble élevé. Avez-vous des options de financement ?",
        "Nous avons déjà un prestataire mais nous ne sommes pas satisfaits. Que proposez-vous de différent ?",
        "Je dois en discuter avec mon équipe. Je vous recontacte la semaine prochaine.",
        "Excellent, nous validons la proposition. Quelles sont les prochaines étapes ?",
        "Nous avons besoin d'une formation sur mesure pour notre secteur d'activité spécifique.",
        "Le timing ne convient pas, nous préférerions commencer en septembre."
    ]
    
    data = {
        'callId': [f'call_{i}' for i in range(len(transcriptions))],
        'contactCrmId': [f'contact_{i}' for i in range(len(transcriptions))],
        'content': transcriptions,
        'startDate': [datetime.now() - timedelta(days=i) for i in range(len(transcriptions))],
        'speakerId': [f'speaker_{i%3}' for i in range(len(transcriptions))]
    }
    
    return pd.DataFrame(data)


@pytest.fixture
def mock_secret_manager():
    """Mock du gestionnaire de secrets."""
    with patch('sensei.utils.secrets.get_secret_manager') as mock:
        secret_manager = Mock()
        secret_manager.get_secret.return_value = '{"type": "service_account", "project_id": "test"}'
        secret_manager.get_bigquery_credentials.return_value = '{"type": "service_account"}'
        mock.return_value = secret_manager
        yield secret_manager


@pytest.fixture
def temp_model_dir(tmp_path):
    """Répertoire temporaire pour sauvegarder les modèles."""
    model_dir = tmp_path / "models"
    model_dir.mkdir()
    return model_dir


@pytest.fixture
def mock_sentence_transformer():
    """Mock du modèle Sentence-BERT."""
    with patch('sentence_transformers.SentenceTransformer') as mock:
        transformer = Mock()
        # Embeddings factices de dimension 384
        transformer.encode.return_value = np.random.rand(10, 384)
        mock.return_value = transformer
        yield transformer


@pytest.fixture
def mock_umap():
    """Mock du modèle UMAP."""
    with patch('umap.UMAP') as mock:
        umap_model = Mock()
        # Embeddings réduits factices
        umap_model.fit_transform.return_value = np.random.rand(10, 50)
        umap_model.transform.return_value = np.random.rand(5, 50)
        mock.return_value = umap_model
        yield umap_model


@pytest.fixture
def mock_hdbscan():
    """Mock du modèle HDBSCAN."""
    with patch('hdbscan.HDBSCAN') as mock:
        hdbscan_model = Mock()
        # Labels de clusters factices
        hdbscan_model.fit_predict.return_value = np.array([0, 0, 1, 1, 2, 2, -1, 0, 1, 2])
        mock.return_value = hdbscan_model
        yield hdbscan_model


# Markers personnalisés pour les tests
def pytest_configure(config):
    """Configuration des markers pytest."""
    config.addinivalue_line("markers", "unit: Tests unitaires rapides")
    config.addinivalue_line("markers", "integration: Tests d'intégration")
    config.addinivalue_line("markers", "slow: Tests lents nécessitant des ressources")


# Hooks pour les tests
def pytest_collection_modifyitems(config, items):
    """Modification automatique des items de test."""
    for item in items:
        # Ajout automatique du marker 'unit' si pas d'autre marker
        if not any(marker.name in ['integration', 'slow'] for marker in item.iter_markers()):
            item.add_marker(pytest.mark.unit)
