"""
Tests unitaires pour les modèles ML.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from sensei.models.base import BaseModel, register_model, get_model_class, MODEL_REGISTRY
from sensei.models.conversion import ConversionModel
from sensei.models.channel import ChannelModel
from sensei.models.nlp_signals import NlpSignalsModel


class TestBaseModel:
    """Tests pour la classe BaseModel."""
    
    def test_register_model_decorator(self):
        """Test du décorateur register_model."""
        @register_model("test_model")
        class TestModel(BaseModel):
            def _create_model(self):
                return Mock()
            
            def _prepare_features(self, df):
                return df
            
            def _fit_model(self, X_train, y_train, X_val=None, y_val=None):
                pass
        
        assert "test_model" in MODEL_REGISTRY
        assert MODEL_REGISTRY["test_model"] == TestModel
        assert TestModel.model_name == "test_model"
    
    def test_get_model_class(self):
        """Test de récupération d'une classe de modèle."""
        # Test avec un modèle existant
        model_class = get_model_class("conversion")
        assert model_class == ConversionModel
        
        # Test avec un modèle inexistant
        with pytest.raises(KeyError):
            get_model_class("inexistant")
    
    def test_base_model_initialization(self):
        """Test d'initialisation du modèle de base."""
        params = {"param1": "value1", "param2": 42}
        
        class TestModel(BaseModel):
            def _create_model(self):
                return Mock()
            
            def _prepare_features(self, df):
                return df
            
            def _fit_model(self, X_train, y_train, X_val=None, y_val=None):
                pass
        
        model = TestModel(params)
        
        assert model.model_params == params
        assert model.model is None
        assert model.feature_columns is None
        assert model.target_column is None
        assert not model.is_trained
        assert model.training_metadata == {}


class TestConversionModel:
    """Tests pour le modèle de conversion."""
    
    def test_initialization(self):
        """Test d'initialisation du modèle de conversion."""
        model = ConversionModel()
        
        assert model.model_name == "conversion"
        assert model.version == "1.0.0"
        assert "objective" in model.model_params
        assert model.model_params["objective"] == "binary"
    
    def test_identify_feature_types(self, sample_features_df):
        """Test d'identification des types de features."""
        model = ConversionModel()
        model._identify_feature_types(sample_features_df)
        
        assert len(model.categorical_features) > 0
        assert len(model.numerical_features) > 0
        assert "secteur_activite" in model.categorical_features
        assert "hubspotscore" in model.numerical_features
    
    def test_prepare_features(self, sample_features_df):
        """Test de préparation des features."""
        model = ConversionModel()
        prepared_df = model._prepare_features(sample_features_df)
        
        # Vérification que les colonnes non-features sont supprimées
        assert "id_prospect" not in prepared_df.columns
        assert "date_features" not in prepared_df.columns
        assert "y_converted_90j" not in prepared_df.columns
        
        # Vérification que les features sont présentes
        assert "hubspotscore" in prepared_df.columns
        assert "secteur_activite" in prepared_df.columns
    
    @patch('lightgbm.LGBMClassifier')
    def test_train(self, mock_lgb, sample_features_df):
        """Test d'entraînement du modèle."""
        # Configuration du mock
        mock_model = Mock()
        mock_model.fit.return_value = None
        mock_model.predict_proba.return_value = np.random.rand(len(sample_features_df), 2)
        mock_lgb.return_value = mock_model
        
        model = ConversionModel()
        
        # Séparation train/val
        train_size = int(len(sample_features_df) * 0.8)
        train_df = sample_features_df.iloc[:train_size]
        val_df = sample_features_df.iloc[train_size:]
        
        # Entraînement
        results = model.train(train_df, val_df)
        
        assert model.is_trained
        assert "train_auc" in results
        assert "val_auc" in results
        assert mock_model.fit.called
    
    def test_predict_without_training(self, sample_features_df):
        """Test de prédiction sans entraînement."""
        model = ConversionModel()
        
        with pytest.raises(ValueError, match="doit être entraîné"):
            model.predict(sample_features_df)


class TestChannelModel:
    """Tests pour le modèle de canal/timing."""
    
    def test_initialization(self):
        """Test d'initialisation du modèle de canal."""
        model = ChannelModel()
        
        assert model.model_name == "channel"
        assert model.version == "1.0.0"
        assert "loss_function" in model.model_params
        assert model.model_params["loss_function"] == "MultiClass"
    
    def test_create_channel_timing_target(self, sample_features_df):
        """Test de création du target canal+timing."""
        model = ChannelModel()
        target = model._create_channel_timing_target(sample_features_df)
        
        assert len(target) == len(sample_features_df)
        assert all("_" in label for label in target)  # Format canal_timing
        
        # Vérification des combinaisons possibles
        unique_labels = set(target)
        for label in unique_labels:
            canal, timing = label.split("_", 1)
            assert canal in model.CHANNELS
            assert timing in model.TIMINGS
    
    @patch('catboost.CatBoostClassifier')
    def test_train(self, mock_catboost, sample_features_df):
        """Test d'entraînement du modèle canal."""
        # Configuration du mock
        mock_model = Mock()
        mock_model.fit.return_value = None
        mock_model.predict.return_value = np.random.randint(0, 12, len(sample_features_df))
        mock_catboost.return_value = mock_model
        
        model = ChannelModel()
        
        # Entraînement
        results = model.train(sample_features_df)
        
        assert model.is_trained
        assert "train_accuracy" in results
        assert mock_model.fit.called
    
    def test_predict_channel_timing(self, sample_features_df):
        """Test de prédiction avec labels décodés."""
        model = ChannelModel()
        
        # Mock du modèle entraîné
        model.is_trained = True
        model.model = Mock()
        model.target_encoder = Mock()
        model.feature_columns = list(sample_features_df.columns)
        
        # Configuration des mocks
        model.model.predict.return_value = np.array([0, 1, 2])
        model.model.predict_proba.return_value = np.array([[0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8]])
        model.target_encoder.inverse_transform.return_value = ["email_matin", "appel_apres_midi", "reunion_soir"]
        
        # Test avec un petit échantillon
        test_df = sample_features_df.head(3)
        results = model.predict_channel_timing(test_df)
        
        assert len(results) == 3
        assert "canal_optimal" in results.columns
        assert "timing_optimal" in results.columns
        assert "confiance" in results.columns


class TestNlpSignalsModel:
    """Tests pour le modèle NLP."""
    
    def test_initialization(self):
        """Test d'initialisation du modèle NLP."""
        model = NlpSignalsModel()
        
        assert model.model_name == "nlp_signals"
        assert model.version == "1.0.0"
        assert "sentence_transformer_model" in model.model_params
    
    def test_preprocess_text(self):
        """Test de préprocessing du texte."""
        model = NlpSignalsModel()
        
        # Texte avec du bruit
        noisy_text = "[00:12:34] Speaker 1: Bonjour!!! Comment allez-vous ???"
        cleaned = model._preprocess_text(noisy_text)
        
        assert "[00:12:34]" not in cleaned
        assert "Speaker 1:" not in cleaned
        assert "!!!" not in cleaned
        assert "Bonjour" in cleaned
    
    def test_prepare_features(self, sample_transcriptions_df):
        """Test de préparation des features NLP."""
        model = NlpSignalsModel()
        prepared_df = model._prepare_features(sample_transcriptions_df)
        
        assert "processed_content" in prepared_df.columns
        assert len(prepared_df) <= len(sample_transcriptions_df)  # Filtrage possible
        
        # Vérification que les textes sont nettoyés
        for text in prepared_df["processed_content"]:
            assert len(text) >= model.model_params["min_text_length"]
    
    @patch('sentence_transformers.SentenceTransformer')
    @patch('umap.UMAP')
    @patch('hdbscan.HDBSCAN')
    def test_train(self, mock_hdbscan, mock_umap, mock_sentence_transformer, sample_transcriptions_df):
        """Test d'entraînement du modèle NLP."""
        # Configuration des mocks
        mock_transformer = Mock()
        mock_transformer.encode.return_value = np.random.rand(len(sample_transcriptions_df), 384)
        mock_sentence_transformer.return_value = mock_transformer
        
        mock_umap_model = Mock()
        mock_umap_model.fit_transform.return_value = np.random.rand(len(sample_transcriptions_df), 50)
        mock_umap.return_value = mock_umap_model
        
        mock_hdbscan_model = Mock()
        mock_hdbscan_model.fit_predict.return_value = np.array([0, 0, 1, 1, 2, 2, -1, 0, 1, 2])
        mock_hdbscan.return_value = mock_hdbscan_model
        
        model = NlpSignalsModel()
        
        # Entraînement
        results = model.train(sample_transcriptions_df)
        
        assert model.is_trained
        assert "n_clusters" in results
        assert "noise_ratio" in results
        assert mock_transformer.encode.called
        assert mock_umap_model.fit_transform.called
        assert mock_hdbscan_model.fit_predict.called
    
    def test_get_cluster_analysis(self):
        """Test d'analyse des clusters."""
        model = NlpSignalsModel()
        
        # Simulation d'un modèle entraîné
        model.is_trained = True
        model.cluster_topics = {
            0: {
                'size': 5,
                'keywords': [('formation', 0.8), ('data', 0.7)],
                'representative_text': 'Exemple de texte...',
                'avg_length': 100
            },
            1: {
                'size': 3,
                'keywords': [('budget', 0.9), ('prix', 0.6)],
                'representative_text': 'Autre exemple...',
                'avg_length': 80
            }
        }
        
        analysis = model.get_cluster_analysis()
        
        assert len(analysis) == 2
        assert "cluster_id" in analysis.columns
        assert "size" in analysis.columns
        assert "top_keywords" in analysis.columns
        
        # Vérification du tri par taille
        assert analysis.iloc[0]["size"] >= analysis.iloc[1]["size"]
