"""
Tests unitaires pour les connecteurs de données.
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, mock_open
import json

from sensei.data.bq_client import SecureBigQueryClient
from sensei.utils.secrets import SecretManager


class TestSecureBigQueryClient:
    """Tests pour le client BigQuery sécurisé."""
    
    @patch('sensei.data.bq_client.bigquery.Client')
    def test_initialization_with_credentials_path(self, mock_bq_client):
        """Test d'initialisation avec un fichier de credentials."""
        with patch('sensei.data.bq_client.service_account.Credentials.from_service_account_file') as mock_creds:
            mock_creds.return_value = Mock()
            
            client = SecureBigQueryClient(
                project_id="test-project",
                credentials_path="/path/to/creds.json",
                use_secret_manager=False
            )
            
            assert client.project_id == "test-project"
            assert client.MAX_BYTES_BILLED == 20 * 1024**3
            mock_creds.assert_called_once_with("/path/to/creds.json")
    
    @patch('sensei.data.bq_client.get_secret_manager')
    @patch('sensei.data.bq_client.bigquery.Client')
    def test_initialization_with_secret_manager(self, mock_bq_client, mock_secret_manager):
        """Test d'initialisation avec Secret Manager."""
        # Configuration du mock Secret Manager
        mock_sm = Mock()
        mock_sm.get_bigquery_credentials.return_value = json.dumps({
            "type": "service_account",
            "project_id": "test-project"
        })
        mock_secret_manager.return_value = mock_sm
        
        with patch('sensei.data.bq_client.service_account.Credentials.from_service_account_info') as mock_creds:
            mock_creds.return_value = Mock()
            
            client = SecureBigQueryClient(project_id="test-project")
            
            assert client.project_id == "test-project"
            mock_sm.get_bigquery_credentials.assert_called_once()
    
    def test_validate_query_permissions_read_allowed(self):
        """Test de validation pour les requêtes de lecture autorisées."""
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        
        # Requête SELECT autorisée
        sql = "SELECT * FROM serving_layer.vw_dim_contact WHERE id = 1"
        client._validate_query_permissions(sql)  # Ne doit pas lever d'exception
    
    def test_validate_query_permissions_forbidden_operations(self):
        """Test de validation pour les opérations interdites."""
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        
        forbidden_queries = [
            "DELETE FROM serving_layer.table1",
            "DROP TABLE serving_layer.table1",
            "TRUNCATE TABLE serving_layer.table1",
            "ALTER TABLE serving_layer.table1 ADD COLUMN test STRING"
        ]
        
        for sql in forbidden_queries:
            with pytest.raises(PermissionError):
                client._validate_query_permissions(sql)
    
    def test_validate_query_permissions_write_to_ml_dataset(self):
        """Test de validation pour l'écriture dans le dataset ML."""
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        
        # Écriture autorisée dans serving_layer_ml
        sql = "CREATE TABLE serving_layer_ml.test AS SELECT 1 as col"
        client._validate_query_permissions(sql)  # Ne doit pas lever d'exception
        
        # Écriture interdite dans serving_layer
        sql = "INSERT INTO serving_layer.table1 VALUES (1)"
        with pytest.raises(PermissionError):
            client._validate_query_permissions(sql)
    
    @patch('sensei.data.bq_client.bigquery.Client')
    def test_query_df_success(self, mock_bq_client):
        """Test de requête réussie."""
        # Configuration du mock
        mock_client_instance = Mock()
        mock_job = Mock()
        mock_job.to_dataframe.return_value = pd.DataFrame({'col1': [1, 2, 3]})
        mock_job.total_bytes_processed = 1000
        mock_job.total_bytes_billed = 500
        mock_job.job_id = "job_123"
        mock_client_instance.query.return_value = mock_job
        mock_bq_client.return_value = mock_client_instance
        
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        client.client = mock_client_instance
        
        # Exécution de la requête
        sql = "SELECT * FROM serving_layer.vw_dim_contact"
        result = client.query_df(sql)
        
        assert len(result) == 3
        assert 'col1' in result.columns
        mock_client_instance.query.assert_called_once()
    
    @patch('sensei.data.bq_client.bigquery.Client')
    def test_query_df_with_params(self, mock_bq_client):
        """Test de requête avec paramètres."""
        mock_client_instance = Mock()
        mock_job = Mock()
        mock_job.to_dataframe.return_value = pd.DataFrame()
        mock_client_instance.query.return_value = mock_job
        mock_bq_client.return_value = mock_client_instance
        
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        client.client = mock_client_instance
        
        # Requête avec paramètres
        sql = "SELECT * FROM serving_layer.table WHERE id = @id"
        params = {"id": "123"}
        client.query_df(sql, params)
        
        # Vérification que les paramètres sont configurés
        call_args = mock_client_instance.query.call_args
        job_config = call_args[1]['job_config']
        assert job_config.query_parameters is not None
    
    def test_execute_query_validation(self):
        """Test de validation pour execute_query."""
        client = SecureBigQueryClient(project_id="test-project", use_secret_manager=False)
        
        # Requête interdite
        sql = "DELETE FROM serving_layer.table1"
        with pytest.raises(PermissionError):
            client.execute_query(sql)


class TestSecretManager:
    """Tests pour le gestionnaire de secrets."""
    
    @patch('sensei.utils.secrets.secretmanager.SecretManagerServiceClient')
    def test_initialization(self, mock_sm_client):
        """Test d'initialisation du Secret Manager."""
        sm = SecretManager(project_id="test-project")
        
        assert sm.project_id == "test-project"
        mock_sm_client.assert_called_once()
    
    def test_initialization_without_project_id(self):
        """Test d'initialisation sans project_id."""
        with pytest.raises(ValueError, match="project_id requis"):
            SecretManager()
    
    @patch('sensei.utils.secrets.secretmanager.SecretManagerServiceClient')
    def test_get_secret_success(self, mock_sm_client):
        """Test de récupération réussie d'un secret."""
        # Configuration du mock
        mock_client = Mock()
        mock_response = Mock()
        mock_response.payload.data = b"secret_value"
        mock_client.access_secret_version.return_value = mock_response
        mock_sm_client.return_value = mock_client
        
        sm = SecretManager(project_id="test-project")
        result = sm.get_secret("test-secret")
        
        assert result == "secret_value"
        mock_client.access_secret_version.assert_called_once()
    
    @patch('sensei.utils.secrets.secretmanager.SecretManagerServiceClient')
    def test_get_secret_error(self, mock_sm_client):
        """Test d'erreur lors de la récupération d'un secret."""
        # Configuration du mock pour lever une exception
        mock_client = Mock()
        mock_client.access_secret_version.side_effect = Exception("Secret not found")
        mock_sm_client.return_value = mock_client
        
        sm = SecretManager(project_id="test-project")
        
        with pytest.raises(Exception, match="Secret not found"):
            sm.get_secret("nonexistent-secret")
    
    @patch('sensei.utils.secrets.secretmanager.SecretManagerServiceClient')
    def test_get_bigquery_credentials(self, mock_sm_client):
        """Test de récupération des credentials BigQuery."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.payload.data = b'{"type": "service_account"}'
        mock_client.access_secret_version.return_value = mock_response
        mock_sm_client.return_value = mock_client
        
        sm = SecretManager(project_id="test-project")
        result = sm.get_bigquery_credentials()
        
        assert result == '{"type": "service_account"}'
        # Vérification que le bon nom de secret est utilisé
        call_args = mock_client.access_secret_version.call_args[1]
        assert "bigquery-service-account-key" in call_args["request"]["name"]
    
    @patch('sensei.utils.secrets.get_secret_manager')
    def test_global_get_secret_function(self, mock_get_sm):
        """Test de la fonction globale get_secret."""
        from sensei.utils.secrets import get_secret
        
        # Configuration du mock
        mock_sm = Mock()
        mock_sm.get_secret.return_value = "test_value"
        mock_get_sm.return_value = mock_sm
        
        result = get_secret("test-secret")
        
        assert result == "test_value"
        mock_sm.get_secret.assert_called_once_with("test-secret", "latest")
