"""
Tests d'intégration pour le Feature Store.
"""

import pytest
import pandas as pd
from datetime import date, timedelta
from unittest.mock import Mock, patch

from sensei.features.build_store import FeatureStoreBuilder


@pytest.mark.integration
class TestFeatureStoreIntegration:
    """Tests d'intégration pour le Feature Store."""
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_feature_store_builder_initialization(self, mock_get_bq_client):
        """Test d'initialisation du FeatureStoreBuilder."""
        mock_client = Mock()
        mock_client.project_id = "test-project"
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        assert builder.project_id == "test-project"
        assert builder.bq_client == mock_client
        assert builder.jinja_env is not None
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_render_sql_template(self, mock_get_bq_client):
        """Test de rendu des templates SQL."""
        mock_client = Mock()
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        # Test avec une date
        target_date = date(2024, 1, 15)
        
        # Le template devrait exister et être rendu
        try:
            sql = builder._render_sql_template("features_daily.sql", target_date=target_date.isoformat())
            
            # Vérifications de base
            assert "test-project" in sql
            assert "2024-01-15" in sql
            assert "CREATE OR REPLACE TABLE" in sql
            assert "serving_layer_ml.features_daily" in sql
            
        except Exception as e:
            pytest.fail(f"Erreur lors du rendu du template: {e}")
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_create_dataset_if_not_exists(self, mock_get_bq_client):
        """Test de création du dataset ML."""
        mock_client = Mock()
        mock_bq_client = Mock()
        
        # Simulation: dataset n'existe pas
        mock_bq_client.get_dataset.side_effect = Exception("Dataset not found")
        mock_bq_client.create_dataset.return_value = Mock()
        
        mock_client.client = mock_bq_client
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        # Exécution
        builder.create_dataset_if_not_exists()
        
        # Vérifications
        mock_bq_client.get_dataset.assert_called_once()
        mock_bq_client.create_dataset.assert_called_once()
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_create_audit_table(self, mock_get_bq_client):
        """Test de création de la table d'audit."""
        mock_client = Mock()
        mock_client.execute_query.return_value = Mock()
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        # Exécution
        builder.create_audit_table()
        
        # Vérification que la requête de création est exécutée
        mock_client.execute_query.assert_called_once()
        
        # Vérification du contenu de la requête
        call_args = mock_client.execute_query.call_args[0]
        sql = call_args[0]
        
        assert "CREATE TABLE IF NOT EXISTS" in sql
        assert "serving_layer_ml.audit" in sql
        assert "audit_id" in sql
        assert "model_name" in sql
        assert "prediction_timestamp" in sql
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_build_features_daily_success(self, mock_get_bq_client):
        """Test de construction réussie des features quotidiennes."""
        mock_client = Mock()
        
        # Mock pour l'exécution de la requête principale
        mock_job = Mock()
        mock_job.job_id = "job_123"
        mock_job.total_bytes_processed = 1000000
        mock_client.execute_query.return_value = mock_job
        
        # Mock pour la requête de comptage
        count_df = pd.DataFrame({'nb_rows': [1500]})
        mock_client.query_df.return_value = count_df
        
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        target_date = date(2024, 1, 15)
        
        # Exécution
        builder.build_features_daily(target_date)
        
        # Vérifications
        assert mock_client.execute_query.called
        assert mock_client.query_df.called
        
        # Vérification que la requête de comptage est correcte
        count_call_args = mock_client.query_df.call_args[0]
        count_sql = count_call_args[0]
        assert "COUNT(*)" in count_sql
        assert "2024-01-15" in count_sql
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_build_features_daily_error_handling(self, mock_get_bq_client):
        """Test de gestion d'erreur lors de la construction des features."""
        mock_client = Mock()
        mock_client.execute_query.side_effect = Exception("BigQuery error")
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        target_date = date(2024, 1, 15)
        
        # Vérification que l'exception est propagée
        with pytest.raises(Exception, match="BigQuery error"):
            builder.build_features_daily(target_date)
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_setup_ml_dataset_complete(self, mock_get_bq_client):
        """Test de configuration complète du dataset ML."""
        mock_client = Mock()
        
        # Mock pour la création du dataset
        mock_bq_client = Mock()
        mock_bq_client.get_dataset.side_effect = Exception("Not found")
        mock_bq_client.create_dataset.return_value = Mock()
        mock_client.client = mock_bq_client
        
        # Mock pour la création de la table d'audit
        mock_client.execute_query.return_value = Mock()
        
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        # Exécution
        builder.setup_ml_dataset()
        
        # Vérifications
        mock_bq_client.create_dataset.assert_called_once()
        mock_client.execute_query.assert_called_once()
    
    @patch('sensei.features.build_store.FeatureStoreBuilder')
    def test_build_features_for_date_utility(self, mock_builder_class):
        """Test de la fonction utilitaire build_features_for_date."""
        from sensei.features.build_store import build_features_for_date
        
        # Configuration du mock
        mock_builder = Mock()
        mock_builder_class.return_value = mock_builder
        
        target_date = date(2024, 1, 15)
        
        # Exécution
        build_features_for_date(target_date, "test-project")
        
        # Vérifications
        mock_builder_class.assert_called_once_with("test-project")
        mock_builder.setup_ml_dataset.assert_called_once()
        mock_builder.build_features_daily.assert_called_once_with(target_date)
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_sql_template_variables_injection(self, mock_get_bq_client):
        """Test d'injection des variables dans les templates SQL."""
        mock_client = Mock()
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="my-test-project")
        
        # Rendu avec des variables spécifiques
        sql = builder._render_sql_template(
            "features_daily.sql",
            target_date="2024-02-20"
        )
        
        # Vérifications des substitutions
        assert "my-test-project" in sql
        assert "2024-02-20" in sql
        
        # Vérification que les variables Jinja sont remplacées
        assert "{{ project_id }}" not in sql
        assert "{{ target_date }}" not in sql
    
    @patch('sensei.features.build_store.get_bq_client')
    def test_feature_store_with_real_date_logic(self, mock_get_bq_client):
        """Test avec logique de dates réaliste."""
        mock_client = Mock()
        mock_job = Mock()
        mock_client.execute_query.return_value = mock_job
        mock_client.query_df.return_value = pd.DataFrame({'nb_rows': [2500]})
        mock_get_bq_client.return_value = mock_client
        
        builder = FeatureStoreBuilder(project_id="test-project")
        
        # Test avec hier
        yesterday = date.today() - timedelta(days=1)
        builder.build_features_daily(yesterday)
        
        # Vérification que la date est correctement formatée
        call_args = mock_client.execute_query.call_args[0]
        sql = call_args[0]
        assert yesterday.isoformat() in sql
