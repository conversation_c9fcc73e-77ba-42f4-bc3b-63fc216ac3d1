#!/usr/bin/env python3
"""
Simple training script for validation.

Tests the basic ML pipeline with minimal dependencies:
- XGBoost for conversion prediction
- CatBoost for channel optimization
- Basic data loading and validation
"""

import argparse
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score
import xgboost as xgb
import catboost as cb

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import logging utilities
try:
    from src.sensei.utils.logging import log_info, log_error
except ImportError:
    # Fallback logging functions
    def log_info(message: str, **kwargs):
        """Simple logging function."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] INFO: {message} {extra}")

    def log_error(message: str, **kwargs):
        """Simple error logging function."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] ERROR: {message} {extra}")


class SimpleConversionModel:
    """Simple conversion prediction model using XGBoost."""
    
    def __init__(self):
        self.model = None
        self.feature_names = []
        self.is_trained = False
    
    def prepare_features(self, data: pd.DataFrame) -> tuple:
        """Prepare features for conversion prediction."""
        # Create synthetic features for validation
        features = pd.DataFrame({
            'nb_interactions': np.random.randint(1, 20, len(data)),
            'hubspot_score': np.random.randint(0, 100, len(data)),
            'nb_jours_actifs': np.random.randint(1, 30, len(data)),
            'duree_moyenne_appels': np.random.uniform(0, 300, len(data)),
            'response_speed_fast': np.random.choice([0, 1], len(data)),
            'budget_large': np.random.choice([0, 1], len(data)),
            'sector_tech': np.random.choice([0, 1], len(data))
        })
        
        # Create synthetic target (realistic conversion rate ~10% to ensure both classes)
        target = np.random.choice([0, 1], len(data), p=[0.9, 0.1])
        
        self.feature_names = list(features.columns)
        return features, pd.Series(target)
    
    def train(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Train XGBoost model with anti-overfitting parameters."""
        log_info("Training conversion model", samples=len(X), features=len(X.columns))
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Configure XGBoost with strong regularization
        self.model = xgb.XGBClassifier(
            n_estimators=50,  # Limited trees
            max_depth=3,      # Shallow trees
            learning_rate=0.1,
            reg_alpha=1.0,    # L1 regularization
            reg_lambda=1.0,   # L2 regularization
            subsample=0.8,    # Row sampling
            colsample_bytree=0.8,  # Column sampling
            random_state=42,
            eval_metric='auc'
        )
        
        # Train with early stopping
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=10,
            verbose=False
        )
        
        # Calculate metrics
        train_pred = self.model.predict_proba(X_train)[:, 1]
        val_pred = self.model.predict_proba(X_val)[:, 1]

        # Check if both classes are present
        if len(np.unique(y_train)) == 2:
            train_auc = roc_auc_score(y_train, train_pred)
        else:
            train_auc = 0.5  # Default for single class

        if len(np.unique(y_val)) == 2:
            val_auc = roc_auc_score(y_val, val_pred)
        else:
            val_auc = 0.5  # Default for single class
        
        self.is_trained = True
        
        metrics = {
            'train_auc': float(train_auc),
            'val_auc': float(val_auc),
            'overfitting_gap': float(train_auc - val_auc),
            'n_estimators_used': self.model.best_iteration if hasattr(self.model, 'best_iteration') else 50,
            'feature_count': len(self.feature_names)
        }
        
        log_info("Conversion model trained", **metrics)
        return metrics
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        return self.model.predict_proba(X)[:, 1]


class SimpleChannelModel:
    """Simple channel optimization model using CatBoost."""
    
    def __init__(self):
        self.model = None
        self.feature_names = []
        self.is_trained = False
        self.classes = ['email_morning', 'email_afternoon', 'call_morning', 'call_afternoon', 'call_evening']
    
    def prepare_features(self, data: pd.DataFrame) -> tuple:
        """Prepare features for channel optimization."""
        # Create synthetic features
        features = pd.DataFrame({
            'nb_appels_historique': np.random.randint(0, 50, len(data)),
            'nb_appels_matin': np.random.randint(0, 10, len(data)),
            'nb_appels_apres_midi': np.random.randint(0, 10, len(data)),
            'duree_moyenne_appels': np.random.uniform(0, 300, len(data)),
            'has_phone': np.random.choice([0, 1], len(data)),
            'has_email': np.random.choice([0, 1], len(data), p=[0.1, 0.9]),
            'response_rate': np.random.uniform(0, 1, len(data))
        })
        
        # Create synthetic target with realistic distribution
        target = np.random.choice(self.classes, len(data), p=[0.1, 0.4, 0.3, 0.15, 0.05])
        
        self.feature_names = list(features.columns)
        return features, pd.Series(target)
    
    def train(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Train CatBoost model with anti-overfitting parameters."""
        log_info("Training channel model", samples=len(X), features=len(X.columns))
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Configure CatBoost with strong regularization
        self.model = cb.CatBoostClassifier(
            iterations=30,        # Limited iterations
            depth=2,             # Shallow trees
            learning_rate=0.1,
            l2_leaf_reg=10.0,    # Strong L2 regularization
            random_strength=1.0,  # Randomization
            bagging_temperature=1.0,
            random_state=42,
            verbose=False,
            early_stopping_rounds=10
        )
        
        # Train with validation
        self.model.fit(
            X_train, y_train,
            eval_set=(X_val, y_val),
            use_best_model=True
        )
        
        # Calculate metrics
        train_pred = self.model.predict(X_train)
        val_pred = self.model.predict(X_val)
        
        train_acc = accuracy_score(y_train, train_pred)
        val_acc = accuracy_score(y_val, val_pred)
        
        self.is_trained = True
        
        metrics = {
            'train_accuracy': float(train_acc),
            'val_accuracy': float(val_acc),
            'overfitting_gap': float(train_acc - val_acc),
            'iterations_used': self.model.tree_count_,
            'feature_count': len(self.feature_names),
            'class_count': len(self.classes)
        }
        
        log_info("Channel model trained", **metrics)
        return metrics
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        return self.model.predict(X)


def create_synthetic_data(n_samples: int = 1000) -> pd.DataFrame:
    """Create synthetic data for validation."""
    log_info("Creating synthetic data", samples=n_samples)
    
    data = pd.DataFrame({
        'id_prospect': [f"PROSPECT_{i:06d}" for i in range(n_samples)],
        'email': [f"user{i}@example.com" for i in range(n_samples)],
        'nom': [f"User {i}" for i in range(n_samples)]
    })
    
    return data


def save_model_results(model_name: str, metrics: Dict[str, Any]) -> None:
    """Save model results to registry."""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    registry_path = models_dir / "registry.json"
    
    # Load existing registry
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
    else:
        registry = {}
    
    # Add new results
    if model_name not in registry:
        registry[model_name] = []
    
    registry[model_name].append({
        "version": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "metrics": metrics,
        "created_at": datetime.now().isoformat(),
        "type": "validation"
    })
    
    # Save registry
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)
    
    log_info(f"Model results saved", model=model_name)


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Simple training validation")
    parser.add_argument("--samples", type=int, default=1000, help="Number of samples")
    parser.add_argument("--models", nargs="+", choices=["conversion", "channel"], 
                       default=["conversion", "channel"], help="Models to train")
    
    args = parser.parse_args()
    
    log_info("Starting simple training validation", 
             samples=args.samples, models=args.models)
    
    # Create synthetic data
    data = create_synthetic_data(args.samples)
    
    results = {}
    
    # Train conversion model
    if "conversion" in args.models:
        try:
            model = SimpleConversionModel()
            X, y = model.prepare_features(data)
            metrics = model.train(X, y)
            save_model_results("conversion", metrics)
            results["conversion"] = {"status": "success", "metrics": metrics}
        except Exception as e:
            log_error(f"Conversion model failed: {e}")
            results["conversion"] = {"status": "error", "error": str(e)}
    
    # Train channel model
    if "channel" in args.models:
        try:
            model = SimpleChannelModel()
            X, y = model.prepare_features(data)
            metrics = model.train(X, y)
            save_model_results("channel", metrics)
            results["channel"] = {"status": "success", "metrics": metrics}
        except Exception as e:
            log_error(f"Channel model failed: {e}")
            results["channel"] = {"status": "error", "error": str(e)}
    
    # Print summary
    print("\n" + "="*60)
    print("VALIDATION TRAINING SUMMARY")
    print("="*60)
    
    for model_name, result in results.items():
        status = result.get("status", "unknown")
        print(f"{model_name:15} | {status.upper():10}")
        
        if status == "success":
            metrics = result.get("metrics", {})
            if "val_auc" in metrics:
                print(f"{'':15} | AUC: {metrics['val_auc']:.3f}")
            if "val_accuracy" in metrics:
                print(f"{'':15} | Accuracy: {metrics['val_accuracy']:.3f}")
    
    print("="*60)
    
    # Return success if all models trained successfully
    success = all(r.get("status") == "success" for r in results.values())
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
