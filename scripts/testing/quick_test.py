#!/usr/bin/env python3
"""
Quick system test for Sensei AI Suite.
Tests core functionality after cleanup.
"""

import sys
import os
from pathlib import Path

# Add paths
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all core imports work."""
    print("🔍 Testing imports...")
    
    try:
        from config.settings import settings, ServerCapacity
        print("✅ Settings import OK")
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False
    
    try:
        from sensei.models.base import MODEL_REGISTRY
        print(f"✅ Models registry OK: {list(MODEL_REGISTRY.keys())}")
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        print("✅ BigQuery client import OK")
    except Exception as e:
        print(f"❌ BigQuery client import failed: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration system."""
    print("⚙️ Testing configuration...")
    
    try:
        from config.settings import settings, get_settings, Environment
        
        # Test different environments
        original_env = os.environ.get('ENVIRONMENT')
        
        os.environ['ENVIRONMENT'] = 'development'
        dev_settings = get_settings()
        
        os.environ['ENVIRONMENT'] = 'production'
        prod_settings = get_settings()
        
        # Restore original
        if original_env:
            os.environ['ENVIRONMENT'] = original_env
        
        # Check differences
        if dev_settings.debug == prod_settings.debug:
            print("❌ Dev and prod settings are identical")
            return False
        
        print(f"✅ Dev debug: {dev_settings.debug}, Prod debug: {prod_settings.debug}")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_models():
    """Test models system."""
    print("🤖 Testing models...")
    
    try:
        from sensei.models.base import MODEL_REGISTRY
        
        expected_models = ['conversion', 'channel', 'nlp_signals']
        
        for model_name in expected_models:
            if model_name not in MODEL_REGISTRY:
                print(f"❌ Model {model_name} not found")
                return False
            
            # Test model creation
            model_class = MODEL_REGISTRY[model_name]
            model = model_class()
            
            print(f"✅ Model {model_name} created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Models test failed: {e}")
        return False

def test_project_structure():
    """Test project structure."""
    print("🏗️ Testing project structure...")
    
    required_files = [
        "pyproject.toml",
        "config/settings.py",
        "src/sensei/api/main.py",
        "scripts/training/train_models.py",
        "scripts/deployment/deploy.sh",
        "deployment/Dockerfile"
    ]
    
    missing = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing.append(file_path)
    
    if missing:
        print(f"❌ Missing files: {missing}")
        return False
    
    print("✅ All required files present")
    return True

def main():
    """Run quick tests."""
    print("🧪 Quick System Test - Sensei AI Suite")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Models", test_models),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return 0
    else:
        print("❌ Some tests failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
