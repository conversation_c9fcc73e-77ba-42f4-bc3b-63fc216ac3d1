#!/usr/bin/env python3
"""
Comprehensive system tests for Sensei AI Suite.
Tests the entire cleaned and optimized system.
"""

import sys
import os
import json
import time
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

# Add src and project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

from config.settings import settings, ServerCapacity


class SystemTester:
    """
    Comprehensive system tester for Sensei AI Suite.
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.results = {}
        
    def test_project_structure(self) -> bool:
        """Test that the project structure is correct."""
        print("🏗️ Testing project structure...")
        
        required_dirs = [
            "src/sensei",
            "config",
            "scripts/training",
            "scripts/deployment",
            "deployment"
        ]
        
        required_files = [
            "pyproject.toml",
            "config/settings.py",
            "src/sensei/api/main.py",
            "scripts/training/train_models.py",
            "scripts/deployment/deploy.sh",
            "deployment/Dockerfile"
        ]
        
        missing_dirs = []
        missing_files = []
        
        for dir_path in required_dirs:
            if not (self.project_root / dir_path).exists():
                missing_dirs.append(dir_path)
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_dirs:
            print(f"❌ Missing directories: {missing_dirs}")
            return False
        
        if missing_files:
            print(f"❌ Missing files: {missing_files}")
            return False
        
        print("✅ Project structure is correct")
        return True
    
    def test_configuration(self) -> bool:
        """Test configuration system."""
        print("⚙️ Testing configuration...")
        
        try:
            # Test settings import
            from config.settings import settings, get_settings
            
            # Test different environments
            os.environ['ENVIRONMENT'] = 'development'
            dev_settings = get_settings()
            
            os.environ['ENVIRONMENT'] = 'production'
            prod_settings = get_settings()
            
            # Verify different configurations
            if dev_settings.debug == prod_settings.debug:
                print("❌ Development and production settings are identical")
                return False
            
            # Test server capacity limits
            for capacity in ServerCapacity:
                settings.server_capacity = capacity
                limits = settings.training_limits
                
                if not all(key in limits for key in ['max_samples_conversion', 'batch_size']):
                    print(f"❌ Missing training limits for capacity {capacity}")
                    return False
            
            print("✅ Configuration system working")
            return True
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False
    
    def test_data_client(self) -> bool:
        """Test BigQuery client with read-only access."""
        print("🗄️ Testing BigQuery client...")
        
        try:
            from sensei.data.bq_client import SecureBigQueryClient
            
            # Test client initialization
            client = SecureBigQueryClient(use_secret_manager=False)
            
            # Test simple query (read-only)
            query = "SELECT 1 as test_value"
            result = client.query_df(query)
            
            if result is None or len(result) == 0:
                print("❌ BigQuery client failed to execute simple query")
                return False
            
            print("✅ BigQuery client working")
            return True
            
        except Exception as e:
            print(f"❌ BigQuery client test failed: {e}")
            return False
    
    def test_models_system(self) -> bool:
        """Test models system."""
        print("🤖 Testing models system...")
        
        try:
            from sensei.models.base import MODEL_REGISTRY
            
            # Check model registry
            expected_models = ['conversion', 'channel', 'nlp_signals']
            
            for model_name in expected_models:
                if model_name not in MODEL_REGISTRY:
                    print(f"❌ Model {model_name} not in registry")
                    return False
                
                # Test model initialization
                model_class = MODEL_REGISTRY[model_name]
                model = model_class()
                
                # Check required methods
                required_methods = ['train', 'predict', 'save', 'load']
                for method in required_methods:
                    if not hasattr(model, method):
                        print(f"❌ Model {model_name} missing method {method}")
                        return False
            
            print("✅ Models system working")
            return True
            
        except Exception as e:
            print(f"❌ Models system test failed: {e}")
            return False
    
    def test_training_script(self) -> bool:
        """Test training script with dry run."""
        print("🏋️ Testing training script...")
        
        try:
            # Test dry run
            cmd = [
                "python", "scripts/training/train_models.py",
                "--capacity", "small",
                "--models", "nlp",
                "--dry-run"
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                print(f"❌ Training script failed: {result.stderr}")
                return False
            
            print("✅ Training script working")
            return True
            
        except Exception as e:
            print(f"❌ Training script test failed: {e}")
            return False
    
    def test_api_startup(self) -> bool:
        """Test API startup and basic endpoints."""
        print("🚀 Testing API startup...")
        
        try:
            # Start API in background
            api_process = subprocess.Popen(
                [
                    "python", "-m", "uvicorn", "src.sensei.api.main:app",
                    "--host", "127.0.0.1",
                    "--port", "8999",
                    "--log-level", "error"
                ],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for startup
            time.sleep(5)
            
            # Test health endpoint
            try:
                response = requests.get("http://127.0.0.1:8999/health", timeout=10)
                
                if response.status_code != 200:
                    print(f"❌ Health endpoint failed: {response.status_code}")
                    return False
                
                health_data = response.json()
                if health_data.get("status") != "healthy":
                    print(f"❌ API not healthy: {health_data}")
                    return False
                
                print("✅ API startup working")
                return True
                
            finally:
                # Clean up
                api_process.terminate()
                api_process.wait(timeout=5)
            
        except Exception as e:
            print(f"❌ API startup test failed: {e}")
            return False
    
    def test_security_features(self) -> bool:
        """Test security features."""
        print("🔒 Testing security features...")
        
        try:
            # Test read-only mode
            if not settings.read_only_mode:
                print("❌ Read-only mode not enabled")
                return False
            
            # Test logging security filter
            from sensei.utils.logging import SecurityFilter
            
            security_filter = SecurityFilter()
            
            # Create test log record with sensitive data
            import logging
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="",
                lineno=0,
                msg="User password is secret123",
                args=(),
                exc_info=None
            )
            
            # Filter should process the record
            security_filter.filter(record)
            
            print("✅ Security features working")
            return True
            
        except Exception as e:
            print(f"❌ Security test failed: {e}")
            return False
    
    def test_deployment_files(self) -> bool:
        """Test deployment files."""
        print("🐳 Testing deployment files...")
        
        try:
            # Check Dockerfile
            dockerfile = self.project_root / "deployment/Dockerfile"
            if not dockerfile.exists():
                print("❌ Dockerfile missing")
                return False
            
            # Check docker-compose
            compose_file = self.project_root / "deployment/docker-compose.yml"
            if not compose_file.exists():
                print("❌ docker-compose.yml missing")
                return False
            
            # Check deployment script
            deploy_script = self.project_root / "scripts/deployment/deploy.sh"
            if not deploy_script.exists():
                print("❌ deploy.sh missing")
                return False
            
            # Check if script is executable
            if not os.access(deploy_script, os.X_OK):
                print("❌ deploy.sh not executable")
                return False
            
            print("✅ Deployment files working")
            return True
            
        except Exception as e:
            print(f"❌ Deployment files test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all system tests."""
        print("🧪 Running comprehensive system tests...")
        print("=" * 60)
        
        tests = [
            ("Project Structure", self.test_project_structure),
            ("Configuration", self.test_configuration),
            ("BigQuery Client", self.test_data_client),
            ("Models System", self.test_models_system),
            ("Training Script", self.test_training_script),
            ("API Startup", self.test_api_startup),
            ("Security Features", self.test_security_features),
            ("Deployment Files", self.test_deployment_files),
        ]
        
        results = {}
        passed = 0
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")
        print(f"✅ Passed: {passed}/{len(tests)}")
        print(f"❌ Failed: {len(tests) - passed}/{len(tests)}")
        
        if passed == len(tests):
            print("🎉 All tests passed! System is ready for production.")
        elif passed >= len(tests) * 0.8:
            print("⚠️  Most tests passed. Minor issues to address.")
        else:
            print("❌ Multiple test failures. System needs attention.")
        
        return results


def main():
    """Main test function."""
    tester = SystemTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    if passed == total:
        return 0
    elif passed >= total * 0.8:
        return 1
    else:
        return 2


if __name__ == "__main__":
    sys.exit(main())
