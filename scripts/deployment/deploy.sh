#!/bin/bash
# Deployment script for Sensei AI Suite

set -e

# Configuration
ENVIRONMENT=${1:-production}
SERVER_CAPACITY=${2:-medium}
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"

echo "🚀 Deploying Sensei AI Suite"
echo "Environment: $ENVIRONMENT"
echo "Server Capacity: $SERVER_CAPACITY"
echo "Project Root: $PROJECT_ROOT"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    echo "❌ Invalid environment. Use: development, staging, or production"
    exit 1
fi

# Validate server capacity
if [[ ! "$SERVER_CAPACITY" =~ ^(small|medium|large|xlarge)$ ]]; then
    echo "❌ Invalid server capacity. Use: small, medium, large, or xlarge"
    exit 1
fi

# Change to project root
cd "$PROJECT_ROOT"

# Load environment configuration
ENV_FILE="config/environments/.env.$ENVIRONMENT"
if [[ -f "$ENV_FILE" ]]; then
    echo "📋 Loading environment configuration: $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    echo "⚠️  Environment file not found: $ENV_FILE"
fi

# Override server capacity
export SERVER_CAPACITY="$SERVER_CAPACITY"

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required"
    exit 1
fi

# Check Poetry (for local deployment)
if [[ "$ENVIRONMENT" == "development" ]] && ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is required for development deployment"
    exit 1
fi

# Check Docker (for production deployment)
if [[ "$ENVIRONMENT" != "development" ]] && ! command -v docker &> /dev/null; then
    echo "❌ Docker is required for production deployment"
    exit 1
fi

# Check credentials
CREDENTIALS_DIR="$PROJECT_ROOT/credentials"
if [[ ! -d "$CREDENTIALS_DIR" ]] || [[ -z "$(ls -A "$CREDENTIALS_DIR" 2>/dev/null)" ]]; then
    echo "⚠️  No credentials found in $CREDENTIALS_DIR"
    echo "Please configure Google Cloud credentials before deployment"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p models logs

# Deployment based on environment
case "$ENVIRONMENT" in
    "development")
        echo "🔧 Development deployment..."
        
        # Install dependencies
        if [[ -f "pyproject.toml" ]]; then
            poetry install
        else
            pip install -r requirements.txt
        fi
        
        # Set environment variables
        export ENVIRONMENT=development
        export PYTHONPATH="$PROJECT_ROOT/src"
        
        # Start API
        echo "🚀 Starting development API..."
        poetry run python -m uvicorn src.sensei.api.main:app \
            --host 127.0.0.1 \
            --port 8000 \
            --reload \
            --log-level debug
        ;;
        
    "staging"|"production")
        echo "🐳 Container deployment..."
        
        # Build and start with Docker Compose
        docker-compose -f deployment/docker-compose.yml build
        
        # Start services
        docker-compose -f deployment/docker-compose.yml up -d sensei-api
        
        # Wait for health check
        echo "⏳ Waiting for service to be healthy..."
        timeout 60 bash -c 'until curl -f http://localhost:8080/health; do sleep 2; done'
        
        if [[ $? -eq 0 ]]; then
            echo "✅ Service is healthy"
        else
            echo "❌ Service failed to start properly"
            docker-compose -f deployment/docker-compose.yml logs sensei-api
            exit 1
        fi
        ;;
esac

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Service Information:"
echo "  Environment: $ENVIRONMENT"
echo "  Server Capacity: $SERVER_CAPACITY"

if [[ "$ENVIRONMENT" == "development" ]]; then
    echo "  API URL: http://localhost:8000"
    echo "  Documentation: http://localhost:8000/docs"
else
    echo "  API URL: http://localhost:8080"
    echo "  Health Check: http://localhost:8080/health"
fi

echo ""
echo "🔧 Management Commands:"
echo "  Check status: curl http://localhost:8080/health"
echo "  View logs: docker-compose -f deployment/docker-compose.yml logs -f"
echo "  Stop service: docker-compose -f deployment/docker-compose.yml down"
echo "  Train models: ./scripts/training/train_models.sh --capacity $SERVER_CAPACITY"
