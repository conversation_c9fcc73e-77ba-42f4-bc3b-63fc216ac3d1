#!/usr/bin/env python3
"""
Script de validation robuste pour détecter l'overfitting et le data leakage.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import StratifiedKFold, TimeSeriesSplit, train_test_split
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, classification_report, confusion_matrix
)
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
import joblib
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import settings
from sensei.data.bq_client import SecureBigQueryClient
from sensei.utils.logging import get_logger

logger = get_logger(__name__)

class RobustModelValidator:
    """Validateur robuste pour détecter l'overfitting et data leakage."""
    
    def __init__(self):
        self.client = SecureBigQueryClient()
        
    def load_clean_conversion_data(self) -> pd.DataFrame:
        """Charge des données de conversion SANS data leakage."""
        logger.info("Chargement des données de conversion propres")
        
        # Requête SANS utiliser les signaux de conversion pour créer le target
        query = f"""
        WITH prospect_base AS (
            SELECT 
                c.id_contact as id_prospect,
                c.nom,
                c.prenom,
                c.email,
                c.numero_telephone,
                c.statut_du_lead,
                CAST(c.hubspotscore AS INT64) as hubspot_score,
                c.source_personnalisee,
                -- Features temporelles AVANT conversion
                MIN(f.startDate) as premier_contact,
                COUNT(DISTINCT f.callId) as nb_interactions_total,
                COUNT(DISTINCT DATE(f.startDate)) as nb_jours_actifs,
                AVG(f.duration) as duree_moyenne_appels,
                -- Features comportementales NEUTRES
                COUNT(DISTINCT CASE WHEN EXTRACT(HOUR FROM f.startDate) BETWEEN 8 AND 12 THEN f.callId END) as appels_matin,
                COUNT(DISTINCT CASE WHEN EXTRACT(HOUR FROM f.startDate) BETWEEN 13 AND 17 THEN f.callId END) as appels_apres_midi,
                COUNT(DISTINCT CASE WHEN EXTRACT(HOUR FROM f.startDate) BETWEEN 18 AND 20 THEN f.callId END) as appels_soir
            FROM `{settings.google_cloud_project}.serving_layer.vw_dim_contact` c
            LEFT JOIN `{settings.google_cloud_project}.serving_layer.vw_fact_modjo_call` f 
                ON c.id_contact = f.contactId
            WHERE c.email IS NOT NULL
            AND c.nom IS NOT NULL
            GROUP BY c.id_contact, c.nom, c.prenom, c.email, c.numero_telephone, c.statut_du_lead, c.hubspotscore, c.source_personnalisee
            HAVING COUNT(DISTINCT f.callId) > 0
        ),
        conversion_labels AS (
            SELECT 
                pb.*,
                -- Target basé sur des critères EXTERNES (pas les features)
                CASE 
                    WHEN pb.statut_du_lead IN ('Client', 'Opportunité fermée gagnée') THEN 1
                    WHEN pb.statut_du_lead IN ('Lead qualifié', 'Opportunité') AND pb.hubspot_score >= 80 THEN 1
                    WHEN pb.nb_interactions_total >= 8 AND pb.duree_moyenne_appels > 300 THEN 1
                    WHEN pb.nb_jours_actifs >= 7 THEN 1
                    ELSE 0
                END as y_converted_90j
            FROM prospect_base pb
        )
        SELECT * FROM conversion_labels
        WHERE y_converted_90j IS NOT NULL
        ORDER BY RAND()
        LIMIT 2000
        """
        
        df = self.client.query_df(query)
        logger.info("Données chargées", samples=len(df), conversion_rate=df['y_converted_90j'].mean())
        
        return df
    
    def prepare_features(self, df: pd.DataFrame) -> tuple:
        """Prépare les features SANS data leakage."""
        
        # Features temporelles
        df['jours_depuis_premier_contact'] = (
            pd.Timestamp.now(tz='UTC') - pd.to_datetime(df['premier_contact'], utc=True)
        ).dt.days.fillna(0)
        
        # Features catégorielles
        df['has_phone'] = df['numero_telephone'].notna().astype(int)
        df['hubspot_score_norm'] = df['hubspot_score'].fillna(0) / 100
        
        # Features comportementales
        df['ratio_appels_matin'] = df['appels_matin'] / (df['nb_interactions_total'] + 1)
        df['ratio_appels_apres_midi'] = df['appels_apres_midi'] / (df['nb_interactions_total'] + 1)
        df['ratio_appels_soir'] = df['appels_soir'] / (df['nb_interactions_total'] + 1)
        
        # Sélection des features PROPRES
        feature_cols = [
            'nb_interactions_total', 'nb_jours_actifs', 'duree_moyenne_appels',
            'jours_depuis_premier_contact', 'has_phone', 'hubspot_score_norm',
            'ratio_appels_matin', 'ratio_appels_apres_midi', 'ratio_appels_soir'
        ]
        
        X = df[feature_cols].fillna(0)
        y = df['y_converted_90j']
        
        logger.info("Features préparées", 
                   feature_count=len(feature_cols),
                   samples=len(X),
                   positive_rate=y.mean())
        
        return X, y, feature_cols
    
    def validate_with_cross_validation(self, X: pd.DataFrame, y: pd.Series) -> dict:
        """Validation croisée robuste."""
        logger.info("Démarrage validation croisée")
        
        # Validation croisée stratifiée (5-fold)
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        models = {
            'logistic': LogisticRegression(random_state=42, max_iter=1000),
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42, max_depth=5)
        }
        
        results = {}
        
        for model_name, model in models.items():
            cv_scores = {
                'accuracy': [], 'precision': [], 'recall': [], 'f1': [], 'auc': []
            }
            
            for fold, (train_idx, val_idx) in enumerate(cv.split(X, y)):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # Entraînement
                model.fit(X_train, y_train)
                
                # Prédictions
                y_pred = model.predict(X_val)
                y_proba = model.predict_proba(X_val)[:, 1]
                
                # Métriques
                cv_scores['accuracy'].append(accuracy_score(y_val, y_pred))
                cv_scores['precision'].append(precision_score(y_val, y_pred, zero_division=0))
                cv_scores['recall'].append(recall_score(y_val, y_pred, zero_division=0))
                cv_scores['f1'].append(f1_score(y_val, y_pred, zero_division=0))
                cv_scores['auc'].append(roc_auc_score(y_val, y_proba))
                
                logger.info(f"Fold {fold+1} - {model_name}", 
                           accuracy=cv_scores['accuracy'][-1],
                           auc=cv_scores['auc'][-1])
            
            # Moyennes et écarts-types
            results[model_name] = {
                metric: {
                    'mean': np.mean(scores),
                    'std': np.std(scores),
                    'scores': scores
                }
                for metric, scores in cv_scores.items()
            }
        
        return results
    
    def validate_temporal_split(self, X: pd.DataFrame, y: pd.Series, df: pd.DataFrame) -> dict:
        """Validation avec split temporel (plus réaliste)."""
        logger.info("Validation avec split temporel")
        
        # Tri par date de premier contact
        df_sorted = df.copy()
        df_sorted['premier_contact'] = pd.to_datetime(df_sorted['premier_contact'])
        df_sorted = df_sorted.sort_values('premier_contact')
        
        # Split 70/30 temporel
        split_idx = int(len(df_sorted) * 0.7)
        
        train_indices = df_sorted.index[:split_idx]
        test_indices = df_sorted.index[split_idx:]
        
        X_train = X.loc[train_indices]
        X_test = X.loc[test_indices]
        y_train = y.loc[train_indices]
        y_test = y.loc[test_indices]
        
        logger.info("Split temporel", 
                   train_samples=len(X_train),
                   test_samples=len(X_test),
                   train_positive_rate=y_train.mean(),
                   test_positive_rate=y_test.mean())
        
        # Test avec modèle simple
        model = LogisticRegression(random_state=42, max_iter=1000)
        model.fit(X_train, y_train)
        
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test)[:, 1]
        
        results = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, zero_division=0),
            'recall': recall_score(y_test, y_pred, zero_division=0),
            'f1': f1_score(y_test, y_pred, zero_division=0),
            'auc': roc_auc_score(y_test, y_proba),
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
        }
        
        return results
    
    def detect_data_leakage(self, X: pd.DataFrame, y: pd.Series, feature_cols: list) -> dict:
        """Détecte le data leakage via corrélations suspectes."""
        logger.info("Détection de data leakage")
        
        correlations = {}
        suspicious_features = []
        
        for col in feature_cols:
            corr = X[col].corr(y)
            correlations[col] = corr
            
            # Corrélation > 0.8 est suspecte
            if abs(corr) > 0.8:
                suspicious_features.append(col)
        
        # Analyse des distributions
        feature_importance = {}
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        for i, col in enumerate(feature_cols):
            feature_importance[col] = model.feature_importances_[i]
        
        return {
            'correlations': correlations,
            'suspicious_features': suspicious_features,
            'feature_importance': feature_importance,
            'max_correlation': max(abs(c) for c in correlations.values())
        }
    
    def run_complete_validation(self) -> dict:
        """Exécute une validation complète."""
        logger.info("=== DÉMARRAGE VALIDATION ROBUSTE ===")
        
        # 1. Chargement des données propres
        df = self.load_clean_conversion_data()
        X, y, feature_cols = self.prepare_features(df)
        
        # 2. Détection de data leakage
        leakage_results = self.detect_data_leakage(X, y, feature_cols)
        
        # 3. Validation croisée
        cv_results = self.validate_with_cross_validation(X, y)
        
        # 4. Validation temporelle
        temporal_results = self.validate_temporal_split(X, y, df)
        
        # 5. Compilation des résultats
        final_results = {
            'data_info': {
                'total_samples': len(df),
                'positive_rate': y.mean(),
                'feature_count': len(feature_cols),
                'features': feature_cols
            },
            'data_leakage': leakage_results,
            'cross_validation': cv_results,
            'temporal_validation': temporal_results
        }
        
        return final_results

def main():
    """Point d'entrée principal."""
    validator = RobustModelValidator()
    results = validator.run_complete_validation()
    
    # Affichage des résultats
    print("\n" + "="*60)
    print("🔍 RAPPORT DE VALIDATION ROBUSTE")
    print("="*60)
    
    # Info données
    data_info = results['data_info']
    print(f"\n📊 DONNÉES:")
    print(f"  • Échantillons: {data_info['total_samples']}")
    print(f"  • Taux de conversion: {data_info['positive_rate']:.1%}")
    print(f"  • Features: {data_info['feature_count']}")
    
    # Data leakage
    leakage = results['data_leakage']
    print(f"\n🚨 DATA LEAKAGE:")
    print(f"  • Corrélation max: {leakage['max_correlation']:.3f}")
    print(f"  • Features suspectes: {len(leakage['suspicious_features'])}")
    if leakage['suspicious_features']:
        print(f"    - {', '.join(leakage['suspicious_features'])}")
    
    # Validation croisée
    cv = results['cross_validation']
    print(f"\n🔄 VALIDATION CROISÉE (5-fold):")
    for model_name, metrics in cv.items():
        print(f"  • {model_name.upper()}:")
        print(f"    - Accuracy: {metrics['accuracy']['mean']:.3f} ± {metrics['accuracy']['std']:.3f}")
        print(f"    - AUC: {metrics['auc']['mean']:.3f} ± {metrics['auc']['std']:.3f}")
        print(f"    - F1: {metrics['f1']['mean']:.3f} ± {metrics['f1']['std']:.3f}")
    
    # Validation temporelle
    temporal = results['temporal_validation']
    print(f"\n⏰ VALIDATION TEMPORELLE:")
    print(f"  • Accuracy: {temporal['accuracy']:.3f}")
    print(f"  • AUC: {temporal['auc']:.3f}")
    print(f"  • F1: {temporal['f1']:.3f}")
    print(f"  • Precision: {temporal['precision']:.3f}")
    print(f"  • Recall: {temporal['recall']:.3f}")
    
    # Diagnostic final
    print(f"\n🎯 DIAGNOSTIC:")
    if leakage['max_correlation'] > 0.8:
        print("  ❌ DATA LEAKAGE DÉTECTÉ - Corrélations suspectes")
    elif cv['logistic']['auc']['mean'] > 0.95:
        print("  ⚠️  OVERFITTING POSSIBLE - Performance trop élevée")
    elif temporal['auc'] < 0.6:
        print("  ⚠️  MODÈLE PEU PERFORMANT - AUC temporelle faible")
    else:
        print("  ✅ MODÈLE SAIN - Métriques réalistes")
    
    print("="*60)
    
    return results

if __name__ == "__main__":
    main()
