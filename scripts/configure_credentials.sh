#!/bin/bash
# Configuration des credentials Google Cloud pour Sensei AI Suite

set -e

echo "🔐 Configuration des credentials Google Cloud..."

# Répertoire du projet
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CREDENTIALS_DIR="$PROJECT_DIR/credentials"

# Recherche du fichier de service account
SERVICE_ACCOUNT_FILE=""

# Recherche de fichiers JSON dans credentials/
if [ -d "$CREDENTIALS_DIR" ]; then
    for file in "$CREDENTIALS_DIR"/*.json; do
        if [ -f "$file" ]; then
            SERVICE_ACCOUNT_FILE="$file"
            break
        fi
    done
fi

if [ -z "$SERVICE_ACCOUNT_FILE" ]; then
    echo "❌ Aucun fichier de service account trouvé dans credentials/"
    echo ""
    echo "📋 Instructions :"
    echo "1. Téléchargez votre service account JSON depuis Google Cloud Console"
    echo "2. Placez-le dans : $CREDENTIALS_DIR/"
    echo "3. Relancez ce script"
    echo ""
    echo "🔗 Guide : https://cloud.google.com/docs/authentication/getting-started"
    exit 1
fi

echo "✅ Service account trouvé : $(basename "$SERVICE_ACCOUNT_FILE")"

# Configuration de la variable d'environnement
export GOOGLE_APPLICATION_CREDENTIALS="$SERVICE_ACCOUNT_FILE"

# Validation du fichier JSON
echo "🔍 Validation du fichier de credentials..."

if ! python3 -c "import json; json.load(open('$SERVICE_ACCOUNT_FILE'))" 2>/dev/null; then
    echo "❌ Fichier JSON invalide : $SERVICE_ACCOUNT_FILE"
    exit 1
fi

# Extraction des informations du service account
PROJECT_ID=$(python3 -c "import json; print(json.load(open('$SERVICE_ACCOUNT_FILE')).get('project_id', 'N/A'))")
CLIENT_EMAIL=$(python3 -c "import json; print(json.load(open('$SERVICE_ACCOUNT_FILE')).get('client_email', 'N/A'))")

echo "✅ Fichier JSON valide"
echo "  📋 Projet : $PROJECT_ID"
echo "  📧 Email : $CLIENT_EMAIL"

# Vérification que c'est le bon projet
if [ "$PROJECT_ID" != "sensei-ai-dev" ]; then
    echo "⚠️  Attention : Le projet dans le service account ($PROJECT_ID) ne correspond pas à sensei-ai-dev"
    echo "   Continuez-vous quand même ? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "❌ Configuration annulée"
        exit 1
    fi
fi

# Configuration des variables d'environnement
echo "⚙️ Configuration des variables d'environnement..."

# Création du fichier .env local
ENV_FILE="$PROJECT_DIR/.env.local"
cat > "$ENV_FILE" << EOF
# Configuration Google Cloud pour Sensei AI Suite
export GOOGLE_APPLICATION_CREDENTIALS="$SERVICE_ACCOUNT_FILE"
export GOOGLE_CLOUD_PROJECT="$PROJECT_ID"
export ENVIRONMENT="production"
export LOG_LEVEL="INFO"

# Variables pour BigQuery
export BQ_PROJECT_ID="$PROJECT_ID"
export BQ_LOCATION="EU"

# Variables pour les tests
export PYTHONPATH="$PROJECT_DIR/src"
EOF

echo "✅ Fichier .env.local créé"

# Test de connexion rapide
echo "🔌 Test de connexion Google Cloud..."

if command -v gcloud &> /dev/null; then
    echo "  📋 gcloud CLI détecté"
    
    # Activation du service account
    if gcloud auth activate-service-account --key-file="$SERVICE_ACCOUNT_FILE" --quiet 2>/dev/null; then
        echo "  ✅ Service account activé"
        
        # Test d'accès au projet
        if gcloud projects describe "$PROJECT_ID" --quiet &>/dev/null; then
            echo "  ✅ Accès au projet validé"
        else
            echo "  ⚠️  Impossible d'accéder au projet $PROJECT_ID"
        fi
    else
        echo "  ⚠️  Impossible d'activer le service account"
    fi
else
    echo "  📋 gcloud CLI non installé (optionnel)"
fi

echo ""
echo "🎉 Configuration terminée !"
echo ""
echo "📋 Prochaines étapes :"
echo "1. Source les variables : source .env.local"
echo "2. Tester BigQuery : python test_bigquery_production.py"
echo "3. Lancer les tests ML : python -m pytest tests/"
echo ""
echo "💡 Pour charger automatiquement les variables :"
echo "   echo 'source .env.local' >> ~/.bashrc"
