#!/usr/bin/env python3
"""
Explore BigQuery datasets and tables for Sensei AI Suite.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from src.sensei.utils.logging import log_info, log_error
except ImportError:
    def log_info(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] INFO: {message} {extra}")
    
    def log_error(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] ERROR: {message} {extra}")


def explore_datasets():
    """Explore all available datasets and tables."""
    try:
        from google.cloud import bigquery
        
        # Set credentials
        credentials_path = Path("credentials/sensei-ai-service-account.json").absolute()
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(credentials_path)
        
        # Initialize client
        project_id = "datalake-sensei"
        client = bigquery.Client(project=project_id)
        
        log_info("Exploring BigQuery datasets", project=project_id)
        
        # List all datasets
        datasets = list(client.list_datasets())
        
        for dataset in datasets:
            dataset_id = dataset.dataset_id
            print(f"\n{'='*60}")
            print(f"DATASET: {dataset_id}")
            print(f"{'='*60}")
            
            try:
                # List tables in dataset
                dataset_ref = client.dataset(dataset_id)
                tables = list(client.list_tables(dataset_ref))
                
                if not tables:
                    print("  No tables found")
                    continue
                
                for table in tables:
                    table_id = table.table_id
                    table_ref = dataset_ref.table(table_id)
                    table_obj = client.get_table(table_ref)
                    
                    print(f"\n  TABLE: {table_id}")
                    print(f"    Rows: {table_obj.num_rows:,}")
                    print(f"    Size: {table_obj.num_bytes / 1024 / 1024:.1f} MB")
                    print(f"    Created: {table_obj.created}")
                    print(f"    Modified: {table_obj.modified}")
                    
                    # Show schema (first 10 columns)
                    print(f"    Schema (first 10 columns):")
                    for i, field in enumerate(table_obj.schema[:10]):
                        print(f"      {field.name}: {field.field_type}")
                    
                    if len(table_obj.schema) > 10:
                        print(f"      ... and {len(table_obj.schema) - 10} more columns")
                    
                    # Sample data for interesting tables
                    if any(keyword in table_id.lower() for keyword in ['contact', 'call', 'prospect', 'lead']):
                        try:
                            sample_query = f"""
                            SELECT *
                            FROM `{project_id}.{dataset_id}.{table_id}`
                            LIMIT 3
                            """
                            sample_data = client.query(sample_query).to_dataframe()
                            print(f"    Sample data:")
                            for col in sample_data.columns[:5]:  # Show first 5 columns
                                print(f"      {col}: {list(sample_data[col].head(3))}")
                        except Exception as e:
                            print(f"    Sample data error: {e}")
                            
            except Exception as e:
                print(f"  Error accessing dataset: {e}")
        
        return True
        
    except Exception as e:
        log_error(f"Dataset exploration failed: {e}")
        return False


def main():
    """Main exploration function."""
    print("="*80)
    print("SENSEI AI SUITE - BIGQUERY DATASET EXPLORATION")
    print("="*80)
    
    success = explore_datasets()
    
    if success:
        print("\n" + "="*80)
        print("✅ EXPLORATION COMPLETED SUCCESSFULLY")
        print("="*80)
        return 0
    else:
        print("\n" + "="*80)
        print("❌ EXPLORATION FAILED")
        print("="*80)
        return 1


if __name__ == "__main__":
    sys.exit(main())
