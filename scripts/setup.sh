#!/bin/bash

# Script de configuration initiale pour Sensei AI Suite
# Usage: ./scripts/setup.sh [--dev]

set -e  # Arrêt en cas d'erreur

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Variables
PYTHON_VERSION="3.11"
POETRY_VERSION="1.6.1"
DEV_MODE=false

# Fonction d'affichage
print_step() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    print_step "Vérification des prérequis..."
    
    # Vérification de Python
    if ! command -v python3.11 &> /dev/null; then
        if ! command -v python3 &> /dev/null; then
            print_error "Python 3.11 n'est pas installé"
            exit 1
        else
            PYTHON_CMD="python3"
            PYTHON_ACTUAL_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
            if [[ "$PYTHON_ACTUAL_VERSION" != "3.11" ]]; then
                print_warning "Python $PYTHON_ACTUAL_VERSION détecté, Python 3.11 recommandé"
            fi
        fi
    else
        PYTHON_CMD="python3.11"
    fi
    
    # Vérification de pip
    if ! command -v pip &> /dev/null; then
        print_error "pip n'est pas installé"
        exit 1
    fi
    
    # Vérification de Git
    if ! command -v git &> /dev/null; then
        print_error "Git n'est pas installé"
        exit 1
    fi
    
    print_success "Prérequis vérifiés"
}

# Installation de Poetry
install_poetry() {
    print_step "Installation de Poetry..."
    
    if command -v poetry &> /dev/null; then
        POETRY_CURRENT_VERSION=$(poetry --version | cut -d' ' -f3)
        print_success "Poetry $POETRY_CURRENT_VERSION déjà installé"
    else
        curl -sSL https://install.python-poetry.org | python3 -
        
        # Ajout de Poetry au PATH
        export PATH="$HOME/.local/bin:$PATH"
        
        if command -v poetry &> /dev/null; then
            print_success "Poetry installé avec succès"
        else
            print_error "Échec de l'installation de Poetry"
            print_error "Veuillez ajouter $HOME/.local/bin à votre PATH"
            exit 1
        fi
    fi
}

# Configuration de Poetry
configure_poetry() {
    print_step "Configuration de Poetry..."
    
    # Configuration pour créer l'environnement virtuel dans le projet
    poetry config virtualenvs.in-project true
    
    # Configuration pour ne pas créer d'environnement virtuel si on est déjà dans un
    poetry config virtualenvs.create true
    
    print_success "Poetry configuré"
}

# Installation des dépendances
install_dependencies() {
    print_step "Installation des dépendances..."
    
    if [[ "$DEV_MODE" == true ]]; then
        print_step "Installation en mode développement..."
        poetry install
    else
        print_step "Installation en mode production..."
        poetry install --only=main
    fi
    
    print_success "Dépendances installées"
}

# Création des répertoires nécessaires
create_directories() {
    print_step "Création des répertoires..."
    
    mkdir -p models
    mkdir -p logs
    mkdir -p data
    mkdir -p tests/fixtures
    
    print_success "Répertoires créés"
}

# Configuration des variables d'environnement
setup_environment() {
    print_step "Configuration de l'environnement..."
    
    if [[ ! -f .env ]]; then
        if [[ -f .env.example ]]; then
            cp .env.example .env
            print_success "Fichier .env créé à partir de .env.example"
            print_warning "Veuillez éditer le fichier .env avec vos configurations"
        else
            print_warning "Fichier .env.example non trouvé"
        fi
    else
        print_success "Fichier .env déjà existant"
    fi
}

# Configuration Git (hooks, etc.)
setup_git() {
    if [[ "$DEV_MODE" == true ]]; then
        print_step "Configuration Git pour le développement..."
        
        # Installation des hooks pre-commit si disponible
        if poetry run pre-commit --version &> /dev/null; then
            poetry run pre-commit install
            print_success "Hooks pre-commit installés"
        else
            print_warning "pre-commit non disponible"
        fi
    fi
}

# Vérification de l'installation
verify_installation() {
    print_step "Vérification de l'installation..."
    
    # Test de l'import du package
    if poetry run python -c "import sensei; print('✅ Package sensei importé avec succès')" 2>/dev/null; then
        print_success "Package sensei fonctionnel"
    else
        print_error "Problème avec l'import du package sensei"
        exit 1
    fi
    
    # Test de la CLI
    if poetry run sensei --help &> /dev/null; then
        print_success "CLI sensei fonctionnelle"
    else
        print_error "Problème avec la CLI sensei"
        exit 1
    fi
    
    # Test des imports des modèles
    if poetry run python -c "from sensei.models.base import MODEL_REGISTRY; print(f'✅ {len(MODEL_REGISTRY)} modèles enregistrés: {list(MODEL_REGISTRY.keys())}')" 2>/dev/null; then
        print_success "Modèles ML enregistrés"
    else
        print_warning "Problème avec l'enregistrement des modèles"
    fi
}

# Affichage des prochaines étapes
show_next_steps() {
    print_success "Installation terminée avec succès!"
    echo ""
    echo -e "${BLUE}Prochaines étapes:${NC}"
    echo ""
    echo "1. Configurez vos variables d'environnement dans le fichier .env"
    echo "2. Configurez vos credentials Google Cloud:"
    echo "   - Définissez GOOGLE_CLOUD_PROJECT"
    echo "   - Configurez GOOGLE_APPLICATION_CREDENTIALS ou Secret Manager"
    echo ""
    echo "3. Testez la configuration:"
    echo "   poetry run sensei status"
    echo ""
    echo "4. Construisez votre premier feature store:"
    echo "   poetry run sensei build-features"
    echo ""
    echo "5. Entraînez votre premier modèle:"
    echo "   poetry run sensei train conversion"
    echo ""
    if [[ "$DEV_MODE" == true ]]; then
        echo "6. Lancez les tests:"
        echo "   make test"
        echo ""
        echo "7. Vérifiez la qualité du code:"
        echo "   make quality"
        echo ""
    fi
    echo -e "${GREEN}Documentation complète disponible dans README.md${NC}"
}

# Fonction principale
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Sensei AI Suite                          ║"
    echo "║              Script de configuration initiale               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Parsing des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                DEV_MODE=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [--dev]"
                echo "  --dev    Installation en mode développement (avec dépendances de dev)"
                exit 0
                ;;
            *)
                print_error "Option inconnue: $1"
                exit 1
                ;;
        esac
    done
    
    if [[ "$DEV_MODE" == true ]]; then
        print_step "Mode développement activé"
    fi
    
    # Exécution des étapes
    check_prerequisites
    install_poetry
    configure_poetry
    install_dependencies
    create_directories
    setup_environment
    setup_git
    verify_installation
    show_next_steps
}

# Gestion des erreurs
trap 'print_error "Script interrompu"; exit 1' INT TERM

# Exécution
main "$@"
