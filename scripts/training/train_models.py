#!/usr/bin/env python3
"""
Production training script for Sensei AI Suite.
Secure, modular, and optimized for different server capacities.
"""

import sys
import os
import argparse
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# Add src and project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

from config.settings import settings, ServerCapacity
from sensei.data.bq_client import SecureBigQueryClient
from sensei.models.base import MODEL_REGISTRY
from sensei.utils.logging import get_logger

logger = get_logger(__name__)


class SecureModelTrainer:
    """
    Secure and modular model trainer.
    Ensures read-only access to production data.
    """
    
    def __init__(self, server_capacity: Optional[ServerCapacity] = None):
        """Initialize the trainer with server capacity settings."""
        self.server_capacity = server_capacity or settings.server_capacity
        self.limits = self._get_training_limits()
        self.client = SecureBigQueryClient(use_secret_manager=False)
        
        logger.info(
            "Trainer initialized",
            server_capacity=self.server_capacity.value,
            limits=self.limits
        )
    
    def _get_training_limits(self) -> Dict[str, int]:
        """Get training limits based on server capacity."""
        limits = {
            ServerCapacity.SMALL: {
                "max_samples_conversion": 2000,
                "max_samples_channel": 2000,
                "max_samples_nlp": 1000,
                "batch_size": 100
            },
            ServerCapacity.MEDIUM: {
                "max_samples_conversion": 10000,
                "max_samples_channel": 10000,
                "max_samples_nlp": 5000,
                "batch_size": 500
            },
            ServerCapacity.LARGE: {
                "max_samples_conversion": 50000,
                "max_samples_channel": 50000,
                "max_samples_nlp": 25000,
                "batch_size": 1000
            },
            ServerCapacity.XLARGE: {
                "max_samples_conversion": 200000,
                "max_samples_channel": 200000,
                "max_samples_nlp": 100000,
                "batch_size": 2000
            }
        }
        return limits[self.server_capacity]
    
    def load_conversion_data(self) -> Optional[pd.DataFrame]:
        """Load conversion training data with security checks."""
        logger.info("Loading conversion data", max_samples=self.limits["max_samples_conversion"])
        
        try:
            # Secure read-only query
            query = f"""
            SELECT 
                c.id_contact as id_prospect,
                c.nom,
                c.prenom,
                c.email,
                COUNT(f.callId) as nb_interactions,
                -- Simulate conversion based on activity (for training)
                CASE 
                    WHEN COUNT(f.callId) >= 3 THEN 1
                    WHEN COUNT(f.callId) = 2 AND RAND() > 0.7 THEN 1
                    WHEN COUNT(f.callId) = 1 AND RAND() > 0.9 THEN 1
                    ELSE 0
                END as y_converted_90j
            FROM `{settings.google_cloud_project}.serving_layer.vw_dim_contact` c
            LEFT JOIN `{settings.google_cloud_project}.serving_layer.vw_fact_modjo_call` f 
                ON c.id_contact = f.contactId
            WHERE c.email IS NOT NULL
            AND c.nom IS NOT NULL
            GROUP BY c.id_contact, c.nom, c.prenom, c.email
            HAVING COUNT(f.callId) > 0
            LIMIT {self.limits["max_samples_conversion"]}
            """
            
            df = self.client.query_df(query)
            
            if df is None or len(df) == 0:
                logger.warning("No conversion data loaded")
                return None
            
            # Add engineered features
            df = self._add_conversion_features(df)
            
            logger.info("Conversion data loaded", samples=len(df), conversion_rate=df['y_converted_90j'].mean())
            return df
            
        except Exception as e:
            logger.error("Failed to load conversion data", error=str(e))
            return None
    
    def load_channel_data(self) -> Optional[pd.DataFrame]:
        """Load channel training data with security checks."""
        logger.info("Loading channel data", max_samples=self.limits["max_samples_channel"])
        
        try:
            query = f"""
            SELECT 
                c.id_contact as id_prospect,
                c.email,
                c.numero_telephone,
                COUNT(f.callId) as nb_appels_historique
            FROM `{settings.google_cloud_project}.serving_layer.vw_dim_contact` c
            LEFT JOIN `{settings.google_cloud_project}.serving_layer.vw_fact_modjo_call` f 
                ON c.id_contact = f.contactId
            WHERE c.email IS NOT NULL
            GROUP BY c.id_contact, c.email, c.numero_telephone
            LIMIT {self.limits["max_samples_channel"]}
            """
            
            df = self.client.query_df(query)
            
            if df is None or len(df) == 0:
                logger.warning("No channel data loaded")
                return None
            
            # Add engineered features
            df = self._add_channel_features(df)
            
            logger.info("Channel data loaded", samples=len(df))
            return df
            
        except Exception as e:
            logger.error("Failed to load channel data", error=str(e))
            return None
    
    def load_nlp_data(self) -> Optional[pd.DataFrame]:
        """Load NLP training data with security checks."""
        logger.info("Loading NLP data", max_samples=self.limits["max_samples_nlp"])
        
        try:
            query = f"""
            SELECT 
                callId,
                speakerId,
                content,
                startTime as startDate,
                endTime
            FROM `{settings.google_cloud_project}.serving_layer.vw_dim_modjo_transcript`
            WHERE content IS NOT NULL 
            AND LENGTH(content) > 50
            AND LENGTH(content) < 1000
            ORDER BY RAND()
            LIMIT {self.limits["max_samples_nlp"]}
            """
            
            df = self.client.query_df(query)
            
            if df is None or len(df) == 0:
                logger.warning("No NLP data loaded")
                return None
            
            # Add contactCrmId for compatibility
            df['contactCrmId'] = df['callId'].apply(lambda x: f"contact_{hash(x) % 1000}")
            
            logger.info("NLP data loaded", samples=len(df))
            return df
            
        except Exception as e:
            logger.error("Failed to load NLP data", error=str(e))
            return None
    
    def _add_conversion_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add engineered features for conversion model."""
        import numpy as np
        
        np.random.seed(42)
        n = len(df)
        
        # Temporal features
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['heure_soumission'] = np.random.randint(8, 18, n)
        df['jour_semaine_soumission'] = np.random.randint(1, 8, n)
        
        # Behavioral features correlated with conversion
        df['duree_reponses_minutes'] = np.where(
            df['y_converted_90j'] == 1,
            np.random.exponential(30, n),  # Converted respond faster
            np.random.exponential(90, n)   # Non-converted slower
        )
        
        df['vitesse_reponse'] = np.where(
            df['duree_reponses_minutes'] < 60, 'rapide',
            np.where(df['duree_reponses_minutes'] < 120, 'moyen', 'lent')
        )
        
        df['budget_declare'] = np.random.choice(['petit', 'moyen', 'grand'], n)
        df['secteur_activite'] = np.random.choice(['tech', 'finance', 'retail'], n)
        
        # Engagement features
        df['score_decouverte_moy_30j'] = np.where(
            df['y_converted_90j'] == 1,
            np.random.beta(3, 2, n),  # Higher scores for converted
            np.random.beta(2, 3, n)   # Lower scores for non-converted
        )
        
        df['nb_interactions_30j'] = df['nb_interactions']
        df['nb_emails_30j'] = np.random.poisson(2, n)
        df['nb_appels_30j'] = np.random.poisson(1, n)
        
        return df
    
    def _add_channel_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add engineered features for channel model."""
        import numpy as np
        import pandas as pd
        
        np.random.seed(42)
        n = len(df)
        
        df['date_features'] = datetime.now().strftime('%Y-%m-%d')
        df['vitesse_reponse'] = np.random.choice(['rapide', 'moyen', 'lent'], n)
        df['budget_declare'] = np.random.choice(['petit', 'moyen', 'grand'], n)
        df['secteur_activite'] = np.random.choice(['tech', 'finance', 'retail'], n)
        
        # Channel preference based on real data
        canal_prefere = []
        timing_prefere = []
        
        for i, row in df.iterrows():
            if pd.notna(row['numero_telephone']) and row['nb_appels_historique'] > 2:
                canal = np.random.choice(['appel', 'reunion'], p=[0.7, 0.3])
                timing = np.random.choice(['matin', 'apres_midi'], p=[0.6, 0.4])
            elif pd.notna(row['email']):
                canal = np.random.choice(['email', 'appel'], p=[0.8, 0.2])
                timing = np.random.choice(['matin', 'apres_midi', 'soir'], p=[0.3, 0.4, 0.3])
            else:
                canal = 'email'
                timing = 'soir'
            
            canal_prefere.append(canal)
            timing_prefere.append(timing)
        
        df['canal_prefere'] = canal_prefere
        df['timing_prefere'] = timing_prefere
        
        # Ratio features
        df['ratio_emails'] = np.random.uniform(0, 1, n)
        df['ratio_appels'] = df['nb_appels_historique'] / (df['nb_appels_historique'].max() + 1)
        df['ratio_reunions'] = np.random.uniform(0, 0.3, n)
        
        return df
    
    def train_model(self, model_name: str, data: pd.DataFrame) -> Optional[Dict]:
        """Train a specific model with the provided data."""
        if model_name not in MODEL_REGISTRY:
            logger.error("Model not found", model_name=model_name, available=list(MODEL_REGISTRY.keys()))
            return None
        
        try:
            # Initialize model
            model_class = MODEL_REGISTRY[model_name]
            model = model_class()
            
            logger.info("Starting training", model_name=model_name, samples=len(data))
            
            # Train model
            metrics = model.train(data, data)  # Using same data for train/val for simplicity
            
            # Save model
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_dir = settings.models_dir / model_name
            model_path = model_dir / f"{model_name}_model_{timestamp}"
            
            model.save(model_path)
            
            # Update registry
            self._update_model_registry(model_name, model_path, metrics)
            
            logger.info("Training completed", model_name=model_name, metrics=metrics)
            return metrics
            
        except Exception as e:
            logger.error("Training failed", model_name=model_name, error=str(e))
            return None
    
    def _update_model_registry(self, model_name: str, model_path: Path, metrics: Dict) -> None:
        """Update the model registry with new model information."""
        registry_path = settings.models_dir / settings.models_registry_file
        
        # Load existing registry
        if registry_path.exists():
            with open(registry_path, 'r') as f:
                registry = json.load(f)
        else:
            registry = {"models": {}, "storage": {"local_path": str(settings.models_dir)}}
        
        # Add new model
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        registry["models"][model_name] = {
            "version": timestamp,
            "path": str(model_path),
            "metrics": metrics,
            "created_at": datetime.now().isoformat(),
            "status": "active",
            "server_capacity": self.server_capacity.value
        }
        
        # Save registry
        registry_path.parent.mkdir(parents=True, exist_ok=True)
        with open(registry_path, 'w') as f:
            json.dump(registry, f, indent=2)
        
        logger.info("Model registry updated", model_name=model_name, path=str(model_path))


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train Sensei AI models")
    parser.add_argument("--models", nargs="+", default=["conversion", "channel", "nlp"],
                       help="Models to train")
    parser.add_argument("--capacity", type=str, choices=[c.value for c in ServerCapacity],
                       default=settings.server_capacity.value,
                       help="Server capacity for training limits")
    parser.add_argument("--dry-run", action="store_true",
                       help="Dry run without actual training")
    
    args = parser.parse_args()
    
    # Initialize trainer
    capacity = ServerCapacity(args.capacity)
    trainer = SecureModelTrainer(capacity)
    
    logger.info("Starting training session", models=args.models, capacity=capacity.value, dry_run=args.dry_run)
    
    results = {}
    
    for model_name in args.models:
        logger.info("Processing model", model_name=model_name)
        
        # Load data based on model type
        if model_name == "conversion":
            data = trainer.load_conversion_data()
        elif model_name == "channel":
            data = trainer.load_channel_data()
        elif model_name == "nlp":
            data = trainer.load_nlp_data()
        else:
            logger.warning("Unknown model type", model_name=model_name)
            continue
        
        if data is None or len(data) == 0:
            logger.warning("No data available for model", model_name=model_name)
            results[model_name] = {"status": "failed", "reason": "no_data"}
            continue
        
        if args.dry_run:
            logger.info("Dry run - would train model", model_name=model_name, samples=len(data))
            results[model_name] = {"status": "dry_run", "samples": len(data)}
        else:
            # Train model
            metrics = trainer.train_model(model_name, data)
            if metrics:
                results[model_name] = {"status": "success", "metrics": metrics}
            else:
                results[model_name] = {"status": "failed", "reason": "training_error"}
    
    # Summary
    logger.info("Training session completed", results=results)
    
    successful = sum(1 for r in results.values() if r["status"] == "success")
    total = len(results)
    
    print(f"\n🎯 Training Summary: {successful}/{total} models trained successfully")
    for model_name, result in results.items():
        status_emoji = "✅" if result["status"] == "success" else "❌" if result["status"] == "failed" else "🔄"
        print(f"  {status_emoji} {model_name}: {result['status']}")
    
    return 0 if successful > 0 else 1


if __name__ == "__main__":
    sys.exit(main())
