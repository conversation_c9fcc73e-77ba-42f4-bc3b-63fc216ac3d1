#!/bin/bash
# Training script for Sensei AI Suite models

set -e

# Default values
CAPACITY="medium"
MODELS="conversion channel nlp"
DRY_RUN=false
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --capacity)
            CAPACITY="$2"
            shift 2
            ;;
        --models)
            MODELS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --capacity CAPACITY    Server capacity (small|medium|large|xlarge)"
            echo "  --models MODELS        Models to train (space-separated)"
            echo "  --dry-run             Dry run without actual training"
            echo "  --help                Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 --capacity large --models \"conversion channel\""
            echo "  $0 --dry-run"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "🤖 Sensei AI Suite - Model Training"
echo "=================================="
echo "Capacity: $CAPACITY"
echo "Models: $MODELS"
echo "Dry Run: $DRY_RUN"
echo "Project Root: $PROJECT_ROOT"

# Validate capacity
if [[ ! "$CAPACITY" =~ ^(small|medium|large|xlarge)$ ]]; then
    echo "❌ Invalid capacity. Use: small, medium, large, or xlarge"
    exit 1
fi

# Change to project root
cd "$PROJECT_ROOT"

# Check prerequisites
echo ""
echo "🔍 Checking prerequisites..."

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required"
    exit 1
fi

# Check credentials
if [[ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
    CREDS_FILE="credentials/sensei-ai-service-account.json"
    if [[ -f "$CREDS_FILE" ]]; then
        export GOOGLE_APPLICATION_CREDENTIALS="$PWD/$CREDS_FILE"
        echo "✅ Using credentials: $CREDS_FILE"
    else
        echo "❌ No Google Cloud credentials found"
        echo "Please configure credentials in credentials/ directory"
        exit 1
    fi
else
    echo "✅ Using credentials: $GOOGLE_APPLICATION_CREDENTIALS"
fi

# Set environment variables
export ENVIRONMENT=production
export GOOGLE_CLOUD_PROJECT=datalake-sensei
export SERVER_CAPACITY="$CAPACITY"
export PYTHONPATH="$PROJECT_ROOT/src"

# Create models directory
mkdir -p models

# Display capacity limits
echo ""
echo "📊 Training limits for capacity '$CAPACITY':"
case "$CAPACITY" in
    "small")
        echo "  Max samples: 2,000 (conversion/channel), 1,000 (nlp)"
        echo "  Memory limit: ~4GB"
        ;;
    "medium")
        echo "  Max samples: 10,000 (conversion/channel), 5,000 (nlp)"
        echo "  Memory limit: ~16GB"
        ;;
    "large")
        echo "  Max samples: 50,000 (conversion/channel), 25,000 (nlp)"
        echo "  Memory limit: ~64GB"
        ;;
    "xlarge")
        echo "  Max samples: 200,000 (conversion/channel), 100,000 (nlp)"
        echo "  Memory limit: ~256GB"
        ;;
esac

# Run training
echo ""
echo "🚀 Starting training..."

if [[ "$DRY_RUN" == "true" ]]; then
    echo "🔄 DRY RUN MODE - No actual training will be performed"
    TRAINING_ARGS="--dry-run"
else
    TRAINING_ARGS=""
fi

# Execute training script
python scripts/training/train_models.py \
    --capacity "$CAPACITY" \
    --models $MODELS \
    $TRAINING_ARGS

TRAINING_EXIT_CODE=$?

echo ""
if [[ $TRAINING_EXIT_CODE -eq 0 ]]; then
    echo "🎉 Training completed successfully!"
    
    # Show model registry
    if [[ -f "models/registry.json" ]]; then
        echo ""
        echo "📋 Model Registry:"
        python -c "
import json
with open('models/registry.json', 'r') as f:
    registry = json.load(f)
for name, info in registry.get('models', {}).items():
    print(f'  ✅ {name}: v{info[\"version\"]} (score: {info.get(\"metrics\", {}).get(\"score\", \"N/A\")})')
"
    fi
    
    echo ""
    echo "🔧 Next steps:"
    echo "  1. Test models: curl http://localhost:8080/models/status"
    echo "  2. Deploy API: ./scripts/deployment/deploy.sh production $CAPACITY"
    echo "  3. Monitor performance: ./scripts/monitoring/check_models.sh"
    
else
    echo "❌ Training failed with exit code $TRAINING_EXIT_CODE"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  1. Check credentials: ls -la credentials/"
    echo "  2. Check logs: tail -f logs/training.log"
    echo "  3. Reduce capacity: --capacity small"
    echo "  4. Test connection: python -c 'from sensei.data.bq_client import SecureBigQueryClient; print(SecureBigQueryClient().client.project)'"
fi

exit $TRAINING_EXIT_CODE
