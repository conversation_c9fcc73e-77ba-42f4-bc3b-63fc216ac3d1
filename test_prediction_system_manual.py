#!/usr/bin/env python3
"""
Test manuel du système de prédiction Sensei AI Suite.
Teste l'orchestration complète des prédictions.
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_prediction_orchestrator():
    """Test de l'orchestrateur de prédictions."""
    print("🎯 Test de l'orchestrateur de prédictions...")

    try:
        # Test des modèles ML disponibles pour les prédictions
        from sensei.models.base import MODEL_REGISTRY

        print(f"✅ Modèles ML disponibles pour prédictions:")
        for model_name, model_class in MODEL_REGISTRY.items():
            print(f"  📋 {model_name}: {model_class.__name__}")

        # Test d'importation du pipeline sécurisé
        from sensei.pipelines.secure_pipeline import SecureMLPipeline
        print(f"✅ Pipeline sécurisé disponible: {SecureMLPipeline.__name__}")

        return True

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_pipeline():
    """Test du pipeline de prédiction."""
    print("\n🔄 Test du pipeline de prédiction...")

    try:
        # Test du pipeline sécurisé existant
        from sensei.pipelines.secure_pipeline import SecureMLPipeline

        print(f"✅ Classe SecureMLPipeline importée")
        print(f"  📋 Module: {SecureMLPipeline.__module__}")

        # Test des méthodes disponibles
        methods = [method for method in dir(SecureMLPipeline) if not method.startswith('_')]
        print(f"  📋 Méthodes publiques: {methods}")

        # Test de l'intégration avec les modèles
        from sensei.models.conversion import ConversionModel
        from sensei.models.channel import ChannelModel
        from sensei.models.nlp_signals import NlpSignalsModel

        print(f"✅ Modèles de prédiction disponibles:")
        print(f"  📋 ConversionModel: {ConversionModel.__name__}")
        print(f"  📋 ChannelModel: {ChannelModel.__name__}")
        print(f"  📋 NlpSignalsModel: {NlpSignalsModel.__name__}")

        return True

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_data_flow():
    """Test du flux de données de prédiction."""
    print("\n📊 Test du flux de données de prédiction...")
    
    try:
        # Simulation du flux de données complet
        np.random.seed(42)
        n_prospects = 50
        
        # 1. Données d'entrée (prospects)
        prospects_data = {
            'id_prospect': [f"prospect_{i}" for i in range(n_prospects)],
            'date_soumission': pd.date_range('2025-01-01', periods=n_prospects, freq='h'),
            'statut': ['nouveau'] * n_prospects,
            'source': np.random.choice(['website', 'email', 'phone'], n_prospects),
        }
        
        prospects_df = pd.DataFrame(prospects_data)
        print(f"✅ Données prospects simulées: {prospects_df.shape}")
        
        # 2. Features enrichies (Feature Store)
        features_data = {
            'id_prospect': prospects_df['id_prospect'],
            'date_features': ['2025-01-15'] * n_prospects,
            
            # Features d'interaction
            'nb_interactions_30j': np.random.poisson(3, n_prospects),
            'duree_reponses_minutes': np.random.exponential(45, n_prospects),
            'nb_emails_30j': np.random.poisson(2, n_prospects),
            'nb_appels_30j': np.random.poisson(1, n_prospects),
            
            # Features temporelles
            'heure_soumission': np.random.randint(8, 18, n_prospects),
            'jour_semaine_soumission': np.random.randint(1, 8, n_prospects),
            
            # Features comportementales
            'vitesse_reponse': np.random.choice(['rapide', 'moyen', 'lent'], n_prospects),
            'budget_declare': np.random.choice(['petit', 'moyen', 'grand'], n_prospects),
            'secteur_activite': np.random.choice(['tech', 'finance', 'retail'], n_prospects),
            
            # Features de conversion
            'score_decouverte_moy_30j': np.random.uniform(0, 1, n_prospects),
            'dernier_score_prequai': np.random.uniform(0, 1, n_prospects),
            
            # Features de canal
            'canal_prefere': np.random.choice(['email', 'appel', 'reunion'], n_prospects),
            'timing_prefere': np.random.choice(['matin', 'apres_midi', 'soir'], n_prospects),
            'ratio_emails': np.random.uniform(0, 1, n_prospects),
            'ratio_appels': np.random.uniform(0, 1, n_prospects),
        }
        
        features_df = pd.DataFrame(features_data)
        print(f"✅ Features enrichies simulées: {features_df.shape}")
        
        # 3. Prédictions simulées
        predictions_data = {
            'id_prospect': prospects_df['id_prospect'],
            'date_prediction': ['2025-01-15'] * n_prospects,
            
            # Prédictions de conversion
            'proba_conversion_90j': np.random.uniform(0, 1, n_prospects),
            'prediction_conversion_90j': np.random.choice([0, 1], n_prospects, p=[0.7, 0.3]),
            
            # Prédictions de canal optimal
            'canal_optimal': np.random.choice(['email', 'appel', 'reunion'], n_prospects),
            'timing_optimal': np.random.choice(['matin', 'apres_midi', 'soir'], n_prospects),
            'confiance_canal': np.random.uniform(0.5, 1.0, n_prospects),
            
            # Signaux NLP (si disponibles)
            'sentiment_score': np.random.uniform(-1, 1, n_prospects),
            'urgence_score': np.random.uniform(0, 1, n_prospects),
            'interet_score': np.random.uniform(0, 1, n_prospects),
        }
        
        predictions_df = pd.DataFrame(predictions_data)
        print(f"✅ Prédictions simulées: {predictions_df.shape}")
        
        # 4. Validation du flux de données
        assert len(prospects_df) == len(features_df) == len(predictions_df)
        assert all(prospects_df['id_prospect'] == features_df['id_prospect'])
        assert all(prospects_df['id_prospect'] == predictions_df['id_prospect'])
        
        print("✅ Cohérence du flux de données validée")
        
        # 5. Métriques de qualité
        conversion_rate = predictions_df['prediction_conversion_90j'].mean()
        avg_proba = predictions_df['proba_conversion_90j'].mean()
        avg_confiance = predictions_df['confiance_canal'].mean()
        
        print(f"✅ Métriques de qualité:")
        print(f"  📊 Taux de conversion prédit: {conversion_rate:.1%}")
        print(f"  📊 Probabilité moyenne: {avg_proba:.3f}")
        print(f"  📊 Confiance canal moyenne: {avg_confiance:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_output_format():
    """Test du format de sortie des prédictions."""
    print("\n📋 Test du format de sortie des prédictions...")
    
    try:
        # Simulation d'une prédiction complète pour un prospect
        prediction_output = {
            'id_prospect': 'prospect_123',
            'date_prediction': '2025-01-15',
            'timestamp_prediction': datetime.now().isoformat(),
            
            # Prédiction de conversion
            'conversion': {
                'probabilite_90j': 0.75,
                'prediction_binaire': 1,
                'confiance': 0.85,
                'facteurs_cles': ['score_decouverte_eleve', 'vitesse_reponse_rapide']
            },
            
            # Prédiction de canal optimal
            'canal_optimal': {
                'canal': 'appel',
                'timing': 'matin',
                'confiance': 0.78,
                'alternatives': [
                    {'canal': 'reunion', 'timing': 'apres_midi', 'score': 0.65},
                    {'canal': 'email', 'timing': 'matin', 'score': 0.45}
                ]
            },
            
            # Signaux NLP (si disponibles)
            'nlp_signals': {
                'sentiment': 0.6,
                'urgence': 0.8,
                'interet': 0.9,
                'themes_cles': ['prix', 'fonctionnalites', 'timing']
            },
            
            # Métadonnées
            'metadata': {
                'version_modele_conversion': '1.0.0',
                'version_modele_canal': '1.0.0',
                'version_modele_nlp': '1.0.0',
                'features_utilisees': 15,
                'qualite_donnees': 0.92
            }
        }
        
        print("✅ Format de prédiction défini")
        print(f"  📋 Prospect: {prediction_output['id_prospect']}")
        print(f"  📋 Conversion: {prediction_output['conversion']['probabilite_90j']:.1%}")
        print(f"  📋 Canal optimal: {prediction_output['canal_optimal']['canal']}")
        print(f"  📋 Timing optimal: {prediction_output['canal_optimal']['timing']}")
        print(f"  📋 Sentiment: {prediction_output['nlp_signals']['sentiment']:.2f}")
        print(f"  📋 Features utilisées: {prediction_output['metadata']['features_utilisees']}")
        
        # Validation du format
        required_fields = ['id_prospect', 'date_prediction', 'conversion', 'canal_optimal']
        for field in required_fields:
            assert field in prediction_output, f"Champ manquant: {field}"
        
        assert 0 <= prediction_output['conversion']['probabilite_90j'] <= 1
        assert prediction_output['canal_optimal']['canal'] in ['email', 'appel', 'reunion']
        assert prediction_output['canal_optimal']['timing'] in ['matin', 'apres_midi', 'soir']
        
        print("✅ Format de sortie validé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test manuel du système de prédiction Sensei AI Suite")
    print("=" * 60)
    
    # Configuration de l'environnement
    os.environ['ENVIRONMENT'] = 'development'
    os.environ['LOG_LEVEL'] = 'INFO'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'sensei-ai-dev'
    
    tests = [
        ("Modèles ML disponibles", test_prediction_orchestrator),
        ("Pipeline sécurisé", test_prediction_pipeline),
        ("Flux de données", test_prediction_data_flow),
        ("Format de sortie", test_prediction_output_format),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 Résumé des tests:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Le système de prédiction fonctionne correctement!")
        return 0
    else:
        print("⚠️  Le système de prédiction nécessite des corrections")
        return 1

if __name__ == "__main__":
    sys.exit(main())
