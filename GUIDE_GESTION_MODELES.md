# 🤖 Guide de Gestion des Modèles - Sensei AI Suite

## 📋 Vue d'ensemble

Ce guide explique la gestion complète du cycle de vie des modèles ML dans Sensei AI Suite, optimisé pour Mac local.

---

## 🏗️ Architecture de Stockage

### **Structure des Répertoires**
```
sensei-AISuite-v1.0/
├── models/                     # 📁 Stockage des modèles
│   ├── conversion/            # 🎯 Modèles de conversion
│   │   ├── conversion_model_20250718_143022.pkl
│   │   └── conversion_model_20250718_150145.pkl
│   ├── channel/               # 📞 Modèles de canal
│   │   ├── channel_model_20250718_143025.pkl
│   │   └── channel_model_20250718_150148.pkl
│   ├── nlp/                   # 🗣️ Modèles NLP
│   │   ├── nlp_model_20250718_143028.pkl
│   │   └── nlp_model_20250718_150151.pkl
│   └── registry.json          # 📋 Registre des modèles
├── mlruns/                    # 📊 MLflow tracking
│   ├── 0/                     # Expérience par défaut
│   └── experiments/           # Expériences nommées
└── api_model_serving.py       # 🚀 API de serving
```

### **Registre des Modèles (registry.json)**
```json
{
  "created_at": "2025-07-18T14:30:22",
  "environment": "production",
  "models": {
    "conversion": {
      "version": "20250718_143022",
      "path": "models/conversion/conversion_model_20250718_143022.pkl",
      "score": 0.847,
      "samples": 8500,
      "created_at": "2025-07-18T14:30:22",
      "status": "active"
    }
  },
  "storage": {
    "local_path": "./models/",
    "mlflow_uri": "./mlruns/",
    "versioning": "timestamp"
  },
  "deployment": {
    "api_endpoint": "http://localhost:8000",
    "model_serving": "local",
    "auto_reload": true
  }
}
```

---

## 🚀 Entraînement des Modèles

### **Commande d'Entraînement**
```bash
# Entraînement complet avec données équilibrées
source .env.local && python train_production_models.py
```

### **Configuration Mac Local**
- **Limite mémoire** : 10,000 échantillons max par modèle
- **Stockage local** : `./models/` et `./mlruns/`
- **MLflow tracking** : Interface web disponible
- **Versioning** : Timestamp automatique

### **Métriques de Performance**
- **Conversion** : AUC-ROC > 0.75
- **Canal** : Accuracy > 0.70
- **NLP** : Score de cohérence > 0.80

---

## 📊 Monitoring avec MLflow

### **Démarrage de l'Interface MLflow**
```bash
# Depuis le répertoire du projet
mlflow ui --backend-store-uri file://$(pwd)/mlruns
```

**Interface disponible** : http://localhost:5000

### **Fonctionnalités MLflow**
- **📈 Tracking** : Métriques, paramètres, artefacts
- **🔄 Comparaison** : Versions de modèles
- **📋 Registre** : Gestion des versions
- **🚀 Déploiement** : Serving automatique

---

## 🔄 Cycle de Vie des Modèles

### **1. Entraînement Initial**
```bash
python train_production_models.py
```
- Création des modèles avec timestamp
- Enregistrement dans MLflow
- Mise à jour du registre JSON

### **2. Réentraînement**
```bash
# Nouveau modèle avec plus de données
python train_production_models.py
```

**Comportement automatique :**
- ✅ **Nouveau modèle** créé avec timestamp unique
- ✅ **Ancien modèle** conservé (pas de suppression)
- ✅ **Registre** mis à jour avec la nouvelle version
- ✅ **API** recharge automatiquement le nouveau modèle

### **3. Gestion des Versions**

#### **Modèles Conservés**
```
models/conversion/
├── conversion_model_20250718_143022.pkl  # Version 1
├── conversion_model_20250718_150145.pkl  # Version 2 (active)
└── conversion_model_20250718_163011.pkl  # Version 3
```

#### **Rollback Manuel**
```python
# Modifier registry.json pour pointer vers une version antérieure
{
  "models": {
    "conversion": {
      "version": "20250718_143022",  # Version antérieure
      "path": "models/conversion/conversion_model_20250718_143022.pkl",
      "status": "active"
    }
  }
}
```

### **4. Nettoyage Automatique**
```python
# Script de nettoyage (à créer)
def cleanup_old_models(keep_last_n=5):
    """Garde seulement les N dernières versions."""
    # Logique de nettoyage basée sur timestamp
```

---

## 🚀 API de Serving

### **Démarrage de l'API**
```bash
# Démarrage en mode développement
python api_model_serving.py

# Ou avec uvicorn directement
uvicorn api_model_serving:app --host 0.0.0.0 --port 8000 --reload
```

### **Endpoints Disponibles**

#### **1. Prédiction de Conversion**
```bash
curl -X POST "http://localhost:8000/predict/conversion" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "12345",
    "nom": "Dupont",
    "email": "<EMAIL>",
    "nb_interactions": 3,
    "vitesse_reponse": "rapide",
    "budget_declare": "grand"
  }'
```

**Réponse :**
```json
{
  "id_prospect": "12345",
  "proba_conversion_90j": 0.847,
  "prediction_conversion_90j": 1,
  "confiance": 0.847,
  "model_version": "20250718_143022"
}
```

#### **2. Prédiction de Canal**
```bash
curl -X POST "http://localhost:8000/predict/channel" \
  -H "Content-Type: application/json" \
  -d '{
    "id_prospect": "12345",
    "email": "<EMAIL>",
    "numero_telephone": "+33123456789",
    "nb_appels_historique": 2
  }'
```

#### **3. Analyse NLP**
```bash
curl -X POST "http://localhost:8000/predict/nlp" \
  -H "Content-Type: application/json" \
  -d '{
    "callId": "67890",
    "speakerId": 1,
    "content": "Bonjour, je suis intéressé par vos solutions...",
    "startDate": 1234567890
  }'
```

#### **4. Statut des Modèles**
```bash
curl "http://localhost:8000/models/status"
```

#### **5. Rechargement des Modèles**
```bash
curl -X POST "http://localhost:8000/models/reload"
```

---

## 🔧 Gestion Avancée

### **Rechargement Automatique**
L'API vérifie automatiquement le registre toutes les 60 secondes et recharge les nouveaux modèles.

### **Cache des Modèles**
- **Mémoire** : Modèles gardés en cache pour performance
- **Rechargement** : Automatique lors de nouvelles versions
- **Fallback** : Gestion d'erreur si modèle indisponible

### **Monitoring de Production**
```python
# Métriques à surveiller
{
  "latency_ms": 150,        # < 500ms
  "throughput_rps": 10,     # Requêtes par seconde
  "error_rate": 0.01,       # < 1%
  "model_accuracy": 0.85    # > 0.75
}
```

---

## 📈 Optimisations Mac Local

### **Mémoire**
- **Limite échantillons** : 10K par modèle
- **Cache modèles** : Chargement à la demande
- **Garbage collection** : Automatique

### **Stockage**
- **Compression** : Modèles joblib compressés
- **Rotation** : Nettoyage automatique des anciens modèles
- **Backup** : Sauvegarde MLflow

### **Performance**
- **Prédiction** : < 100ms par requête
- **Chargement** : < 5s par modèle
- **API** : Async FastAPI

---

## 🚀 Déploiement Production

### **Cloud Run / Vertex AI**
```dockerfile
# Dockerfile optimisé
FROM python:3.11-slim
COPY models/ /app/models/
COPY api_model_serving.py /app/
CMD ["uvicorn", "api_model_serving:app", "--host", "0.0.0.0", "--port", "8080"]
```

### **Kubernetes**
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sensei-ai-api
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: api
        image: sensei-ai:latest
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: models
          mountPath: /app/models
```

---

## 🎯 Bonnes Pratiques

### **Entraînement**
1. **Données équilibrées** : Vérifier la distribution des classes
2. **Validation croisée** : Éviter l'overfitting
3. **Métriques métier** : AUC, précision, rappel
4. **Versioning** : Timestamp automatique

### **Déploiement**
1. **Tests A/B** : Comparer versions de modèles
2. **Rollback rapide** : Retour version précédente
3. **Monitoring** : Alertes sur dégradation performance
4. **Documentation** : Changelog des versions

### **Maintenance**
1. **Réentraînement** : Mensuel ou basé sur drift
2. **Nettoyage** : Suppression anciennes versions
3. **Backup** : Sauvegarde modèles critiques
4. **Audit** : Traçabilité des changements

---

## 📞 Support

**Questions fréquentes :**
- **Modèle non chargé** : Vérifier `registry.json` et chemins
- **Performance lente** : Réduire taille échantillons
- **Erreur mémoire** : Ajuster `MAX_SAMPLES_PER_MODEL`
- **API indisponible** : Vérifier port 8000 libre

**Logs utiles :**
```bash
# Logs API
tail -f api.log

# Logs MLflow
mlflow server --backend-store-uri ./mlruns --default-artifact-root ./mlruns
```
