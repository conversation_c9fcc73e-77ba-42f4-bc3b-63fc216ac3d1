# Fichiers et dossiers à exclure de l'image Docker

# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Tests
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
.readthedocs.yml

# Jupyter Notebooks
.ipynb_checkpoints

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Models (peuvent être volumineux)
models/
*.pkl
*.joblib
*.h5
*.pb

# Data files (peuvent être volumineux)
data/
*.csv
*.parquet
*.json
*.xlsx

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml

# Configuration locale
.env.local
.env.development
.env.test
config.local.yml

# Secrets
*.pem
*.key
service-account*.json
