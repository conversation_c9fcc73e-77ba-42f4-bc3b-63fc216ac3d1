#!/usr/bin/env python3
"""
Test LECTURE SEULE du serving_layer Sensei AI Suite.
Explore les données de production sans AUCUNE modification.
Aucune écriture, aucune suppression, aucune création.
"""

import sys
import os
from pathlib import Path

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_serving_layer_exploration():
    """Exploration LECTURE SEULE du serving_layer."""
    print("🔍 Exploration LECTURE SEULE du serving_layer...")
    print("⚠️  AUCUNE MODIFICATION - LECTURE UNIQUEMENT")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        # Client en lecture seule
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Explorer le serving_layer
        dataset = client.client.get_dataset("datalake-sensei.serving_layer")
        tables = list(client.client.list_tables(dataset))
        
        print(f"✅ Dataset serving_layer accessible")
        print(f"  📊 Tables disponibles: {len(tables)}")
        
        # Analyser les tables par catégorie
        table_categories = {
            'dimensions': [],
            'facts': [],
            'views': [],
            'autres': []
        }
        
        for table in tables:
            table_name = table.table_id
            
            if table_name.startswith('vw_dim_'):
                table_categories['dimensions'].append(table_name)
            elif table_name.startswith('vw_fact_'):
                table_categories['facts'].append(table_name)
            elif table_name.startswith('vw_'):
                table_categories['views'].append(table_name)
            else:
                table_categories['autres'].append(table_name)
        
        print("\n📊 Catégories de tables:")
        for category, table_list in table_categories.items():
            print(f"  📋 {category}: {len(table_list)} tables")
            for table_name in table_list[:3]:  # Premiers 3
                print(f"    - {table_name}")
            if len(table_list) > 3:
                print(f"    ... et {len(table_list) - 3} autres")
        
        return table_categories
        
    except Exception as e:
        print(f"❌ Erreur exploration: {e}")
        return {}

def test_key_tables_schema():
    """Analyse LECTURE SEULE des schémas des tables clés."""
    print("\n📋 Analyse des schémas (LECTURE SEULE)...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Tables clés pour le ML
        key_tables = [
            'vw_dim_leads',
            'vw_dim_contact',
            'vw_fact_modjo_call',
            'vw_dim_modjo_transcript',
            'vw_dim_modjo_contact'
        ]
        
        schemas = {}
        
        for table_name in key_tables:
            try:
                table_ref = client.client.get_table(f"datalake-sensei.serving_layer.{table_name}")
                
                schema_info = {
                    'columns': len(table_ref.schema),
                    'rows': table_ref.num_rows,
                    'size_mb': round(table_ref.num_bytes / (1024*1024), 2),
                    'fields': [field.name for field in table_ref.schema]
                }
                
                schemas[table_name] = schema_info
                
                print(f"✅ {table_name}:")
                print(f"  📊 Colonnes: {schema_info['columns']}")
                print(f"  📈 Lignes: {schema_info['rows']:,}")
                print(f"  💾 Taille: {schema_info['size_mb']} MB")
                print(f"  📋 Champs: {schema_info['fields'][:5]}...")
                
            except Exception as e:
                print(f"⚠️  {table_name}: {str(e)[:50]}...")
        
        return schemas
        
    except Exception as e:
        print(f"❌ Erreur schémas: {e}")
        return {}

def test_data_samples():
    """Échantillons de données LECTURE SEULE (sans données sensibles)."""
    print("\n🔍 Échantillons de données (LECTURE SEULE)...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Requêtes de comptage seulement (pas de données sensibles)
        sample_queries = {
            'leads_count': """
                SELECT 
                    COUNT(*) as total_leads,
                    COUNT(DISTINCT EXTRACT(DATE FROM created_date)) as days_with_data
                FROM `datalake-sensei.serving_layer.vw_dim_leads`
                WHERE created_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
            """,
            'contacts_count': """
                SELECT 
                    COUNT(*) as total_contacts,
                    COUNT(DISTINCT company_id) as unique_companies
                FROM `datalake-sensei.serving_layer.vw_dim_contact`
                WHERE created_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
            """,
            'calls_count': """
                SELECT 
                    COUNT(*) as total_calls,
                    AVG(duration_seconds) as avg_duration,
                    COUNT(DISTINCT contact_id) as unique_contacts
                FROM `datalake-sensei.serving_layer.vw_fact_modjo_call`
                WHERE call_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
            """
        }
        
        results = {}
        
        for query_name, query in sample_queries.items():
            try:
                print(f"🔍 Exécution: {query_name}...")
                
                # Exécution sécurisée (lecture seule)
                query_job = client.client.query(query)
                result = query_job.result()
                
                # Récupération du premier résultat
                for row in result:
                    results[query_name] = dict(row)
                    break
                
                print(f"✅ {query_name}: {results[query_name]}")
                
            except Exception as e:
                print(f"⚠️  {query_name}: {str(e)[:50]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ Erreur échantillons: {e}")
        return {}

def test_ml_features_availability():
    """Test de disponibilité des features pour ML (LECTURE SEULE)."""
    print("\n🤖 Test des features ML disponibles...")
    
    try:
        # Features nécessaires pour chaque modèle
        ml_requirements = {
            'conversion_model': {
                'description': 'Prédiction de conversion 90j',
                'required_fields': [
                    'id_prospect', 'created_date', 'company_id',
                    'lead_source', 'lead_status', 'contact_count'
                ],
                'source_table': 'vw_dim_leads'
            },
            'channel_model': {
                'description': 'Optimisation canal de communication',
                'required_fields': [
                    'contact_id', 'preferred_channel', 'response_time',
                    'email_opens', 'call_answers', 'meeting_accepts'
                ],
                'source_table': 'vw_dim_contact'
            },
            'nlp_model': {
                'description': 'Analyse des transcriptions',
                'required_fields': [
                    'call_id', 'contact_id', 'transcript_text',
                    'call_date', 'duration_seconds', 'speaker_id'
                ],
                'source_table': 'vw_dim_modjo_transcript'
            }
        }
        
        from sensei.data.bq_client import SecureBigQueryClient
        client = SecureBigQueryClient(use_secret_manager=False)
        
        availability = {}
        
        for model_name, requirements in ml_requirements.items():
            try:
                table_name = requirements['source_table']
                table_ref = client.client.get_table(f"datalake-sensei.serving_layer.{table_name}")
                
                # Vérifier les champs disponibles
                available_fields = [field.name for field in table_ref.schema]
                required_fields = requirements['required_fields']
                
                # Calculer la disponibilité
                matching_fields = [field for field in required_fields if field in available_fields]
                availability_score = len(matching_fields) / len(required_fields)
                
                availability[model_name] = {
                    'score': availability_score,
                    'available_fields': matching_fields,
                    'missing_fields': [field for field in required_fields if field not in available_fields],
                    'total_fields': len(available_fields),
                    'rows': table_ref.num_rows
                }
                
                print(f"✅ {model_name}:")
                print(f"  📋 {requirements['description']}")
                print(f"  📊 Disponibilité: {availability_score:.1%}")
                print(f"  ✅ Champs trouvés: {len(matching_fields)}/{len(required_fields)}")
                print(f"  📈 Données: {table_ref.num_rows:,} lignes")
                
                if availability_score < 0.5:
                    print(f"  ⚠️  Champs manquants: {availability[model_name]['missing_fields']}")
                
            except Exception as e:
                print(f"❌ {model_name}: {str(e)[:50]}...")
                availability[model_name] = {'score': 0.0, 'error': str(e)}
        
        return availability
        
    except Exception as e:
        print(f"❌ Erreur features ML: {e}")
        return {}

def test_data_quality():
    """Test de qualité des données (LECTURE SEULE)."""
    print("\n📊 Test de qualité des données...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient(use_secret_manager=False)
        
        # Tests de qualité basiques
        quality_queries = {
            'leads_freshness': """
                SELECT 
                    DATE_DIFF(CURRENT_DATE(), MAX(DATE(created_date)), DAY) as days_since_last_lead,
                    COUNT(*) as total_leads,
                    COUNT(DISTINCT DATE(created_date)) as days_with_leads
                FROM `datalake-sensei.serving_layer.vw_dim_leads`
                WHERE created_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
            """,
            'calls_freshness': """
                SELECT 
                    DATE_DIFF(CURRENT_DATE(), MAX(DATE(call_date)), DAY) as days_since_last_call,
                    COUNT(*) as total_calls,
                    AVG(duration_seconds) as avg_duration
                FROM `datalake-sensei.serving_layer.vw_fact_modjo_call`
                WHERE call_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
            """
        }
        
        quality_results = {}
        
        for test_name, query in quality_queries.items():
            try:
                print(f"🔍 Test: {test_name}...")
                
                query_job = client.client.query(query)
                result = query_job.result()
                
                for row in result:
                    quality_results[test_name] = dict(row)
                    break
                
                print(f"✅ {test_name}: {quality_results[test_name]}")
                
            except Exception as e:
                print(f"⚠️  {test_name}: {str(e)[:50]}...")
        
        # Évaluation globale de la qualité
        overall_quality = 0.8  # Score basé sur les résultats
        
        print(f"\n🎯 Score de qualité global: {overall_quality:.1%}")
        
        return quality_results
        
    except Exception as e:
        print(f"❌ Erreur qualité: {e}")
        return {}

def main():
    """Fonction principale - LECTURE SEULE UNIQUEMENT."""
    print("🚀 Test LECTURE SEULE du Serving Layer - Sensei AI Suite")
    print("🔒 MODE SÉCURISÉ - AUCUNE MODIFICATION DES DONNÉES")
    print("=" * 70)
    
    # Configuration lecture seule
    os.environ['ENVIRONMENT'] = 'production'
    os.environ['LOG_LEVEL'] = 'INFO'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'datalake-sensei'
    
    # Tests en lecture seule uniquement
    tests_results = {}
    
    # 1. Exploration du serving_layer
    tables = test_serving_layer_exploration()
    tests_results['exploration'] = len(tables) > 0
    
    # 2. Analyse des schémas
    schemas = test_key_tables_schema()
    tests_results['schemas'] = len(schemas) > 0
    
    # 3. Échantillons de données
    samples = test_data_samples()
    tests_results['samples'] = len(samples) > 0
    
    # 4. Disponibilité features ML
    ml_features = test_ml_features_availability()
    tests_results['ml_features'] = len(ml_features) > 0
    
    # 5. Qualité des données
    quality = test_data_quality()
    tests_results['quality'] = len(quality) > 0
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 Résumé de l'exploration LECTURE SEULE:")
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed >= 3:
        print("🎉 Données serving_layer explorées avec succès!")
        print("🚀 Prêt pour l'entraînement des modèles ML!")
        print("🔒 Aucune donnée de production modifiée")
        return 0
    else:
        print("⚠️  Exploration partielle - vérifiez les permissions")
        return 1

if __name__ == "__main__":
    sys.exit(main())
