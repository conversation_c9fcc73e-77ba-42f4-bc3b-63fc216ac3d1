#!/usr/bin/env python3
"""
Test de connexion BigQuery en production pour Sensei AI Suite.
Valide l'accès aux données réelles et les permissions.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Ajout du chemin src au PYTHONPATH
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_bigquery_connection():
    """Test de connexion de base à BigQuery."""
    print("🔌 Test de connexion BigQuery...")
    
    try:
        from sensei.data.bq_client import get_bq_client
        
        # Tentative de connexion
        client = get_bq_client()
        
        print(f"✅ Client BigQuery créé")
        print(f"  📋 Projet: {client.project}")
        
        return client
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_project_access(client):
    """Test d'accès au projet Sensei."""
    print("\n🏢 Test d'accès au projet...")
    
    try:
        # Liste des datasets
        datasets = list(client.list_datasets())
        
        print(f"✅ Accès au projet validé")
        print(f"  📊 Datasets trouvés: {len(datasets)}")
        
        for dataset in datasets[:10]:  # Premiers 10
            print(f"    📋 {dataset.dataset_id}")
        
        return datasets
        
    except Exception as e:
        print(f"❌ Erreur d'accès projet: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_sensei_datasets(client):
    """Test d'accès aux datasets Sensei spécifiques."""
    print("\n📊 Test d'accès aux datasets Sensei...")
    
    expected_datasets = [
        "raw_data",
        "serving_layer_ml", 
        "analytics",
        "staging"
    ]
    
    found_datasets = []
    
    try:
        for dataset_name in expected_datasets:
            try:
                dataset = client.get_dataset(f"sensei-ai-dev.{dataset_name}")
                found_datasets.append(dataset_name)
                print(f"  ✅ {dataset_name}: Accessible")
                
                # Compter les tables
                tables = list(client.list_tables(dataset))
                print(f"    📋 Tables: {len(tables)}")
                
            except Exception as e:
                print(f"  ❌ {dataset_name}: {str(e)[:50]}...")
        
        print(f"\n✅ Datasets accessibles: {len(found_datasets)}/{len(expected_datasets)}")
        
        return found_datasets
        
    except Exception as e:
        print(f"❌ Erreur datasets: {e}")
        return []

def test_raw_data_access(client):
    """Test d'accès aux données brutes."""
    print("\n📋 Test d'accès aux données brutes...")
    
    try:
        # Test de requête simple sur raw_data
        query = """
        SELECT 
            table_name,
            row_count,
            size_bytes
        FROM `sensei-ai-dev.raw_data.__TABLES__`
        ORDER BY row_count DESC
        LIMIT 10
        """
        
        print("🔍 Exécution de la requête de test...")
        
        from sensei.data.bq_client import SecureBigQueryClient
        secure_client = SecureBigQueryClient()
        
        # Test de validation de requête
        is_valid = secure_client._validate_query_permissions(query)
        print(f"  📋 Validation sécurité: {'✅ OK' if is_valid else '❌ BLOQUÉ'}")
        
        if is_valid:
            # Exécution de la requête
            df = secure_client.query_df(query)
            
            print(f"✅ Requête exécutée avec succès")
            print(f"  📊 Tables trouvées: {len(df)}")
            
            if len(df) > 0:
                print("  📋 Principales tables:")
                for _, row in df.head().iterrows():
                    print(f"    📄 {row['table_name']}: {row['row_count']:,} lignes")
            
            return df
        else:
            print("⚠️  Requête bloquée par la sécurité")
            return None
        
    except Exception as e:
        print(f"❌ Erreur données brutes: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_serving_layer_access(client):
    """Test d'accès au serving layer ML."""
    print("\n🎯 Test d'accès au serving layer ML...")
    
    try:
        # Vérification de l'existence de serving_layer_ml
        dataset = client.get_dataset("sensei-ai-dev.serving_layer_ml")
        
        print(f"✅ Dataset serving_layer_ml accessible")
        
        # Liste des tables
        tables = list(client.list_tables(dataset))
        print(f"  📊 Tables ML: {len(tables)}")
        
        ml_tables = []
        for table in tables:
            ml_tables.append(table.table_id)
            print(f"    📋 {table.table_id}")
        
        # Test spécifique de features_daily si elle existe
        if "features_daily" in ml_tables:
            print("\n🎯 Test de la table features_daily...")
            
            query = """
            SELECT 
                date_features,
                COUNT(*) as nb_prospects,
                COUNT(DISTINCT id_prospect) as prospects_uniques
            FROM `sensei-ai-dev.serving_layer_ml.features_daily`
            WHERE date_features >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
            GROUP BY date_features
            ORDER BY date_features DESC
            LIMIT 7
            """
            
            from sensei.data.bq_client import SecureBigQueryClient
            secure_client = SecureBigQueryClient()
            
            if secure_client._validate_query_permissions(query):
                df = secure_client.query_df(query)
                print(f"  ✅ Features récentes: {len(df)} jours")
                
                if len(df) > 0:
                    for _, row in df.iterrows():
                        print(f"    📅 {row['date_features']}: {row['nb_prospects']:,} prospects")
                
                return df
            else:
                print("  ⚠️  Requête features_daily bloquée")
        
        return ml_tables
        
    except Exception as e:
        print(f"❌ Erreur serving layer: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_permissions_and_security():
    """Test des permissions et de la sécurité."""
    print("\n🔒 Test des permissions et sécurité...")
    
    try:
        from sensei.data.bq_client import SecureBigQueryClient
        
        client = SecureBigQueryClient()
        
        # Test de requêtes autorisées
        allowed_queries = [
            "SELECT 1 as test",
            "SELECT COUNT(*) FROM `sensei-ai-dev.raw_data.prospects`",
            "SELECT * FROM `sensei-ai-dev.serving_layer_ml.features_daily` LIMIT 10"
        ]
        
        # Test de requêtes interdites
        forbidden_queries = [
            "DROP TABLE `sensei-ai-dev.raw_data.prospects`",
            "DELETE FROM `sensei-ai-dev.raw_data.prospects`",
            "CREATE TABLE test AS SELECT * FROM `sensei-ai-dev.raw_data.prospects`"
        ]
        
        print("✅ Test des requêtes autorisées:")
        for query in allowed_queries:
            is_valid = client._validate_query_permissions(query)
            status = "✅ OK" if is_valid else "❌ BLOQUÉ"
            print(f"  {status} {query[:50]}...")
        
        print("\n🚫 Test des requêtes interdites:")
        for query in forbidden_queries:
            is_valid = client._validate_query_permissions(query)
            status = "✅ BLOQUÉ" if not is_valid else "❌ AUTORISÉ"
            print(f"  {status} {query[:50]}...")
        
        print("✅ Système de sécurité validé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur sécurité: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test."""
    print("🚀 Test de connexion BigQuery Production - Sensei AI Suite")
    print("=" * 70)
    
    # Configuration
    os.environ['ENVIRONMENT'] = 'production'
    os.environ['LOG_LEVEL'] = 'INFO'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'sensei-ai-dev'
    
    # Tests séquentiels
    tests_results = {}
    
    # 1. Connexion de base
    client = test_bigquery_connection()
    tests_results['connexion'] = client is not None
    
    if not client:
        print("\n❌ Impossible de continuer sans connexion BigQuery")
        return 1
    
    # 2. Accès projet
    datasets = test_project_access(client)
    tests_results['projet'] = len(datasets) > 0
    
    # 3. Datasets Sensei
    sensei_datasets = test_sensei_datasets(client)
    tests_results['datasets_sensei'] = len(sensei_datasets) > 0
    
    # 4. Données brutes
    raw_data = test_raw_data_access(client)
    tests_results['raw_data'] = raw_data is not None
    
    # 5. Serving layer
    serving_data = test_serving_layer_access(client)
    tests_results['serving_layer'] = serving_data is not None
    
    # 6. Sécurité
    security_ok = test_permissions_and_security()
    tests_results['security'] = security_ok
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 Résumé des tests BigQuery:")
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Connexion BigQuery Production validée!")
        print("🚀 Prêt pour les tests avec données réelles!")
        return 0
    else:
        print("⚠️  Problèmes de connexion détectés")
        print("🔧 Vérifiez les credentials et permissions")
        return 1

if __name__ == "__main__":
    sys.exit(main())
