"""
Configuration settings for Sensei AI Suite.
Supports multiple environments and server capacities.
"""

import os
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    from pydantic import BaseSettings, Field


class Environment(str, Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class ServerCapacity(str, Enum):
    """Server capacity levels for training optimization."""
    SMALL = "small"      # Local dev, limited resources
    MEDIUM = "medium"    # Standard server
    LARGE = "large"      # High-performance server
    XLARGE = "xlarge"    # Enterprise server


class Settings(BaseSettings):
    """Main settings class."""
    
    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT)
    debug: bool = Field(default=False)
    log_level: str = Field(default="INFO")
    
    # Project
    project_name: str = Field(default="sensei-ai")
    version: str = Field(default="1.0.0")
    
    # Google Cloud
    google_cloud_project: str = Field(default="datalake-sensei")
    google_application_credentials: Optional[str] = Field(default=None)
    bigquery_location: str = Field(default="EU")
    bigquery_max_bytes_billed: int = Field(default=21474836480)  # 20GB
    
    # Server capacity
    server_capacity: ServerCapacity = Field(default=ServerCapacity.SMALL)
    
    # Training limits based on server capacity
    @property
    def training_limits(self) -> Dict[str, int]:
        """Get training limits based on server capacity."""
        limits = {
            ServerCapacity.SMALL: {
                "max_samples_conversion": 2000,
                "max_samples_channel": 2000,
                "max_samples_nlp": 1000,
                "batch_size": 100,
                "max_memory_gb": 4
            },
            ServerCapacity.MEDIUM: {
                "max_samples_conversion": 10000,
                "max_samples_channel": 10000,
                "max_samples_nlp": 5000,
                "batch_size": 500,
                "max_memory_gb": 16
            },
            ServerCapacity.LARGE: {
                "max_samples_conversion": 50000,
                "max_samples_channel": 50000,
                "max_samples_nlp": 25000,
                "batch_size": 1000,
                "max_memory_gb": 64
            },
            ServerCapacity.XLARGE: {
                "max_samples_conversion": 200000,
                "max_samples_channel": 200000,
                "max_samples_nlp": 100000,
                "batch_size": 2000,
                "max_memory_gb": 256
            }
        }
        return limits[self.server_capacity]
    
    # API
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    api_workers: int = Field(default=1)
    api_reload: bool = Field(default=True)
    
    # Models
    models_dir: Path = Field(default=Path("models"))
    models_registry_file: str = Field(default="registry.json")
    models_cache_ttl: int = Field(default=3600)  # 1 hour
    
    # Security
    enable_cors: bool = Field(default=True)
    cors_origins: list = Field(default=["*"])
    read_only_mode: bool = Field(default=True)
    
    # Monitoring
    enable_metrics: bool = Field(default=True)
    metrics_port: int = Field(default=9090)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


class DevelopmentSettings(Settings):
    """Development environment settings."""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.environment = Environment.DEVELOPMENT
        self.debug = True
        self.log_level = "DEBUG"
        self.api_reload = True
        self.server_capacity = ServerCapacity.SMALL


class StagingSettings(Settings):
    """Staging environment settings."""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.environment = Environment.STAGING
        self.debug = False
        self.log_level = "INFO"
        self.api_reload = False
        self.server_capacity = ServerCapacity.MEDIUM


class ProductionSettings(Settings):
    """Production environment settings."""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.environment = Environment.PRODUCTION
        self.debug = False
        self.log_level = "WARNING"
        self.api_reload = False
        self.cors_origins = []  # Restrict CORS in production
        self.server_capacity = ServerCapacity.LARGE


def get_settings() -> Settings:
    """Get settings based on environment."""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "staging":
        return StagingSettings()
    else:
        return DevelopmentSettings()


# Global settings instance
settings = get_settings()
