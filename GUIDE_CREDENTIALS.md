# 🔐 Guide de Configuration des Credentials Google Cloud

## 📋 Vue d'ensemble

Pour tester Sensei AI Suite avec BigQuery en production, vous devez configurer un **Service Account** avec les bonnes permissions.

---

## 🚀 Configuration Rapide

### Étape 1 : C<PERSON>er/Obtenir le Service Account

1. **Ouvrir Google Cloud Console** : https://console.cloud.google.com/
2. **Sélectionner le projet** : `sensei-ai-dev`
3. **Navigation** : IAM & Admin > Service Accounts
4. **Créer ou sélectionner** un service account existant

### Étape 2 : Configurer les Permissions

Le service account doit avoir ces rôles **minimum** :

```
✅ BigQuery Data Viewer          # Lecture des données
✅ BigQuery Job User             # Exécution de requêtes  
✅ BigQuery Metadata Viewer      # Accès aux métadonnées
✅ BigQuery Read Session User    # Sessions de lecture (optionnel)
```

**Permissions recommandées pour les tests :**
```
✅ BigQuery Data Editor          # Si vous voulez tester l'écriture
✅ BigQuery Admin               # Pour les tests complets (temporaire)
```

### Étape 3 : Télécharger la Clé JSON

1. **Cliquer sur le service account**
2. **Onglet "Keys"**
3. **"Add Key" > "Create new key"**
4. **Format : JSON**
5. **Télécharger** le fichier (ex: `sensei-ai-dev-12345.json`)

### Étape 4 : Placer le Fichier

```bash
# Placer le fichier dans le projet
mv ~/Downloads/sensei-ai-dev-12345.json credentials/sensei-ai-service-account.json
```

### Étape 5 : Configuration Automatique

```bash
# Exécuter le script de configuration
./scripts/configure_credentials.sh

# Charger les variables d'environnement
source .env.local

# Tester la configuration
python test_credentials_setup.py
```

---

## 🔍 Validation Étape par Étape

### Test 1 : Fichier de Credentials
```bash
python test_credentials_setup.py
```

**Résultat attendu :**
```
✅ Variable définie: /path/to/credentials/sensei-ai-service-account.json
✅ Fichier trouvé: sensei-ai-service-account.json
✅ Structure JSON valide
  📋 Type: service_account
  📋 Projet: sensei-ai-dev
  📧 Email: <EMAIL>
```

### Test 2 : Connexion BigQuery
```bash
python test_bigquery_production.py
```

**Résultat attendu :**
```
✅ Client BigQuery créé
  📋 Projet: sensei-ai-dev
✅ Accès au projet validé
  📊 Datasets trouvés: 4
    📋 raw_data
    📋 serving_layer_ml
    📋 analytics
    📋 staging
```

---

## 🛠️ Dépannage

### Problème : "DefaultCredentialsError"
```bash
❌ Your default credentials were not found
```

**Solutions :**
1. Vérifiez que `GOOGLE_APPLICATION_CREDENTIALS` est défini
2. Vérifiez que le fichier JSON existe
3. Rechargez les variables : `source .env.local`

### Problème : "Permission denied"
```bash
❌ 403 Access Denied: BigQuery BigQuery: Permission denied
```

**Solutions :**
1. Vérifiez les rôles du service account
2. Ajoutez `BigQuery Data Viewer` minimum
3. Attendez 5-10 minutes pour la propagation

### Problème : "Project not found"
```bash
❌ 404 Not found: Project sensei-ai-dev
```

**Solutions :**
1. Vérifiez l'orthographe du projet
2. Vérifiez que le service account appartient au bon projet
3. Vérifiez l'accès au projet

### Problème : "Dataset not found"
```bash
❌ 404 Not found: Dataset sensei-ai-dev:raw_data
```

**Solutions :**
1. Vérifiez que les datasets existent
2. Vérifiez les permissions sur les datasets spécifiques
3. Contactez l'admin pour accès aux données

---

## 🔒 Sécurité

### ✅ Bonnes Pratiques

- **Permissions minimales** : Seulement les rôles nécessaires
- **Rotation des clés** : Changez les clés régulièrement
- **Pas de commit** : Jamais de credentials dans Git
- **Environnement local** : Utilisez `.env.local` pour les tests

### ❌ À Éviter

- **Rôles trop larges** : Évitez `Owner` ou `Editor`
- **Clés permanentes** : Préférez les clés temporaires
- **Partage de clés** : Une clé par développeur/environnement
- **Stockage non sécurisé** : Pas dans le cloud public

---

## 🎯 Tests Recommandés

### Test Minimal (Lecture seule)
```bash
# Test de base
python test_credentials_setup.py

# Test BigQuery simple  
python test_bigquery_production.py
```

### Test Complet (Avec données)
```bash
# Test du Feature Store
python -c "
from sensei.features.build_store import FeatureStoreBuilder
builder = FeatureStoreBuilder('sensei-ai-dev')
print('✅ Feature Store accessible')
"

# Test des modèles
python -c "
from sensei.models.conversion import ConversionModel
model = ConversionModel()
print('✅ Modèles ML prêts')
"
```

---

## 📞 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** : Les erreurs détaillées sont dans les traces
2. **Testez étape par étape** : Utilisez `test_credentials_setup.py`
3. **Vérifiez les permissions** : Dans Google Cloud Console
4. **Contactez l'admin** : Pour l'accès aux datasets Sensei

---

## ✅ Checklist Finale

Avant de continuer avec les tests en production :

- [ ] Service account créé avec bonnes permissions
- [ ] Fichier JSON téléchargé et placé dans `credentials/`
- [ ] Script `configure_credentials.sh` exécuté
- [ ] Variables d'environnement chargées (`source .env.local`)
- [ ] Test `test_credentials_setup.py` réussi
- [ ] Test `test_bigquery_production.py` réussi
- [ ] Accès aux datasets Sensei confirmé

**🎉 Une fois cette checklist complète, vous êtes prêt pour les tests en production !**
