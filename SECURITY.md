# 🔒 Politique de Sécurité - Sensei AI Suite

## 🎯 **Vue d'Ensemble**

La sécurité est une priorité absolue pour Sensei AI Suite. Ce document décrit nos politiques de sécurité, les vulnérabilités supportées, et comment signaler des problèmes de sécurité.

## 🛡️ **Versions Supportées**

| Version | Support Sécurité | Fin de Support |
|---------|------------------|----------------|
| 1.0.x   | ✅ Support complet | 2025-12-31 |
| 0.x.x   | ❌ Non supporté | 2024-12-31 |

## 🚨 **Signalement de Vulnérabilités**

### **Processus de Signalement**

Si vous découvrez une vulnérabilité de sécurité, **NE PAS** créer d'issue publique. Suivez ce processus :

1. **Email sécurisé** : <EMAIL>
2. **Objet** : `[SECURITY] Description courte de la vulnérabilité`
3. **Contenu** : Description détaillée avec étapes de reproduction

### **Informations à Inclure**

- **Type de vulnérabilité** (injection, XSS, CSRF, etc.)
- **Composant affecté** (API, modèles, infrastructure)
- **Impact potentiel** (confidentialité, intégrité, disponibilité)
- **Étapes de reproduction** détaillées
- **Preuve de concept** (si applicable)
- **Suggestions de correction** (optionnel)

### **Temps de Réponse**

- **Accusé de réception** : 24 heures
- **Évaluation initiale** : 72 heures
- **Correction critique** : 7 jours
- **Correction standard** : 30 jours

## 🔐 **Mesures de Sécurité Implémentées**

### **Architecture Sécurisée**

#### **Accès aux Données**
- ✅ **Read-Only par défaut** : Aucune modification des données production
- ✅ **Permissions minimales** : Accès limité aux tables `serving_layer.*`
- ✅ **Limitation des coûts** : Max 20GB par requête BigQuery
- ✅ **Audit complet** : Logging de tous les accès aux données

#### **API et Authentification**
- ✅ **Validation stricte** : Pydantic v2 pour tous les inputs
- ✅ **Rate limiting** : Protection contre les attaques DDoS
- ✅ **CORS configuré** : Restriction des origines en production
- ✅ **HTTPS obligatoire** : TLS 1.3 minimum en production

#### **Infrastructure**
- ✅ **Conteneurisation** : Isolation des processus avec Docker
- ✅ **Utilisateur non-root** : Exécution avec privilèges minimaux
- ✅ **Secrets management** : Variables d'environnement sécurisées
- ✅ **Monitoring** : Détection d'anomalies en temps réel

### **Sécurité du Code**

#### **Développement Sécurisé**
```python
# ✅ Bon exemple - Validation stricte
from pydantic import BaseModel, Field, validator

class SecureInput(BaseModel):
    user_id: str = Field(..., regex="^[a-zA-Z0-9_-]+$", max_length=50)
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if any(char in v for char in ['<', '>', '"', "'"]):
            raise ValueError("Caractères dangereux détectés")
        return v

# ❌ Mauvais exemple - Injection possible
def unsafe_query(user_input: str):
    query = f"SELECT * FROM table WHERE id = '{user_input}'"  # DANGER!
    return execute_query(query)
```

#### **Gestion des Secrets**
```python
# ✅ Bon exemple - Variables d'environnement
import os
from config.settings import settings

def get_credentials():
    return os.getenv('GOOGLE_APPLICATION_CREDENTIALS')

# ❌ Mauvais exemple - Credentials en dur
API_KEY = "sk-1234567890abcdef"  # JAMAIS FAIRE ÇA!
```

#### **Logging Sécurisé**
```python
# ✅ Bon exemple - Données sensibles filtrées
from sensei.utils.logging import get_logger

logger = get_logger(__name__)

def process_user_data(email: str, password: str):
    logger.info("Traitement utilisateur", email=email[:3] + "***")
    # password n'est jamais loggé

# ❌ Mauvais exemple - Exposition de données sensibles
logger.info(f"Login attempt: {email} with password {password}")  # DANGER!
```

## 🔍 **Audit et Monitoring**

### **Logs de Sécurité**

Tous les événements de sécurité sont loggés :

```json
{
  "timestamp": "2025-01-18T12:34:56Z",
  "level": "WARNING",
  "event": "authentication_failure",
  "source_ip": "*************",
  "user_agent": "curl/7.68.0",
  "endpoint": "/predict/conversion",
  "reason": "invalid_token"
}
```

### **Métriques de Sécurité**

- **Tentatives d'authentification échouées**
- **Requêtes avec payloads suspects**
- **Accès aux endpoints sensibles**
- **Erreurs de validation des inputs**
- **Utilisation anormale des ressources**

### **Alertes Automatiques**

```bash
# Configuration des alertes
export SECURITY_WEBHOOK_URL="https://hooks.slack.com/..."
export ALERT_THRESHOLD_FAILED_AUTH=10
export ALERT_THRESHOLD_SUSPICIOUS_REQUESTS=50
```

## 🛠️ **Configuration Sécurisée**

### **Variables d'Environnement Obligatoires**

```env
# Sécurité de base
READ_ONLY_MODE=true
ENABLE_CORS=false  # En production
LOG_LEVEL=WARNING  # En production

# Authentification
API_KEY_REQUIRED=true
JWT_SECRET_KEY=your-super-secret-key-here

# Limites de sécurité
MAX_REQUEST_SIZE=10MB
RATE_LIMIT_PER_MINUTE=100
BIGQUERY_MAX_BYTES_BILLED=21474836480
```

### **Configuration Docker Sécurisée**

```dockerfile
# Utilisateur non-root
RUN useradd --create-home --shell /bin/bash sensei
USER sensei

# Permissions minimales
RUN chmod 750 /app
RUN chmod 640 /app/config/*

# Pas de secrets dans l'image
# Utiliser des volumes ou secrets Kubernetes
```

### **Configuration Kubernetes**

```yaml
apiVersion: v1
kind: SecurityContext
spec:
  runAsNonRoot: true
  runAsUser: 1000
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
```

## 🔒 **Bonnes Pratiques pour les Développeurs**

### **Checklist Sécurité**

Avant chaque commit :

- [ ] **Pas de secrets** en dur dans le code
- [ ] **Validation** de tous les inputs utilisateur
- [ ] **Logging sécurisé** sans données sensibles
- [ ] **Gestion d'erreurs** sans exposition d'informations
- [ ] **Tests de sécurité** pour les nouvelles fonctionnalités

### **Outils de Sécurité**

```bash
# Scan des secrets
poetry run bandit -r src/

# Audit des dépendances
poetry audit

# Scan des vulnérabilités
safety check

# Analyse statique
poetry run semgrep --config=auto src/
```

### **Tests de Sécurité**

```python
import pytest
from fastapi.testclient import TestClient

def test_sql_injection_protection():
    """Test protection contre l'injection SQL."""
    malicious_input = "'; DROP TABLE users; --"
    
    response = client.post("/predict/conversion", json={
        "id_prospect": malicious_input
    })
    
    # Doit être rejeté par la validation
    assert response.status_code == 422

def test_xss_protection():
    """Test protection contre XSS."""
    xss_payload = "<script>alert('xss')</script>"
    
    response = client.post("/predict/nlp", json={
        "content": xss_payload
    })
    
    # Contenu doit être échappé
    assert "<script>" not in response.json()["content"]
```

## 🚨 **Incident Response**

### **Procédure d'Urgence**

En cas d'incident de sécurité :

1. **Isolation** : Arrêter les services affectés
2. **Évaluation** : Déterminer l'impact et la cause
3. **Containment** : Limiter la propagation
4. **Éradication** : Supprimer la menace
5. **Recovery** : Restaurer les services
6. **Lessons Learned** : Analyser et améliorer

### **Contacts d'Urgence**

- **Équipe sécurité** : <EMAIL>
- **Responsable technique** : <EMAIL>
- **Astreinte 24/7** : +33 1 23 45 67 89

### **Communication**

- **Interne** : Slack #security-incidents
- **Externe** : Status page et email clients
- **Régulateurs** : CNIL si données personnelles

## 📋 **Compliance et Réglementation**

### **RGPD (GDPR)**

- ✅ **Privacy by Design** : Sécurité dès la conception
- ✅ **Minimisation** : Collecte minimale de données
- ✅ **Pseudonymisation** : Hash des identifiants
- ✅ **Droit à l'oubli** : Suppression sur demande
- ✅ **Audit trail** : Traçabilité complète

### **Standards de Sécurité**

- **ISO 27001** : Management de la sécurité
- **OWASP Top 10** : Protection contre les vulnérabilités web
- **NIST Framework** : Gestion des risques cyber
- **SOC 2 Type II** : Contrôles de sécurité

## 🔄 **Mises à Jour de Sécurité**

### **Processus de Patch**

1. **Évaluation** : Criticité et impact
2. **Test** : Validation en environnement de test
3. **Déploiement** : Mise en production progressive
4. **Vérification** : Contrôle post-déploiement

### **Calendrier**

- **Critiques** : Immédiat (< 24h)
- **Importantes** : Hebdomadaire
- **Mineures** : Mensuel
- **Préventives** : Trimestriel

## 📞 **Contact Sécurité**

### **Équipe Sécurité**

- **Email** : <EMAIL>
- **PGP Key** : [Clé publique](https://sensei-ai.com/pgp-key.asc)
- **Bug Bounty** : <EMAIL>

### **Reconnaissance**

Nous remercions les chercheurs en sécurité qui signalent des vulnérabilités de manière responsable. Les contributeurs peuvent être mentionnés dans nos remerciements (avec leur accord).

---

**🛡️ La sécurité est l'affaire de tous. Merci de nous aider à maintenir Sensei AI Suite sécurisé !**
