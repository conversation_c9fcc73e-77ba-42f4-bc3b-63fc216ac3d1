[tool.poetry]
name = "sensei-ai"
version = "1.0.0"
description = "Optimisation de la prospection marketing & commerciale grâce à l'IA"
authors = ["Sensei Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "sensei", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
# Data & ML
pandas = "^2.1.0"
numpy = "^1.24.0"
scikit-learn = "^1.3.0"
lightgbm = "^4.1.0"
catboost = "^1.2.0"
# NLP
sentence-transformers = "^2.2.0"
umap-learn = "^0.5.0"
hdbscan = "^0.8.0"
# BigQuery & GCP
google-cloud-bigquery = "^3.11.0"
google-cloud-secret-manager = "^2.16.0"
db-dtypes = "^1.1.0"
# CLI & Utils
typer = {extras = ["all"], version = "^0.9.0"}
jinja2 = "^3.1.0"
structlog = "^23.1.0"
pydantic = "^2.4.0"
# Optimization
optuna = "^3.4.0"
# Monitoring
mlflow = "^2.7.0"

[tool.poetry.group.dev.dependencies]
# Code quality
black = "^23.9.0"
isort = "^5.12.0"
mypy = "^1.5.0"
# Testing
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.0"
pytest-asyncio = "^0.21.0"
# Type stubs
types-requests = "^2.31.0"
pandas-stubs = "^2.1.0"

[tool.poetry.scripts]
sensei = "sensei.cli:app"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Configuration des outils
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["sensei"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "lightgbm.*",
    "catboost.*",
    "umap.*",
    "hdbscan.*",
    "optuna.*",
    "mlflow.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/sensei",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=80",
    "--strict-markers",
    "--strict-config",
    "--verbose"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests"
]

[tool.coverage.run]
source = ["src/sensei"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
