version: '3.8'

services:
  sensei-api:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - GOOGLE_CLOUD_PROJECT=datalake-sensei
      - SERVER_CAPACITY=medium
      - READ_ONLY_MODE=true
    volumes:
      - ../models:/app/models:ro
      - ../logs:/app/logs
      - ../credentials:/app/credentials:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  sensei-training:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    environment:
      - ENVIRONMENT=production
      - GOOGLE_CLOUD_PROJECT=datalake-sensei
      - SERVER_CAPACITY=large
      - READ_ONLY_MODE=true
    volumes:
      - ../models:/app/models
      - ../logs:/app/logs
      - ../credentials:/app/credentials:ro
    command: ["python", "scripts/training/train_models.py", "--capacity", "large"]
    profiles:
      - training

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    profiles:
      - monitoring

volumes:
  grafana-storage:
