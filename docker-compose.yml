version: '3.8'

services:
  sensei-ai:
    build:
      context: .
      dockerfile: Dockerfile
    image: sensei-ai:latest
    container_name: sensei-ai-dev
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-sensei-ai-dev}
    volumes:
      # Code source pour le développement
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      # Modèles persistants
      - ./models:/app/models
      # Logs
      - ./logs:/app/logs
      # Credentials (à adapter selon votre setup)
      - ${GOOGLE_APPLICATION_CREDENTIALS:-./service-account.json}:/app/credentials.json:ro
    working_dir: /app
    command: ["--help"]
    networks:
      - sensei-network

  # Service pour les tests
  sensei-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: test
    image: sensei-ai:test
    container_name: sensei-ai-test
    environment:
      - ENVIRONMENT=test
      - LOG_LEVEL=DEBUG
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./coverage:/app/coverage
    working_dir: /app
    command: ["pytest", "--cov=src/sensei", "--cov-report=html:/app/coverage"]
    networks:
      - sensei-network

  # Service pour le développement avec hot reload
  sensei-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: sensei-ai:dev
    container_name: sensei-ai-dev-server
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-sensei-ai-dev}
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./models:/app/models
      - ./logs:/app/logs
    working_dir: /app
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    networks:
      - sensei-network

networks:
  sensei-network:
    driver: bridge

volumes:
  models:
    driver: local
  logs:
    driver: local
