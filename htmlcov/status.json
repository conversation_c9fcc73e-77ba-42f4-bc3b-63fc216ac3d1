{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "93a87c94f4845ff26983abee9bcb01de", "files": {"z_225ebce00ad1f26e_cli_py": {"hash": "950a3c1ab97898711ae5a8a50d7e786a", "index": {"url": "z_225ebce00ad1f26e_cli_py.html", "file": "src/sensei/cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 2, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5202a09847f578b_bq_client_py": {"hash": "e30b8089b99f51feb0e635725c2ac647", "index": {"url": "z_d5202a09847f578b_bq_client_py.html", "file": "src/sensei/data/bq_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49f17e046f1d4cbf_build_store_py": {"hash": "38a5071626b912509222bf5e4dde0d7e", "index": {"url": "z_49f17e046f1d4cbf_build_store_py.html", "file": "src/sensei/features/build_store.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 4, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_base_py": {"hash": "8b058054326b7d4be638c84b6ca1b81d", "index": {"url": "z_700a5f020f40201c_base_py.html", "file": "src/sensei/models/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_channel_py": {"hash": "ef1eb11b75c6747eac4cee9f86f8b8ff", "index": {"url": "z_700a5f020f40201c_channel_py.html", "file": "src/sensei/models/channel.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_conversion_py": {"hash": "a56c29552fce777d954409782224009c", "index": {"url": "z_700a5f020f40201c_conversion_py.html", "file": "src/sensei/models/conversion.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_nlp_signals_py": {"hash": "30125f6809c79501b99664281af0af2b", "index": {"url": "z_700a5f020f40201c_nlp_signals_py.html", "file": "src/sensei/models/nlp_signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_af055d2789dbda91_secure_pipeline_py": {"hash": "717cbf866b51d8c41a5668a338ec6e91", "index": {"url": "z_af055d2789dbda91_secure_pipeline_py.html", "file": "src/sensei/pipelines/secure_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_logging_py": {"hash": "ae0a9ca058fe87acfeb7a50c003da290", "index": {"url": "z_27f2730d026b53db_logging_py.html", "file": "src/sensei/utils/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_secrets_py": {"hash": "0902b7e99cbca8b34c55d420ef924a1b", "index": {"url": "z_27f2730d026b53db_secrets_py.html", "file": "src/sensei/utils/secrets.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_security_py": {"hash": "1508e65a64b56b088ada5c7270ac50c7", "index": {"url": "z_27f2730d026b53db_security_py.html", "file": "src/sensei/utils/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}