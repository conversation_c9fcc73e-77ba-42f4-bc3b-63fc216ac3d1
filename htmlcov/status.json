{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "93a87c94f4845ff26983abee9bcb01de", "files": {"z_225ebce00ad1f26e_cli_py": {"hash": "950a3c1ab97898711ae5a8a50d7e786a", "index": {"url": "z_225ebce00ad1f26e_cli_py.html", "file": "src/sensei/cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 2, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d5202a09847f578b_bq_client_py": {"hash": "68e67c912b9df1e450fbce192dfcb76e", "index": {"url": "z_d5202a09847f578b_bq_client_py.html", "file": "src/sensei/data/bq_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49f17e046f1d4cbf_build_store_py": {"hash": "cffdc0a4d94c567e6c9c6ff188c6dc0e", "index": {"url": "z_49f17e046f1d4cbf_build_store_py.html", "file": "src/sensei/features/build_store.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 4, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_base_py": {"hash": "d034fbe675e8909eaa95296da39955c9", "index": {"url": "z_700a5f020f40201c_base_py.html", "file": "src/sensei/models/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_channel_py": {"hash": "dac71e563f0e142fa01323cbf98d2159", "index": {"url": "z_700a5f020f40201c_channel_py.html", "file": "src/sensei/models/channel.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 32, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_conversion_py": {"hash": "afe6420ae5f51818dc9275ef89c5ade4", "index": {"url": "z_700a5f020f40201c_conversion_py.html", "file": "src/sensei/models/conversion.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_700a5f020f40201c_nlp_signals_py": {"hash": "b3710f64bf0a7421ae3deda1e61f5f3d", "index": {"url": "z_700a5f020f40201c_nlp_signals_py.html", "file": "src/sensei/models/nlp_signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_af055d2789dbda91_secure_pipeline_py": {"hash": "717cbf866b51d8c41a5668a338ec6e91", "index": {"url": "z_af055d2789dbda91_secure_pipeline_py.html", "file": "src/sensei/pipelines/secure_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_logging_py": {"hash": "ae0a9ca058fe87acfeb7a50c003da290", "index": {"url": "z_27f2730d026b53db_logging_py.html", "file": "src/sensei/utils/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_secrets_py": {"hash": "7328cc432e61cdf2cb1f4fde8a3b68d0", "index": {"url": "z_27f2730d026b53db_secrets_py.html", "file": "src/sensei/utils/secrets.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_27f2730d026b53db_security_py": {"hash": "1508e65a64b56b088ada5c7270ac50c7", "index": {"url": "z_27f2730d026b53db_security_py.html", "file": "src/sensei/utils/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}