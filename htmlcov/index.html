<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">15%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:23 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html">src/sensei/cli.py</a></td>
                <td>187</td>
                <td>187</td>
                <td>2</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html">src/sensei/data/bq_client.py</a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html">src/sensei/features/build_store.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>4</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html">src/sensei/models/base.py</a></td>
                <td>134</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="45 134">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html">src/sensei/models/channel.py</a></td>
                <td>173</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="32 173">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html">src/sensei/models/conversion.py</a></td>
                <td>112</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="23 112">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html">src/sensei/models/nlp_signals.py</a></td>
                <td>156</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="57 156">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html">src/sensei/utils/logging.py</a></td>
                <td>29</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="28 29">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html">src/sensei/utils/secrets.py</a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html">src/sensei/utils/security.py</a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1200</td>
                <td>1015</td>
                <td>6</td>
                <td class="right" data-ratio="185 1200">15%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:23 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_27f2730d026b53db_security_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_225ebce00ad1f26e_cli_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
