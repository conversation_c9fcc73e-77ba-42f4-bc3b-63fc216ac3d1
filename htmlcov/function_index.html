<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">50%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:05 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t35">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t35"><data value='main'>main</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t61">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t61"><data value='build_features'>build_features</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t118">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t118"><data value='train'>train</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t254">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t254"><data value='score'>score</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t390">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t390"><data value='list_models'>list_models</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t415">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html#t415"><data value='status'>status</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>2</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t34">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t34"><data value='init__'>SecureBigQueryClient.__init__</data></a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t87">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t87"><data value='validate_query_permissions'>SecureBigQueryClient._validate_query_permissions</data></a></td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t123">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t123"><data value='query_df'>SecureBigQueryClient.query_df</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t186">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t186"><data value='execute_query'>SecureBigQueryClient.execute_query</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t247">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t247"><data value='get_bq_client'>get_bq_client</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t24">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t24"><data value='init__'>FeatureStoreBuilder.__init__</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t51">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t51"><data value='render_sql_template'>FeatureStoreBuilder._render_sql_template</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t65">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t65"><data value='create_dataset_if_not_exists'>FeatureStoreBuilder.create_dataset_if_not_exists</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t106">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t106"><data value='build_features_daily'>FeatureStoreBuilder.build_features_daily</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t155">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t155"><data value='create_audit_table'>FeatureStoreBuilder.create_audit_table</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t184">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t184"><data value='setup_ml_dataset'>FeatureStoreBuilder.setup_ml_dataset</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t199">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t199"><data value='build_features_for_date'>build_features_for_date</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t27">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t27"><data value='register_model'>register_model</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t34">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t34"><data value='decorator'>register_model.decorator</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t41">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t41"><data value='get_model_class'>get_model_class</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t70">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t70"><data value='init__'>BaseModel.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t92">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t92"><data value='create_model'>BaseModel._create_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t102">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t102"><data value='prepare_features'>BaseModel._prepare_features</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t114">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t114"><data value='validate_data'>BaseModel._validate_data</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t136">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t136"><data value='train'>BaseModel.train</data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t215">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t215"><data value='fit_model'>BaseModel._fit_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t233">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t233"><data value='predict'>BaseModel.predict</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t265">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t265"><data value='evaluate_model'>BaseModel._evaluate_model</data></a></td>
                <td>11</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="3 11">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t307">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t307"><data value='save'>BaseModel.save</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t346">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t346"><data value='load'>BaseModel.load</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t377">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t377"><data value='get_feature_importance'>BaseModel.get_feature_importance</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t53">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t53"><data value='init__'>ChannelModel.__init__</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t83">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t83"><data value='create_model'>ChannelModel._create_model</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t92">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t92"><data value='create_channel_timing_target'>ChannelModel._create_channel_timing_target</data></a></td>
                <td>23</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="22 23">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t151">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t151"><data value='identify_feature_types'>ChannelModel._identify_feature_types</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t183">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t183"><data value='prepare_features'>ChannelModel._prepare_features</data></a></td>
                <td>35</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="32 35">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t273">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t273"><data value='train'>ChannelModel.train</data></a></td>
                <td>26</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="15 26">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t356">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t356"><data value='fit_model'>ChannelModel._fit_model</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t396">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t396"><data value='evaluate_channel_model'>ChannelModel._evaluate_channel_model</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t442">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t442"><data value='predict'>ChannelModel.predict</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t471">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t471"><data value='predict_channel_timing'>ChannelModel.predict_channel_timing</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t37">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t37"><data value='init__'>ConversionModel.__init__</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t68">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t68"><data value='create_model'>ConversionModel._create_model</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t77">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t77"><data value='identify_feature_types'>ConversionModel._identify_feature_types</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t109">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t109"><data value='prepare_features'>ConversionModel._prepare_features</data></a></td>
                <td>35</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="28 35">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t192">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t192"><data value='fit_model'>ConversionModel._fit_model</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t235">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t235"><data value='optimize_hyperparameters'>ConversionModel.optimize_hyperparameters</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t267">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t267"><data value='objective'>ConversionModel.optimize_hyperparameters.objective</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t40">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t40"><data value='init__'>NlpSignalsModel.__init__</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t79">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t79"><data value='create_model'>NlpSignalsModel._create_model</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t89">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t89"><data value='preprocess_text'>NlpSignalsModel._preprocess_text</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t127">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t127"><data value='prepare_features'>NlpSignalsModel._prepare_features</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t166">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t166"><data value='create_embeddings'>NlpSignalsModel._create_embeddings</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t200">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t200"><data value='reduce_dimensions'>NlpSignalsModel._reduce_dimensions</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t230">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t230"><data value='cluster_embeddings'>NlpSignalsModel._cluster_embeddings</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t263">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t263"><data value='extract_cluster_topics'>NlpSignalsModel._extract_cluster_topics</data></a></td>
                <td>26</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="22 26">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t343">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t343"><data value='train'>NlpSignalsModel.train</data></a></td>
                <td>19</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="18 19">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t419">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t419"><data value='predict'>NlpSignalsModel.predict</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t455">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t455"><data value='get_cluster_analysis'>NlpSignalsModel.get_cluster_analysis</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t480">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t480"><data value='fit_model'>NlpSignalsModel._fit_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t27">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t27"><data value='init__'>SecureMLPipeline.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t65">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t65"><data value='validate_access'>SecureMLPipeline._validate_access</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t80">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t80"><data value='apply_consent_filter'>SecureMLPipeline._apply_consent_filter</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t95">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t95"><data value='compute_feature_hash'>SecureMLPipeline._compute_feature_hash</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t120">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t120"><data value='anonymize_data'>SecureMLPipeline._anonymize_data</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t151">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t151"><data value='secure_train'>SecureMLPipeline.secure_train</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t230">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t230"><data value='secure_predict'>SecureMLPipeline.secure_predict</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t332">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t332"><data value='save_predictions_to_bq'>SecureMLPipeline._save_predictions_to_bq</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t348">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t348"><data value='save_audit_records_to_bq'>SecureMLPipeline._save_audit_records_to_bq</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t364">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t364"><data value='generate_privacy_report'>SecureMLPipeline.generate_privacy_report</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t14">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t14"><data value='configure_logging'>configure_logging</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t40">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t40"><data value='add_service_metadata'>configure_logging.add_service_metadata</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t75">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t75"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t89">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html#t89"><data value='auto_configure'>_auto_configure</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t17">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t17"><data value='init__'>SecretManager.__init__</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t33">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t33"><data value='get_secret'>SecretManager.get_secret</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t68">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t68"><data value='get_bigquery_credentials'>SecretManager.get_bigquery_credentials</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t77">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t77"><data value='get_database_url'>SecretManager.get_database_url</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t91">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t91"><data value='get_secret_manager'>get_secret_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t104">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t104"><data value='get_secret'>get_secret</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t29">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t29"><data value='init__'>PIIHasher.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t41">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t41"><data value='hash_identifier'>PIIHasher.hash_identifier</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t63">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t63"><data value='hash_dataframe_columns'>PIIHasher.hash_dataframe_columns</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t105">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t105"><data value='init__'>AuditLogger.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t115">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t115"><data value='create_audit_record'>AuditLogger.create_audit_record</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t159">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t159"><data value='log_prediction_batch'>AuditLogger.log_prediction_batch</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t221">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t221"><data value='init__'>DataRetentionManager.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t230">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t230"><data value='get_retention_date'>DataRetentionManager.get_retention_date</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t248">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t248"><data value='generate_cleanup_queries'>DataRetentionManager.generate_cleanup_queries</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t291">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t291"><data value='init__'>ConsentManager.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t300">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t300"><data value='check_processing_consent'>ConsentManager.check_processing_consent</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t324">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t324"><data value='filter_consented_prospects'>ConsentManager.filter_consented_prospects</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t353">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t353"><data value='log_consent_check'>ConsentManager.log_consent_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t375">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t375"><data value='validate_model_access'>SecurityValidator.validate_model_access</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t407">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t407"><data value='validate_data_export'>SecurityValidator.validate_data_export</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1185</td>
                <td>594</td>
                <td>6</td>
                <td class="right" data-ratio="591 1185">50%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:05 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
