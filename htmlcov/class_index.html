<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">50%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:05 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html">src/sensei/cli.py</a></td>
                <td class="name left"><a href="z_225ebce00ad1f26e_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>187</td>
                <td>187</td>
                <td>2</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t20">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html#t20"><data value='SecureBigQueryClient'>SecureBigQueryClient</data></a></td>
                <td>62</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="34 62">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html">src/sensei/data/bq_client.py</a></td>
                <td class="name left"><a href="z_d5202a09847f578b_bq_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="20 23">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t19">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html#t19"><data value='FeatureStoreBuilder'>FeatureStoreBuilder</data></a></td>
                <td>50</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="41 50">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html">src/sensei/features/build_store.py</a></td>
                <td class="name left"><a href="z_49f17e046f1d4cbf_build_store_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t59">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html#t59"><data value='BaseModel'>BaseModel</data></a></td>
                <td>88</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="36 88">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html">src/sensei/models/base.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t27">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html#t27"><data value='ChannelModel'>ChannelModel</data></a></td>
                <td>137</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="105 137">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html">src/sensei/models/channel.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_channel_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t26">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html#t26"><data value='ConversionModel'>ConversionModel</data></a></td>
                <td>88</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="53 88">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html">src/sensei/models/conversion.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_conversion_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t28">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html#t28"><data value='NlpSignalsModel'>NlpSignalsModel</data></a></td>
                <td>122</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="101 122">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html">src/sensei/models/nlp_signals.py</a></td>
                <td class="name left"><a href="z_700a5f020f40201c_nlp_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t22">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html#t22"><data value='SecureMLPipeline'>SecureMLPipeline</data></a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html">src/sensei/pipelines/secure_pipeline.py</a></td>
                <td class="name left"><a href="z_af055d2789dbda91_secure_pipeline_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html">src/sensei/utils/logging.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="28 29">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t14">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html#t14"><data value='SecretManager'>SecretManager</data></a></td>
                <td>16</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html">src/sensei/utils/secrets.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_secrets_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="14 17">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t21">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t21"><data value='PIIHasher'>PIIHasher</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t100">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t100"><data value='AuditLogger'>AuditLogger</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t207">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t207"><data value='DataRetentionManager'>DataRetentionManager</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t286">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t286"><data value='ConsentManager'>ConsentManager</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t369">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html#t369"><data value='SecurityValidator'>SecurityValidator</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html">src/sensei/utils/security.py</a></td>
                <td class="name left"><a href="z_27f2730d026b53db_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1185</td>
                <td>594</td>
                <td>6</td>
                <td class="right" data-ratio="591 1185">50%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-16 17:05 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
